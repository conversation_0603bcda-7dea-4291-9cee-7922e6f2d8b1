// src/utils/responseWrapper.js

/**
 * Standardized API response wrapper
 * Format: {success: boolean, data: any, message?: string, errors?: any}
 */

/**
 * Send a successful response
 * @param {Object} res - Express response object
 * @param {any} data - Response data
 * @param {string} message - Optional success message
 * @param {number} statusCode - HTTP status code (default: 200)
 */
const sendSuccess = (res, data = null, message = null, statusCode = 200) => {
  const response = {
    success: true,
    data
  };

  if (message) {
    response.message = message;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send an error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {any} errors - Detailed error information
 * @param {number} statusCode - HTTP status code (default: 400)
 */
const sendError = (res, message, errors = null, statusCode = 400) => {
  const response = {
    success: false,
    message
  };

  if (errors) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send a validation error response
 * @param {Object} res - Express response object
 * @param {Array} validationErrors - Array of validation errors
 */
const sendValidationError = (res, validationErrors) => {
  return sendError(res, 'Validation failed', validationErrors, 422);
};

/**
 * Send an unauthorized error response
 * @param {Object} res - Express response object
 * @param {string} message - Optional custom message
 */
const sendUnauthorized = (res, message = 'Unauthorized') => {
  return sendError(res, message, null, 401);
};

/**
 * Send a forbidden error response
 * @param {Object} res - Express response object
 * @param {string} message - Optional custom message
 */
const sendForbidden = (res, message = 'Forbidden') => {
  return sendError(res, message, null, 403);
};

/**
 * Send a not found error response
 * @param {Object} res - Express response object
 * @param {string} message - Optional custom message
 */
const sendNotFound = (res, message = 'Resource not found') => {
  return sendError(res, message, null, 404);
};

/**
 * Send an internal server error response
 * @param {Object} res - Express response object
 * @param {string} message - Optional custom message
 */
const sendInternalError = (res, message = 'Internal server error') => {
  return sendError(res, message, null, 500);
};

module.exports = {
  sendSuccess,
  sendError,
  sendValidationError,
  sendUnauthorized,
  sendForbidden,
  sendNotFound,
  sendInternalError
};
