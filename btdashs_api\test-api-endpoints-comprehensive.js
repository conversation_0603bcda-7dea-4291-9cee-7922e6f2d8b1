#!/usr/bin/env node

// test-api-endpoints-comprehensive.js - Comprehensive API endpoint testing with real authentication

const axios = require("axios");

// Configuration
const API_BASE_URL = process.env.API_URL || "http://localhost:5000";
const INTERNAL_API_KEY = process.env.INTERNAL_API_KEY || "WVcQAfUkPxMt3nGanMWWBLUIAaw8LJdoBffYbzzCCLs";
const ACCESS_TOKEN =
	"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

// ANSI color codes
const colors = {
	reset: "\x1b[0m",
	bright: "\x1b[1m",
	red: "\x1b[31m",
	green: "\x1b[32m",
	yellow: "\x1b[33m",
	blue: "\x1b[34m",
	cyan: "\x1b[36m",
};

class APITester {
	constructor() {
		this.results = {
			total: 0,
			passed: 0,
			failed: 0,
			errors: [],
		};
		this.startTime = Date.now();
	}

	log(message, color = "reset") {
		console.log(`${colors[color]}${message}${colors.reset}`);
	}

	logHeader(message) {
		const border = "=".repeat(60);
		this.log(border, "cyan");
		this.log(`  ${message}`, "cyan");
		this.log(border, "cyan");
	}

	async testEndpoint(name, endpoint, expectedStatus = 200, options = {}) {
		this.results.total++;

		try {
			const config = {
				method: options.method || "GET",
				url: `${API_BASE_URL}${endpoint}`,
				timeout: 10000,
				validateStatus: () => true, // Don't throw on any status code
				...options,
			};

			// Add authentication headers if specified
			if (options.useAuth) {
				config.headers = {
					...config.headers,
					Authorization: `Bearer ${ACCESS_TOKEN}`,
				};
			}

			if (options.useInternalKey) {
				config.headers = {
					...config.headers,
					"x-internal-api-key": INTERNAL_API_KEY,
				};
			}

			const startTime = Date.now();
			const response = await axios(config);
			const duration = Date.now() - startTime;

			const success = response.status === expectedStatus;

			if (success) {
				this.results.passed++;
				this.log(`✅ ${name} - ${response.status} (${duration}ms)`, "green");

				// Log response structure for successful requests
				if (response.data && typeof response.data === "object") {
					if (response.data.success !== undefined) {
						this.log(`   Success: ${response.data.success}`, "green");
					}
					if (response.data.data && Array.isArray(response.data.data)) {
						this.log(`   Data count: ${response.data.data.length}`, "green");
					}
					if (response.data.message) {
						this.log(`   Message: ${response.data.message}`, "green");
					}
				}
			} else {
				this.results.failed++;
				this.log(`❌ ${name} - Expected ${expectedStatus}, got ${response.status} (${duration}ms)`, "red");

				if (response.data) {
					this.log(`   Response: ${JSON.stringify(response.data).substring(0, 200)}`, "red");
				}

				this.results.errors.push({
					name,
					endpoint,
					expected: expectedStatus,
					actual: response.status,
					response: response.data,
				});
			}

			return success;
		} catch (error) {
			this.results.failed++;
			this.log(`❌ ${name} - ERROR: ${error.message}`, "red");

			this.results.errors.push({
				name,
				endpoint,
				error: error.message,
			});

			return false;
		}
	}

	async runAllTests() {
		this.logHeader("BTDashs API - Comprehensive Endpoint Testing");

		this.log("🚀 Starting API endpoint testing...", "bright");
		this.log(`📊 Configuration:`, "cyan");
		this.log(`   - API Base URL: ${API_BASE_URL}`, "cyan");
		this.log(`   - Using Auth Token: ${ACCESS_TOKEN ? "Yes" : "No"}`, "cyan");
		this.log(`   - Using Internal Key: ${INTERNAL_API_KEY ? "Yes" : "No"}`, "cyan");

		// Test system endpoints
		this.log("\n=== SYSTEM ENDPOINTS ===", "yellow");
		await this.testEndpoint("Health Check", "/health");
		await this.testEndpoint("Monitoring Health", "/monitoring/health");
		await this.testEndpoint("Monitoring Metrics", "/monitoring/metrics");
		await this.testEndpoint("Monitoring Dashboard", "/monitoring/dashboard");
		await this.testEndpoint("API Endpoints Documentation", "/api/endpoints");
		await this.testEndpoint("API Endpoints JSON", "/api/endpoints/json");

		// Test public data endpoints
		this.log("\n=== PUBLIC DATA ENDPOINTS ===", "yellow");
		const publicEndpoints = [
			["Subnets", "/api/subnets"],
			["Companies", "/api/companies"],
			["Jobs", "/api/jobs"],
			["Products", "/api/products"],
			["Events", "/api/events"],
			["News", "/api/news"],
			["Categories", "/api/categories"],
			["Validators", "/api/validators"],
			["Validator Performance", "/api/validator-performance"],
			["Network Prices", "/api/network-prices"],
			["Network Stats", "/api/network-stats"],
			["Skills", "/api/skills"],
		];

		for (const [name, endpoint] of publicEndpoints) {
			await this.testEndpoint(name, endpoint, 200, { useInternalKey: true });
		}

		// Test authenticated endpoints
		this.log("\n=== AUTHENTICATED ENDPOINTS ===", "yellow");
		await this.testEndpoint("User Profile", "/api/user/me", 200, { useAuth: true });
		await this.testEndpoint("User Company", "/api/user/company", 200, { useAuth: true });
		await this.testEndpoint("User Preferences", "/api/user/preferences", 200, { useAuth: true });
		await this.testEndpoint("User Ads", "/api/ads", 200, { useAuth: true });

		// Test authentication failures
		this.log("\n=== AUTHENTICATION TESTS ===", "yellow");
		await this.testEndpoint("User Profile (No Auth)", "/api/user/me", 401);
		await this.testEndpoint("User Ads (No Auth)", "/api/ads", 401);

		// Test internal endpoints
		this.log("\n=== INTERNAL ENDPOINTS ===", "yellow");
		await this.testEndpoint("Ad Slots", "/api/slots", 200, { useInternalKey: true });
		await this.testEndpoint("Update Status", "/api/update/status", 200, { useInternalKey: true });

		// Test error handling
		this.log("\n=== ERROR HANDLING TESTS ===", "yellow");
		await this.testEndpoint("404 Not Found", "/api/nonexistent-endpoint", 404);
		await this.testEndpoint("Invalid Resource ID", "/api/companies/999999", 404, { useInternalKey: true });

		// Test specific resource endpoints
		this.log("\n=== RESOURCE DETAIL TESTS ===", "yellow");
		await this.testEndpoint("Company by ID", "/api/companies/1", 200, { useInternalKey: true });
		await this.testEndpoint("Job by ID", "/api/jobs/47", 200, { useInternalKey: true });
		await this.testEndpoint("Event by ID", "/api/events/1", 200, { useInternalKey: true });

		// Generate final report
		this.generateFinalReport();
	}

	generateFinalReport() {
		const duration = Date.now() - this.startTime;
		const durationSeconds = (duration / 1000).toFixed(2);

		this.logHeader("API Testing Summary");

		// Overall results
		this.log("📈 Overall Results:", "bright");
		this.log(`   Total Tests: ${this.results.total}`, "cyan");
		this.log(`   Passed: ${this.results.passed}`, "green");
		this.log(`   Failed: ${this.results.failed}`, this.results.failed > 0 ? "red" : "cyan");
		this.log(`   Duration: ${durationSeconds}s`, "cyan");

		// Success rate
		const successRate = this.results.total > 0 ? ((this.results.passed / this.results.total) * 100).toFixed(1) : 0;
		this.log(
			`   Success Rate: ${successRate}%`,
			successRate >= 90 ? "green" : successRate >= 70 ? "yellow" : "red"
		);

		// Error details
		if (this.results.errors.length > 0) {
			this.log("\n❌ Failed Tests:", "red");
			this.results.errors.forEach((error, index) => {
				this.log(`   ${index + 1}. ${error.name}`, "red");
				this.log(`      Endpoint: ${error.endpoint}`, "red");
				if (error.expected && error.actual) {
					this.log(`      Expected: ${error.expected}, Got: ${error.actual}`, "red");
				}
				if (error.error) {
					this.log(`      Error: ${error.error}`, "red");
				}
				if (error.response) {
					this.log(`      Response: ${JSON.stringify(error.response).substring(0, 100)}...`, "red");
				}
			});
		}

		// Final status
		const allPassed = this.results.failed === 0;
		this.log("\n🎯 Final Status:", "bright");
		if (allPassed) {
			this.log("   🎉 ALL TESTS PASSED! 🎉", "green");
			this.log("   The API is working correctly with all endpoints responding as expected.", "green");
		} else {
			this.log("   ⚠️  SOME TESTS FAILED", "red");
			this.log("   Please review the failed tests above and check the API implementation.", "red");
		}

		// Recommendations
		this.log("\n💡 Recommendations:", "cyan");
		if (successRate >= 95) {
			this.log("   - Excellent! API is performing very well.", "green");
		} else if (successRate >= 80) {
			this.log("   - Good performance, but some endpoints need attention.", "yellow");
		} else {
			this.log("   - Multiple issues detected. Review API implementation.", "red");
		}

		// Exit with appropriate code
		process.exit(allPassed ? 0 : 1);
	}
}

// Main execution
async function main() {
	const tester = new APITester();

	try {
		await tester.runAllTests();
	} catch (error) {
		tester.log(`💥 Fatal error during API testing: ${error.message}`, "red");
		console.error(error);
		process.exit(1);
	}
}

// Handle process signals
process.on("SIGINT", () => {
	console.log("\n🛑 API testing interrupted by user");
	process.exit(1);
});

process.on("SIGTERM", () => {
	console.log("\n🛑 API testing terminated");
	process.exit(1);
});

// Run the tests
if (require.main === module) {
	main();
}

module.exports = APITester;
