"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Category, Subnet, SubnetMetric } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import { ArrowUpRight, Heart, Pickaxe, Users, Zap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface CompanySubnetsGridProps {
  subnets: (Subnet & {
    metrics?: SubnetMetric;
    categories?: Category[];
  })[];
}

// Define a set of gradient backgrounds for the cards
const gradients = [
  "bg-gradient-to-br from-purple-500 to-indigo-600",
  "bg-gradient-to-br from-blue-500 to-cyan-600",
  "bg-gradient-to-br from-emerald-500 to-teal-600",
  "bg-gradient-to-br from-rose-500 to-pink-600",
  "bg-gradient-to-br from-amber-500 to-orange-600",
  "bg-gradient-to-br from-violet-500 to-purple-600",
  "bg-gradient-to-br from-green-500 to-emerald-600",
  "bg-gradient-to-br from-red-500 to-rose-600",
];

export function CompanySubnetsGrid({ subnets }: CompanySubnetsGridProps) {
  if (!subnets || subnets.length === 0) {
    return (
      <div className="text-center py-12 border rounded-lg">
        <p className="text-muted-foreground">No subnets available</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {subnets.map((subnet, index) => {
        // Get primary category for the badge
        const primaryCategory = subnet.categories?.[0]?.name ?? "Subnet";

        return (
          <Link href={`/subnets/${subnet.netuid}`} key={subnet.netuid}>
            <Card
              className={cn(
                "relative h-full hover:shadow-lg transition-all overflow-hidden text-white",
                gradients[index % gradients.length]
              )}
            >
              <div className="absolute top-4 right-4 bg-white/20 rounded-full p-1.5">
                <ArrowUpRight className="h-4 w-4" />
              </div>

              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="relative w-14 h-14 rounded-full overflow-hidden bg-white/20 p-2 flex items-center justify-center">
                    {subnet.image_url ? (
                      <Image
                        src={subnet.image_url}
                        alt={subnet.name}
                        width={40}
                        height={40}
                        className="object-contain rounded-full"
                      />
                    ) : (
                      <span className="text-black text-xl font-bold">
                        {subnet.subnet_symbol || subnet.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  <div>
                    <h3 className="font-bold text-xl text-white">
                      {subnet.name}
                    </h3>
                    <Badge
                      variant="outline"
                      className="border-white/30 text-white mt-1"
                    >
                      sn: {subnet.netuid}
                    </Badge>
                  </div>
                </div>
                <p className="text-white/90 mb-6 line-clamp-2">
                  {subnet.description_short || "No description available."}
                </p>

                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div className="bg-white/10 rounded-lg p-2 text-center">
                    <Users className="h-4 w-4 mx-auto mb-1" />
                    <span className="block text-xs text-white/70">
                      Validators
                    </span>
                    <span className="font-medium">
                      {subnet.active_validators || 0}
                    </span>
                  </div>
                  <div className="bg-white/10 rounded-lg p-2 text-center">
                    <Zap className="h-4 w-4 mx-auto mb-1" />
                    <span className="block text-xs text-white/70">
                      Emissions
                    </span>
                    <span className="font-medium">
                      {subnet.metrics?.emission || 0}
                    </span>
                  </div>
                  <div className="bg-white/10 rounded-lg p-2 text-center">
                    <Pickaxe className="h-4 w-4 mx-auto mb-1" />
                    <span className="block text-xs text-white/70">Miners</span>
                    <span className="font-medium">
                      {subnet.active_miners || 0}
                    </span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="px-6 pb-6 border-t border-white/10 mt-2 pt-4">
                <Badge className="bg-white/30 hover:bg-white/40 text-white">
                  View Subnet Details
                </Badge>
              </CardFooter>
            </Card>
          </Link>
        );
      })}
    </div>
  );
}
