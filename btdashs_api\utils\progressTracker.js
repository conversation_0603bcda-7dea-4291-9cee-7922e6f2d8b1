class ProgressTracker {
	constructor(totalItems, itemName = "items") {
		this.totalItems = totalItems;
		this.completedItems = 0;
		this.itemName = itemName;
		this.startTime = Date.now();
		this.additionalMetrics = {};
	}

	addMetric(name, value) {
		this.additionalMetrics[name] = value;
	}

	increment(itemsCompleted = 1) {
		this.completedItems += itemsCompleted;
		this.updateDisplay();
	}

	updateDisplay() {
		const elapsedTime = ((Date.now() - this.startTime) / 1000).toFixed(1);
		const progressPercent = ((this.completedItems / this.totalItems) * 100).toFixed(1);

		const progressBarLength = 30;
		const filledLength = Math.round((progressBarLength * this.completedItems) / this.totalItems);
		const progressBar = "█".repeat(filledLength) + "░".repeat(progressBarLength - filledLength);

		// Build additional metrics string
		let metricsStr = "";
		for (const [name, value] of Object.entries(this.additionalMetrics)) {
			metricsStr += ` | ${name}: ${value}`;
		}

		process.stdout.write(
			`\rProgress: [${progressBar}] ${progressPercent}% | ` +
				`${this.itemName}: ${this.completedItems}/${this.totalItems}` +
				metricsStr +
				` | Time: ${elapsedTime}s\n`
		);
	}

	complete() {
		const totalTime = ((Date.now() - this.startTime) / 1000).toFixed(1);
		console.log(`\nCompleted ${this.completedItems} ${this.itemName} in ${totalTime} seconds`);
	}
}

module.exports = ProgressTracker;
