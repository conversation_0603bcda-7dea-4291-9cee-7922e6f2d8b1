// app/api/user/me/route.ts

import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const GET = async function getUser(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/me`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json(
				{ success: false, message: error.message || "Failed to fetch user data" },
				{ status: response.status }
			);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		console.error("Error fetching user data:", error);
		return NextResponse.json({ success: false, message: error.message || "Server error" }, { status: 500 });
	}
};

export const PUT = async function updateUser(request: Request) {
	try {
		const res = new NextResponse();
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const body = await request.json();

		const response = await fetch(`${process.env.API_BASE_URL}/user/me`, {
			method: "PUT",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json(
				{ success: false, message: error.message || "Failed to update user data" },
				{ status: response.status }
			);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message }, res);
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		console.error("Error updating user data:", error);
		return NextResponse.json({ success: false, message: error.message || "Server error" }, { status: 500 });
	}
};
