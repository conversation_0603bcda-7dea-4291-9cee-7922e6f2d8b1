"use client";

import { getCategoryGradient } from "@/components/ui/category-gradients";

import Link from "next/link";
import { Star, Brain, Database, MessageSquare, Globe } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { LineChart, Line } from "recharts";

const data = [
  { value: 100 },
  { value: 120 },
  { value: 110 },
  { value: 130 },
  { value: 125 },
];

const subnets: Array<{
  id: string;
  name: string;
  category: keyof typeof categoryColors;
  categoryName: string;
  icon: React.ComponentType<{ className?: string }>;
  price: string;
  change1h: string;
  change24h: string;
  change7d: string;
  marketCap: string;
  volume: string;
  supply: string;
}> = [
  {
    id: "subnet-1",
    name: "Text Prompting Subnet",
    category: "nlp",
    categoryName: "Natural Language Processing",
    icon: MessageSquare,
    price: "$97,617.96",
    change1h: "+0.00%",
    change24h: "+0.20%",
    change7d: "+0.82%",
    marketCap: "$1,935,266,866,609",
    volume: "$16,067,079,778",
    supply: "19.82M TAO",
  },
  {
    id: "subnet-2",
    name: "Image Generation Subnet",
    category: "ai",
    categoryName: "AI & Machine Learning",
    icon: Brain,
    price: "$2,697.23",
    change1h: "-0.08%",
    change24h: "+1.05%",
    change7d: "+2.20%",
    marketCap: "$325,158,688,373",
    volume: "$9,495,280,013",
    supply: "120.55M TAO",
  },
  {
    id: "subnet-3",
    name: "Distributed Storage Subnet",
    category: "storage",
    categoryName: "Data Storage",
    icon: Database,
    price: "$1,234.56",
    change1h: "+0.15%",
    change24h: "-0.50%",
    change7d: "+1.82%",
    marketCap: "$225,158,688,373",
    volume: "$7,495,280,013",
    supply: "90.55M TAO",
  },
  {
    id: "subnet-4",
    name: "Edge Computing Subnet",
    category: "compute",
    categoryName: "Distributed Computing",
    icon: Globe,
    price: "$567.89",
    change1h: "+0.30%",
    change24h: "+1.20%",
    change7d: "+3.82%",
    marketCap: "$125,158,688,373",
    volume: "$5,495,280,013",
    supply: "60.55M TAO",
  },
];

const categoryColors = {
  ai: "bg-purple-500",
  nlp: "bg-blue-500",
  storage: "bg-green-500",
  compute: "bg-orange-500",
};

export function SubnetsTable() {
  return (
    <div className="rounded-lg border bg-card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-8">#</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>1h %</TableHead>
            <TableHead>24h %</TableHead>
            <TableHead>7d %</TableHead>
            <TableHead className="hidden md:table-cell">Market Cap</TableHead>
            <TableHead className="hidden lg:table-cell">Volume(24h)</TableHead>
            <TableHead className="hidden xl:table-cell">
              Circulating Supply
            </TableHead>
            <TableHead className="hidden lg:table-cell">Last 7 Days</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {subnets.map((subnet, i) => (
            <TableRow key={subnet.id}>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-muted-foreground" />
                  <span>{i + 1}</span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <subnet.icon className="h-5 w-5 text-muted-foreground" />
                  <Link
                    href={`/subnets/${subnet.id}`}
                    className="font-medium hover:underline"
                  >
                    {subnet.name}
                  </Link>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="secondary" className="gap-1">
                  <div
                    className={`h-2 w-2 rounded-full ${
                      categoryColors[subnet.category]
                    }`}
                  />
                  {subnet.categoryName}
                </Badge>
              </TableCell>
              <TableCell>{subnet.price}</TableCell>
              <TableCell className="text-green-600">
                {subnet.change1h}
              </TableCell>
              <TableCell className="text-green-600">
                {subnet.change24h}
              </TableCell>
              <TableCell className="text-green-600">
                {subnet.change7d}
              </TableCell>
              <TableCell className="hidden md:table-cell">
                {subnet.marketCap}
              </TableCell>
              <TableCell className="hidden lg:table-cell">
                {subnet.volume}
              </TableCell>
              <TableCell className="hidden xl:table-cell">
                {subnet.supply}
              </TableCell>
              <TableCell className="hidden lg:table-cell">
                <LineChart width={160} height={40} data={data}>
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#22c55e"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
