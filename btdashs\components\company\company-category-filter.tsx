"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  Brain,
  Database,
  Network,
  Code,
  Server,
  Globe,
  Lock,
  Zap,
} from "lucide-react";

interface CategoryFilterProps {
  selectedCategory: string | null;
  onCategoryChange: (category: string | null) => void;
}

// Define categories with icons
const categories = [
  { id: "all", name: "All Categories", icon: Globe },
  { id: "ai-research", name: "AI Research", icon: Brain },
  { id: "neural-networks", name: "Neural Networks", icon: Network },
  { id: "machine-learning", name: "Machine Learning", icon: Brain },
  { id: "data-storage", name: "Data Storage", icon: Database },
  { id: "data-processing", name: "Data Processing", icon: Server },
  { id: "big-data", name: "Big Data", icon: Database },
  {
    id: "decentralized-computing",
    name: "Decentralized Computing",
    icon: Network,
  },
  {
    id: "decentralized-infrastructure",
    name: "Decentralized Infrastructure",
    icon: Server,
  },
  { id: "cryptography", name: "Cryptography", icon: Lock },
  { id: "edge-computing", name: "Edge Computing", icon: Zap },
  { id: "quantum-computing", name: "Quantum Computing", icon: Code },
];

export function CompanyCategoryFilter({
  selectedCategory,
  onCategoryChange,
}: CategoryFilterProps) {
  return (
    <div className="mb-8">
      <h2 className="text-lg font-medium mb-4">Filter by Category</h2>
      <div className="flex flex-wrap gap-3">
        {categories.map((category) => {
          const Icon = category.icon;
          const isSelected =
            category.id === selectedCategory ||
            (category.id === "all" && selectedCategory === null);

          return (
            <Button
              key={category.id}
              variant={isSelected ? "default" : "outline"}
              className={cn(
                "gap-2 h-auto py-2",
                isSelected && "bg-primary text-primary-foreground"
              )}
              onClick={() =>
                onCategoryChange(category.id === "all" ? null : category.id)
              }
            >
              <Icon className="h-4 w-4" />
              <span>{category.name}</span>
              {isSelected && (
                <Badge
                  variant="secondary"
                  className="ml-1 bg-primary-foreground/20 text-primary-foreground"
                >
                  {category.id === "all" ? categories.length - 1 : 1}
                </Badge>
              )}
            </Button>
          );
        })}
      </div>
    </div>
  );
}
