// app/dashboard/campaigns/page.tsx
import { fetchWithFallback } from "@/lib/utils";
import { cookies } from "next/headers";
import CampaignsClientWrapper from "./client-wrapper";

export const revalidate = 60; // Revalidate every 60 seconds

export default async function CampaignsPage({
	searchParams,
}: {
	searchParams: { [key: string]: string | string[] | undefined };
}) {
	const paramsData = await searchParams;
	const success = paramsData.success === "true";

	const cookieHeader = (await cookies()).toString();

	// Fetch campaigns from our API
	const campaignsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/campaigns`, {
		headers: { Cookie: cookieHeader },
		next: { tags: ["campaigns"] }, // Add cache tagging
	});

	// Handle new response format
	let campaigns = [];
	if (campaignsRes.success) {
		campaigns = campaignsRes.data || [];
	} else {
		console.error("Failed to fetch campaigns:", campaignsRes.message || campaignsRes.error);
	}

	return (
		<div className="flex flex-col gap-4">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">My Campaigns</h1>
			</div>

			<CampaignsClientWrapper campaigns={campaigns} success={success} />
		</div>
	);
}
