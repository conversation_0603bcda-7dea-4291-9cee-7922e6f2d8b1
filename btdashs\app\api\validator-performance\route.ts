import { getAllItems } from "@/lib/data/utils";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const data = await getAllItems("validators");
		return NextResponse.json({
			success: true,
			data,
			message: "Validator performance fetched successfully",
		});
	} catch (err) {
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch validator performance",
				errors: err instanceof Error ? err.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
