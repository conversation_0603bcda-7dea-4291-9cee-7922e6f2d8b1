import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Event } from "@/lib/db/models";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { EventsGrid } from "./events-grid";

interface EventsSectionProps {
	title: string;
	events: Event[];
	limit?: number;
	entityType?: "subnet" | "company" | "application";
	entityId?: string | number;
}

export function EventsSection({ title, events, limit = 4, entityType, entityId }: EventsSectionProps) {
	// Limit the number of events to display
	const displayedEvents = limit ? events.slice(0, limit) : events;
	const hasMoreEvents = events.length > limit;

	// Build the view all URL based on entity type
	let viewAllUrl = "/events";
	if (entityType && entityId) {
		viewAllUrl = `/events?${entityType}=${entityId}`;
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle className="flex items-center gap-2">{title}</CardTitle>
				{hasMoreEvents && (
					<Link href={viewAllUrl}>
						<Button variant="ghost" size="sm" className="gap-1">
							View All
							<ArrowRight className="h-4 w-4" />
						</Button>
					</Link>
				)}
			</CardHeader>
			<CardContent>
				{events.length > 0 ? (
					<EventsGrid events={displayedEvents} />
				) : (
					<div className="text-center py-12 border rounded-lg bg-muted/20">
						<p className="text-muted-foreground">No event listings available at this time.</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
