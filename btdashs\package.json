{"name": "dynamictaomarketcap", "version": "0.1.0", "private": true, "license": "MIT", "description": "Dynamic TAO Market Cap is a web application that provides real-time market data and analytics for the TAO cryptocurrency. It offers features such as price tracking, market capitalization, trading volume, and historical data analysis.", "keywords": ["TAO", "cryptocurrency", "market cap", "real-time data", "analytics"], "engines": {"node": "20.x"}, "packageManager": "pnpm@10.10.0", "author": "Dynamic TAO Team", "homepage": "https://dynamictaomarketcap.com", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@amcharts/amcharts5": "^5.12.2", "@auth0/nextjs-auth0": "^4.5.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.1.7", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.23.0", "lodash.throttle": "^4.1.1", "lucide-react": "^0.454.0", "marked": "^15.0.8", "next": "15.2.4", "next-themes": "^0.4.4", "radix-ui": "^1.2.0", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-stringify": "^11.0.0", "sonner": "^1.7.1", "swiper": "^11.2.6", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "winston": "^3.17.0", "winston-papertrail-transport": "^1.0.9", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/axios": "^0.9.36", "@types/lodash.throttle": "^4.1.9", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/winston": "^2.4.4", "eslint": "^9.26.0", "eslint-config-next": "^15.3.2", "eslint-plugin-typescript": "^0.14.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}