"use client"

import { useState } from "react"
import { SubnetOverview } from "@/components/dashboard/subnet-overview"
import { SubnetLeaderboard } from "@/components/dashboard/subnet-leaderboard"
import { SubnetSearch } from "@/components/dashboard/subnet-search"
import { SubnetComparison } from "@/components/dashboard/subnet-comparison"
import { SubnetCategoryWidget } from "@/components/dashboard/subnet-category-widget"

export default function DashboardPage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  return (
    <div className="py-8 px-6 sm:px-8 lg:px-12">
      <h1 className="text-3xl font-bold mb-6">Bittensor Subnet Dashboard</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-8">
          <SubnetOverview selectedCategory={selectedCategory} />
          <SubnetLeaderboard />
        </div>
        <div className="space-y-8">
          <SubnetCategoryWidget onCategorySelect={setSelectedCategory} />
          <SubnetSearch />
          <SubnetComparison />
        </div>
      </div>
    </div>
  )
}

