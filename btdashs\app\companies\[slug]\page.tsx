import { auth0 } from "@/lib/auth0";
import { fetchWithFallback, getIdFromSlug } from "@/lib/data/utils";
import { Category, Company, Job, News, Product, Subnet, SubnetMetric } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import { cookies } from "next/headers";
import CompanyClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

interface Params {
	params: { slug: string };
}

export async function generateMetadata({ params }: Params): Promise<Metadata> {
	const paramsData = await params;
	const slug = paramsData.slug;

	const id = await getIdFromSlug("companies", slug);
	const companyRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies/${id}`);
	const company = companyRes.data;

	return generateSEOMetadata({
		title: `${company.name} | DynamicTaoMarketCap`,
		description: company.description || `Explore ${company.name}'s contributions to the TAO ecosystem.`,
		url: `${process.env.APP_BASE_URL}/companies/${slug}`,
		image: company.logo_url || `${process.env.APP_BASE_URL}/default-company-og.jpg`,
	});
}

export default async function CompanyPage({ params }: Params) {
	const paramsData = await params;
	const slug = paramsData.slug;
	const id = await getIdFromSlug("companies", slug);

	const [companyRes, subnetsRes, subnetsMetricsRes, productsRes, newsRes, categoriesRes, jobsRes, eventsRes] =
		await Promise.all([
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies/${id}`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnet-metrics`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/news`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/jobs`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`),
		]);

	let authorized_job_admin = false;
	let authorized_events_admin = false;

	const session = await auth0.getSession();
	if (session) {
		const cookieHeader = (await cookies()).toString();
		const userData = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/me`, {
			headers: { Cookie: cookieHeader },
		});

		if (!userData.error && userData.data) {
			authorized_job_admin = userData.data.authorized_job_admin;
			authorized_events_admin = userData.data.authorized_events_admin;
		}
	}

	if (companyRes.error) console.error("Company fetch error", companyRes.error);
	if (subnetsRes.error) console.error("Subnets fetch error", subnetsRes.error);
	if (subnetsMetricsRes.error) console.error("Subnets metrics fetch error", subnetsMetricsRes.error);
	if (productsRes.error) console.error("Products fetch error", productsRes.error);
	if (newsRes.error) console.error("News fetch error", newsRes.error);
	if (categoriesRes.error) console.error("Categories fetch error", categoriesRes.error);
	if (jobsRes.error) console.error("Jobs fetch error", jobsRes.error);
	if (eventsRes.error) console.error("Events fetch error", eventsRes.error);

	const company: Company = companyRes.data;
	const subnets = (subnetsRes.data || []).filter((s: Subnet) => company.subnet_ids?.includes(s.netuid));
	const subnetsMetrics = (subnetsMetricsRes.data || []).filter((m: SubnetMetric) =>
		company.subnet_ids?.includes(m.netuid)
	);
	const products = (productsRes.data || []).filter((p: Product) => company.product_ids?.includes(p.id));
	const news = (newsRes.data || []).filter((n: News) => company.news_ids?.includes(n.id));
	const categories = (categoriesRes.data || []).filter((cat: Category) => company.category_ids?.includes(cat.id));
	const jobs = (jobsRes.data || []).filter((j: Job) => company.job_ids?.includes(j.id));
	const events = (eventsRes.data || []).filter((e: any) => company.event_ids?.includes(e.id));

	return (
		<CompanyClientWrapper
			company={company}
			subnets={subnets}
			subnetsMetrics={subnetsMetrics}
			products={products}
			news={news}
			categories={categories}
			jobs={jobs}
			events={events}
			authorized_job_admin={authorized_job_admin}
			authorized_events_admin={authorized_events_admin}
		/>
	);
}
