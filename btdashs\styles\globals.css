@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
  /* Softer, less bright light mode */
    --background: 210 30% 96%;
    /* Reduced brightness and saturation */
    --foreground: 222.2 84% 4.9%;
  
    --card: 0 0% 98%;
    /* Slightly off-white for cards */
    --card-foreground: 222.2 47.4% 11.2%;
  
    --popover: 0 0% 98%;
    --popover-foreground: 222.2 47.4% 11.2%;
  
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
  
    --secondary: 210 30% 92%;
    /* Softer secondary */
    --secondary-foreground: 222.2 47.4% 11.2%;
  
    --muted: 210 30% 92%;
    --muted-foreground: 215.4 16.3% 46.9%;
  
    --accent: 210 30% 92%;
    --accent-foreground: 222.2 47.4% 11.2%;
  
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
  
    --border: 214.3 31.8% 86%;
    /* Slightly darker border */
    --input: 214.3 31.8% 86%;
    --ring: 222.2 84% 4.9%;
  
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    @apply antialiased;
  }
}

/* Custom utility classes */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}


/* Markdown body styles */
.markdown-body {
  line-height: 1.6;
}

.markdown-body h1 {
  font-size: 1.8em;
  font-weight: bold;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: #ffffff;
}

.markdown-body h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: #ffffff;
}

.markdown-body h3 {
  font-size: 1.3em;
  font-weight: bold;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: #ffffff;
}

.markdown-body p {
  margin-bottom: 1em;
  color: #d1d5db;
}

.markdown-body ul {
  list-style-type: disc;
  margin-left: 1.5em;
  margin-bottom: 1em;
  color: #d1d5db;
}

.markdown-body ol {
  list-style-type: decimal;
  margin-left: 1.5em;
  margin-bottom: 1em;
  color: #d1d5db;
}

.markdown-body li {
  margin-bottom: 0.5em;
}

.markdown-body a {
  color: #60a5fa;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

.markdown-body code {
  font-family: monospace;
  background-color: #1e293b;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
  color: #e2e8f0;
}

.markdown-body pre {
  background-color: #1e293b;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin-bottom: 1em;
}

.markdown-body pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  color: #e2e8f0;
}

.markdown-body blockquote {
  border-left: 4px solid #4b5563;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  color: #9ca3af;
}

.markdown-body hr {
  border: 0;
  border-top: 1px solid #4b5563;
  margin: 1.5em 0;
}

.markdown-body img {
  max-width: 100%;
  border-radius: 5px;
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #4b5563;
  padding: 0.5em;
  text-align: left;
}

.markdown-body th {
  background-color: #1e293b;
  font-weight: bold;
}
