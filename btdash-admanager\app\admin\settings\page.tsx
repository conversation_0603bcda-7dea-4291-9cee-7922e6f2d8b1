"use client";

import { Save } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

export default function SettingsPage() {
	return (
		<div className="container mx-auto p-6">
			<h1 className="mb-6 text-3xl font-bold">Settings</h1>

			<Tabs defaultValue="account" className="w-full">
				<TabsList className="mb-4">
					<TabsTrigger value="account">Account</TabsTrigger>
					<TabsTrigger value="general">General</TabsTrigger>
					<TabsTrigger value="notifications">Notifications</TabsTrigger>
					<TabsTrigger value="appearance">Appearance</TabsTrigger>
				</TabsList>

				<TabsContent value="account">
					<Card>
						<CardHeader>
							<CardTitle>Admin Account</CardTitle>
							<CardDescription>Manage your admin account information and role</CardDescription>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="space-y-2">
								<Label htmlFor="admin-name">Name</Label>
								<Input id="admin-name" defaultValue="Admin User" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="admin-email">Email</Label>
								<Input id="admin-email" type="email" defaultValue="<EMAIL>" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="admin-role">Role</Label>
								<Input id="admin-role" defaultValue="Administrator" disabled />
								<p className="text-sm text-muted-foreground">
									Your role determines your access level in the system
								</p>
							</div>
							<div className="space-y-2">
								<Label htmlFor="admin-permissions">Permissions</Label>
								<div className="grid grid-cols-2 gap-2 text-sm">
									<div className="flex items-center space-x-2">
										<div className="h-2 w-2 rounded-full bg-green-500"></div>
										<span>Campaign Management</span>
									</div>
									<div className="flex items-center space-x-2">
										<div className="h-2 w-2 rounded-full bg-green-500"></div>
										<span>Ad Approval</span>
									</div>
									<div className="flex items-center space-x-2">
										<div className="h-2 w-2 rounded-full bg-green-500"></div>
										<span>Analytics Access</span>
									</div>
									<div className="flex items-center space-x-2">
										<div className="h-2 w-2 rounded-full bg-green-500"></div>
										<span>System Settings</span>
									</div>
								</div>
							</div>
						</CardContent>
						<CardFooter>
							<Button>
								<Save className="mr-2 h-4 w-4" />
								Save Changes
							</Button>
						</CardFooter>
					</Card>
				</TabsContent>

				<TabsContent value="general">
					<Card>
						<CardHeader>
							<CardTitle>General Settings</CardTitle>
							<CardDescription>Manage your account settings and preferences</CardDescription>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="space-y-2">
								<Label htmlFor="company-name">Company Name</Label>
								<Input id="company-name" defaultValue="AdManager Inc." />
							</div>
							<div className="space-y-2">
								<Label htmlFor="contact-email">Contact Email</Label>
								<Input id="contact-email" type="email" defaultValue="<EMAIL>" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="company-address">Company Address</Label>
								<Textarea id="company-address" defaultValue="123 Ad Street, Marketing City, 94103" />
							</div>
							<div className="space-y-2">
								<Label htmlFor="timezone">Timezone</Label>
								<select
									id="timezone"
									className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
								>
									<option value="UTC-8">Pacific Time (UTC-8)</option>
									<option value="UTC-5">Eastern Time (UTC-5)</option>
									<option value="UTC+0">UTC</option>
									<option value="UTC+1">Central European Time (UTC+1)</option>
									<option value="UTC+8">China Standard Time (UTC+8)</option>
								</select>
							</div>
						</CardContent>
						<CardFooter>
							<Button>
								<Save className="mr-2 h-4 w-4" />
								Save Changes
							</Button>
						</CardFooter>
					</Card>
				</TabsContent>

				<TabsContent value="notifications">
					<Card>
						<CardHeader>
							<CardTitle>Notification Settings</CardTitle>
							<CardDescription>Manage how you receive notifications</CardDescription>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="font-medium">Email Notifications</p>
									<p className="text-sm text-muted-foreground">
										Receive email notifications for important events
									</p>
								</div>
								<Switch defaultChecked />
							</div>
							<div className="flex items-center justify-between">
								<div>
									<p className="font-medium">New Ad Request Alerts</p>
									<p className="text-sm text-muted-foreground">
										Get notified when new ad requests are submitted
									</p>
								</div>
								<Switch defaultChecked />
							</div>
							<div className="flex items-center justify-between">
								<div>
									<p className="font-medium">Campaign Performance Reports</p>
									<p className="text-sm text-muted-foreground">Receive weekly performance reports</p>
								</div>
								<Switch defaultChecked />
							</div>
							<div className="flex items-center justify-between">
								<div>
									<p className="font-medium">System Notifications</p>
									<p className="text-sm text-muted-foreground">
										Get notified about system updates and maintenance
									</p>
								</div>
								<Switch />
							</div>
						</CardContent>
						<CardFooter>
							<Button>
								<Save className="mr-2 h-4 w-4" />
								Save Changes
							</Button>
						</CardFooter>
					</Card>
				</TabsContent>

				<TabsContent value="appearance">
					<Card>
						<CardHeader>
							<CardTitle>Appearance Settings</CardTitle>
							<CardDescription>Customize the look and feel of your dashboard</CardDescription>
						</CardHeader>
						<CardContent className="space-y-6">
							<div className="space-y-2">
								<Label>Theme</Label>
								<div className="flex gap-4">
									<div className="flex items-center space-x-2">
										<input
											type="radio"
											id="light"
											name="theme"
											className="h-4 w-4"
											defaultChecked
										/>
										<Label htmlFor="light">Light</Label>
									</div>
									<div className="flex items-center space-x-2">
										<input type="radio" id="dark" name="theme" className="h-4 w-4" />
										<Label htmlFor="dark">Dark</Label>
									</div>
									<div className="flex items-center space-x-2">
										<input type="radio" id="system" name="theme" className="h-4 w-4" />
										<Label htmlFor="system">System</Label>
									</div>
								</div>
							</div>
							<div className="flex items-center justify-between">
								<div>
									<p className="font-medium">Compact Mode</p>
									<p className="text-sm text-muted-foreground">
										Display more content with less spacing
									</p>
								</div>
								<Switch />
							</div>
							<div className="flex items-center justify-between">
								<div>
									<p className="font-medium">Show Analytics on Dashboard</p>
									<p className="text-sm text-muted-foreground">
										Display analytics charts on the main dashboard
									</p>
								</div>
								<Switch defaultChecked />
							</div>
						</CardContent>
						<CardFooter>
							<Button>
								<Save className="mr-2 h-4 w-4" />
								Save Changes
							</Button>
						</CardFooter>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
