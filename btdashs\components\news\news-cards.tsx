import { Card, CardContent } from "@/components/ui/card";
import { News, Category } from "@/lib/db/models";
import { Calendar } from "lucide-react";
import Link from "next/link";
import ReactMarkdown from "react-markdown";
import { Badge } from "@/components/ui/badge";
import { CategoryTag } from "@/components/category-tag"; // Adjust the path as needed

interface NewsCardProps {
  article?: News; // Make optional for skeleton loading
  categories?: Category[]; // Make optional for skeleton loading
  isLoading?: boolean; // Add a loading prop
}

export function NewsCard({ article, categories, isLoading }: NewsCardProps) {
  if (isLoading) {
    // Skeleton loading UI
    return (
      <Card className="overflow-hidden hover:shadow-md transition-shadow">
        <CardContent className="p-0">
          <div className="p-6 animate-pulse">
            <div className="flex justify-between items-start mb-2">
              <div className="h-4 w-32 bg-gray-300 rounded"></div>
              <div className="flex gap-1">
                <div className="h-4 w-16 bg-gray-300 rounded"></div>
                <div className="h-4 w-16 bg-gray-300 rounded"></div>
              </div>
            </div>
            <div className="h-6 w-3/4 bg-gray-300 rounded mb-2"></div>
            <div className="h-4 w-full bg-gray-300 rounded mb-1"></div>
            <div className="h-4 w-full bg-gray-300 rounded mb-1"></div>
            <div className="h-4 w-5/6 bg-gray-300 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const filteredCategories = categories?.filter((cat) => {
    return article?.category_ids?.includes(cat.id);
  });

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardContent className="p-0">
        <Link href={`/news/${article?.id}`} className="block">
          <div className="p-6">
            <div className="flex justify-between items-start mb-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 mr-1" />
                {article &&
                  new Date(article.publication_date).toLocaleString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {filteredCategories?.map((cat) => (
                  <CategoryTag
                    key={cat.id}
                    category={cat.name}
                    className="text-xs text-white/80 bg-white/20 hover:bg-white/30"
                  />
                ))}
              </div>
            </div>
            <h2 className="text-xl font-bold mb-2 hover:underline">
              {article?.title}
            </h2>

            <div className="prose prose-sm dark:prose-invert line-clamp-6">
              <ReactMarkdown>{article?.content || ""}</ReactMarkdown>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
}
