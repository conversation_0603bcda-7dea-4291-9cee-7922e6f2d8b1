// components/ui/multi-select.tsx
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface SelectOption {
	value: number;
	label: string;
}

interface MultiSelectProps {
	options: SelectOption[];
	selected: number[];
	onChange: (values: number[]) => void;
	placeholder?: string;
	single?: boolean;
}

export function MultiSelect({
	options,
	selected,
	onChange,
	placeholder = "Select items...",
	single = false,
}: MultiSelectProps) {
	const [isOpen, setIsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	const handleSelect = (value: number) => {
		let newSelected;
		if (single) {
			newSelected = selected.includes(value) ? [] : [value];
		} else {
			newSelected = selected.includes(value) ? selected.filter((v) => v !== value) : [...selected, value];
		}
		onChange(newSelected);
		if (single) setIsOpen(false);
	};

	const removeSelected = (value: number, e: React.MouseEvent) => {
		e.stopPropagation();
		onChange(selected.filter((v) => v !== value));
	};

	const selectedLabels = selected.map((value) => options.find((opt) => opt.value === value)?.label || "");

	return (
		<div className="relative" ref={dropdownRef}>
			<Button
				variant="outline"
				onClick={() => setIsOpen(!isOpen)}
				className="w-full justify-between min-h-10 h-auto py-1"
			>
				<div className="flex flex-wrap gap-1">
					{selected.length === 0 ? (
						<span className="text-muted-foreground">{placeholder}</span>
					) : (
						selectedLabels.map((label, index) => (
							<Badge key={selected[index]} variant="secondary" className="flex items-center gap-1">
								{label}
								<X
									className="h-3 w-3 cursor-pointer"
									onClick={(e) => removeSelected(selected[index], e)}
								/>
							</Badge>
						))
					)}
				</div>
				<span className="ml-2">▼</span>
			</Button>
			{isOpen && (
				<div className="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg dark:bg-gray-800 border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto">
					{options.map((option) => (
						<div
							key={option.value}
							className={`flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${
								selected.includes(option.value) ? "bg-gray-100 dark:bg-gray-700" : ""
							}`}
							onClick={() => handleSelect(option.value)}
						>
							<Check
								className={`mr-2 h-4 w-4 ${
									selected.includes(option.value) ? "opacity-100" : "opacity-0"
								}`}
							/>
							{option.label}
						</div>
					))}
				</div>
			)}
		</div>
	);
}
