const db = require("../../infrastructure/database/knex");
const redisClient = require("../../infrastructure/redis/redisClient");
const logger = require("../../../logger");

class FraudDetectionService {
	constructor() {
		this.suspiciousThresholds = {
			// Click fraud detection
			clicksPerMinute: 10, // Max clicks per minute from same IP
			clicksPerHour: 100, // Max clicks per hour from same IP
			clickRateThreshold: 0.8, // Suspicious if CTR > 80%

			// Impression fraud detection
			impressionsPerMinute: 50, // Max impressions per minute from same IP
			impressionsPerHour: 1000, // Max impressions per hour from same IP

			// User behavior patterns
			sessionDurationMin: 1, // Minimum session duration in seconds
			bounceRateThreshold: 0.95, // Suspicious if bounce rate > 95%

			// Geographic anomalies
			unusualGeoPattern: 5, // Flag if same user appears in 5+ different countries

			// Time-based patterns
			nightTimeActivityThreshold: 0.8, // Suspicious if 80%+ activity during night hours
		};
	}

	/**
	 * Analyze click for potential fraud
	 * @param {Object} clickData - Click data to analyze
	 * @returns {Promise<Object>} Fraud analysis result
	 */
	async analyzeClick(clickData) {
		try {
			const { ad_id, ip_address, user_agent, session_id, user_id } = clickData;
			const suspiciousFactors = [];
			let riskScore = 0;

			// 1. Check IP-based click frequency
			const ipClickFrequency = await this.checkIPClickFrequency(ip_address);
			if (ipClickFrequency.suspicious) {
				suspiciousFactors.push({
					type: "high_click_frequency",
					details: ipClickFrequency,
					severity: "high",
				});
				riskScore += 30;
			}

			// 2. Check user agent patterns
			const userAgentAnalysis = await this.analyzeUserAgent(user_agent);
			if (userAgentAnalysis.suspicious) {
				suspiciousFactors.push({
					type: "suspicious_user_agent",
					details: userAgentAnalysis,
					severity: "medium",
				});
				riskScore += 15;
			}

			// 3. Check click timing patterns
			const timingAnalysis = await this.analyzeClickTiming(ad_id, ip_address);
			if (timingAnalysis.suspicious) {
				suspiciousFactors.push({
					type: "suspicious_timing",
					details: timingAnalysis,
					severity: "medium",
				});
				riskScore += 20;
			}

			// 4. Check geographic consistency
			if (user_id) {
				const geoAnalysis = await this.analyzeGeographicPattern(user_id, ip_address);
				if (geoAnalysis.suspicious) {
					suspiciousFactors.push({
						type: "geographic_anomaly",
						details: geoAnalysis,
						severity: "medium",
					});
					riskScore += 25;
				}
			}

			// 5. Check session behavior
			const sessionAnalysis = await this.analyzeSessionBehavior(session_id);
			if (sessionAnalysis.suspicious) {
				suspiciousFactors.push({
					type: "suspicious_session",
					details: sessionAnalysis,
					severity: "low",
				});
				riskScore += 10;
			}

			const result = {
				click_id: clickData.id,
				ad_id,
				ip_address,
				risk_score: Math.min(riskScore, 100),
				risk_level: this.calculateRiskLevel(riskScore),
				suspicious_factors: suspiciousFactors,
				action_recommended: this.getRecommendedAction(riskScore),
				analyzed_at: new Date(),
			};

			// Log suspicious activity
			if (riskScore > 50) {
				logger.warn("Suspicious click detected", result);
			}

			// Store fraud analysis result
			await this.storeFraudAnalysis("click", result);

			return result;
		} catch (error) {
			logger.error("Error analyzing click for fraud", { error, clickData });
			return {
				risk_score: 0,
				risk_level: "unknown",
				suspicious_factors: [],
				action_recommended: "allow",
				error: error.message,
			};
		}
	}

	/**
	 * Analyze impression for potential fraud
	 * @param {Object} impressionData - Impression data to analyze
	 * @returns {Promise<Object>} Fraud analysis result
	 */
	async analyzeImpression(impressionData) {
		try {
			const { ad_id, ip_address, user_agent, session_id, user_id } = impressionData;
			const suspiciousFactors = [];
			let riskScore = 0;

			// 1. Check IP-based impression frequency
			const ipImpressionFrequency = await this.checkIPImpressionFrequency(ip_address);
			if (ipImpressionFrequency.suspicious) {
				suspiciousFactors.push({
					type: "high_impression_frequency",
					details: ipImpressionFrequency,
					severity: "medium",
				});
				riskScore += 20;
			}

			// 2. Check for bot-like behavior
			const botAnalysis = await this.analyzeBotBehavior(user_agent, ip_address);
			if (botAnalysis.suspicious) {
				suspiciousFactors.push({
					type: "bot_behavior",
					details: botAnalysis,
					severity: "high",
				});
				riskScore += 35;
			}

			// 3. Check impression timing patterns
			const timingAnalysis = await this.analyzeImpressionTiming(ad_id, ip_address);
			if (timingAnalysis.suspicious) {
				suspiciousFactors.push({
					type: "suspicious_timing",
					details: timingAnalysis,
					severity: "medium",
				});
				riskScore += 15;
			}

			const result = {
				impression_id: impressionData.id,
				ad_id,
				ip_address,
				risk_score: Math.min(riskScore, 100),
				risk_level: this.calculateRiskLevel(riskScore),
				suspicious_factors: suspiciousFactors,
				action_recommended: this.getRecommendedAction(riskScore),
				analyzed_at: new Date(),
			};

			// Store fraud analysis result
			await this.storeFraudAnalysis("impression", result);

			return result;
		} catch (error) {
			logger.error("Error analyzing impression for fraud", { error, impressionData });
			return {
				risk_score: 0,
				risk_level: "unknown",
				suspicious_factors: [],
				action_recommended: "allow",
				error: error.message,
			};
		}
	}

	/**
	 * Check IP click frequency
	 * @param {string} ipAddress - IP address to check
	 * @returns {Promise<Object>} Frequency analysis
	 */
	async checkIPClickFrequency(ipAddress) {
		try {
			const now = Date.now();
			const oneMinuteAgo = now - 60 * 1000;
			const oneHourAgo = now - 60 * 60 * 1000;

			// Use Redis for real-time frequency tracking
			const minuteKey = `click_freq:${ipAddress}:${Math.floor(now / 60000)}`;
			const hourKey = `click_freq:${ipAddress}:${Math.floor(now / 3600000)}`;

			const [minuteClicks, hourClicks] = await Promise.all([
				redisClient.incr(minuteKey),
				redisClient.incr(hourKey),
			]);

			// Set expiration
			await Promise.all([
				redisClient.expire(minuteKey, 120), // 2 minutes
				redisClient.expire(hourKey, 7200), // 2 hours
			]);

			const suspicious =
				minuteClicks > this.suspiciousThresholds.clicksPerMinute ||
				hourClicks > this.suspiciousThresholds.clicksPerHour;

			return {
				suspicious,
				clicks_per_minute: minuteClicks,
				clicks_per_hour: hourClicks,
				thresholds: {
					minute: this.suspiciousThresholds.clicksPerMinute,
					hour: this.suspiciousThresholds.clicksPerHour,
				},
			};
		} catch (error) {
			logger.error("Error checking IP click frequency", { error, ipAddress });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Check IP impression frequency
	 * @param {string} ipAddress - IP address to check
	 * @returns {Promise<Object>} Frequency analysis
	 */
	async checkIPImpressionFrequency(ipAddress) {
		try {
			const now = Date.now();
			const minuteKey = `impression_freq:${ipAddress}:${Math.floor(now / 60000)}`;
			const hourKey = `impression_freq:${ipAddress}:${Math.floor(now / 3600000)}`;

			const [minuteImpressions, hourImpressions] = await Promise.all([
				redisClient.incr(minuteKey),
				redisClient.incr(hourKey),
			]);

			await Promise.all([redisClient.expire(minuteKey, 120), redisClient.expire(hourKey, 7200)]);

			const suspicious =
				minuteImpressions > this.suspiciousThresholds.impressionsPerMinute ||
				hourImpressions > this.suspiciousThresholds.impressionsPerHour;

			return {
				suspicious,
				impressions_per_minute: minuteImpressions,
				impressions_per_hour: hourImpressions,
				thresholds: {
					minute: this.suspiciousThresholds.impressionsPerMinute,
					hour: this.suspiciousThresholds.impressionsPerHour,
				},
			};
		} catch (error) {
			logger.error("Error checking IP impression frequency", { error, ipAddress });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Analyze user agent for bot patterns
	 * @param {string} userAgent - User agent string
	 * @returns {Promise<Object>} User agent analysis
	 */
	async analyzeUserAgent(userAgent) {
		try {
			const botPatterns = [
				/bot/i,
				/crawler/i,
				/spider/i,
				/scraper/i,
				/curl/i,
				/wget/i,
				/python/i,
				/java/i,
				/headless/i,
				/phantom/i,
				/selenium/i,
			];

			const suspiciousPatterns = [
				/^Mozilla\/5\.0$/, // Too generic
				/Windows NT 10\.0.*rv:91\.0.*Gecko.*Firefox\/91\.0/, // Common bot signature
			];

			const isBot = botPatterns.some((pattern) => pattern.test(userAgent));
			const isSuspicious = suspiciousPatterns.some((pattern) => pattern.test(userAgent));

			return {
				suspicious: isBot || isSuspicious,
				is_bot: isBot,
				is_suspicious_pattern: isSuspicious,
				user_agent: userAgent,
			};
		} catch (error) {
			logger.error("Error analyzing user agent", { error, userAgent });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Analyze click timing patterns
	 * @param {number} adId - Ad ID
	 * @param {string} ipAddress - IP address
	 * @returns {Promise<Object>} Timing analysis
	 */
	async analyzeClickTiming(adId, ipAddress) {
		try {
			// Get recent clicks from this IP for this ad
			const recentClicks = await db("dtm_ads.ad_clicks")
				.where({ ad_id: adId, ip_address: ipAddress })
				.where("created_at", ">", new Date(Date.now() - 60 * 60 * 1000)) // Last hour
				.orderBy("created_at", "desc")
				.limit(10);

			if (recentClicks.length < 2) {
				return { suspicious: false, reason: "insufficient_data" };
			}

			// Calculate intervals between clicks
			const intervals = [];
			for (let i = 1; i < recentClicks.length; i++) {
				const interval = new Date(recentClicks[i - 1].created_at) - new Date(recentClicks[i].created_at);
				intervals.push(interval / 1000); // Convert to seconds
			}

			// Check for suspiciously regular intervals (bot behavior)
			const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
			const variance =
				intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
			const standardDeviation = Math.sqrt(variance);

			// Low variance indicates regular, bot-like behavior
			const suspicious = standardDeviation < 2 && avgInterval < 30;

			return {
				suspicious,
				click_count: recentClicks.length,
				avg_interval: avgInterval,
				standard_deviation: standardDeviation,
				intervals,
			};
		} catch (error) {
			logger.error("Error analyzing click timing", { error, adId, ipAddress });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Analyze impression timing patterns
	 * @param {number} adId - Ad ID
	 * @param {string} ipAddress - IP address
	 * @returns {Promise<Object>} Timing analysis
	 */
	async analyzeImpressionTiming(adId, ipAddress) {
		try {
			const recentImpressions = await db("dtm_ads.ad_impressions")
				.where({ ad_id: adId, ip_address: ipAddress })
				.where("created_at", ">", new Date(Date.now() - 60 * 60 * 1000))
				.orderBy("created_at", "desc")
				.limit(20);

			if (recentImpressions.length < 5) {
				return { suspicious: false, reason: "insufficient_data" };
			}

			// Check for too many impressions in short time
			const suspicious = recentImpressions.length > 15; // More than 15 impressions per hour

			return {
				suspicious,
				impression_count: recentImpressions.length,
				threshold: 15,
			};
		} catch (error) {
			logger.error("Error analyzing impression timing", { error, adId, ipAddress });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Analyze geographic patterns for user
	 * @param {number} userId - User ID
	 * @param {string} ipAddress - Current IP address
	 * @returns {Promise<Object>} Geographic analysis
	 */
	async analyzeGeographicPattern(userId, ipAddress) {
		try {
			// This would typically use a GeoIP service
			// For now, we'll do a simplified check based on IP patterns

			const recentIPs = await db("dtm_ads.ad_clicks")
				.where({ user_id: userId })
				.where("created_at", ">", new Date(Date.now() - 24 * 60 * 60 * 1000)) // Last 24 hours
				.distinct("ip_address")
				.pluck("ip_address");

			// Simple heuristic: if user appears from many different IP ranges, it's suspicious
			const uniqueIPRanges = new Set(recentIPs.map((ip) => ip.split(".").slice(0, 2).join(".")));
			const suspicious = uniqueIPRanges.size > 3; // More than 3 different IP ranges

			return {
				suspicious,
				unique_ips: recentIPs.length,
				unique_ip_ranges: uniqueIPRanges.size,
				threshold: 3,
			};
		} catch (error) {
			logger.error("Error analyzing geographic pattern", { error, userId, ipAddress });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Analyze session behavior
	 * @param {string} sessionId - Session ID
	 * @returns {Promise<Object>} Session analysis
	 */
	async analyzeSessionBehavior(sessionId) {
		try {
			if (!sessionId) {
				return { suspicious: true, reason: "no_session_id" };
			}

			// Get all activities for this session
			const [impressions, clicks] = await Promise.all([
				db("dtm_ads.ad_impressions").where({ session_id: sessionId }).count("* as count").first(),
				db("dtm_ads.ad_clicks").where({ session_id: sessionId }).count("* as count").first(),
			]);

			const impressionCount = parseInt(impressions.count);
			const clickCount = parseInt(clicks.count);

			// Calculate click-through rate
			const ctr = impressionCount > 0 ? clickCount / impressionCount : 0;

			// Suspicious if CTR is too high (indicates click fraud)
			const suspicious = ctr > this.suspiciousThresholds.clickRateThreshold;

			return {
				suspicious,
				impressions: impressionCount,
				clicks: clickCount,
				ctr,
				threshold: this.suspiciousThresholds.clickRateThreshold,
			};
		} catch (error) {
			logger.error("Error analyzing session behavior", { error, sessionId });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Analyze for bot behavior patterns
	 * @param {string} userAgent - User agent
	 * @param {string} ipAddress - IP address
	 * @returns {Promise<Object>} Bot analysis
	 */
	async analyzeBotBehavior(userAgent, ipAddress) {
		try {
			const userAgentAnalysis = await this.analyzeUserAgent(userAgent);

			// Additional bot checks could include:
			// - JavaScript execution capability
			// - Mouse movement patterns
			// - Scroll behavior
			// - Time spent on page

			return {
				suspicious: userAgentAnalysis.is_bot,
				details: userAgentAnalysis,
			};
		} catch (error) {
			logger.error("Error analyzing bot behavior", { error, userAgent, ipAddress });
			return { suspicious: false, error: error.message };
		}
	}

	/**
	 * Calculate risk level based on score
	 * @param {number} riskScore - Risk score (0-100)
	 * @returns {string} Risk level
	 */
	calculateRiskLevel(riskScore) {
		if (riskScore >= 80) return "critical";
		if (riskScore >= 60) return "high";
		if (riskScore >= 40) return "medium";
		if (riskScore >= 20) return "low";
		return "minimal";
	}

	/**
	 * Get recommended action based on risk score
	 * @param {number} riskScore - Risk score (0-100)
	 * @returns {string} Recommended action
	 */
	getRecommendedAction(riskScore) {
		if (riskScore >= 80) return "block";
		if (riskScore >= 60) return "flag";
		if (riskScore >= 40) return "monitor";
		return "allow";
	}

	/**
	 * Store fraud analysis result
	 * @param {string} type - Analysis type (click/impression)
	 * @param {Object} result - Analysis result
	 * @returns {Promise<void>}
	 */
	async storeFraudAnalysis(type, result) {
		try {
			// Store in database for historical analysis
			await db("dtm_ads.fraud_analysis").insert({
				type,
				entity_id: result.click_id || result.impression_id,
				ad_id: result.ad_id,
				ip_address: result.ip_address,
				risk_score: result.risk_score,
				risk_level: result.risk_level,
				suspicious_factors: JSON.stringify(result.suspicious_factors),
				action_recommended: result.action_recommended,
				created_at: new Date(),
			});
		} catch (error) {
			logger.error("Error storing fraud analysis", { error, type, result });
		}
	}

	/**
	 * Get fraud statistics
	 * @param {Object} options - Query options
	 * @returns {Promise<Object>} Fraud statistics
	 */
	async getFraudStatistics(options = {}) {
		try {
			const { days = 7, ad_id = null } = options;
			const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

			let query = db("dtm_ads.fraud_analysis").where("created_at", ">=", startDate);

			if (ad_id) {
				query = query.where("ad_id", ad_id);
			}

			const [totalAnalyses, riskLevelStats, actionStats] = await Promise.all([
				query.clone().count("* as count").first(),
				query.clone().select("risk_level").count("* as count").groupBy("risk_level"),
				query.clone().select("action_recommended").count("* as count").groupBy("action_recommended"),
			]);

			return {
				period_days: days,
				total_analyses: parseInt(totalAnalyses.count),
				risk_level_breakdown: riskLevelStats.reduce((acc, stat) => {
					acc[stat.risk_level] = parseInt(stat.count);
					return acc;
				}, {}),
				action_breakdown: actionStats.reduce((acc, stat) => {
					acc[stat.action_recommended] = parseInt(stat.count);
					return acc;
				}, {}),
				fraud_rate:
					totalAnalyses.count > 0
						? riskLevelStats
								.filter((s) => ["high", "critical"].includes(s.risk_level))
								.reduce((sum, s) => sum + parseInt(s.count), 0) / parseInt(totalAnalyses.count)
						: 0,
			};
		} catch (error) {
			logger.error("Error getting fraud statistics", { error, options });
			throw error;
		}
	}
}

module.exports = FraudDetectionService;
