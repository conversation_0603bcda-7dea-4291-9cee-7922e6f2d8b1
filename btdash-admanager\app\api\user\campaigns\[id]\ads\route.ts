// app/api/user/campaigns/[id]/ads/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

// GET /api/user/campaigns/[id]/ads - Get ads for a specific campaign
export async function GET(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const campaignId = paramsData.id;

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${campaignId}/ads`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Failed to fetch campaign ads: ${errorText}`);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}
