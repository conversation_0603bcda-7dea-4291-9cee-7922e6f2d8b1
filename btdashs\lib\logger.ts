// logger.js

import winston from "winston";
import { PapertrailTransport } from "winston-papertrail-transport";

const { combine, timestamp, colorize, printf } = winston.format;

const logFormat = printf(({ level, message, timestamp, ...meta }) => {
	const metaStr =
		meta && meta[Symbol.for("splat")]
			? Object.entries((meta[Symbol.for("splat")] as Record<string, any>[])[0])
					.map(([key, val]) => `"${key}": ${JSON.stringify(val)}`)
					.join(", ")
			: "";
	return `[${timestamp}] ${level}: ${message}${metaStr ? `: ${metaStr}` : ""}`;
});

const transports: winston.transport[] = [
	new winston.transports.Console({
		level: "debug",
	}),
];

if (process.env.PAPERTRAIL_HOST && process.env.PAPERTRAIL_PORT) {
	transports.push(
		new PapertrailTransport({
			host: process.env.PAPERTRAIL_HOST,
			port: Number(process.env.PAPERTRAIL_PORT),
			hostname: process.env.PAPERTRAIL_HOSTNAME || "local-dev",
			program: "api-service",
			level: "debug",
		})
	);
}

const logger = winston.createLogger({
	format: combine(timestamp(), colorize(), logFormat),
	transports,
});

export { logger };
