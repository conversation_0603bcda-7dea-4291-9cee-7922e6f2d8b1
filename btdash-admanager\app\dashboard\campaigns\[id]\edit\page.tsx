"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Save, Trash2 } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Campaign {
	id: number;
	name: string;
	status: "pending" | "active" | "paused" | "rejected" | "completed";
	total_budget: string;
	budget_cpc: string | null;
	budget_cpm: string | null;
	start_date: string;
	end_date: string;
	advertiser_id: number;
	manager_id: number;
}

export default function EditCampaignPage() {
	const router = useRouter();
	const params = useParams();
	const { toast } = useToast();
	const campaignId = params.id as string;

	const [campaign, setCampaign] = useState<Campaign | null>(null);
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [deleting, setDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const [formData, setFormData] = useState({
		name: "",
		total_budget: "",
		budget_cpc: "",
		budget_cpm: "",
		start_date: "",
		end_date: "",
		status: "pending" as Campaign["status"],
	});

	useEffect(() => {
		if (campaignId) {
			fetchCampaign();
		}
	}, [campaignId]);

	const fetchCampaign = async () => {
		try {
			setLoading(true);

			const response = await fetch(`/api/user/campaigns/${campaignId}`);

			if (!response.ok) {
				throw new Error(`Failed to fetch campaign: ${response.status}`);
			}

			const result = await response.json();
			if (result.success) {
				const campaignData = result.data;
				setCampaign(campaignData);

				// Populate form with existing data
				setFormData({
					name: campaignData.name || "",
					total_budget: campaignData.total_budget || "",
					budget_cpc: campaignData.budget_cpc || "",
					budget_cpm: campaignData.budget_cpm || "",
					start_date: campaignData.start_date ? campaignData.start_date.split("T")[0] : "",
					end_date: campaignData.end_date ? campaignData.end_date.split("T")[0] : "",
					status: campaignData.status || "pending",
				});
			} else {
				throw new Error(result.message || "Failed to fetch campaign");
			}
		} catch (error) {
			console.error("Error fetching campaign:", error);
			setError(error instanceof Error ? error.message : "Failed to fetch campaign");
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSave = async () => {
		try {
			setSaving(true);

			// Prepare update data
			const updateData = {
				name: formData.name,
				total_budget: formData.total_budget ? parseFloat(formData.total_budget) : null,
				budget_cpc: formData.budget_cpc ? parseFloat(formData.budget_cpc) : null,
				budget_cpm: formData.budget_cpm ? parseFloat(formData.budget_cpm) : null,
				start_date: formData.start_date,
				end_date: formData.end_date,
			};

			const response = await fetch(`/api/user/campaigns/${campaignId}`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(updateData),
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to update campaign: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Campaign Updated",
					description: "Your campaign has been successfully updated.",
				});
				router.push(`/dashboard/campaigns/${campaignId}`);
			} else {
				throw new Error(result.message || "Failed to update campaign");
			}
		} catch (error) {
			console.error("Error updating campaign:", error);
			toast({
				title: "Update Failed",
				description: error instanceof Error ? error.message : "Failed to update campaign",
				variant: "destructive",
			});
		} finally {
			setSaving(false);
		}
	};

	const handleDelete = async () => {
		// Check if campaign has budget and warn about no refund
		const hasBudget =
			campaign &&
			(parseFloat(campaign.total_budget || "0") > 0 ||
				parseFloat(campaign.budget_cpc || "0") > 0 ||
				parseFloat(campaign.budget_cpm || "0") > 0);

		const confirmMessage = hasBudget
			? "⚠️ WARNING: This campaign has a budget allocated. Deleting it will NOT issue a refund - you will lose the money.\n\nAre you sure you want to delete this campaign? This action cannot be undone."
			: "Are you sure you want to delete this campaign? This action cannot be undone.";

		if (!confirm(confirmMessage)) {
			return;
		}

		try {
			setDeleting(true);

			const response = await fetch(`/api/user/campaigns/${campaignId}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Failed to delete campaign: ${errorText}`);
			}

			const result = await response.json();
			if (result.success) {
				const description = result.data?.message || "Your campaign has been successfully deleted.";

				toast({
					title: "Campaign Deleted",
					description,
					variant: hasBudget ? "destructive" : "default",
				});
				router.push("/dashboard/campaigns");
			} else {
				throw new Error(result.message || "Failed to delete campaign");
			}
		} catch (error) {
			console.error("Error deleting campaign:", error);
			toast({
				title: "Delete Failed",
				description: error instanceof Error ? error.message : "Failed to delete campaign",
				variant: "destructive",
			});
		} finally {
			setDeleting(false);
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
					<p className="mt-4 text-gray-600 dark:text-gray-400">Loading campaign...</p>
				</div>
			</div>
		);
	}

	if (error || !campaign) {
		return (
			<div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
				<div className="text-center">
					<p className="text-red-600 mb-4">{error || "Campaign not found"}</p>
					<Button onClick={() => router.push("/dashboard/campaigns")}>Back to Campaigns</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 dark:bg-gray-900">
			<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
				{/* Header */}
				<div className="mb-8">
					<div className="flex items-center gap-4 mb-4">
						<Button
							variant="ghost"
							size="icon"
							onClick={() => router.push(`/dashboard/campaigns/${campaignId}`)}
						>
							<ArrowLeft className="h-4 w-4" />
						</Button>
						<div>
							<h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Edit Campaign</h1>
							<p className="text-gray-600 dark:text-gray-400">Update your campaign details</p>
						</div>
					</div>
				</div>

				{/* Edit Form */}
				<Card>
					<CardHeader>
						<CardTitle>Campaign Details</CardTitle>
						<CardDescription>
							Update your campaign information. Changes will be reviewed if the campaign is active.
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						{/* Basic Information */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="name">Campaign Name</Label>
								<Input
									id="name"
									value={formData.name}
									onChange={(e) => handleInputChange("name", e.target.value)}
									placeholder="Enter campaign name"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="status">Status</Label>
								<Select
									value={formData.status}
									onValueChange={(value) => handleInputChange("status", value)}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="pending">Pending</SelectItem>
										<SelectItem value="active">Active</SelectItem>
										<SelectItem value="paused">Paused</SelectItem>
										<SelectItem value="completed">Completed</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Budget Information */}
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div className="space-y-2">
								<Label htmlFor="total_budget">Total Budget ($)</Label>
								<Input
									id="total_budget"
									type="number"
									step="0.01"
									value={formData.total_budget}
									onChange={(e) => handleInputChange("total_budget", e.target.value)}
									placeholder="0.00"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="budget_cpc">CPC Budget ($)</Label>
								<Input
									id="budget_cpc"
									type="number"
									step="0.01"
									value={formData.budget_cpc}
									onChange={(e) => handleInputChange("budget_cpc", e.target.value)}
									placeholder="0.00"
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="budget_cpm">CPM Budget ($)</Label>
								<Input
									id="budget_cpm"
									type="number"
									step="0.01"
									value={formData.budget_cpm}
									onChange={(e) => handleInputChange("budget_cpm", e.target.value)}
									placeholder="0.00"
								/>
							</div>
						</div>

						{/* Schedule */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="start_date">Start Date</Label>
								<Input
									id="start_date"
									type="date"
									value={formData.start_date}
									onChange={(e) => handleInputChange("start_date", e.target.value)}
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="end_date">End Date</Label>
								<Input
									id="end_date"
									type="date"
									value={formData.end_date}
									onChange={(e) => handleInputChange("end_date", e.target.value)}
								/>
							</div>
						</div>

						{/* Actions */}
						<div className="flex justify-between pt-6">
							<Button variant="destructive" onClick={handleDelete} disabled={deleting || saving}>
								{deleting ? (
									<>
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
										Deleting...
									</>
								) : (
									<>
										<Trash2 className="h-4 w-4 mr-2" />
										Delete Campaign
									</>
								)}
							</Button>

							<div className="flex gap-2">
								<Button
									variant="outline"
									onClick={() => router.push(`/dashboard/campaigns/${campaignId}`)}
									disabled={saving || deleting}
								>
									Cancel
								</Button>
								<Button onClick={handleSave} disabled={saving || deleting}>
									{saving ? (
										<>
											<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
											Saving...
										</>
									) : (
										<>
											<Save className="h-4 w-4 mr-2" />
											Save Changes
										</>
									)}
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
