"use client";

import { CompanyApplicationsGrid } from "@/components/company/company-applications-grid";
import { CompanyEvents } from "@/components/company/company-events";
import { CompanyHeader } from "@/components/company/company-header";
import { CompanyJobs } from "@/components/company/company-jobs";
import { CompanyNews } from "@/components/company/company-news";
import { CompanySubnetsGrid } from "@/components/company/company-subnets-grid";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Category, Company, Event, Job, News, Product, Subnet, SubnetMetric } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import { AlertCircle, Award, Briefcase, Calendar, Code, Home, Newspaper, Users } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import ReactMarkdown from "react-markdown";

interface CompanyClientWrapperProps {
	company: Company;
	subnets: Subnet[];
	subnetsMetrics: SubnetMetric[];
	products: Product[];
	news: News[];
	categories: Category[];
	jobs: Job[];
	events: Event[];
	authorized_job_admin: boolean;
	authorized_events_admin: boolean;
}

export default function CompanyClientWrapper({
	company,
	subnets,
	subnetsMetrics,
	products,
	news,
	categories,
	jobs,
	events,
	authorized_job_admin,
	authorized_events_admin,
}: CompanyClientWrapperProps) {
	const [isRetrying, setIsRetrying] = useState(false);

	if (!company) {
		return (
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				<div className="p-8 text-center border rounded-lg">
					<AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
					<h2 className="text-2xl font-bold mb-2">Company not found</h2>
					<div className="flex flex-col sm:flex-row gap-4 justify-center">
						<Button onClick={() => setIsRetrying(true)} disabled={isRetrying} className="gap-2">
							{isRetrying ? "Retrying..." : "Try Again"}
						</Button>
						<Link href="/companies">
							<Button variant="outline" className="gap-2">
								<Home className="h-4 w-4" />
								Back to Companies
							</Button>
						</Link>
					</div>
				</div>
			</div>
		);
	}

	const gradients = {
		subnets: "bg-gradient-to-br from-purple-500 to-indigo-600",
		applications: "bg-gradient-to-br from-blue-500 to-cyan-600",
		news: "bg-gradient-to-br from-amber-500 to-orange-600",
		jobs: "bg-gradient-to-br from-emerald-500 to-teal-600",
		events: "bg-gradient-to-br from-pink-500 to-rose-600",
	};

	return (
		<>
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				<div className="max-w-[1600px] mx-auto">
					<CompanyHeader company={company} categories={categories} />

					{/* Company Overview Cards */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mt-8">
						<Link href="#subnets">
							<Card
								className={cn(
									"h-full hover:shadow-lg transition-all overflow-hidden text-white",
									gradients.subnets
								)}
							>
								<CardContent className="p-6 flex items-center justify-between">
									<div>
										<h3 className="text-xl font-bold mb-1">Subnets</h3>
										<p className="text-white/80 text-sm">{subnets.length} Active Subnets</p>
									</div>
									<div className="bg-white/20 p-3 rounded-full">
										<Code className="h-6 w-6" />
									</div>
								</CardContent>
							</Card>
						</Link>

						<Link href="#applications">
							<Card
								className={cn(
									"h-full hover:shadow-lg transition-all overflow-hidden text-white",
									gradients.applications
								)}
							>
								<CardContent className="p-6 flex items-center justify-between">
									<div>
										<h3 className="text-xl font-bold mb-1">Applications</h3>
										<p className="text-white/80 text-sm">{products.length} Products</p>
									</div>
									<div className="bg-white/20 p-3 rounded-full">
										<Briefcase className="h-6 w-6" />
									</div>
								</CardContent>
							</Card>
						</Link>

						<Link href="#news">
							<Card
								className={cn(
									"h-full hover:shadow-lg transition-all overflow-hidden text-white",
									gradients.news
								)}
							>
								<CardContent className="p-6 flex items-center justify-between">
									<div>
										<h3 className="text-xl font-bold mb-1">News</h3>
										<p className="text-white/80 text-sm">{news.length} Recent Articles</p>
									</div>
									<div className="bg-white/20 p-3 rounded-full">
										<Newspaper className="h-6 w-6" />
									</div>
								</CardContent>
							</Card>
						</Link>

						<Link href="#jobs">
							<Card
								className={cn(
									"h-full hover:shadow-lg transition-all overflow-hidden text-white",
									gradients.jobs
								)}
							>
								<CardContent className="p-6 flex items-center justify-between">
									<div>
										<h3 className="text-xl font-bold mb-1">Jobs</h3>
										<p className="text-white/80 text-sm">{jobs.length} Open Positions</p>
									</div>
									<div className="bg-white/20 p-3 rounded-full">
										<Users className="h-6 w-6" />
									</div>
								</CardContent>
							</Card>
						</Link>
						<Link href="#events">
							<Card
								className={cn(
									"h-full hover:shadow-lg transition-all overflow-hidden text-white",
									gradients.events
								)}
							>
								<CardContent className="p-6 flex items-center justify-between">
									<div>
										<h3 className="text-xl font-bold mb-1">Events</h3>
										<p className="text-white/80 text-sm">{events.length} Upcoming Events</p>
									</div>
									<div className="bg-white/20 p-3 rounded-full">
										<Users className="h-6 w-6" />
									</div>
								</CardContent>
							</Card>
						</Link>
					</div>

					{/* Company Highlights */}
					<div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
						<Card className="border-0 shadow-md bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
							<CardContent className="p-6">
								<h3 className="text-xl font-bold mb-4 flex items-center">
									<Award className="h-5 w-5 mr-2 text-amber-500" />
									Company Highlights
								</h3>
								<div className="space-y-4">
									<div className="flex justify-between items-center border-b pb-2 border-gray-200 dark:border-gray-700">
										<span className="text-muted-foreground">Founded</span>
										{company.foundedyear || "n/a"}
									</div>
									<div className="flex justify-between items-center border-b pb-2 border-gray-200 dark:border-gray-700">
										<span className="text-muted-foreground">Team Size</span>
										<span className="font-medium">{company.teamsize || "n/a"}</span>
									</div>
									<div className="flex justify-between items-center border-b pb-2 border-gray-200 dark:border-gray-700">
										<span className="text-muted-foreground">Location</span>
										<span className="font-medium">{company.location || "n/a"}</span>
									</div>
									<div className="flex justify-between items-center border-b pb-2 border-gray-200 dark:border-gray-700">
										<span className="text-muted-foreground">Subnets</span>
										<span className="font-medium">{subnets.length}</span>
									</div>
									<div className="flex justify-between items-center">
										<span className="text-muted-foreground">Applications</span>
										<span className="font-medium">{products.length}</span>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="border-0 shadow-md col-span-2 overflow-hidden">
							<div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 text-white">
								<h3 className="text-lg font-medium">Key Partnership Spotlight</h3>
							</div>
							<CardContent className="p-6">
								<div className="flex flex-col md:flex-row gap-6">
									<div className="flex-1 md:border-r border-gray-200 dark:border-gray-700 md:pr-6">
										<h4 className="text-lg font-bold mb-3 flex items-center">
											<Award className="h-5 w-5 mr-2 text-amber-500" />
											About {company.name}
										</h4>
										<div className="text-muted-foreground mb-4 line-clamp-3">
											<ReactMarkdown>{company.description}</ReactMarkdown>
										</div>
										<div className="grid grid-cols-2 gap-4 text-sm">
											<div>
												<p className="text-muted-foreground">Founded</p>
												<p className="font-medium">{company.foundedyear || "n/a"}</p>
											</div>
											<div>
												<p className="text-muted-foreground">Team Size</p>
												<p className="font-medium">{company.teamsize || "n/a"}</p>
											</div>
											<div>
												<p className="text-muted-foreground">Location</p>
												<p className="font-medium">{company.location || "n/a"}</p>
											</div>
											<div>
												<p className="text-muted-foreground">Categories</p>
												<div className="flex flex-wrap gap-1 mt-1">
													{categories.slice(0, 2).map((category) => (
														<Badge key={category.id} variant="outline" className="text-xs">
															{category.name}
														</Badge>
													))}
													{categories.length > 2 && (
														<Badge variant="outline" className="text-xs">
															+{categories.length - 2}
														</Badge>
													)}
												</div>
											</div>
										</div>
									</div>

									<div className="flex-1 flex items-center justify-center">
										<div className="w-full">
											<h4 className="text-sm font-medium text-muted-foreground mb-3">
												Sponsored
											</h4>
											<SmartAdBanner slotId={11} />
										</div>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>

					<div className="w-full mt-8">
						<SmartAdBanner slotId={12} />
					</div>

					{/* Half Page Ad */}
					<div className="w-full mt-8 flex justify-center">
						<SmartAdBanner slotId={13} />
					</div>

					<div className="mt-8">
						<Tabs defaultValue="subnets" className="space-y-8">
							<TabsList className="bg-background border dark:border-gray-800">
								<TabsTrigger value="subnets" id="subnets" className="data-[state=active]:bg-muted">
									<Code className="h-4 w-4 mr-2" />
									Subnets ({subnets.length})
								</TabsTrigger>
								<TabsTrigger
									value="applications"
									id="applications"
									className="data-[state=active]:bg-muted"
								>
									<Briefcase className="h-4 w-4 mr-2" />
									Applications ({products.length})
								</TabsTrigger>
								<TabsTrigger value="news" id="news" className="data-[state=active]:bg-muted">
									<Newspaper className="h-4 w-4 mr-2" />
									News ({news.length})
								</TabsTrigger>
								<TabsTrigger value="jobs" id="jobs" className="data-[state=active]:bg-muted">
									<Users className="h-4 w-4 mr-2" />
									Jobs ({jobs.length})
								</TabsTrigger>

								<TabsTrigger value="events" id="events" className="data-[state=active]:bg-muted">
									<Calendar className="h-4 w-4 mr-2" />
									Events ({events.length})
								</TabsTrigger>
							</TabsList>

							<TabsContent value="subnets">
								<CompanySubnetsGrid
									subnets={subnets.map((subnet) => ({
										...subnet,
										metrics: subnetsMetrics.find((metric) => metric.netuid === subnet.netuid),
										categories: categories.filter((cat) => subnet.category_ids?.includes(cat.id)),
									}))}
								/>
							</TabsContent>
							<TabsContent value="applications">
								<CompanyApplicationsGrid
									products={products}
									categories={categories}
									companies={[company]}
								/>
							</TabsContent>
							<TabsContent value="news">
								<CompanyNews news={news} categories={categories} />
							</TabsContent>
							<TabsContent value="jobs">
								<CompanyJobs
									jobs={jobs}
									companyId={company.id}
									authorized_job_admin={authorized_job_admin}
								/>
							</TabsContent>
							<TabsContent value="events">
								<CompanyEvents
									events={events}
									companyId={company.id}
									authorized_events_admin={authorized_events_admin}
								/>
							</TabsContent>
						</Tabs>

						{/* Square Button Ad */}
						<div className="mt-8 flex justify-center">
							<SmartAdBanner slotId={14} />
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
