// components/Events/event-grid.tsx
import { EventCard } from "@/components/events/event-card";
import type { Category, Company, Event, Product, Subnet } from "@/lib/db/models";

interface EventsGridProps {
	events: (Event & {
		companies?: Company[];
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	editMode?: boolean;
	onEdit?: (event: Event) => void;
	onDelete?: (eventId: number) => void;
	loading?: boolean;
}

export function EventsGrid({ events, editMode = false, onEdit, onDelete, loading = false }: EventsGridProps) {
	if (loading) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				{Array.from({ length: 6 }).map((_, i) => (
					<div key={i} className="h-[180px] bg-muted animate-pulse rounded-lg"></div>
				))}
			</div>
		);
	}

	if (events.length === 0) {
		return (
			<div className="text-center py-12 border rounded-lg">
				<p className="text-muted-foreground">No events found</p>
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
			{events.map((event) => (
				<EventCard key={event.id} event={event} editMode={editMode} onEdit={onEdit} onDelete={onDelete} />
			))}
		</div>
	);
}
