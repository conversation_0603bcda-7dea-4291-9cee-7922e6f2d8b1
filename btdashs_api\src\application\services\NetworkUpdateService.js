const axios = require("axios");
const logger = require("../../../logger");

class NetworkUpdateService {
	constructor() {
		this.apiUrl = process.env.API_URL;
		this.internalApiKey = process.env.INTERNAL_API_KEY;

		if (!this.apiUrl) {
			logger.warn("API_URL is not defined in environment variables");
		}

		if (!this.internalApiKey) {
			logger.warn("INTERNAL_API_KEY is not defined in environment variables");
		}
	}

	/**
	 * Update all TAO data
	 * @returns {Promise<Object>} Update result
	 */
	async updateAllTao() {
		try {
			if (!this.apiUrl || !this.internalApiKey) {
				throw new Error("API_URL or INTERNAL_API_KEY is not configured");
			}

			logger.info("Starting TAO data update");

			const response = await axios.post(
				`${this.apiUrl}/update/all-tao`,
				{},
				{
					headers: {
						"x-internal-api-key": this.internalApi<PERSON>ey,
					},
					timeout: 300000, // 5 minutes timeout
				}
			);

			logger.info("TAO data update completed successfully", {
				status: response.status,
				data: response.data,
			});

			return {
				success: true,
				status: response.status,
				data: response.data,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			logger.error("TAO data update failed", {
				error: error.message,
				status: error.response?.status,
				data: error.response?.data,
			});

			return {
				success: false,
				error: error.message,
				status: error.response?.status,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Update GitHub team members and contributions
	 * @returns {Promise<Object>} Update result
	 */
	async updateGithubData() {
		try {
			if (!this.apiUrl || !this.internalApiKey) {
				throw new Error("API_URL or INTERNAL_API_KEY is not configured");
			}

			logger.info("Starting GitHub data update");

			const [teamMembersResponse, contributionsResponse] = await Promise.allSettled([
				axios.post(
					`${this.apiUrl}/update/github-team-members`,
					{},
					{
						headers: {
							"x-internal-api-key": this.internalApiKey,
						},
						timeout: 180000, // 3 minutes timeout
					}
				),
				axios.post(
					`${this.apiUrl}/update/github-contributions`,
					{},
					{
						headers: {
							"x-internal-api-key": this.internalApiKey,
						},
						timeout: 180000, // 3 minutes timeout
					}
				),
			]);

			const results = {
				success: true,
				teamMembers: {
					success: teamMembersResponse.status === "fulfilled",
					data: teamMembersResponse.status === "fulfilled" ? teamMembersResponse.value.data : null,
					error: teamMembersResponse.status === "rejected" ? teamMembersResponse.reason.message : null,
				},
				contributions: {
					success: contributionsResponse.status === "fulfilled",
					data: contributionsResponse.status === "fulfilled" ? contributionsResponse.value.data : null,
					error: contributionsResponse.status === "rejected" ? contributionsResponse.reason.message : null,
				},
				timestamp: new Date().toISOString(),
			};

			// Check if both operations failed
			if (!results.teamMembers.success && !results.contributions.success) {
				results.success = false;
				logger.error("GitHub data update failed completely", results);
			} else if (!results.teamMembers.success || !results.contributions.success) {
				logger.warn("GitHub data update partially failed", results);
			} else {
				logger.info("GitHub data update completed successfully", results);
			}

			return results;
		} catch (error) {
			logger.error("GitHub data update failed", {
				error: error.message,
			});

			return {
				success: false,
				error: error.message,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Update validator data (if endpoint exists)
	 * @returns {Promise<Object>} Update result
	 */
	async updateValidatorData() {
		try {
			if (!this.apiUrl || !this.internalApiKey) {
				throw new Error("API_URL or INTERNAL_API_KEY is not configured");
			}

			logger.info("Starting validator data update");

			const response = await axios.post(
				`${this.apiUrl}/update/validators`,
				{},
				{
					headers: {
						"x-internal-api-key": this.internalApiKey,
					},
					timeout: 300000, // 5 minutes timeout
				}
			);

			logger.info("Validator data update completed successfully", {
				status: response.status,
				data: response.data,
			});

			return {
				success: true,
				status: response.status,
				data: response.data,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			// Don't log as error if endpoint doesn't exist (404)
			if (error.response?.status === 404) {
				logger.debug("Validator update endpoint not found, skipping");
				return {
					success: true,
					skipped: true,
					reason: "Endpoint not available",
					timestamp: new Date().toISOString(),
				};
			}

			logger.error("Validator data update failed", {
				error: error.message,
				status: error.response?.status,
				data: error.response?.data,
			});

			return {
				success: false,
				error: error.message,
				status: error.response?.status,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Update network statistics
	 * @returns {Promise<Object>} Update result
	 */
	async updateNetworkStats() {
		try {
			if (!this.apiUrl || !this.internalApiKey) {
				throw new Error("API_URL or INTERNAL_API_KEY is not configured");
			}

			logger.info("Starting network stats update");

			const response = await axios.post(
				`${this.apiUrl}/update/network-stats`,
				{},
				{
					headers: {
						"x-internal-api-key": this.internalApiKey,
					},
					timeout: 180000, // 3 minutes timeout
				}
			);

			logger.info("Network stats update completed successfully", {
				status: response.status,
				data: response.data,
			});

			return {
				success: true,
				status: response.status,
				data: response.data,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			// Don't log as error if endpoint doesn't exist (404)
			if (error.response?.status === 404) {
				logger.debug("Network stats update endpoint not found, skipping");
				return {
					success: true,
					skipped: true,
					reason: "Endpoint not available",
					timestamp: new Date().toISOString(),
				};
			}

			logger.error("Network stats update failed", {
				error: error.message,
				status: error.response?.status,
				data: error.response?.data,
			});

			return {
				success: false,
				error: error.message,
				status: error.response?.status,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Run comprehensive network data update (TAO + GitHub only)
	 * @returns {Promise<Object>} Combined update results
	 */
	async updateAllNetworkData() {
		try {
			logger.info("Starting comprehensive network data update (TAO + GitHub)");

			const [taoResult, githubResult] = await Promise.allSettled([this.updateAllTao(), this.updateGithubData()]);

			const results = {
				success: true,
				tao:
					taoResult.status === "fulfilled"
						? taoResult.value
						: { success: false, error: taoResult.reason.message },
				github:
					githubResult.status === "fulfilled"
						? githubResult.value
						: { success: false, error: githubResult.reason.message },
				timestamp: new Date().toISOString(),
			};

			// Calculate overall success
			const successCount = [results.tao.success, results.github.success].filter(Boolean).length;

			results.success = successCount >= 1; // At least 50% success rate (1 out of 2)
			results.successRate = successCount / 2;

			if (results.success) {
				logger.info("Comprehensive network data update completed", {
					successRate: results.successRate,
					successCount,
					totalOperations: 2,
				});
			} else {
				logger.error("Comprehensive network data update failed", {
					successRate: results.successRate,
					successCount,
					totalOperations: 2,
				});
			}

			return results;
		} catch (error) {
			logger.error("Comprehensive network data update failed", {
				error: error.message,
			});

			return {
				success: false,
				error: error.message,
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * Get update status and last run information
	 * @returns {Promise<Object>} Status information
	 */
	async getUpdateStatus() {
		try {
			// This could be enhanced to store last run times in database/cache
			return {
				configured: !!(this.apiUrl && this.internalApiKey),
				apiUrl: this.apiUrl ? "configured" : "missing",
				internalApiKey: this.internalApiKey ? "configured" : "missing",
				lastCheck: new Date().toISOString(),
			};
		} catch (error) {
			logger.error("Error getting update status", { error });
			return {
				configured: false,
				error: error.message,
			};
		}
	}
}

module.exports = NetworkUpdateService;
