"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/robust-predicates";
exports.ids = ["vendor-chunks/robust-predicates"];
exports.modules = {

/***/ "(ssr)/./node_modules/robust-predicates/esm/incircle.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/incircle.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* binding */ incircle),\n/* harmony export */   incirclefast: () => (/* binding */ incirclefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst iccerrboundA = (10 + 96 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundB = (4 + 48 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst iccerrboundC = (44 + 576 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst aa = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst axtbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst aytbc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bxtca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bytca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cxtab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cytab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abtt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bctt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst catt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _16c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _32 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _32b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(32);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _64 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(64);\n\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adx, _8), _8, adx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdx, _8), _8, bdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdx, _8), _8, cdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, 2 * adx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, 2 * ady, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, 2 * bdy, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, 2 * cdx, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, 2 * cdy, _16), _16,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(axtbclen, axtbc, adxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * adx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(aytbclen, aytbc, adytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * ady, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, adytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bxtcalen, bxtca, bdxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bytcalen, bytca, bdytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * bdy, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, bdytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cxtablen, cxtab, cdxtail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdx, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdxtail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(cytablen, cytab, cdytail, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, 2 * cdy, _16), _16,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len2, _8, cdytail, _16b), _16b,\n                (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nfunction incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nfunction incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/incircle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/insphere.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/insphere.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   insphere: () => (/* binding */ insphere),\n/* harmony export */   inspherefast: () => (/* binding */ inspherefast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst isperrboundA = (16 + 224 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundB = (5 + 72 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst isperrboundC = (71 + 1408 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst cd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst de = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst da = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst eb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst abc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bcd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cde = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst dea = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst abd = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst bce = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst cda = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst deb = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst eac = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\n\nconst adet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst bdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst cdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst ddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst edet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\nconst abdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cddet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(2304);\nconst cdedet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(3456);\nconst deter = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(5760);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst _24 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(24);\nconst _48 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _48b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(48);\nconst _96 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst _192 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nconst _384x = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384y = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _384z = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(384);\nconst _768 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(768);\n\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, a, az, _8), _8,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, b, bz, _8b), _8b,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, c, cz, _8c), _8c, _16, out);\n}\n\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(alen, a, blen, b, _48), _48,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(clen, c, dlen, d, _48b), _48b), _48b, _96);\n\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, x, _192), _192, x, _384x), _384x,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, y, _192), _192, y, _384y), _384y,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\n\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    s1 = ax * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n\n    const deterlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet,\n        liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n            liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet,\n            liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet,\n            liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n\n    return deter[deterlen - 1];\n}\n\nconst xdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst ydet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst zdet = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(96);\nconst fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(1152);\n\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum_three)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, x, _48), _48, x, xdet), xdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, y, _48), _48, y, ydet), ydet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)((0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\n\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    s1 = aex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n\n    const finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet,\n            liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.negate)(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet,\n            liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 &&\n        bextail === 0 && beytail === 0 && beztail === 0 &&\n        cextail === 0 && ceytail === 0 && ceztail === 0 &&\n        dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n\n    errbound = isperrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n\n    const abeps = (aex * beytail + bey * aextail) - (aey * bextail + bex * aeytail);\n    const bceps = (bex * ceytail + cey * bextail) - (bey * cextail + cex * beytail);\n    const cdeps = (cex * deytail + dey * cextail) - (cey * dextail + dex * ceytail);\n    const daeps = (dex * aeytail + aey * dextail) - (dey * aextail + aex * deytail);\n    const aceps = (aex * ceytail + cey * aextail) - (aey * cextail + cex * aeytail);\n    const bdeps = (bex * deytail + dey * bextail) - (bey * dextail + dex * beytail);\n    det +=\n        (((bex * bex + bey * bey + bez * bez) * ((cez * daeps + dez * aceps + aez * cdeps) +\n        (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) *\n        ((aez * bceps - bez * aceps + cez * abeps) + (aeztail * bc3 - beztail * ac3 + ceztail * ab3))) -\n        ((aex * aex + aey * aey + aez * aez) * ((bez * cdeps - cez * bdeps + dez * bceps) +\n        (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) *\n        ((dez * abeps + aez * bdeps + bez * daeps) + (deztail * ab3 + aeztail * bd3 + beztail * da3)))) +\n        2 * (((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) +\n        (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3)) -\n        ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) +\n        (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\n\nfunction insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    const det =\n        (clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab)) +\n        (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent =\n        (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift +\n        (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift +\n        (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift +\n        (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\n\nfunction inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    return (clift * dab - dlift * abc) + (alift * bcd - blift * cda);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvZXNtL2luc3BoZXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwRzs7QUFFMUcsaUNBQWlDLDZDQUFPLElBQUksNkNBQU87QUFDbkQsK0JBQStCLDZDQUFPLElBQUksNkNBQU87QUFDakQsa0NBQWtDLDZDQUFPLElBQUksNkNBQU8sR0FBRyw2Q0FBTzs7QUFFOUQsV0FBVyw2Q0FBRztBQUNkLFdBQVcsNkNBQUc7QUFDZCxXQUFXLDZDQUFHO0FBQ2QsV0FBVyw2Q0FBRztBQUNkLFdBQVcsNkNBQUc7QUFDZCxXQUFXLDZDQUFHO0FBQ2QsV0FBVyw2Q0FBRztBQUNkLFdBQVcsNkNBQUc7QUFDZCxXQUFXLDZDQUFHO0FBQ2QsV0FBVyw2Q0FBRzs7QUFFZCxZQUFZLDZDQUFHO0FBQ2YsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHO0FBQ2YsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHO0FBQ2YsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHOztBQUVmLGFBQWEsNkNBQUc7QUFDaEIsYUFBYSw2Q0FBRztBQUNoQixhQUFhLDZDQUFHO0FBQ2hCLGFBQWEsNkNBQUc7QUFDaEIsYUFBYSw2Q0FBRztBQUNoQixjQUFjLDZDQUFHO0FBQ2pCLGNBQWMsNkNBQUc7QUFDakIsZUFBZSw2Q0FBRztBQUNsQixjQUFjLDZDQUFHOztBQUVqQixXQUFXLDZDQUFHO0FBQ2QsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHO0FBQ2YsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixhQUFhLDZDQUFHO0FBQ2hCLFlBQVksNkNBQUc7QUFDZixhQUFhLDZDQUFHO0FBQ2hCLGNBQWMsNkNBQUc7QUFDakIsY0FBYyw2Q0FBRztBQUNqQixjQUFjLDZDQUFHO0FBQ2pCLGFBQWEsNkNBQUc7O0FBRWhCO0FBQ0EsV0FBVyxtREFBUztBQUNwQixRQUFRLCtDQUFLO0FBQ2IsUUFBUSwrQ0FBSztBQUNiLFFBQVEsK0NBQUs7QUFDYjs7QUFFQTtBQUNBLGdCQUFnQiw2Q0FBRztBQUNuQixRQUFRLDZDQUFHO0FBQ1gsUUFBUSxnREFBTSxDQUFDLDZDQUFHOztBQUVsQixXQUFXLG1EQUFTO0FBQ3BCLFFBQVEsK0NBQUssQ0FBQywrQ0FBSztBQUNuQixRQUFRLCtDQUFLLENBQUMsK0NBQUs7QUFDbkIsUUFBUSwrQ0FBSyxDQUFDLCtDQUFLO0FBQ25COztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxxQkFBcUIsbURBQVM7QUFDOUI7QUFDQTtBQUNBLFFBQVEsbURBQVM7QUFDakI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsYUFBYSw2Q0FBRztBQUNoQixhQUFhLDZDQUFHO0FBQ2hCLGFBQWEsNkNBQUc7QUFDaEIsWUFBWSw2Q0FBRzs7QUFFZjtBQUNBO0FBQ0EsV0FBVyxtREFBUztBQUNwQixRQUFRLCtDQUFLLENBQUMsK0NBQUs7QUFDbkIsUUFBUSwrQ0FBSyxDQUFDLCtDQUFLO0FBQ25CLFFBQVEsK0NBQUssQ0FBQywrQ0FBSztBQUNuQjs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0EsUUFBUSw4Q0FBUTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBLFFBQVEsOENBQVE7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQSxRQUFRLDhDQUFRO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1CQUFtQiw2Q0FBRztBQUN0QixRQUFRLDZDQUFHO0FBQ1gsWUFBWSxnREFBTTtBQUNsQjtBQUNBLFFBQVEsNkNBQUc7QUFDWCxZQUFZLGdEQUFNO0FBQ2xCOztBQUVBLGNBQWMsa0RBQVE7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDBDQUEwQyxvREFBYzs7QUFFeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxyb2J1c3QtcHJlZGljYXRlc1xcZXNtXFxpbnNwaGVyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Vwc2lsb24sIHNwbGl0dGVyLCByZXN1bHRlcnJib3VuZCwgZXN0aW1hdGUsIHZlYywgc3VtLCBzdW1fdGhyZWUsIHNjYWxlLCBuZWdhdGV9IGZyb20gJy4vdXRpbC5qcyc7XG5cbmNvbnN0IGlzcGVycmJvdW5kQSA9ICgxNiArIDIyNCAqIGVwc2lsb24pICogZXBzaWxvbjtcbmNvbnN0IGlzcGVycmJvdW5kQiA9ICg1ICsgNzIgKiBlcHNpbG9uKSAqIGVwc2lsb247XG5jb25zdCBpc3BlcnJib3VuZEMgPSAoNzEgKyAxNDA4ICogZXBzaWxvbikgKiBlcHNpbG9uICogZXBzaWxvbjtcblxuY29uc3QgYWIgPSB2ZWMoNCk7XG5jb25zdCBiYyA9IHZlYyg0KTtcbmNvbnN0IGNkID0gdmVjKDQpO1xuY29uc3QgZGUgPSB2ZWMoNCk7XG5jb25zdCBlYSA9IHZlYyg0KTtcbmNvbnN0IGFjID0gdmVjKDQpO1xuY29uc3QgYmQgPSB2ZWMoNCk7XG5jb25zdCBjZSA9IHZlYyg0KTtcbmNvbnN0IGRhID0gdmVjKDQpO1xuY29uc3QgZWIgPSB2ZWMoNCk7XG5cbmNvbnN0IGFiYyA9IHZlYygyNCk7XG5jb25zdCBiY2QgPSB2ZWMoMjQpO1xuY29uc3QgY2RlID0gdmVjKDI0KTtcbmNvbnN0IGRlYSA9IHZlYygyNCk7XG5jb25zdCBlYWIgPSB2ZWMoMjQpO1xuY29uc3QgYWJkID0gdmVjKDI0KTtcbmNvbnN0IGJjZSA9IHZlYygyNCk7XG5jb25zdCBjZGEgPSB2ZWMoMjQpO1xuY29uc3QgZGViID0gdmVjKDI0KTtcbmNvbnN0IGVhYyA9IHZlYygyNCk7XG5cbmNvbnN0IGFkZXQgPSB2ZWMoMTE1Mik7XG5jb25zdCBiZGV0ID0gdmVjKDExNTIpO1xuY29uc3QgY2RldCA9IHZlYygxMTUyKTtcbmNvbnN0IGRkZXQgPSB2ZWMoMTE1Mik7XG5jb25zdCBlZGV0ID0gdmVjKDExNTIpO1xuY29uc3QgYWJkZXQgPSB2ZWMoMjMwNCk7XG5jb25zdCBjZGRldCA9IHZlYygyMzA0KTtcbmNvbnN0IGNkZWRldCA9IHZlYygzNDU2KTtcbmNvbnN0IGRldGVyID0gdmVjKDU3NjApO1xuXG5jb25zdCBfOCA9IHZlYyg4KTtcbmNvbnN0IF84YiA9IHZlYyg4KTtcbmNvbnN0IF84YyA9IHZlYyg4KTtcbmNvbnN0IF8xNiA9IHZlYygxNik7XG5jb25zdCBfMjQgPSB2ZWMoMjQpO1xuY29uc3QgXzQ4ID0gdmVjKDQ4KTtcbmNvbnN0IF80OGIgPSB2ZWMoNDgpO1xuY29uc3QgXzk2ID0gdmVjKDk2KTtcbmNvbnN0IF8xOTIgPSB2ZWMoMTkyKTtcbmNvbnN0IF8zODR4ID0gdmVjKDM4NCk7XG5jb25zdCBfMzg0eSA9IHZlYygzODQpO1xuY29uc3QgXzM4NHogPSB2ZWMoMzg0KTtcbmNvbnN0IF83NjggPSB2ZWMoNzY4KTtcblxuZnVuY3Rpb24gc3VtX3RocmVlX3NjYWxlKGEsIGIsIGMsIGF6LCBieiwgY3osIG91dCkge1xuICAgIHJldHVybiBzdW1fdGhyZWUoXG4gICAgICAgIHNjYWxlKDQsIGEsIGF6LCBfOCksIF84LFxuICAgICAgICBzY2FsZSg0LCBiLCBieiwgXzhiKSwgXzhiLFxuICAgICAgICBzY2FsZSg0LCBjLCBjeiwgXzhjKSwgXzhjLCBfMTYsIG91dCk7XG59XG5cbmZ1bmN0aW9uIGxpZnRleGFjdChhbGVuLCBhLCBibGVuLCBiLCBjbGVuLCBjLCBkbGVuLCBkLCB4LCB5LCB6LCBvdXQpIHtcbiAgICBjb25zdCBsZW4gPSBzdW0oXG4gICAgICAgIHN1bShhbGVuLCBhLCBibGVuLCBiLCBfNDgpLCBfNDgsXG4gICAgICAgIG5lZ2F0ZShzdW0oY2xlbiwgYywgZGxlbiwgZCwgXzQ4YiksIF80OGIpLCBfNDhiLCBfOTYpO1xuXG4gICAgcmV0dXJuIHN1bV90aHJlZShcbiAgICAgICAgc2NhbGUoc2NhbGUobGVuLCBfOTYsIHgsIF8xOTIpLCBfMTkyLCB4LCBfMzg0eCksIF8zODR4LFxuICAgICAgICBzY2FsZShzY2FsZShsZW4sIF85NiwgeSwgXzE5MiksIF8xOTIsIHksIF8zODR5KSwgXzM4NHksXG4gICAgICAgIHNjYWxlKHNjYWxlKGxlbiwgXzk2LCB6LCBfMTkyKSwgXzE5MiwgeiwgXzM4NHopLCBfMzg0eiwgXzc2OCwgb3V0KTtcbn1cblxuZnVuY3Rpb24gaW5zcGhlcmVleGFjdChheCwgYXksIGF6LCBieCwgYnksIGJ6LCBjeCwgY3ksIGN6LCBkeCwgZHksIGR6LCBleCwgZXksIGV6KSB7XG4gICAgbGV0IGJ2aXJ0LCBjLCBhaGksIGFsbywgYmhpLCBibG8sIF9pLCBfaiwgXzAsIHMxLCBzMCwgdDEsIHQwLCB1MztcblxuICAgIHMxID0gYXggKiBieTtcbiAgICBjID0gc3BsaXR0ZXIgKiBheDtcbiAgICBhaGkgPSBjIC0gKGMgLSBheCk7XG4gICAgYWxvID0gYXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYnk7XG4gICAgYmhpID0gYyAtIChjIC0gYnkpO1xuICAgIGJsbyA9IGJ5IC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGJ4ICogYXk7XG4gICAgYyA9IHNwbGl0dGVyICogYng7XG4gICAgYWhpID0gYyAtIChjIC0gYngpO1xuICAgIGFsbyA9IGJ4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGF5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGF5KTtcbiAgICBibG8gPSBheSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBhYlswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGFiWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBhYlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGFiWzNdID0gdTM7XG4gICAgczEgPSBieCAqIGN5O1xuICAgIGMgPSBzcGxpdHRlciAqIGJ4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGJ4KTtcbiAgICBhbG8gPSBieCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBjeSk7XG4gICAgYmxvID0gY3kgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gY3ggKiBieTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBjeCk7XG4gICAgYWxvID0gY3ggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYnk7XG4gICAgYmhpID0gYyAtIChjIC0gYnkpO1xuICAgIGJsbyA9IGJ5IC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIGJjWzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgYmNbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICB1MyA9IF9qICsgX2k7XG4gICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgIGJjWzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgYmNbM10gPSB1MztcbiAgICBzMSA9IGN4ICogZHk7XG4gICAgYyA9IHNwbGl0dGVyICogY3g7XG4gICAgYWhpID0gYyAtIChjIC0gY3gpO1xuICAgIGFsbyA9IGN4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGR5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGR5KTtcbiAgICBibG8gPSBkeSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBkeCAqIGN5O1xuICAgIGMgPSBzcGxpdHRlciAqIGR4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGR4KTtcbiAgICBhbG8gPSBkeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBjeSk7XG4gICAgYmxvID0gY3kgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgY2RbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBjZFsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgY2RbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBjZFszXSA9IHUzO1xuICAgIHMxID0gZHggKiBleTtcbiAgICBjID0gc3BsaXR0ZXIgKiBkeDtcbiAgICBhaGkgPSBjIC0gKGMgLSBkeCk7XG4gICAgYWxvID0gZHggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogZXk7XG4gICAgYmhpID0gYyAtIChjIC0gZXkpO1xuICAgIGJsbyA9IGV5IC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGV4ICogZHk7XG4gICAgYyA9IHNwbGl0dGVyICogZXg7XG4gICAgYWhpID0gYyAtIChjIC0gZXgpO1xuICAgIGFsbyA9IGV4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGR5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGR5KTtcbiAgICBibG8gPSBkeSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBkZVswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGRlWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBkZVsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGRlWzNdID0gdTM7XG4gICAgczEgPSBleCAqIGF5O1xuICAgIGMgPSBzcGxpdHRlciAqIGV4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGV4KTtcbiAgICBhbG8gPSBleCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBheTtcbiAgICBiaGkgPSBjIC0gKGMgLSBheSk7XG4gICAgYmxvID0gYXkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gYXggKiBleTtcbiAgICBjID0gc3BsaXR0ZXIgKiBheDtcbiAgICBhaGkgPSBjIC0gKGMgLSBheCk7XG4gICAgYWxvID0gYXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogZXk7XG4gICAgYmhpID0gYyAtIChjIC0gZXkpO1xuICAgIGJsbyA9IGV5IC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIGVhWzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgZWFbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICB1MyA9IF9qICsgX2k7XG4gICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgIGVhWzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgZWFbM10gPSB1MztcbiAgICBzMSA9IGF4ICogY3k7XG4gICAgYyA9IHNwbGl0dGVyICogYXg7XG4gICAgYWhpID0gYyAtIChjIC0gYXgpO1xuICAgIGFsbyA9IGF4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGN5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGN5KTtcbiAgICBibG8gPSBjeSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBjeCAqIGF5O1xuICAgIGMgPSBzcGxpdHRlciAqIGN4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGN4KTtcbiAgICBhbG8gPSBjeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBheTtcbiAgICBiaGkgPSBjIC0gKGMgLSBheSk7XG4gICAgYmxvID0gYXkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgYWNbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBhY1sxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgYWNbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBhY1szXSA9IHUzO1xuICAgIHMxID0gYnggKiBkeTtcbiAgICBjID0gc3BsaXR0ZXIgKiBieDtcbiAgICBhaGkgPSBjIC0gKGMgLSBieCk7XG4gICAgYWxvID0gYnggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogZHk7XG4gICAgYmhpID0gYyAtIChjIC0gZHkpO1xuICAgIGJsbyA9IGR5IC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGR4ICogYnk7XG4gICAgYyA9IHNwbGl0dGVyICogZHg7XG4gICAgYWhpID0gYyAtIChjIC0gZHgpO1xuICAgIGFsbyA9IGR4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJ5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGJ5KTtcbiAgICBibG8gPSBieSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBiZFswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGJkWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBiZFsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGJkWzNdID0gdTM7XG4gICAgczEgPSBjeCAqIGV5O1xuICAgIGMgPSBzcGxpdHRlciAqIGN4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGN4KTtcbiAgICBhbG8gPSBjeCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBleTtcbiAgICBiaGkgPSBjIC0gKGMgLSBleSk7XG4gICAgYmxvID0gZXkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gZXggKiBjeTtcbiAgICBjID0gc3BsaXR0ZXIgKiBleDtcbiAgICBhaGkgPSBjIC0gKGMgLSBleCk7XG4gICAgYWxvID0gZXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogY3k7XG4gICAgYmhpID0gYyAtIChjIC0gY3kpO1xuICAgIGJsbyA9IGN5IC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIGNlWzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgY2VbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICB1MyA9IF9qICsgX2k7XG4gICAgYnZpcnQgPSB1MyAtIF9qO1xuICAgIGNlWzJdID0gX2ogLSAodTMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgY2VbM10gPSB1MztcbiAgICBzMSA9IGR4ICogYXk7XG4gICAgYyA9IHNwbGl0dGVyICogZHg7XG4gICAgYWhpID0gYyAtIChjIC0gZHgpO1xuICAgIGFsbyA9IGR4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGF5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGF5KTtcbiAgICBibG8gPSBheSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBheCAqIGR5O1xuICAgIGMgPSBzcGxpdHRlciAqIGF4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGF4KTtcbiAgICBhbG8gPSBheCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBkeTtcbiAgICBiaGkgPSBjIC0gKGMgLSBkeSk7XG4gICAgYmxvID0gZHkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgZGFbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBkYVsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIHUzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IHUzIC0gX2o7XG4gICAgZGFbMl0gPSBfaiAtICh1MyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBkYVszXSA9IHUzO1xuICAgIHMxID0gZXggKiBieTtcbiAgICBjID0gc3BsaXR0ZXIgKiBleDtcbiAgICBhaGkgPSBjIC0gKGMgLSBleCk7XG4gICAgYWxvID0gZXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYnk7XG4gICAgYmhpID0gYyAtIChjIC0gYnkpO1xuICAgIGJsbyA9IGJ5IC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGJ4ICogZXk7XG4gICAgYyA9IHNwbGl0dGVyICogYng7XG4gICAgYWhpID0gYyAtIChjIC0gYngpO1xuICAgIGFsbyA9IGJ4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGV5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGV5KTtcbiAgICBibG8gPSBleSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBlYlswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGViWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgdTMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gdTMgLSBfajtcbiAgICBlYlsyXSA9IF9qIC0gKHUzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGViWzNdID0gdTM7XG5cbiAgICBjb25zdCBhYmNsZW4gPSBzdW1fdGhyZWVfc2NhbGUoYWIsIGJjLCBhYywgY3osIGF6LCAtYnosIGFiYyk7XG4gICAgY29uc3QgYmNkbGVuID0gc3VtX3RocmVlX3NjYWxlKGJjLCBjZCwgYmQsIGR6LCBieiwgLWN6LCBiY2QpO1xuICAgIGNvbnN0IGNkZWxlbiA9IHN1bV90aHJlZV9zY2FsZShjZCwgZGUsIGNlLCBleiwgY3osIC1keiwgY2RlKTtcbiAgICBjb25zdCBkZWFsZW4gPSBzdW1fdGhyZWVfc2NhbGUoZGUsIGVhLCBkYSwgYXosIGR6LCAtZXosIGRlYSk7XG4gICAgY29uc3QgZWFibGVuID0gc3VtX3RocmVlX3NjYWxlKGVhLCBhYiwgZWIsIGJ6LCBleiwgLWF6LCBlYWIpO1xuICAgIGNvbnN0IGFiZGxlbiA9IHN1bV90aHJlZV9zY2FsZShhYiwgYmQsIGRhLCBkeiwgYXosIGJ6LCBhYmQpO1xuICAgIGNvbnN0IGJjZWxlbiA9IHN1bV90aHJlZV9zY2FsZShiYywgY2UsIGViLCBleiwgYnosIGN6LCBiY2UpO1xuICAgIGNvbnN0IGNkYWxlbiA9IHN1bV90aHJlZV9zY2FsZShjZCwgZGEsIGFjLCBheiwgY3osIGR6LCBjZGEpO1xuICAgIGNvbnN0IGRlYmxlbiA9IHN1bV90aHJlZV9zY2FsZShkZSwgZWIsIGJkLCBieiwgZHosIGV6LCBkZWIpO1xuICAgIGNvbnN0IGVhY2xlbiA9IHN1bV90aHJlZV9zY2FsZShlYSwgYWMsIGNlLCBjeiwgZXosIGF6LCBlYWMpO1xuXG4gICAgY29uc3QgZGV0ZXJsZW4gPSBzdW1fdGhyZWUoXG4gICAgICAgIGxpZnRleGFjdChjZGVsZW4sIGNkZSwgYmNlbGVuLCBiY2UsIGRlYmxlbiwgZGViLCBiY2RsZW4sIGJjZCwgYXgsIGF5LCBheiwgYWRldCksIGFkZXQsXG4gICAgICAgIGxpZnRleGFjdChkZWFsZW4sIGRlYSwgY2RhbGVuLCBjZGEsIGVhY2xlbiwgZWFjLCBjZGVsZW4sIGNkZSwgYngsIGJ5LCBieiwgYmRldCksIGJkZXQsXG4gICAgICAgIHN1bV90aHJlZShcbiAgICAgICAgICAgIGxpZnRleGFjdChlYWJsZW4sIGVhYiwgZGVibGVuLCBkZWIsIGFiZGxlbiwgYWJkLCBkZWFsZW4sIGRlYSwgY3gsIGN5LCBjeiwgY2RldCksIGNkZXQsXG4gICAgICAgICAgICBsaWZ0ZXhhY3QoYWJjbGVuLCBhYmMsIGVhY2xlbiwgZWFjLCBiY2VsZW4sIGJjZSwgZWFibGVuLCBlYWIsIGR4LCBkeSwgZHosIGRkZXQpLCBkZGV0LFxuICAgICAgICAgICAgbGlmdGV4YWN0KGJjZGxlbiwgYmNkLCBhYmRsZW4sIGFiZCwgY2RhbGVuLCBjZGEsIGFiY2xlbiwgYWJjLCBleCwgZXksIGV6LCBlZGV0KSwgZWRldCwgY2RkZXQsIGNkZWRldCksIGNkZWRldCwgYWJkZXQsIGRldGVyKTtcblxuICAgIHJldHVybiBkZXRlcltkZXRlcmxlbiAtIDFdO1xufVxuXG5jb25zdCB4ZGV0ID0gdmVjKDk2KTtcbmNvbnN0IHlkZXQgPSB2ZWMoOTYpO1xuY29uc3QgemRldCA9IHZlYyg5Nik7XG5jb25zdCBmaW4gPSB2ZWMoMTE1Mik7XG5cbmZ1bmN0aW9uIGxpZnRhZGFwdChhLCBiLCBjLCBheiwgYnosIGN6LCB4LCB5LCB6LCBvdXQpIHtcbiAgICBjb25zdCBsZW4gPSBzdW1fdGhyZWVfc2NhbGUoYSwgYiwgYywgYXosIGJ6LCBjeiwgXzI0KTtcbiAgICByZXR1cm4gc3VtX3RocmVlKFxuICAgICAgICBzY2FsZShzY2FsZShsZW4sIF8yNCwgeCwgXzQ4KSwgXzQ4LCB4LCB4ZGV0KSwgeGRldCxcbiAgICAgICAgc2NhbGUoc2NhbGUobGVuLCBfMjQsIHksIF80OCksIF80OCwgeSwgeWRldCksIHlkZXQsXG4gICAgICAgIHNjYWxlKHNjYWxlKGxlbiwgXzI0LCB6LCBfNDgpLCBfNDgsIHosIHpkZXQpLCB6ZGV0LCBfMTkyLCBvdXQpO1xufVxuXG5mdW5jdGlvbiBpbnNwaGVyZWFkYXB0KGF4LCBheSwgYXosIGJ4LCBieSwgYnosIGN4LCBjeSwgY3osIGR4LCBkeSwgZHosIGV4LCBleSwgZXosIHBlcm1hbmVudCkge1xuICAgIGxldCBhYjMsIGJjMywgY2QzLCBkYTMsIGFjMywgYmQzO1xuXG4gICAgbGV0IGFleHRhaWwsIGJleHRhaWwsIGNleHRhaWwsIGRleHRhaWw7XG4gICAgbGV0IGFleXRhaWwsIGJleXRhaWwsIGNleXRhaWwsIGRleXRhaWw7XG4gICAgbGV0IGFlenRhaWwsIGJlenRhaWwsIGNlenRhaWwsIGRlenRhaWw7XG5cbiAgICBsZXQgYnZpcnQsIGMsIGFoaSwgYWxvLCBiaGksIGJsbywgX2ksIF9qLCBfMCwgczEsIHMwLCB0MSwgdDA7XG5cbiAgICBjb25zdCBhZXggPSBheCAtIGV4O1xuICAgIGNvbnN0IGJleCA9IGJ4IC0gZXg7XG4gICAgY29uc3QgY2V4ID0gY3ggLSBleDtcbiAgICBjb25zdCBkZXggPSBkeCAtIGV4O1xuICAgIGNvbnN0IGFleSA9IGF5IC0gZXk7XG4gICAgY29uc3QgYmV5ID0gYnkgLSBleTtcbiAgICBjb25zdCBjZXkgPSBjeSAtIGV5O1xuICAgIGNvbnN0IGRleSA9IGR5IC0gZXk7XG4gICAgY29uc3QgYWV6ID0gYXogLSBlejtcbiAgICBjb25zdCBiZXogPSBieiAtIGV6O1xuICAgIGNvbnN0IGNleiA9IGN6IC0gZXo7XG4gICAgY29uc3QgZGV6ID0gZHogLSBlejtcblxuICAgIHMxID0gYWV4ICogYmV5O1xuICAgIGMgPSBzcGxpdHRlciAqIGFleDtcbiAgICBhaGkgPSBjIC0gKGMgLSBhZXgpO1xuICAgIGFsbyA9IGFleCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBiZXk7XG4gICAgYmhpID0gYyAtIChjIC0gYmV5KTtcbiAgICBibG8gPSBiZXkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gYmV4ICogYWV5O1xuICAgIGMgPSBzcGxpdHRlciAqIGJleDtcbiAgICBhaGkgPSBjIC0gKGMgLSBiZXgpO1xuICAgIGFsbyA9IGJleCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBhZXk7XG4gICAgYmhpID0gYyAtIChjIC0gYWV5KTtcbiAgICBibG8gPSBhZXkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgYWJbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBhYlsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIGFiMyA9IF9qICsgX2k7XG4gICAgYnZpcnQgPSBhYjMgLSBfajtcbiAgICBhYlsyXSA9IF9qIC0gKGFiMyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBhYlszXSA9IGFiMztcbiAgICBzMSA9IGJleCAqIGNleTtcbiAgICBjID0gc3BsaXR0ZXIgKiBiZXg7XG4gICAgYWhpID0gYyAtIChjIC0gYmV4KTtcbiAgICBhbG8gPSBiZXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogY2V5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGNleSk7XG4gICAgYmxvID0gY2V5IC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGNleCAqIGJleTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjZXg7XG4gICAgYWhpID0gYyAtIChjIC0gY2V4KTtcbiAgICBhbG8gPSBjZXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYmV5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGJleSk7XG4gICAgYmxvID0gYmV5IC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIGJjWzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgYmNbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICBiYzMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gYmMzIC0gX2o7XG4gICAgYmNbMl0gPSBfaiAtIChiYzMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgYmNbM10gPSBiYzM7XG4gICAgczEgPSBjZXggKiBkZXk7XG4gICAgYyA9IHNwbGl0dGVyICogY2V4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGNleCk7XG4gICAgYWxvID0gY2V4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGRleTtcbiAgICBiaGkgPSBjIC0gKGMgLSBkZXkpO1xuICAgIGJsbyA9IGRleSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBkZXggKiBjZXk7XG4gICAgYyA9IHNwbGl0dGVyICogZGV4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGRleCk7XG4gICAgYWxvID0gZGV4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGNleTtcbiAgICBiaGkgPSBjIC0gKGMgLSBjZXkpO1xuICAgIGJsbyA9IGNleSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBjZFswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGNkWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgY2QzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IGNkMyAtIF9qO1xuICAgIGNkWzJdID0gX2ogLSAoY2QzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGNkWzNdID0gY2QzO1xuICAgIHMxID0gZGV4ICogYWV5O1xuICAgIGMgPSBzcGxpdHRlciAqIGRleDtcbiAgICBhaGkgPSBjIC0gKGMgLSBkZXgpO1xuICAgIGFsbyA9IGRleCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBhZXk7XG4gICAgYmhpID0gYyAtIChjIC0gYWV5KTtcbiAgICBibG8gPSBhZXkgLSBiaGk7XG4gICAgczAgPSBhbG8gKiBibG8gLSAoczEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIHQxID0gYWV4ICogZGV5O1xuICAgIGMgPSBzcGxpdHRlciAqIGFleDtcbiAgICBhaGkgPSBjIC0gKGMgLSBhZXgpO1xuICAgIGFsbyA9IGFleCAtIGFoaTtcbiAgICBjID0gc3BsaXR0ZXIgKiBkZXk7XG4gICAgYmhpID0gYyAtIChjIC0gZGV5KTtcbiAgICBibG8gPSBkZXkgLSBiaGk7XG4gICAgdDAgPSBhbG8gKiBibG8gLSAodDEgLSBhaGkgKiBiaGkgLSBhbG8gKiBiaGkgLSBhaGkgKiBibG8pO1xuICAgIF9pID0gczAgLSB0MDtcbiAgICBidmlydCA9IHMwIC0gX2k7XG4gICAgZGFbMF0gPSBzMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQwKTtcbiAgICBfaiA9IHMxICsgX2k7XG4gICAgYnZpcnQgPSBfaiAtIHMxO1xuICAgIF8wID0gczEgLSAoX2ogLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgX2kgPSBfMCAtIHQxO1xuICAgIGJ2aXJ0ID0gXzAgLSBfaTtcbiAgICBkYVsxXSA9IF8wIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDEpO1xuICAgIGRhMyA9IF9qICsgX2k7XG4gICAgYnZpcnQgPSBkYTMgLSBfajtcbiAgICBkYVsyXSA9IF9qIC0gKGRhMyAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBkYVszXSA9IGRhMztcbiAgICBzMSA9IGFleCAqIGNleTtcbiAgICBjID0gc3BsaXR0ZXIgKiBhZXg7XG4gICAgYWhpID0gYyAtIChjIC0gYWV4KTtcbiAgICBhbG8gPSBhZXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogY2V5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGNleSk7XG4gICAgYmxvID0gY2V5IC0gYmhpO1xuICAgIHMwID0gYWxvICogYmxvIC0gKHMxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICB0MSA9IGNleCAqIGFleTtcbiAgICBjID0gc3BsaXR0ZXIgKiBjZXg7XG4gICAgYWhpID0gYyAtIChjIC0gY2V4KTtcbiAgICBhbG8gPSBjZXggLSBhaGk7XG4gICAgYyA9IHNwbGl0dGVyICogYWV5O1xuICAgIGJoaSA9IGMgLSAoYyAtIGFleSk7XG4gICAgYmxvID0gYWV5IC0gYmhpO1xuICAgIHQwID0gYWxvICogYmxvIC0gKHQxIC0gYWhpICogYmhpIC0gYWxvICogYmhpIC0gYWhpICogYmxvKTtcbiAgICBfaSA9IHMwIC0gdDA7XG4gICAgYnZpcnQgPSBzMCAtIF9pO1xuICAgIGFjWzBdID0gczAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MCk7XG4gICAgX2ogPSBzMSArIF9pO1xuICAgIGJ2aXJ0ID0gX2ogLSBzMTtcbiAgICBfMCA9IHMxIC0gKF9qIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIF9pID0gXzAgLSB0MTtcbiAgICBidmlydCA9IF8wIC0gX2k7XG4gICAgYWNbMV0gPSBfMCAtIChfaSArIGJ2aXJ0KSArIChidmlydCAtIHQxKTtcbiAgICBhYzMgPSBfaiArIF9pO1xuICAgIGJ2aXJ0ID0gYWMzIC0gX2o7XG4gICAgYWNbMl0gPSBfaiAtIChhYzMgLSBidmlydCkgKyAoX2kgLSBidmlydCk7XG4gICAgYWNbM10gPSBhYzM7XG4gICAgczEgPSBiZXggKiBkZXk7XG4gICAgYyA9IHNwbGl0dGVyICogYmV4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGJleCk7XG4gICAgYWxvID0gYmV4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGRleTtcbiAgICBiaGkgPSBjIC0gKGMgLSBkZXkpO1xuICAgIGJsbyA9IGRleSAtIGJoaTtcbiAgICBzMCA9IGFsbyAqIGJsbyAtIChzMSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgdDEgPSBkZXggKiBiZXk7XG4gICAgYyA9IHNwbGl0dGVyICogZGV4O1xuICAgIGFoaSA9IGMgLSAoYyAtIGRleCk7XG4gICAgYWxvID0gZGV4IC0gYWhpO1xuICAgIGMgPSBzcGxpdHRlciAqIGJleTtcbiAgICBiaGkgPSBjIC0gKGMgLSBiZXkpO1xuICAgIGJsbyA9IGJleSAtIGJoaTtcbiAgICB0MCA9IGFsbyAqIGJsbyAtICh0MSAtIGFoaSAqIGJoaSAtIGFsbyAqIGJoaSAtIGFoaSAqIGJsbyk7XG4gICAgX2kgPSBzMCAtIHQwO1xuICAgIGJ2aXJ0ID0gczAgLSBfaTtcbiAgICBiZFswXSA9IHMwIC0gKF9pICsgYnZpcnQpICsgKGJ2aXJ0IC0gdDApO1xuICAgIF9qID0gczEgKyBfaTtcbiAgICBidmlydCA9IF9qIC0gczE7XG4gICAgXzAgPSBzMSAtIChfaiAtIGJ2aXJ0KSArIChfaSAtIGJ2aXJ0KTtcbiAgICBfaSA9IF8wIC0gdDE7XG4gICAgYnZpcnQgPSBfMCAtIF9pO1xuICAgIGJkWzFdID0gXzAgLSAoX2kgKyBidmlydCkgKyAoYnZpcnQgLSB0MSk7XG4gICAgYmQzID0gX2ogKyBfaTtcbiAgICBidmlydCA9IGJkMyAtIF9qO1xuICAgIGJkWzJdID0gX2ogLSAoYmQzIC0gYnZpcnQpICsgKF9pIC0gYnZpcnQpO1xuICAgIGJkWzNdID0gYmQzO1xuXG4gICAgY29uc3QgZmlubGVuID0gc3VtKFxuICAgICAgICBzdW0oXG4gICAgICAgICAgICBuZWdhdGUobGlmdGFkYXB0KGJjLCBjZCwgYmQsIGRleiwgYmV6LCAtY2V6LCBhZXgsIGFleSwgYWV6LCBhZGV0KSwgYWRldCksIGFkZXQsXG4gICAgICAgICAgICBsaWZ0YWRhcHQoY2QsIGRhLCBhYywgYWV6LCBjZXosIGRleiwgYmV4LCBiZXksIGJleiwgYmRldCksIGJkZXQsIGFiZGV0KSwgYWJkZXQsXG4gICAgICAgIHN1bShcbiAgICAgICAgICAgIG5lZ2F0ZShsaWZ0YWRhcHQoZGEsIGFiLCBiZCwgYmV6LCBkZXosIGFleiwgY2V4LCBjZXksIGNleiwgY2RldCksIGNkZXQpLCBjZGV0LFxuICAgICAgICAgICAgbGlmdGFkYXB0KGFiLCBiYywgYWMsIGNleiwgYWV6LCAtYmV6LCBkZXgsIGRleSwgZGV6LCBkZGV0KSwgZGRldCwgY2RkZXQpLCBjZGRldCwgZmluKTtcblxuICAgIGxldCBkZXQgPSBlc3RpbWF0ZShmaW5sZW4sIGZpbik7XG4gICAgbGV0IGVycmJvdW5kID0gaXNwZXJyYm91bmRCICogcGVybWFuZW50O1xuICAgIGlmIChkZXQgPj0gZXJyYm91bmQgfHwgLWRldCA+PSBlcnJib3VuZCkge1xuICAgICAgICByZXR1cm4gZGV0O1xuICAgIH1cblxuICAgIGJ2aXJ0ID0gYXggLSBhZXg7XG4gICAgYWV4dGFpbCA9IGF4IC0gKGFleCArIGJ2aXJ0KSArIChidmlydCAtIGV4KTtcbiAgICBidmlydCA9IGF5IC0gYWV5O1xuICAgIGFleXRhaWwgPSBheSAtIChhZXkgKyBidmlydCkgKyAoYnZpcnQgLSBleSk7XG4gICAgYnZpcnQgPSBheiAtIGFlejtcbiAgICBhZXp0YWlsID0gYXogLSAoYWV6ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZXopO1xuICAgIGJ2aXJ0ID0gYnggLSBiZXg7XG4gICAgYmV4dGFpbCA9IGJ4IC0gKGJleCArIGJ2aXJ0KSArIChidmlydCAtIGV4KTtcbiAgICBidmlydCA9IGJ5IC0gYmV5O1xuICAgIGJleXRhaWwgPSBieSAtIChiZXkgKyBidmlydCkgKyAoYnZpcnQgLSBleSk7XG4gICAgYnZpcnQgPSBieiAtIGJlejtcbiAgICBiZXp0YWlsID0gYnogLSAoYmV6ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZXopO1xuICAgIGJ2aXJ0ID0gY3ggLSBjZXg7XG4gICAgY2V4dGFpbCA9IGN4IC0gKGNleCArIGJ2aXJ0KSArIChidmlydCAtIGV4KTtcbiAgICBidmlydCA9IGN5IC0gY2V5O1xuICAgIGNleXRhaWwgPSBjeSAtIChjZXkgKyBidmlydCkgKyAoYnZpcnQgLSBleSk7XG4gICAgYnZpcnQgPSBjeiAtIGNlejtcbiAgICBjZXp0YWlsID0gY3ogLSAoY2V6ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZXopO1xuICAgIGJ2aXJ0ID0gZHggLSBkZXg7XG4gICAgZGV4dGFpbCA9IGR4IC0gKGRleCArIGJ2aXJ0KSArIChidmlydCAtIGV4KTtcbiAgICBidmlydCA9IGR5IC0gZGV5O1xuICAgIGRleXRhaWwgPSBkeSAtIChkZXkgKyBidmlydCkgKyAoYnZpcnQgLSBleSk7XG4gICAgYnZpcnQgPSBkeiAtIGRlejtcbiAgICBkZXp0YWlsID0gZHogLSAoZGV6ICsgYnZpcnQpICsgKGJ2aXJ0IC0gZXopO1xuICAgIGlmIChhZXh0YWlsID09PSAwICYmIGFleXRhaWwgPT09IDAgJiYgYWV6dGFpbCA9PT0gMCAmJlxuICAgICAgICBiZXh0YWlsID09PSAwICYmIGJleXRhaWwgPT09IDAgJiYgYmV6dGFpbCA9PT0gMCAmJlxuICAgICAgICBjZXh0YWlsID09PSAwICYmIGNleXRhaWwgPT09IDAgJiYgY2V6dGFpbCA9PT0gMCAmJlxuICAgICAgICBkZXh0YWlsID09PSAwICYmIGRleXRhaWwgPT09IDAgJiYgZGV6dGFpbCA9PT0gMCkge1xuICAgICAgICByZXR1cm4gZGV0O1xuICAgIH1cblxuICAgIGVycmJvdW5kID0gaXNwZXJyYm91bmRDICogcGVybWFuZW50ICsgcmVzdWx0ZXJyYm91bmQgKiBNYXRoLmFicyhkZXQpO1xuXG4gICAgY29uc3QgYWJlcHMgPSAoYWV4ICogYmV5dGFpbCArIGJleSAqIGFleHRhaWwpIC0gKGFleSAqIGJleHRhaWwgKyBiZXggKiBhZXl0YWlsKTtcbiAgICBjb25zdCBiY2VwcyA9IChiZXggKiBjZXl0YWlsICsgY2V5ICogYmV4dGFpbCkgLSAoYmV5ICogY2V4dGFpbCArIGNleCAqIGJleXRhaWwpO1xuICAgIGNvbnN0IGNkZXBzID0gKGNleCAqIGRleXRhaWwgKyBkZXkgKiBjZXh0YWlsKSAtIChjZXkgKiBkZXh0YWlsICsgZGV4ICogY2V5dGFpbCk7XG4gICAgY29uc3QgZGFlcHMgPSAoZGV4ICogYWV5dGFpbCArIGFleSAqIGRleHRhaWwpIC0gKGRleSAqIGFleHRhaWwgKyBhZXggKiBkZXl0YWlsKTtcbiAgICBjb25zdCBhY2VwcyA9IChhZXggKiBjZXl0YWlsICsgY2V5ICogYWV4dGFpbCkgLSAoYWV5ICogY2V4dGFpbCArIGNleCAqIGFleXRhaWwpO1xuICAgIGNvbnN0IGJkZXBzID0gKGJleCAqIGRleXRhaWwgKyBkZXkgKiBiZXh0YWlsKSAtIChiZXkgKiBkZXh0YWlsICsgZGV4ICogYmV5dGFpbCk7XG4gICAgZGV0ICs9XG4gICAgICAgICgoKGJleCAqIGJleCArIGJleSAqIGJleSArIGJleiAqIGJleikgKiAoKGNleiAqIGRhZXBzICsgZGV6ICogYWNlcHMgKyBhZXogKiBjZGVwcykgK1xuICAgICAgICAoY2V6dGFpbCAqIGRhMyArIGRlenRhaWwgKiBhYzMgKyBhZXp0YWlsICogY2QzKSkgKyAoZGV4ICogZGV4ICsgZGV5ICogZGV5ICsgZGV6ICogZGV6KSAqXG4gICAgICAgICgoYWV6ICogYmNlcHMgLSBiZXogKiBhY2VwcyArIGNleiAqIGFiZXBzKSArIChhZXp0YWlsICogYmMzIC0gYmV6dGFpbCAqIGFjMyArIGNlenRhaWwgKiBhYjMpKSkgLVxuICAgICAgICAoKGFleCAqIGFleCArIGFleSAqIGFleSArIGFleiAqIGFleikgKiAoKGJleiAqIGNkZXBzIC0gY2V6ICogYmRlcHMgKyBkZXogKiBiY2VwcykgK1xuICAgICAgICAoYmV6dGFpbCAqIGNkMyAtIGNlenRhaWwgKiBiZDMgKyBkZXp0YWlsICogYmMzKSkgKyAoY2V4ICogY2V4ICsgY2V5ICogY2V5ICsgY2V6ICogY2V6KSAqXG4gICAgICAgICgoZGV6ICogYWJlcHMgKyBhZXogKiBiZGVwcyArIGJleiAqIGRhZXBzKSArIChkZXp0YWlsICogYWIzICsgYWV6dGFpbCAqIGJkMyArIGJlenRhaWwgKiBkYTMpKSkpICtcbiAgICAgICAgMiAqICgoKGJleCAqIGJleHRhaWwgKyBiZXkgKiBiZXl0YWlsICsgYmV6ICogYmV6dGFpbCkgKiAoY2V6ICogZGEzICsgZGV6ICogYWMzICsgYWV6ICogY2QzKSArXG4gICAgICAgIChkZXggKiBkZXh0YWlsICsgZGV5ICogZGV5dGFpbCArIGRleiAqIGRlenRhaWwpICogKGFleiAqIGJjMyAtIGJleiAqIGFjMyArIGNleiAqIGFiMykpIC1cbiAgICAgICAgKChhZXggKiBhZXh0YWlsICsgYWV5ICogYWV5dGFpbCArIGFleiAqIGFlenRhaWwpICogKGJleiAqIGNkMyAtIGNleiAqIGJkMyArIGRleiAqIGJjMykgK1xuICAgICAgICAoY2V4ICogY2V4dGFpbCArIGNleSAqIGNleXRhaWwgKyBjZXogKiBjZXp0YWlsKSAqIChkZXogKiBhYjMgKyBhZXogKiBiZDMgKyBiZXogKiBkYTMpKSk7XG5cbiAgICBpZiAoZGV0ID49IGVycmJvdW5kIHx8IC1kZXQgPj0gZXJyYm91bmQpIHtcbiAgICAgICAgcmV0dXJuIGRldDtcbiAgICB9XG5cbiAgICByZXR1cm4gaW5zcGhlcmVleGFjdChheCwgYXksIGF6LCBieCwgYnksIGJ6LCBjeCwgY3ksIGN6LCBkeCwgZHksIGR6LCBleCwgZXksIGV6KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGluc3BoZXJlKGF4LCBheSwgYXosIGJ4LCBieSwgYnosIGN4LCBjeSwgY3osIGR4LCBkeSwgZHosIGV4LCBleSwgZXopIHtcbiAgICBjb25zdCBhZXggPSBheCAtIGV4O1xuICAgIGNvbnN0IGJleCA9IGJ4IC0gZXg7XG4gICAgY29uc3QgY2V4ID0gY3ggLSBleDtcbiAgICBjb25zdCBkZXggPSBkeCAtIGV4O1xuICAgIGNvbnN0IGFleSA9IGF5IC0gZXk7XG4gICAgY29uc3QgYmV5ID0gYnkgLSBleTtcbiAgICBjb25zdCBjZXkgPSBjeSAtIGV5O1xuICAgIGNvbnN0IGRleSA9IGR5IC0gZXk7XG4gICAgY29uc3QgYWV6ID0gYXogLSBlejtcbiAgICBjb25zdCBiZXogPSBieiAtIGV6O1xuICAgIGNvbnN0IGNleiA9IGN6IC0gZXo7XG4gICAgY29uc3QgZGV6ID0gZHogLSBlejtcblxuICAgIGNvbnN0IGFleGJleSA9IGFleCAqIGJleTtcbiAgICBjb25zdCBiZXhhZXkgPSBiZXggKiBhZXk7XG4gICAgY29uc3QgYWIgPSBhZXhiZXkgLSBiZXhhZXk7XG4gICAgY29uc3QgYmV4Y2V5ID0gYmV4ICogY2V5O1xuICAgIGNvbnN0IGNleGJleSA9IGNleCAqIGJleTtcbiAgICBjb25zdCBiYyA9IGJleGNleSAtIGNleGJleTtcbiAgICBjb25zdCBjZXhkZXkgPSBjZXggKiBkZXk7XG4gICAgY29uc3QgZGV4Y2V5ID0gZGV4ICogY2V5O1xuICAgIGNvbnN0IGNkID0gY2V4ZGV5IC0gZGV4Y2V5O1xuICAgIGNvbnN0IGRleGFleSA9IGRleCAqIGFleTtcbiAgICBjb25zdCBhZXhkZXkgPSBhZXggKiBkZXk7XG4gICAgY29uc3QgZGEgPSBkZXhhZXkgLSBhZXhkZXk7XG4gICAgY29uc3QgYWV4Y2V5ID0gYWV4ICogY2V5O1xuICAgIGNvbnN0IGNleGFleSA9IGNleCAqIGFleTtcbiAgICBjb25zdCBhYyA9IGFleGNleSAtIGNleGFleTtcbiAgICBjb25zdCBiZXhkZXkgPSBiZXggKiBkZXk7XG4gICAgY29uc3QgZGV4YmV5ID0gZGV4ICogYmV5O1xuICAgIGNvbnN0IGJkID0gYmV4ZGV5IC0gZGV4YmV5O1xuXG4gICAgY29uc3QgYWxpZnQgPSBhZXggKiBhZXggKyBhZXkgKiBhZXkgKyBhZXogKiBhZXo7XG4gICAgY29uc3QgYmxpZnQgPSBiZXggKiBiZXggKyBiZXkgKiBiZXkgKyBiZXogKiBiZXo7XG4gICAgY29uc3QgY2xpZnQgPSBjZXggKiBjZXggKyBjZXkgKiBjZXkgKyBjZXogKiBjZXo7XG4gICAgY29uc3QgZGxpZnQgPSBkZXggKiBkZXggKyBkZXkgKiBkZXkgKyBkZXogKiBkZXo7XG5cbiAgICBjb25zdCBkZXQgPVxuICAgICAgICAoY2xpZnQgKiAoZGV6ICogYWIgKyBhZXogKiBiZCArIGJleiAqIGRhKSAtIGRsaWZ0ICogKGFleiAqIGJjIC0gYmV6ICogYWMgKyBjZXogKiBhYikpICtcbiAgICAgICAgKGFsaWZ0ICogKGJleiAqIGNkIC0gY2V6ICogYmQgKyBkZXogKiBiYykgLSBibGlmdCAqIChjZXogKiBkYSArIGRleiAqIGFjICsgYWV6ICogY2QpKTtcblxuICAgIGNvbnN0IGFlenBsdXMgPSBNYXRoLmFicyhhZXopO1xuICAgIGNvbnN0IGJlenBsdXMgPSBNYXRoLmFicyhiZXopO1xuICAgIGNvbnN0IGNlenBsdXMgPSBNYXRoLmFicyhjZXopO1xuICAgIGNvbnN0IGRlenBsdXMgPSBNYXRoLmFicyhkZXopO1xuICAgIGNvbnN0IGFleGJleXBsdXMgPSBNYXRoLmFicyhhZXhiZXkpICsgTWF0aC5hYnMoYmV4YWV5KTtcbiAgICBjb25zdCBiZXhjZXlwbHVzID0gTWF0aC5hYnMoYmV4Y2V5KSArIE1hdGguYWJzKGNleGJleSk7XG4gICAgY29uc3QgY2V4ZGV5cGx1cyA9IE1hdGguYWJzKGNleGRleSkgKyBNYXRoLmFicyhkZXhjZXkpO1xuICAgIGNvbnN0IGRleGFleXBsdXMgPSBNYXRoLmFicyhkZXhhZXkpICsgTWF0aC5hYnMoYWV4ZGV5KTtcbiAgICBjb25zdCBhZXhjZXlwbHVzID0gTWF0aC5hYnMoYWV4Y2V5KSArIE1hdGguYWJzKGNleGFleSk7XG4gICAgY29uc3QgYmV4ZGV5cGx1cyA9IE1hdGguYWJzKGJleGRleSkgKyBNYXRoLmFicyhkZXhiZXkpO1xuICAgIGNvbnN0IHBlcm1hbmVudCA9XG4gICAgICAgIChjZXhkZXlwbHVzICogYmV6cGx1cyArIGJleGRleXBsdXMgKiBjZXpwbHVzICsgYmV4Y2V5cGx1cyAqIGRlenBsdXMpICogYWxpZnQgK1xuICAgICAgICAoZGV4YWV5cGx1cyAqIGNlenBsdXMgKyBhZXhjZXlwbHVzICogZGV6cGx1cyArIGNleGRleXBsdXMgKiBhZXpwbHVzKSAqIGJsaWZ0ICtcbiAgICAgICAgKGFleGJleXBsdXMgKiBkZXpwbHVzICsgYmV4ZGV5cGx1cyAqIGFlenBsdXMgKyBkZXhhZXlwbHVzICogYmV6cGx1cykgKiBjbGlmdCArXG4gICAgICAgIChiZXhjZXlwbHVzICogYWV6cGx1cyArIGFleGNleXBsdXMgKiBiZXpwbHVzICsgYWV4YmV5cGx1cyAqIGNlenBsdXMpICogZGxpZnQ7XG5cbiAgICBjb25zdCBlcnJib3VuZCA9IGlzcGVycmJvdW5kQSAqIHBlcm1hbmVudDtcbiAgICBpZiAoZGV0ID4gZXJyYm91bmQgfHwgLWRldCA+IGVycmJvdW5kKSB7XG4gICAgICAgIHJldHVybiBkZXQ7XG4gICAgfVxuICAgIHJldHVybiAtaW5zcGhlcmVhZGFwdChheCwgYXksIGF6LCBieCwgYnksIGJ6LCBjeCwgY3ksIGN6LCBkeCwgZHksIGR6LCBleCwgZXksIGV6LCBwZXJtYW5lbnQpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaW5zcGhlcmVmYXN0KHBheCwgcGF5LCBwYXosIHBieCwgcGJ5LCBwYnosIHBjeCwgcGN5LCBwY3osIHBkeCwgcGR5LCBwZHosIHBleCwgcGV5LCBwZXopIHtcbiAgICBjb25zdCBhZXggPSBwYXggLSBwZXg7XG4gICAgY29uc3QgYmV4ID0gcGJ4IC0gcGV4O1xuICAgIGNvbnN0IGNleCA9IHBjeCAtIHBleDtcbiAgICBjb25zdCBkZXggPSBwZHggLSBwZXg7XG4gICAgY29uc3QgYWV5ID0gcGF5IC0gcGV5O1xuICAgIGNvbnN0IGJleSA9IHBieSAtIHBleTtcbiAgICBjb25zdCBjZXkgPSBwY3kgLSBwZXk7XG4gICAgY29uc3QgZGV5ID0gcGR5IC0gcGV5O1xuICAgIGNvbnN0IGFleiA9IHBheiAtIHBlejtcbiAgICBjb25zdCBiZXogPSBwYnogLSBwZXo7XG4gICAgY29uc3QgY2V6ID0gcGN6IC0gcGV6O1xuICAgIGNvbnN0IGRleiA9IHBkeiAtIHBlejtcblxuICAgIGNvbnN0IGFiID0gYWV4ICogYmV5IC0gYmV4ICogYWV5O1xuICAgIGNvbnN0IGJjID0gYmV4ICogY2V5IC0gY2V4ICogYmV5O1xuICAgIGNvbnN0IGNkID0gY2V4ICogZGV5IC0gZGV4ICogY2V5O1xuICAgIGNvbnN0IGRhID0gZGV4ICogYWV5IC0gYWV4ICogZGV5O1xuICAgIGNvbnN0IGFjID0gYWV4ICogY2V5IC0gY2V4ICogYWV5O1xuICAgIGNvbnN0IGJkID0gYmV4ICogZGV5IC0gZGV4ICogYmV5O1xuXG4gICAgY29uc3QgYWJjID0gYWV6ICogYmMgLSBiZXogKiBhYyArIGNleiAqIGFiO1xuICAgIGNvbnN0IGJjZCA9IGJleiAqIGNkIC0gY2V6ICogYmQgKyBkZXogKiBiYztcbiAgICBjb25zdCBjZGEgPSBjZXogKiBkYSArIGRleiAqIGFjICsgYWV6ICogY2Q7XG4gICAgY29uc3QgZGFiID0gZGV6ICogYWIgKyBhZXogKiBiZCArIGJleiAqIGRhO1xuXG4gICAgY29uc3QgYWxpZnQgPSBhZXggKiBhZXggKyBhZXkgKiBhZXkgKyBhZXogKiBhZXo7XG4gICAgY29uc3QgYmxpZnQgPSBiZXggKiBiZXggKyBiZXkgKiBiZXkgKyBiZXogKiBiZXo7XG4gICAgY29uc3QgY2xpZnQgPSBjZXggKiBjZXggKyBjZXkgKiBjZXkgKyBjZXogKiBjZXo7XG4gICAgY29uc3QgZGxpZnQgPSBkZXggKiBkZXggKyBkZXkgKiBkZXkgKyBkZXogKiBkZXo7XG5cbiAgICByZXR1cm4gKGNsaWZ0ICogZGFiIC0gZGxpZnQgKiBhYmMpICsgKGFsaWZ0ICogYmNkIC0gYmxpZnQgKiBjZGEpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/insphere.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient2d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient2d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient2d: () => (/* binding */ orient2d),\n/* harmony export */   orient2dfast: () => (/* binding */ orient2dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst ccwerrboundA = (3 + 16 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundB = (2 + 12 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst ccwerrboundC = (9 + 64 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst B = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst C1 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst C2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\nconst D = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(16);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nfunction orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nfunction orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient2d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/orient3d.js":
/*!********************************************************!*\
  !*** ./node_modules/robust-predicates/esm/orient3d.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orient3d: () => (/* binding */ orient3d),\n/* harmony export */   orient3dfast: () => (/* binding */ orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/robust-predicates/esm/util.js\");\n\n\nconst o3derrboundA = (7 + 56 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundB = (3 + 28 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\nconst o3derrboundC = (26 + 288 * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon * _util_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\nconst bc = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ca = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ab = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst at_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_c = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bt_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_a = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst ct_b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\nconst bct = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst cat = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst abt = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst u = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(4);\n\nconst _8 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _8b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _16 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(8);\nconst _12 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(12);\n\nlet fin = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\nlet fin2 = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.vec)(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = _util_js__WEBPACK_IMPORTED_MODULE_0__.splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adz, _8), _8,\n            (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdz, _8b), _8b, _16), _16,\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdz, _8), _8, fin);\n\n    let det = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.estimate)(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + _util_js__WEBPACK_IMPORTED_MODULE_0__.resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adz, _16), _16);\n\n    const catlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdz, _16), _16);\n\n    const abtlen = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.sum)(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.scale)(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nfunction orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nfunction orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/orient3d.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/esm/util.js":
/*!****************************************************!*\
  !*** ./node_modules/robust-predicates/esm/util.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   estimate: () => (/* binding */ estimate),\n/* harmony export */   negate: () => (/* binding */ negate),\n/* harmony export */   resulterrbound: () => (/* binding */ resulterrbound),\n/* harmony export */   scale: () => (/* binding */ scale),\n/* harmony export */   splitter: () => (/* binding */ splitter),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   sum_three: () => (/* binding */ sum_three),\n/* harmony export */   vec: () => (/* binding */ vec)\n/* harmony export */ });\nconst epsilon = 1.1102230246251565e-16;\nconst splitter = 134217729;\nconst resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nfunction sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nfunction sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nfunction scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nfunction negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nfunction estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nfunction vec(n) {\n    return new Float64Array(n);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/esm/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/robust-predicates/index.js":
/*!*************************************************!*\
  !*** ./node_modules/robust-predicates/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   incircle: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incircle),\n/* harmony export */   incirclefast: () => (/* reexport safe */ _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__.incirclefast),\n/* harmony export */   insphere: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.insphere),\n/* harmony export */   inspherefast: () => (/* reexport safe */ _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__.inspherefast),\n/* harmony export */   orient2d: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2d),\n/* harmony export */   orient2dfast: () => (/* reexport safe */ _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__.orient2dfast),\n/* harmony export */   orient3d: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3d),\n/* harmony export */   orient3dfast: () => (/* reexport safe */ _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__.orient3dfast)\n/* harmony export */ });\n/* harmony import */ var _esm_orient2d_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./esm/orient2d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient2d.js\");\n/* harmony import */ var _esm_orient3d_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./esm/orient3d.js */ \"(ssr)/./node_modules/robust-predicates/esm/orient3d.js\");\n/* harmony import */ var _esm_incircle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./esm/incircle.js */ \"(ssr)/./node_modules/robust-predicates/esm/incircle.js\");\n/* harmony import */ var _esm_insphere_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./esm/insphere.js */ \"(ssr)/./node_modules/robust-predicates/esm/insphere.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcm9idXN0LXByZWRpY2F0ZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUN5RDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXHJvYnVzdC1wcmVkaWNhdGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7b3JpZW50MmQsIG9yaWVudDJkZmFzdH0gZnJvbSAnLi9lc20vb3JpZW50MmQuanMnO1xuZXhwb3J0IHtvcmllbnQzZCwgb3JpZW50M2RmYXN0fSBmcm9tICcuL2VzbS9vcmllbnQzZC5qcyc7XG5leHBvcnQge2luY2lyY2xlLCBpbmNpcmNsZWZhc3R9IGZyb20gJy4vZXNtL2luY2lyY2xlLmpzJztcbmV4cG9ydCB7aW5zcGhlcmUsIGluc3BoZXJlZmFzdH0gZnJvbSAnLi9lc20vaW5zcGhlcmUuanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/robust-predicates/index.js\n");

/***/ })

};
;