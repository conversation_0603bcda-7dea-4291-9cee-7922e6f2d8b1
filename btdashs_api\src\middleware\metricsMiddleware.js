// src/middleware/metricsMiddleware.js - Express middleware for metrics collection

const metricsCollector = require('../infrastructure/monitoring/MetricsCollector');
const logger = require('../../logger');

/**
 * Middleware to collect request metrics
 */
const requestMetricsMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  // Store start time on request object
  req.startTime = startTime;
  
  // Override res.end to capture response metrics
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // Record request metrics
    metricsCollector.recordRequest(req, res, responseTime);
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

/**
 * Error handling middleware that records error metrics
 */
const errorMetricsMiddleware = (error, req, res, next) => {
  // Record error metrics
  metricsCollector.recordError(
    error.constructor.name,
    error,
    {
      endpoint: `${req.method} ${req.path}`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.auth?.sub
    }
  );
  
  // Continue with error handling
  next(error);
};

/**
 * Database query metrics wrapper
 */
const wrapDatabaseQuery = (originalQuery) => {
  return async function(...args) {
    const startTime = Date.now();
    const queryString = typeof args[0] === 'string' ? args[0] : 'complex-query';
    
    try {
      const result = await originalQuery.apply(this, args);
      const duration = Date.now() - startTime;
      
      // Record successful query
      metricsCollector.recordDatabaseQuery(queryString, duration);
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Record failed query
      metricsCollector.recordDatabaseQuery(queryString, duration, error);
      
      throw error;
    }
  };
};

/**
 * Middleware to expose metrics endpoint
 */
const metricsEndpointMiddleware = (req, res) => {
  try {
    const metrics = metricsCollector.getMetrics();
    
    // Convert Maps to Objects for JSON serialization
    const serializedMetrics = {
      ...metrics,
      requests: {
        ...metrics.requests,
        byEndpoint: Object.fromEntries(metrics.requests.byEndpoint),
        byStatusCode: Object.fromEntries(metrics.requests.byStatusCode)
      },
      errors: {
        ...metrics.errors,
        byType: Object.fromEntries(metrics.errors.byType)
      }
    };
    
    res.json(serializedMetrics);
  } catch (error) {
    logger.error('Error retrieving metrics', { error: error.message });
    res.status(500).json({
      error: 'Failed to retrieve metrics',
      message: error.message
    });
  }
};

/**
 * Middleware to expose health metrics endpoint
 */
const healthMetricsMiddleware = (req, res) => {
  try {
    const healthMetrics = metricsCollector.getHealthMetrics();
    
    // Set appropriate status code based on health
    const statusCode = healthMetrics.status === 'healthy' ? 200 :
                      healthMetrics.status === 'degraded' ? 200 : 503;
    
    res.status(statusCode).json({
      status: healthMetrics.status,
      timestamp: new Date().toISOString(),
      ...healthMetrics
    });
  } catch (error) {
    logger.error('Error retrieving health metrics', { error: error.message });
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve health metrics',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Prometheus-style metrics endpoint
 */
const prometheusMetricsMiddleware = (req, res) => {
  try {
    const metrics = metricsCollector.getMetrics();
    
    // Generate Prometheus format
    const prometheusMetrics = [
      '# HELP http_requests_total Total number of HTTP requests',
      '# TYPE http_requests_total counter',
      `http_requests_total ${metrics.requests.total}`,
      '',
      '# HELP http_request_errors_total Total number of HTTP request errors',
      '# TYPE http_request_errors_total counter',
      `http_request_errors_total ${metrics.requests.errors}`,
      '',
      '# HELP http_request_duration_seconds HTTP request duration in seconds',
      '# TYPE http_request_duration_seconds histogram',
      `http_request_duration_seconds_sum ${metrics.requests.responseTimes.reduce((sum, r) => sum + r.duration, 0) / 1000}`,
      `http_request_duration_seconds_count ${metrics.requests.responseTimes.length}`,
      '',
      '# HELP process_memory_usage_bytes Process memory usage in bytes',
      '# TYPE process_memory_usage_bytes gauge',
      `process_memory_usage_bytes ${metrics.system.memory.heapUsed}`,
      '',
      '# HELP database_queries_total Total number of database queries',
      '# TYPE database_queries_total counter',
      `database_queries_total ${metrics.database.queries.total}`,
      '',
      '# HELP database_query_errors_total Total number of database query errors',
      '# TYPE database_query_errors_total counter',
      `database_query_errors_total ${metrics.database.queries.errors}`,
      '',
      '# HELP application_errors_total Total number of application errors',
      '# TYPE application_errors_total counter',
      `application_errors_total ${metrics.errors.total}`,
      ''
    ].join('\n');
    
    res.setHeader('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
    res.send(prometheusMetrics);
  } catch (error) {
    logger.error('Error generating Prometheus metrics', { error: error.message });
    res.status(500).send('# Error generating metrics\n');
  }
};

/**
 * Initialize metrics collection for database
 */
const initializeDatabaseMetrics = (knex) => {
  // Wrap knex query methods
  const originalQuery = knex.raw.bind(knex);
  knex.raw = wrapDatabaseQuery(originalQuery);
  
  // Monitor connection pool
  if (knex.client && knex.client.pool) {
    setInterval(() => {
      const pool = knex.client.pool;
      metricsCollector.metrics.database.connections = {
        active: pool.numUsed(),
        idle: pool.numFree(),
        total: pool.numUsed() + pool.numFree()
      };
    }, 30000); // Update every 30 seconds
  }
  
  logger.info('Database metrics collection initialized');
};

/**
 * Alert system for critical metrics
 */
const checkAlerts = () => {
  const metrics = metricsCollector.getHealthMetrics();
  
  // Check error rate
  const errorRate = parseFloat(metrics.requests.errorRate);
  if (errorRate > 10) {
    logger.error('ALERT: High error rate detected', {
      errorRate: metrics.requests.errorRate,
      totalRequests: metrics.requests.total
    });
  }
  
  // Check memory usage
  const memoryUsage = parseFloat(metrics.memory.usage);
  if (memoryUsage > 90) {
    logger.error('ALERT: High memory usage detected', {
      memoryUsage: metrics.memory.usage,
      heapUsage: metrics.memory.heap
    });
  }
  
  // Check database performance
  const avgDbDuration = parseFloat(metrics.database.avgDuration);
  if (avgDbDuration > 1000) {
    logger.error('ALERT: Slow database queries detected', {
      avgDuration: metrics.database.avgDuration,
      totalQueries: metrics.database.queries
    });
  }
};

// Run alert checks every 2 minutes
setInterval(checkAlerts, 120000);

module.exports = {
  requestMetricsMiddleware,
  errorMetricsMiddleware,
  metricsEndpointMiddleware,
  healthMetricsMiddleware,
  prometheusMetricsMiddleware,
  initializeDatabaseMetrics,
  wrapDatabaseQuery
};
