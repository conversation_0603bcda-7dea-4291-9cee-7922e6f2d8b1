// src/middleware/internal.js
const logger = require("../../logger");
const { sendForbidden } = require("../utils/responseWrapper");

module.exports = function checkInternalKey(req, res, next) {
	const incomingKey = req.headers["x-internal-api-key"];
	if (!incomingKey || incomingKey !== process.env.INTERNAL_API_KEY) {
		logger.warn("Forbidden: Invalid internal API key", {
			ip: req.ip,
			path: req.path,
			method: req.method,
			providedKey: incomingKey ? "PROVIDED" : "MISSING",
		});
		return sendForbidden(res, "Invalid or missing internal API key");
	}
	next();
};
