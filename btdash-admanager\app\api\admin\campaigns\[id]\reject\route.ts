// app/api/admin/campaigns/[id]/reject/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

export async function POST(req: Request, { params }: { params: { id: string } }) {
	try {
		const { token } = await auth0.getAccessToken();
		const { id } = await params;
		const body = await req.json();

		const response = await fetch(`${process.env.API_BASE_URL}/admin/campaigns/${id}/reject`, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		console.error("Campaign rejection error:", error);

		return NextResponse.json(
			{
				success: false,
				message: error.message || "Internal server error",
				error: error.code || "unknown_error",
			},
			{ status: 500 }
		);
	}
}
