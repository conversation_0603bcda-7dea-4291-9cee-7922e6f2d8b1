"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-contour";
exports.ids = ["vendor-chunks/d3-contour"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-contour/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring) {\n  var i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n  return area;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1jb250b3VyXFxzcmNcXGFyZWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocmluZykge1xuICB2YXIgaSA9IDAsIG4gPSByaW5nLmxlbmd0aCwgYXJlYSA9IHJpbmdbbiAtIDFdWzFdICogcmluZ1swXVswXSAtIHJpbmdbbiAtIDFdWzBdICogcmluZ1swXVsxXTtcbiAgd2hpbGUgKCsraSA8IG4pIGFyZWEgKz0gcmluZ1tpIC0gMV1bMV0gKiByaW5nW2ldWzBdIC0gcmluZ1tpIC0gMV1bMF0gKiByaW5nW2ldWzFdO1xuICByZXR1cm4gYXJlYTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/array.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/array.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\n\nvar slice = array.slice;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJyYXkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtY29udG91clxcc3JjXFxhcnJheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgYXJyYXkgPSBBcnJheS5wcm90b3R5cGU7XG5cbmV4cG9ydCB2YXIgc2xpY2UgPSBhcnJheS5zbGljZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/ascending.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-contour/src/ascending.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n  return a - b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXNjZW5kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtY29udG91clxcc3JjXFxhc2NlbmRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oYSwgYikge1xuICByZXR1cm4gYSAtIGI7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/constant.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/constant.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (x => () => x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29uc3RhbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLFlBQVksRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWNvbnRvdXJcXHNyY1xcY29uc3RhbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgeCA9PiAoKSA9PiB4O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring, hole) {\n  var i = -1, n = hole.length, c;\n  while (++i < n) if (c = ringContains(ring, hole[i])) return c;\n  return 0;\n}\n\nfunction ringContains(ring, point) {\n  var x = point[0], y = point[1], contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;\n  }\n  return contains;\n}\n\nfunction segmentContains(a, b, c) {\n  var i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\n\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\n\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29udGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSw4Q0FBOEMsT0FBTztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWNvbnRvdXJcXHNyY1xcY29udGFpbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocmluZywgaG9sZSkge1xuICB2YXIgaSA9IC0xLCBuID0gaG9sZS5sZW5ndGgsIGM7XG4gIHdoaWxlICgrK2kgPCBuKSBpZiAoYyA9IHJpbmdDb250YWlucyhyaW5nLCBob2xlW2ldKSkgcmV0dXJuIGM7XG4gIHJldHVybiAwO1xufVxuXG5mdW5jdGlvbiByaW5nQ29udGFpbnMocmluZywgcG9pbnQpIHtcbiAgdmFyIHggPSBwb2ludFswXSwgeSA9IHBvaW50WzFdLCBjb250YWlucyA9IC0xO1xuICBmb3IgKHZhciBpID0gMCwgbiA9IHJpbmcubGVuZ3RoLCBqID0gbiAtIDE7IGkgPCBuOyBqID0gaSsrKSB7XG4gICAgdmFyIHBpID0gcmluZ1tpXSwgeGkgPSBwaVswXSwgeWkgPSBwaVsxXSwgcGogPSByaW5nW2pdLCB4aiA9IHBqWzBdLCB5aiA9IHBqWzFdO1xuICAgIGlmIChzZWdtZW50Q29udGFpbnMocGksIHBqLCBwb2ludCkpIHJldHVybiAwO1xuICAgIGlmICgoKHlpID4geSkgIT09ICh5aiA+IHkpKSAmJiAoKHggPCAoeGogLSB4aSkgKiAoeSAtIHlpKSAvICh5aiAtIHlpKSArIHhpKSkpIGNvbnRhaW5zID0gLWNvbnRhaW5zO1xuICB9XG4gIHJldHVybiBjb250YWlucztcbn1cblxuZnVuY3Rpb24gc2VnbWVudENvbnRhaW5zKGEsIGIsIGMpIHtcbiAgdmFyIGk7IHJldHVybiBjb2xsaW5lYXIoYSwgYiwgYykgJiYgd2l0aGluKGFbaSA9ICsoYVswXSA9PT0gYlswXSldLCBjW2ldLCBiW2ldKTtcbn1cblxuZnVuY3Rpb24gY29sbGluZWFyKGEsIGIsIGMpIHtcbiAgcmV0dXJuIChiWzBdIC0gYVswXSkgKiAoY1sxXSAtIGFbMV0pID09PSAoY1swXSAtIGFbMF0pICogKGJbMV0gLSBhWzFdKTtcbn1cblxuZnVuY3Rpb24gd2l0aGluKHAsIHEsIHIpIHtcbiAgcmV0dXJuIHAgPD0gcSAmJiBxIDw9IHIgfHwgciA8PSBxICYmIHEgPD0gcDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contours.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contours.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-contour/src/ascending.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-contour/src/area.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-contour/src/contains.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-contour/src/noop.js\");\n\n\n\n\n\n\n\n\nvar cases = [\n  [],\n  [[[1.0, 1.5], [0.5, 1.0]]],\n  [[[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [0.5, 1.0]]],\n  [[[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 1.5], [0.5, 1.0]], [[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 0.5], [1.0, 1.5]]],\n  [[[1.0, 0.5], [0.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 0.5]]],\n  [[[1.0, 1.5], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.0, 0.5]], [[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.5, 1.0]]],\n  [[[1.0, 1.5], [1.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 1.5]]],\n  []\n];\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var dx = 1,\n      dy = 1,\n      threshold = d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n      smooth = smoothLinear;\n\n  function contours(values) {\n    var tz = threshold(values);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      const e = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, finite);\n      tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(...(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e[0], e[1], tz), tz);\n      while (tz[tz.length - 1] >= e[1]) tz.pop();\n      while (tz[1] < e[0]) tz.shift();\n    } else {\n      tz = tz.slice().sort(_ascending_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n    }\n\n    return tz.map(value => contour(values, value));\n  }\n\n  // Accumulate, smooth contour rings, assign holes to exterior rings.\n  // Based on https://github.com/mbostock/shapefile/blob/v0.6.2/shp/polygon.js\n  function contour(values, value) {\n    const v = value == null ? NaN : +value;\n    if (isNaN(v)) throw new Error(`invalid value: ${value}`);\n\n    var polygons = [],\n        holes = [];\n\n    isorings(values, v, function(ring) {\n      smooth(ring, values, v);\n      if ((0,_area_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ring) > 0) polygons.push([ring]);\n      else holes.push(ring);\n    });\n\n    holes.forEach(function(hole) {\n      for (var i = 0, n = polygons.length, polygon; i < n; ++i) {\n        if ((0,_contains_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((polygon = polygons[i])[0], hole) !== -1) {\n          polygon.push(hole);\n          return;\n        }\n      }\n    });\n\n    return {\n      type: \"MultiPolygon\",\n      value: value,\n      coordinates: polygons\n    };\n  }\n\n  // Marching squares with isolines stitched into rings.\n  // Based on https://github.com/topojson/topojson-client/blob/v3.0.0/src/stitch.js\n  function isorings(values, value, callback) {\n    var fragmentByStart = new Array,\n        fragmentByEnd = new Array,\n        x, y, t0, t1, t2, t3;\n\n    // Special case for the first row (y = -1, t2 = t3 = 0).\n    x = y = -1;\n    t1 = above(values[0], value);\n    cases[t1 << 1].forEach(stitch);\n    while (++x < dx - 1) {\n      t0 = t1, t1 = above(values[x + 1], value);\n      cases[t0 | t1 << 1].forEach(stitch);\n    }\n    cases[t1 << 0].forEach(stitch);\n\n    // General case for the intermediate rows.\n    while (++y < dy - 1) {\n      x = -1;\n      t1 = above(values[y * dx + dx], value);\n      t2 = above(values[y * dx], value);\n      cases[t1 << 1 | t2 << 2].forEach(stitch);\n      while (++x < dx - 1) {\n        t0 = t1, t1 = above(values[y * dx + dx + x + 1], value);\n        t3 = t2, t2 = above(values[y * dx + x + 1], value);\n        cases[t0 | t1 << 1 | t2 << 2 | t3 << 3].forEach(stitch);\n      }\n      cases[t1 | t2 << 3].forEach(stitch);\n    }\n\n    // Special case for the last row (y = dy - 1, t0 = t1 = 0).\n    x = -1;\n    t2 = values[y * dx] >= value;\n    cases[t2 << 2].forEach(stitch);\n    while (++x < dx - 1) {\n      t3 = t2, t2 = above(values[y * dx + x + 1], value);\n      cases[t2 << 2 | t3 << 3].forEach(stitch);\n    }\n    cases[t2 << 3].forEach(stitch);\n\n    function stitch(line) {\n      var start = [line[0][0] + x, line[0][1] + y],\n          end = [line[1][0] + x, line[1][1] + y],\n          startIndex = index(start),\n          endIndex = index(end),\n          f, g;\n      if (f = fragmentByEnd[startIndex]) {\n        if (g = fragmentByStart[endIndex]) {\n          delete fragmentByEnd[f.end];\n          delete fragmentByStart[g.start];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[f.start] = fragmentByEnd[g.end] = {start: f.start, end: g.end, ring: f.ring.concat(g.ring)};\n          }\n        } else {\n          delete fragmentByEnd[f.end];\n          f.ring.push(end);\n          fragmentByEnd[f.end = endIndex] = f;\n        }\n      } else if (f = fragmentByStart[endIndex]) {\n        if (g = fragmentByEnd[startIndex]) {\n          delete fragmentByStart[f.start];\n          delete fragmentByEnd[g.end];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[g.start] = fragmentByEnd[f.end] = {start: g.start, end: f.end, ring: g.ring.concat(f.ring)};\n          }\n        } else {\n          delete fragmentByStart[f.start];\n          f.ring.unshift(start);\n          fragmentByStart[f.start = startIndex] = f;\n        }\n      } else {\n        fragmentByStart[startIndex] = fragmentByEnd[endIndex] = {start: startIndex, end: endIndex, ring: [start, end]};\n      }\n    }\n  }\n\n  function index(point) {\n    return point[0] * 2 + point[1] * (dx + 1) * 4;\n  }\n\n  function smoothLinear(ring, values, value) {\n    ring.forEach(function(point) {\n      var x = point[0],\n          y = point[1],\n          xt = x | 0,\n          yt = y | 0,\n          v1 = valid(values[yt * dx + xt]);\n      if (x > 0 && x < dx && xt === x) {\n        point[0] = smooth1(x, valid(values[yt * dx + xt - 1]), v1, value);\n      }\n      if (y > 0 && y < dy && yt === y) {\n        point[1] = smooth1(y, valid(values[(yt - 1) * dx + xt]), v1, value);\n      }\n    });\n  }\n\n  contours.contour = contour;\n\n  contours.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = Math.floor(_[0]), _1 = Math.floor(_[1]);\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, contours;\n  };\n\n  contours.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_8__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_), contours) : threshold;\n  };\n\n  contours.smooth = function(_) {\n    return arguments.length ? (smooth = _ ? smoothLinear : _noop_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"], contours) : smooth === smoothLinear;\n  };\n\n  return contours;\n}\n\n// When computing the extent, ignore infinite values (as well as invalid ones).\nfunction finite(x) {\n  return isFinite(x) ? x : NaN;\n}\n\n// Is the (possibly invalid) x greater than or equal to the (known valid) value?\n// Treat any invalid value as below negative infinity.\nfunction above(x, value) {\n  return x == null ? false : +x >= value;\n}\n\n// During smoothing, treat any invalid value as negative infinity.\nfunction valid(v) {\n  return v == null || isNaN(v = +v) ? -Infinity : v;\n}\n\nfunction smooth1(x, v0, v1, value) {\n  const a = value - v0;\n  const b = v1 - v0;\n  const d = isFinite(a) || isFinite(b) ? a / b : Math.sign(a) / Math.sign(b);\n  return isNaN(d) ? x : x + d - 0.5;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contours.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/density.js":
/*!************************************************!*\
  !*** ./node_modules/d3-contour/src/density.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n\n\n\n\n\nfunction defaultX(d) {\n  return d[0];\n}\n\nfunction defaultY(d) {\n  return d[1];\n}\n\nfunction defaultWeight() {\n  return 1;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var x = defaultX,\n      y = defaultY,\n      weight = defaultWeight,\n      dx = 960,\n      dy = 500,\n      r = 20, // blur radius\n      k = 2, // log2(grid cell size)\n      o = r * 3, // grid offset, to pad for blur\n      n = (dx + o * 2) >> k, // grid width\n      m = (dy + o * 2) >> k, // grid height\n      threshold = (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(20);\n\n  function grid(data) {\n    var values = new Float32Array(n * m),\n        pow2k = Math.pow(2, -k),\n        i = -1;\n\n    for (const d of data) {\n      var xi = (x(d, ++i, data) + o) * pow2k,\n          yi = (y(d, i, data) + o) * pow2k,\n          wi = +weight(d, i, data);\n      if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n        var x0 = Math.floor(xi),\n            y0 = Math.floor(yi),\n            xt = xi - x0 - 0.5,\n            yt = yi - y0 - 0.5;\n        values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n        values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n        values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n        values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n      }\n    }\n\n    (0,d3_array__WEBPACK_IMPORTED_MODULE_1__.blur2)({data: values, width: n, height: m}, r * pow2k);\n    return values;\n  }\n\n  function density(data) {\n    var values = grid(data),\n        tz = threshold(values),\n        pow4k = Math.pow(2, 2 * k);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Number.MIN_VALUE, (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k, tz);\n    }\n\n    return (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n        .size([n, m])\n        .thresholds(tz.map(d => d * pow4k))\n      (values)\n        .map((c, i) => (c.value = +tz[i], transform(c)));\n  }\n\n  density.contours = function(data) {\n    var values = grid(data),\n        contours = (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([n, m]),\n        pow4k = Math.pow(2, 2 * k),\n        contour = value => {\n          value = +value;\n          var c = transform(contours.contour(values, value * pow4k));\n          c.value = value; // preserve exact threshold value\n          return c;\n        };\n    Object.defineProperty(contour, \"max\", {get: () => (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k});\n    return contour;\n  };\n\n  function transform(geometry) {\n    geometry.coordinates.forEach(transformPolygon);\n    return geometry;\n  }\n\n  function transformPolygon(coordinates) {\n    coordinates.forEach(transformRing);\n  }\n\n  function transformRing(coordinates) {\n    coordinates.forEach(transformPoint);\n  }\n\n  // TODO Optimize.\n  function transformPoint(coordinates) {\n    coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n    coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n  }\n\n  function resize() {\n    o = r * 3;\n    n = (dx + o * 2) >> k;\n    m = (dy + o * 2) >> k;\n    return density;\n  }\n\n  density.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : x;\n  };\n\n  density.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : y;\n  };\n\n  density.weight = function(_) {\n    return arguments.length ? (weight = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : weight;\n  };\n\n  density.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = +_[0], _1 = +_[1];\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, resize();\n  };\n\n  density.cellSize = function(_) {\n    if (!arguments.length) return 1 << k;\n    if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n    return k = Math.floor(Math.log(_) / Math.LN2), resize();\n  };\n\n  density.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_5__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_), density) : threshold;\n  };\n\n  density.bandwidth = function(_) {\n    if (!arguments.length) return Math.sqrt(r * (r + 1));\n    if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n    return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n  };\n\n  return density;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvZGVuc2l0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTJDO0FBQ1Y7QUFDSTtBQUNBOztBQUVyQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBZSxzQ0FBVztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3REFBUTs7QUFFMUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLElBQUksK0NBQUssRUFBRSxrQ0FBa0M7QUFDN0M7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxvREFBSyxtQkFBbUIsb0RBQUc7QUFDdEM7O0FBRUEsV0FBVyx3REFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxtQkFBbUIsd0RBQVE7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBLDJDQUEyQyxXQUFXLG9EQUFHLGlCQUFpQjtBQUMxRTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxpRUFBaUUsd0RBQVE7QUFDekU7O0FBRUE7QUFDQSxpRUFBaUUsd0RBQVE7QUFDekU7O0FBRUE7QUFDQSxzRUFBc0Usd0RBQVE7QUFDOUU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw0RkFBNEYsd0RBQVEsQ0FBQyw0Q0FBSyxZQUFZLHdEQUFRO0FBQzlIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWNvbnRvdXJcXHNyY1xcZGVuc2l0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2JsdXIyLCBtYXgsIHRpY2tzfSBmcm9tIFwiZDMtYXJyYXlcIjtcbmltcG9ydCB7c2xpY2V9IGZyb20gXCIuL2FycmF5LmpzXCI7XG5pbXBvcnQgY29uc3RhbnQgZnJvbSBcIi4vY29uc3RhbnQuanNcIjtcbmltcG9ydCBDb250b3VycyBmcm9tIFwiLi9jb250b3Vycy5qc1wiO1xuXG5mdW5jdGlvbiBkZWZhdWx0WChkKSB7XG4gIHJldHVybiBkWzBdO1xufVxuXG5mdW5jdGlvbiBkZWZhdWx0WShkKSB7XG4gIHJldHVybiBkWzFdO1xufVxuXG5mdW5jdGlvbiBkZWZhdWx0V2VpZ2h0KCkge1xuICByZXR1cm4gMTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHZhciB4ID0gZGVmYXVsdFgsXG4gICAgICB5ID0gZGVmYXVsdFksXG4gICAgICB3ZWlnaHQgPSBkZWZhdWx0V2VpZ2h0LFxuICAgICAgZHggPSA5NjAsXG4gICAgICBkeSA9IDUwMCxcbiAgICAgIHIgPSAyMCwgLy8gYmx1ciByYWRpdXNcbiAgICAgIGsgPSAyLCAvLyBsb2cyKGdyaWQgY2VsbCBzaXplKVxuICAgICAgbyA9IHIgKiAzLCAvLyBncmlkIG9mZnNldCwgdG8gcGFkIGZvciBibHVyXG4gICAgICBuID0gKGR4ICsgbyAqIDIpID4+IGssIC8vIGdyaWQgd2lkdGhcbiAgICAgIG0gPSAoZHkgKyBvICogMikgPj4gaywgLy8gZ3JpZCBoZWlnaHRcbiAgICAgIHRocmVzaG9sZCA9IGNvbnN0YW50KDIwKTtcblxuICBmdW5jdGlvbiBncmlkKGRhdGEpIHtcbiAgICB2YXIgdmFsdWVzID0gbmV3IEZsb2F0MzJBcnJheShuICogbSksXG4gICAgICAgIHBvdzJrID0gTWF0aC5wb3coMiwgLWspLFxuICAgICAgICBpID0gLTE7XG5cbiAgICBmb3IgKGNvbnN0IGQgb2YgZGF0YSkge1xuICAgICAgdmFyIHhpID0gKHgoZCwgKytpLCBkYXRhKSArIG8pICogcG93MmssXG4gICAgICAgICAgeWkgPSAoeShkLCBpLCBkYXRhKSArIG8pICogcG93MmssXG4gICAgICAgICAgd2kgPSArd2VpZ2h0KGQsIGksIGRhdGEpO1xuICAgICAgaWYgKHdpICYmIHhpID49IDAgJiYgeGkgPCBuICYmIHlpID49IDAgJiYgeWkgPCBtKSB7XG4gICAgICAgIHZhciB4MCA9IE1hdGguZmxvb3IoeGkpLFxuICAgICAgICAgICAgeTAgPSBNYXRoLmZsb29yKHlpKSxcbiAgICAgICAgICAgIHh0ID0geGkgLSB4MCAtIDAuNSxcbiAgICAgICAgICAgIHl0ID0geWkgLSB5MCAtIDAuNTtcbiAgICAgICAgdmFsdWVzW3gwICsgeTAgKiBuXSArPSAoMSAtIHh0KSAqICgxIC0geXQpICogd2k7XG4gICAgICAgIHZhbHVlc1t4MCArIDEgKyB5MCAqIG5dICs9IHh0ICogKDEgLSB5dCkgKiB3aTtcbiAgICAgICAgdmFsdWVzW3gwICsgMSArICh5MCArIDEpICogbl0gKz0geHQgKiB5dCAqIHdpO1xuICAgICAgICB2YWx1ZXNbeDAgKyAoeTAgKyAxKSAqIG5dICs9ICgxIC0geHQpICogeXQgKiB3aTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBibHVyMih7ZGF0YTogdmFsdWVzLCB3aWR0aDogbiwgaGVpZ2h0OiBtfSwgciAqIHBvdzJrKTtcbiAgICByZXR1cm4gdmFsdWVzO1xuICB9XG5cbiAgZnVuY3Rpb24gZGVuc2l0eShkYXRhKSB7XG4gICAgdmFyIHZhbHVlcyA9IGdyaWQoZGF0YSksXG4gICAgICAgIHR6ID0gdGhyZXNob2xkKHZhbHVlcyksXG4gICAgICAgIHBvdzRrID0gTWF0aC5wb3coMiwgMiAqIGspO1xuXG4gICAgLy8gQ29udmVydCBudW1iZXIgb2YgdGhyZXNob2xkcyBpbnRvIHVuaWZvcm0gdGhyZXNob2xkcy5cbiAgICBpZiAoIUFycmF5LmlzQXJyYXkodHopKSB7XG4gICAgICB0eiA9IHRpY2tzKE51bWJlci5NSU5fVkFMVUUsIG1heCh2YWx1ZXMpIC8gcG93NGssIHR6KTtcbiAgICB9XG5cbiAgICByZXR1cm4gQ29udG91cnMoKVxuICAgICAgICAuc2l6ZShbbiwgbV0pXG4gICAgICAgIC50aHJlc2hvbGRzKHR6Lm1hcChkID0+IGQgKiBwb3c0aykpXG4gICAgICAodmFsdWVzKVxuICAgICAgICAubWFwKChjLCBpKSA9PiAoYy52YWx1ZSA9ICt0eltpXSwgdHJhbnNmb3JtKGMpKSk7XG4gIH1cblxuICBkZW5zaXR5LmNvbnRvdXJzID0gZnVuY3Rpb24oZGF0YSkge1xuICAgIHZhciB2YWx1ZXMgPSBncmlkKGRhdGEpLFxuICAgICAgICBjb250b3VycyA9IENvbnRvdXJzKCkuc2l6ZShbbiwgbV0pLFxuICAgICAgICBwb3c0ayA9IE1hdGgucG93KDIsIDIgKiBrKSxcbiAgICAgICAgY29udG91ciA9IHZhbHVlID0+IHtcbiAgICAgICAgICB2YWx1ZSA9ICt2YWx1ZTtcbiAgICAgICAgICB2YXIgYyA9IHRyYW5zZm9ybShjb250b3Vycy5jb250b3VyKHZhbHVlcywgdmFsdWUgKiBwb3c0aykpO1xuICAgICAgICAgIGMudmFsdWUgPSB2YWx1ZTsgLy8gcHJlc2VydmUgZXhhY3QgdGhyZXNob2xkIHZhbHVlXG4gICAgICAgICAgcmV0dXJuIGM7XG4gICAgICAgIH07XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGNvbnRvdXIsIFwibWF4XCIsIHtnZXQ6ICgpID0+IG1heCh2YWx1ZXMpIC8gcG93NGt9KTtcbiAgICByZXR1cm4gY29udG91cjtcbiAgfTtcblxuICBmdW5jdGlvbiB0cmFuc2Zvcm0oZ2VvbWV0cnkpIHtcbiAgICBnZW9tZXRyeS5jb29yZGluYXRlcy5mb3JFYWNoKHRyYW5zZm9ybVBvbHlnb24pO1xuICAgIHJldHVybiBnZW9tZXRyeTtcbiAgfVxuXG4gIGZ1bmN0aW9uIHRyYW5zZm9ybVBvbHlnb24oY29vcmRpbmF0ZXMpIHtcbiAgICBjb29yZGluYXRlcy5mb3JFYWNoKHRyYW5zZm9ybVJpbmcpO1xuICB9XG5cbiAgZnVuY3Rpb24gdHJhbnNmb3JtUmluZyhjb29yZGluYXRlcykge1xuICAgIGNvb3JkaW5hdGVzLmZvckVhY2godHJhbnNmb3JtUG9pbnQpO1xuICB9XG5cbiAgLy8gVE9ETyBPcHRpbWl6ZS5cbiAgZnVuY3Rpb24gdHJhbnNmb3JtUG9pbnQoY29vcmRpbmF0ZXMpIHtcbiAgICBjb29yZGluYXRlc1swXSA9IGNvb3JkaW5hdGVzWzBdICogTWF0aC5wb3coMiwgaykgLSBvO1xuICAgIGNvb3JkaW5hdGVzWzFdID0gY29vcmRpbmF0ZXNbMV0gKiBNYXRoLnBvdygyLCBrKSAtIG87XG4gIH1cblxuICBmdW5jdGlvbiByZXNpemUoKSB7XG4gICAgbyA9IHIgKiAzO1xuICAgIG4gPSAoZHggKyBvICogMikgPj4gaztcbiAgICBtID0gKGR5ICsgbyAqIDIpID4+IGs7XG4gICAgcmV0dXJuIGRlbnNpdHk7XG4gIH1cblxuICBkZW5zaXR5LnggPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAoeCA9IHR5cGVvZiBfID09PSBcImZ1bmN0aW9uXCIgPyBfIDogY29uc3RhbnQoK18pLCBkZW5zaXR5KSA6IHg7XG4gIH07XG5cbiAgZGVuc2l0eS55ID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHkgPSB0eXBlb2YgXyA9PT0gXCJmdW5jdGlvblwiID8gXyA6IGNvbnN0YW50KCtfKSwgZGVuc2l0eSkgOiB5O1xuICB9O1xuXG4gIGRlbnNpdHkud2VpZ2h0ID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHdlaWdodCA9IHR5cGVvZiBfID09PSBcImZ1bmN0aW9uXCIgPyBfIDogY29uc3RhbnQoK18pLCBkZW5zaXR5KSA6IHdlaWdodDtcbiAgfTtcblxuICBkZW5zaXR5LnNpemUgPSBmdW5jdGlvbihfKSB7XG4gICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSByZXR1cm4gW2R4LCBkeV07XG4gICAgdmFyIF8wID0gK19bMF0sIF8xID0gK19bMV07XG4gICAgaWYgKCEoXzAgPj0gMCAmJiBfMSA+PSAwKSkgdGhyb3cgbmV3IEVycm9yKFwiaW52YWxpZCBzaXplXCIpO1xuICAgIHJldHVybiBkeCA9IF8wLCBkeSA9IF8xLCByZXNpemUoKTtcbiAgfTtcblxuICBkZW5zaXR5LmNlbGxTaXplID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIDEgPDwgaztcbiAgICBpZiAoISgoXyA9ICtfKSA+PSAxKSkgdGhyb3cgbmV3IEVycm9yKFwiaW52YWxpZCBjZWxsIHNpemVcIik7XG4gICAgcmV0dXJuIGsgPSBNYXRoLmZsb29yKE1hdGgubG9nKF8pIC8gTWF0aC5MTjIpLCByZXNpemUoKTtcbiAgfTtcblxuICBkZW5zaXR5LnRocmVzaG9sZHMgPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodGhyZXNob2xkID0gdHlwZW9mIF8gPT09IFwiZnVuY3Rpb25cIiA/IF8gOiBBcnJheS5pc0FycmF5KF8pID8gY29uc3RhbnQoc2xpY2UuY2FsbChfKSkgOiBjb25zdGFudChfKSwgZGVuc2l0eSkgOiB0aHJlc2hvbGQ7XG4gIH07XG5cbiAgZGVuc2l0eS5iYW5kd2lkdGggPSBmdW5jdGlvbihfKSB7XG4gICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSByZXR1cm4gTWF0aC5zcXJ0KHIgKiAociArIDEpKTtcbiAgICBpZiAoISgoXyA9ICtfKSA+PSAwKSkgdGhyb3cgbmV3IEVycm9yKFwiaW52YWxpZCBiYW5kd2lkdGhcIik7XG4gICAgcmV0dXJuIHIgPSAoTWF0aC5zcXJ0KDQgKiBfICogXyArIDEpIC0gMSkgLyAyLCByZXNpemUoKTtcbiAgfTtcblxuICByZXR1cm4gZGVuc2l0eTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/density.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contourDensity: () => (/* reexport safe */ _density_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   contours: () => (/* reexport safe */ _contours_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n/* harmony import */ var _density_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./density.js */ \"(ssr)/./node_modules/d3-contour/src/density.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRDtBQUNLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtY29udG91clxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgY29udG91cnN9IGZyb20gXCIuL2NvbnRvdXJzLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgY29udG91ckRlbnNpdHl9IGZyb20gXCIuL2RlbnNpdHkuanNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/noop.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/noop.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvbm9vcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsc0NBQVciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1jb250b3VyXFxzcmNcXG5vb3AuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7fVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/noop.js\n");

/***/ })

};
;