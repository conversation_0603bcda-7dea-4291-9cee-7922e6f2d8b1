"use client";

import { NewsCard } from "@/components/news/news-cards";
import { NewsFilters } from "@/components/news/news-filters";
import { Button } from "@/components/ui/button";
import type { Category, News } from "@/lib/db/models";
import { Newspaper } from "lucide-react";
import { useState } from "react";

interface NewsClientWrapperProps {
	news: News[];
	categories: Category[];
}

export default function NewsClientWrapper({ news, categories }: NewsClientWrapperProps) {
	const [category, setCategory] = useState<string | null>(null);

	// Apply category filtering client-side
	const filteredNews = category ? news.filter((item) => item.category_ids?.includes(parseInt(category))) : news;

	const handleClearCategory = () => setCategory(null);

	if (!news || news.length === 0) {
		return (
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				<div className="p-8 text-center border rounded-lg">
					<Newspaper className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
					<p className="text-muted-foreground mb-4">No news articles found</p>
					{category && (
						<Button onClick={handleClearCategory} variant="outline">
							Clear Filter
						</Button>
					)}
				</div>
			</div>
		);
	}

	return (
		<div className="py-8 px-6 sm:px-8 lg:px-12">
			<div className="max-w-[1600px] mx-auto">
				<div className="mb-8">
					<h1 className="text-3xl font-bold mb-4">Bittensor News</h1>
					<p className="text-lg text-muted-foreground">
						Stay up-to-date with the latest developments, updates, and announcements from the Bittensor
						ecosystem.
					</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
					<div className="lg:col-span-3">
						<NewsFilters selectedCategory={category} onCategoryChange={setCategory} />

						{filteredNews.length === 0 ? (
							<div className="p-8 text-center border rounded-lg">
								<Newspaper className="h-12 w-12 text-muted-foreground mx-auto mb-4" />

								{
									<Button onClick={handleClearCategory} variant="outline">
										Clear Filter
									</Button>
								}
							</div>
						) : (
							<div className="space-y-6">
								{filteredNews.length > 0
									? filteredNews.map((article) => (
											<NewsCard key={article.id} article={article} categories={categories} />
									  ))
									: Array.from({ length: 3 }).map((_, index) => (
											<NewsCard key={index} isLoading={true} />
									  ))}
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
