"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import type { DailyMetric } from "@/lib/analytics"

interface LineChartProps {
  data: DailyMetric[]
  title: string
  description?: string
  metric: "impressions" | "clicks" | "ctr"
  height?: number
}

export function LineChart({ data, title, description, metric, height = 300 }: LineChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set dimensions
    const width = canvas.width
    const chartHeight = height - 40 // Leave space for labels

    // Find min and max values
    const values = data.map((d) => d[metric])
    const maxValue = Math.max(...values) * 1.1 // Add 10% padding
    const minValue = metric === "ctr" ? 0 : Math.min(...values) * 0.9 // Add padding for non-CTR metrics

    // Draw axes
    ctx.beginPath()
    ctx.strokeStyle = "#e2e8f0"
    ctx.moveTo(40, 20)
    ctx.lineTo(40, chartHeight)
    ctx.lineTo(width - 20, chartHeight)
    ctx.stroke()

    // Draw data points and line
    if (data.length > 1) {
      const xStep = (width - 60) / (data.length - 1)

      // Draw line
      ctx.beginPath()
      ctx.strokeStyle = "#3b82f6"
      ctx.lineWidth = 2

      data.forEach((point, i) => {
        const x = 40 + i * xStep
        const y = chartHeight - ((point[metric] - minValue) / (maxValue - minValue)) * (chartHeight - 40)

        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()

      // Draw points
      data.forEach((point, i) => {
        const x = 40 + i * xStep
        const y = chartHeight - ((point[metric] - minValue) / (maxValue - minValue)) * (chartHeight - 40)

        ctx.beginPath()
        ctx.fillStyle = "#3b82f6"
        ctx.arc(x, y, 4, 0, Math.PI * 2)
        ctx.fill()
      })

      // Draw x-axis labels (every 5th day)
      ctx.fillStyle = "#64748b"
      ctx.font = "10px sans-serif"
      ctx.textAlign = "center"

      data.forEach((point, i) => {
        if (i % 5 === 0 || i === data.length - 1) {
          const x = 40 + i * xStep
          const date = new Date(point.date)
          const label = `${date.getDate()}/${date.getMonth() + 1}`
          ctx.fillText(label, x, chartHeight + 15)
        }
      })

      // Draw y-axis labels
      ctx.textAlign = "right"
      ctx.textBaseline = "middle"

      const yStep = (chartHeight - 40) / 5
      for (let i = 0; i <= 5; i++) {
        const y = chartHeight - i * yStep
        const value = minValue + (i / 5) * (maxValue - minValue)
        let label = ""

        if (metric === "impressions" || metric === "clicks") {
          label = value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value.toFixed(0)
        } else if (metric === "ctr") {
          label = `${(value * 100).toFixed(1)}%`
        }

        ctx.fillText(label, 35, y)
      }
    }
  }, [data, metric, height])

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="w-full" style={{ height: `${height}px` }}>
          <canvas ref={canvasRef} width={800} height={height} style={{ width: "100%", height: `${height}px` }} />
        </div>
      </CardContent>
    </Card>
  )
}
