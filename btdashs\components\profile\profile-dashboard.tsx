"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
	Category,
	Company,
	Event,
	Job,
	Product,
	Skills,
	Subnet,
	User,
	UserEducation,
	UserExperience,
	UserPreferences,
	UserSkills,
} from "@/lib/db/models";

import Link from "next/link";
import { useEffect, useState } from "react";
import UserEventsClient from "../events/user-events";
import UserJobsClient from "../jobs/user-jobs";
import ProfileBasicInfoClient from "./profile-basic-info";
import ProfileEducationClient from "./profile-education";
import ProfileExperienceClient from "./profile-experience";
import ProfilePreferencesClient from "./profile-preferences";
import ProfileSkillsClient from "./profile-skills";

export default function ProfileDashboardClient({
	profile,
	skills,
	userSkills,
	preferences,
	educations,
	experiences,
	jobs,
	events,
	companies,
	categories,
	subnets,
	products,
	allEvents,
}: {
	profile: User;
	skills: Skills[];
	userSkills: UserSkills[];
	preferences: UserPreferences;
	educations: UserEducation[];
	experiences: UserExperience[];
	jobs: (Job & {
		company?: Company;
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	events: (Event & {
		companies?: Company[];
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	companies: Company[];
	categories: Category[];
	subnets: Subnet[];
	products: Product[];
	allEvents: Event[];
}) {
	// Filter tabs based on authorization
	const tabs = [
		{ value: "basic-info", label: "Basic Info" },
		{ value: "skills", label: "Skills" },
		{ value: "experience", label: "Experience" },
		{ value: "education", label: "Education" },
		{ value: "projects", label: "Projects" },
		{ value: "preferences", label: "Preferences" },
		...(profile?.authorized_job_admin ? [{ value: "jobs", label: "Jobs" }] : []),
		...(profile?.authorized_events_admin ? [{ value: "events", label: "Events" }] : []),
	];

	// State for active tab
	const [activeTab, setActiveTab] = useState("basic-info");

	// Handle hash changes and initial load
	useEffect(() => {
		const handleHashChange = () => {
			const hash = window.location.hash.substring(1);
			if (hash && tabs.some((tab) => tab.value === hash)) {
				setActiveTab(hash);
			}
		};

		// Check initial hash
		handleHashChange();

		// Add event listener for future hash changes
		window.addEventListener("hashchange", handleHashChange);

		return () => {
			window.removeEventListener("hashchange", handleHashChange);
		};
	}, []);

	// Update URL hash when tab changes
	const handleTabChange = (value: string) => {
		setActiveTab(value);
		window.location.hash = value;
	};

	return (
		<div className="bg-background rounded-lg shadow-md dark:border dark:border-border">
			<div className="flex justify-between items-center p-6">
				<h2 className="text-2xl font-semibold">
					{profile?.first_name} {profile?.last_name}
				</h2>
				<Link href="/auth/logout" prefetch={false}>
					<Button variant="destructive">Logout</Button>
				</Link>
			</div>

			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<div className="border-b dark:border-border">
					<TabsList>
						{tabs.map((tab) => (
							<TabsTrigger key={tab.value} value={tab.value}>
								{tab.label}
							</TabsTrigger>
						))}
					</TabsList>
				</div>

				<div className="p-6">
					<TabsContent value="basic-info">
						<ProfileBasicInfoClient initialProfile={profile} />
					</TabsContent>
					<TabsContent value="skills">
						<ProfileSkillsClient availableSkills={skills} userSkills={userSkills} />
					</TabsContent>
					<TabsContent value="experience">
						<ProfileExperienceClient initialExperience={experiences} />
					</TabsContent>
					<TabsContent value="education">
						<ProfileEducationClient initialEducation={educations} />
					</TabsContent>
					<TabsContent value="preferences">
						<ProfilePreferencesClient initialPreferences={preferences} />
					</TabsContent>
					{profile?.authorized_job_admin && (
						<TabsContent value="jobs">
							<UserJobsClient
								initialJobs={jobs}
								companies={companies}
								categories={categories}
								subnets={subnets}
								products={products}
							/>
						</TabsContent>
					)}
					{profile?.authorized_events_admin && (
						<TabsContent value="events">
							<UserEventsClient
								initialEvents={events}
								companies={companies}
								categories={categories}
								subnets={subnets}
								products={products}
								allEvents={allEvents}
							/>
						</TabsContent>
					)}
				</div>
			</Tabs>
		</div>
	);
}
