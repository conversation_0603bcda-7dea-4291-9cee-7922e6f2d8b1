"use client";

import { useState } from "react";
import { ArrowDownUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const tokens = [
  { id: "tao", name: "TA<PERSON>" },
  { id: "subnet1", name: "Subnet 1" },
  { id: "subnet2", name: "Subnet 2" },
  { id: "subnet3", name: "Subnet 3" },
];

export function QuickSwapWidget() {
  const [fromToken, setFromToken] = useState(tokens[0]);
  const [toToken, setToToken] = useState(tokens[1]);
  const [fromAmount, setFromAmount] = useState("");
  const [toAmount, setToAmount] = useState("");

  const handleSwap = () => {
    // In a real application, this would trigger the actual swap process
    //console.log(`Swapping ${fromAmount} ${fromToken.name} to ${toAmount} ${toToken.name}`)
  };

  const handleFromAmountChange = (value: string) => {
    setFromAmount(value);
    // Simulate exchange rate calculation
    setToAmount((Number.parseFloat(value) * 0.95).toFixed(2));
  };

  const switchTokens = () => {
    setFromToken(toToken);
    setToToken(fromToken);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Swap</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Select
              value={fromToken.id}
              onValueChange={(value) =>
                setFromToken(tokens.find((t) => t.id === value) || tokens[0])
              }
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="From" />
              </SelectTrigger>
              <SelectContent>
                {tokens.map((token) => (
                  <SelectItem key={token.id} value={token.id}>
                    {token.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.00"
              value={fromAmount}
              onChange={(e) => handleFromAmountChange(e.target.value)}
              className="flex-1"
            />
          </div>
        </div>

        <div className="flex justify-center">
          <Button variant="ghost" size="icon" onClick={switchTokens}>
            <ArrowDownUp className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-2">
          <div className="flex space-x-2">
            <Select
              value={toToken.id}
              onValueChange={(value) =>
                setToToken(tokens.find((t) => t.id === value) || tokens[1])
              }
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="To" />
              </SelectTrigger>
              <SelectContent>
                {tokens.map((token) => (
                  <SelectItem key={token.id} value={token.id}>
                    {token.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.00"
              value={toAmount}
              readOnly
              className="flex-1"
            />
          </div>
        </div>

        <Button className="w-full" onClick={handleSwap}>
          Swap
        </Button>
      </CardContent>
    </Card>
  );
}
