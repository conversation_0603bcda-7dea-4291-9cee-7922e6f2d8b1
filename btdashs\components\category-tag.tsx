import { cn } from "@/lib/utils";
import { getCategoryGradient } from "@/components/ui/category-gradients";

type CategoryTagProps = {
  category: string;
  className?: string;
};

export function CategoryTag({ category, className }: CategoryTagProps) {
  return (
    <span
      className={cn(
        "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r shadow-sm transition-all hover:shadow-md",
        getCategoryGradient(category),
        className
      )}
    >
      {category}
    </span>
  );
}
