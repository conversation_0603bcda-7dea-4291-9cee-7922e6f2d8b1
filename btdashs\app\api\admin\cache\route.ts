// app/api/admin/cache/route.ts
export const runtime = "nodejs";

import { auth0 } from "@/lib/auth0";
import { cacheManager } from "@/lib/cache/cache-manager";
import { NextResponse } from "next/server";

const NAMESPACE = process.env.AUTH0_CLAIM_NAMESPACE;

async function isUserAdmin(accessToken: string) {
	try {
		// Check admin status by calling the backend API
		const response = await fetch(`${process.env.API_BASE_URL}/admin/verify`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		// If the request succeeds, user is admin
		return response.ok;
	} catch {
		return false;
	}
}

export const GET = async function getCacheStats(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const isAdmin = accessToken && (await isUserAdmin(accessToken));
		if (!isAdmin) {
			return NextResponse.json({ error: "Not authorized" }, { status: 403 });
		}

		const stats = await cacheManager.getStats();
		return NextResponse.json(stats);
	} catch (error) {
		console.error("Cache stats error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
};

export const DELETE = async function clearCache(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const isAdmin = accessToken && (await isUserAdmin(accessToken));
		if (!isAdmin) {
			return NextResponse.json({ error: "Not authorized" }, { status: 403 });
		}

		const { searchParams } = new URL(request.url);
		const table = searchParams.get("table") || undefined;

		await cacheManager.clearCache(table);

		return NextResponse.json({
			success: true,
			message: `Cache cleared for ${table || "all tables"}`,
		});
	} catch (error) {
		console.error("Cache clearing error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
};
