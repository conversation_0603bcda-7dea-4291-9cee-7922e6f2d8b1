const axios = require("axios");
const dotenv = require("dotenv");

dotenv.config();

const TAOSTATS_API_BASE_URL = process.env.TAOSTATS_API_BASE_URL;
const TAOSTATS_API_KEY = process.env.TAOSTATS_API_KEY;

if (!TAOSTATS_API_BASE_URL || !TAOSTATS_API_BASE_URL.startsWith("http")) {
	throw new Error("TAOSTATS_API_BASE_URL is missing or invalid in .env");
}

if (!TAOSTATS_API_KEY) {
	throw new Error("TAOSTATS_API_KEY is missing in .env");
}

const defaultDelayMs = 60000; // 1 minute

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const fetchWithRetry = async (url, params = {}, retries = 5, delayMs = defaultDelayMs) => {
	for (let i = 0; i < retries; i++) {
		try {
			const response = await axios.get(url, {
				headers: {
					Accept: "application/json",
					Authorization: TAOSTATS_API_KEY,
				},
				params,
			});
			return response.data;
		} catch (error) {
			if (error.response?.status === 429) {
				console.warn(`Rate limit hit. Retrying in ${delayMs}ms...`);
				await delay(delayMs);
				delayMs *= 2; // Exponential backoff
			} else {
				throw error;
			}
		}
	}
	throw new Error(`Failed to fetch ${url} after ${retries} retries`);
};

exports.fetchAllPages = async (endpoint, params = {}) => {
	let results = [];
	let currentPage = 1;
	let totalPages = 1;

	while (currentPage <= totalPages) {
		try {
			console.log(`Fetching ${endpoint}/${JSON.stringify(params)}, page ${currentPage}...`);
			const response = await fetchWithRetry(`${TAOSTATS_API_BASE_URL}${endpoint}`, {
				...params,
				page: currentPage,
			});

			if (response && response.data.length > 0) {
				results = results.concat(response.data);

				// Update pagination details
				totalPages = response.pagination?.total_pages || totalPages;
				currentPage = response.pagination?.next_page || totalPages + 1;
			} else {
				break;
			}
		} catch (error) {
			console.error(`Error fetching pages for ${endpoint}:`, error);
			break;
		}
	}

	return results;
};

exports.callTaoStatsAPI = async (endpoint, params = {}) => {
	console.log(`Fetching ${endpoint}?${new URLSearchParams(params).toString()}...`);
	return fetchWithRetry(`${TAOSTATS_API_BASE_URL}${endpoint}`, params);
};

exports.getApiStatus = async () => {
	try {
		const response = await axios.get(`${TAOSTATS_API_BASE_URL}/api/status/v1`, {
			headers: {
				Accept: "application/json",
				Authorization: TAOSTATS_API_KEY,
			},
		});
		console.log("TaoStats API status:", response.data);
		return response.data;
	} catch (error) {
		console.error("Error fetching TaoStats API status:", error.response ? error.response.data : error.message);
		return { error: "Failed to fetch TaoStats API status" };
	}
};
