// app/api/user/me/route.ts

import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const GET = async function getUser(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		//console.log("Access Token:", accessToken);

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/me`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		const userData = await response.json();

		return NextResponse.json(userData);
	} catch (error) {
		return NextResponse.error();
	}
};

export const PUT = async function updateUser(request: Request) {
	try {
		const res = new NextResponse();
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const body = await request.json();

		const response = await fetch(`${process.env.API_BASE_URL}/user/me`, {
			method: "PUT",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});
		const userData = await response.json();

		return NextResponse.json(userData, res);
	} catch (error) {
		return NextResponse.error();
	}
};
