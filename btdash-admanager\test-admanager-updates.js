// test-admanager-updates.js - Test script to verify btdash-admanager API updates
const axios = require("axios");

const API_BASE_URL = "http://localhost:3001/api";
const APP_BASE_URL = "http://localhost:3000";

// Real JWT token for testing
const MOCK_JWT_TOKEN =
	"eyJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiZGlyIn0..jIP8L5KFs2wpQokL._R5jvrZ-BZimwEvLjNFLLXEanCYtDust4Ospb0mfvYtlUx5vOROJm4kLlQ0CyvZx8GVcj79XeQ5_BjWBiDqA8bAPVJvlc3zYiFsaEgp1jbPcgRG4rRMnDAm6WvT1ACk5fTtP11okjLyOAe4x4A32GMAlllxhtIarAV2nWGOLcBiC5dwsSK203h_Q8WnK71B2bK0NLtzAQcBkfFJ4lLKdF6Wd4sSVmXukJFko0FieQS_vXvm8QF4Umc7Tv_oiyD52yzjj58LvxF1k_tsIGpb5MwXVWpUxdeI6kBr4HWmY5T_oyNMaWl3GCDAlTCYgg3ZQZS2Bi5gcOjFzqJ8CZJ1JjjYjGf89c90qiJ3c6oD_VxUYIHuSTUV-N6JCeyut_VvpZOVeSZZGx9gqqa_HO121lo5jp-UaovuRlCYC5odWImnDsxLD1WDC6lTSz5CLyEkZvJGpkkm9LyhoESV16pdzhsYh9Ds-jwdUBVP9TOq8mn1rp5dHRyQVb8riBuRVso8NPzEZv5tWADriE3NJwDqcIkBDfUaYhpw3qhfdORxNCo7GiSsxzPLLrRpkS66WTmRtbm0-hR9Dbarv3Cpc2tkdeyc5z9VYfwDwnODfgMs8MS0jVzxmo3XeF1bgKEA5SSw5IHg7GBjKMICdEENnucKNAIB1uKDlhcWAXjFZ9EtfjQIxToCJ8RfFgUTri9E5rvONlNBKTyQyAvof--I7Ycg_uopFjk0l69u2aXhbkYb0sHZTDiwzlAgKEYTnzjDez3ySMNbopGLu4sX1MEJPU_71V-MS2UmdfLG1rKyIe5jzN6BHT-6oH5HKywYK6azmcwyRSmUJhfzPEh7bgsv2KV3ORNRd1xEx5G318t9BrCC0drD65ribnx3IhgyLwtnMRqj1Qz9L9SyS6Hw1MuBt3GD6LB8c9xYnPuhk8_cVHzSUjWZjPPdJ93FLkIpmKFvj5zAGZdTtWqwjqtdilF7IC6n6Glek48DbFNF0e7RNLgH3XTS72WlO9d7TcjIdQHmH3y59BVUVdk4jH3uxgaEsbs6K674mnONctzOZGM8HrtyoCintVqgTzMOX0hQ1PxlNb46ZipLQn9L6g1Zxlb3cyLEykd0nR5T9GyUFU8E6asEi96JLIw2EcGPZffg1d31VU4gXoIJSq1G5Mn9ZSZyr9jCFExNXUZolgDiBJDaddv_spHq-dLgsuK8joHLBDL0K84dJ30Yl3F9DK6CsQwTJSABfmbERCgaraEgfeKt2-YhWmeXX7ufAg7zc6IIxTWzXPn9O1dHTEoYIVxA9FSGsTXAToqDtbRvgjCVR5RzU6ovL_6Hc2nHEvf3uBhiDK571DPY8P9Zh7yEu_W3cTBGpNWGssy8o1Q7lJVyAXs8yzhOiiPCYOL9RMiuoG5WOGAPcqh3iHwd09j1JfR2bYwPN2Sdkb70w2Jpok0DBLtdhHFNL950gbMX4LCFBg32CT6gb-1t1l4G5zcm7O48Pu0ziBBmDY8wHWInjk5PyejP7NvLt5PzCDF42QB8bVipPK1cHMgnpyytfanJERd521wv7kVqEdO5t-0xN5mZFBCsuQxgoOPHVS6IF6pzdD8G-AAYnjIB5DMIuB384TnuDsYppuN-0KEaJDSup9UyIUJw-M5x7tOogIwjquqaFfAGdQcpnVhb-yrXD6xibipg8xPPbMEPjejxPxCuY41dQ-Q1Q3BmnisLiOqJF110n9f0o47svX4y9CObHaHJudqnko5PYFR8C9yoWFRfZPFfyoNScXJKqJrTyzGPMicdOd4W8b7T34z7grkJ4hhjl2_5OOmTyBhIDE_ulEh74EEUpHDqA9h8V9l-8bxiQ7dK_DpYOrgT9sl6PpitMNW4zgpe0VpJr9TM1axsQiBNGZNOnL8EuiI4lxO8sGajWEJBcquzj8x5kXePlpa9-7HNM2oXyDVivuEMxwHzToK7kEUetPj-e7xxKGsCkHrgtCtaHR5C9I8Ads3qnqMkn9bUqRNFwO2GMq6Pv12_T5xtGZcCWWtsp6_72v0kSqgstPnJMdeDlFlAznKj87nt6zAM25d8BUPBFrwJiExx4gq2X3PP74LXD-dmgIz8nhHQ1iam5kw4xR3rvyvn2hUpi1Mt99ziAPcVpfEhjP0uzd-ZtlhzcEPKIrYJ6hD_jcOwQCYPTqRN4rEVcmbmUX3O_STaKP1Nm2Ohz2W3fKJcrxdy39P6-Di7joqcU3PTI6PtupPSduGgn4AP3VDH2Vqzt0-XbsT7lvQ8ucClTXd-aFx7KLy0UF5KE2Kdj5u-AOGuVDsKozMdtH9x5PL_4xpaZJbc0yP9XuTRpQCED3CiWAEupIRsRtydSaRvQlboFGiiofSMHp57CDJsMwo0YhrnYecl74n0_3J-Tuq21wuqY2g0jq-LJ4-IJAFDf0frZv_OokVZuayKG1DEaeV_yuu64X0UxcZZ8tqarqqyhxqFt2qmVhE9Helg9sHlWw9jjsJsrL1bF0ULmFXRIBjS1q252K5eehFbTVu9spmJlS0YojSdUeL84-ee8xzauzLUnrRJWkXBZT4y9-AZXmD4lswV7qwQvC2xOrBUho7B4Xazp_DHBM7MgxhzYi0LX_2KAd4Ae65V1JQerPrC7GRBwwCjilXj2AP9wMNarOFrGQp_jWW9OWmEJTjfnqbywb7BkgMI7mJ6ZF59kmWL8TybaaDkzLOTOUD8-UayaNywBPVDfvTx3TgTVdIcTKvvyEVyEnmELfEDwxdn5LyYA99M8YCgwU1rbjMlkCXdnbkbzAdXsIPmlO9kQQKpTWt-wYN7mN7GWfU4PuSSSs-76iNKfWQPeYkCMOeMbOa8WP2pJH622BxKtlnTqc5TpeUWuy3ENhehC539bHYjqjxj4q_PZqLsZh-NfkFToIE2ZAPGPXUfnnSzobvgtqIDmwn5FD_H01edwshsN1vajDwWB7prG8gULk1nRmlQCsS2fWVpNBAuaxQs40RJa48KD-L0tN6Zw6OuxuGnYSVo0aSvVczKuRXiaciKIP5ZySAUmk9IqeOdT27EhMwbTltGPPSr3b8ddaZzsoEqGyTvqehQfTTAnNAZ52mJZdcll76kYqcDDWZYhQPUd1mF0fHjGcFporPhB8BCryOUm6gMNGTGw39VQE6PmMv9p2NHAaNbAzh74XfR1NdUTYXVh5HWSpb9r_xNhZM1mn7pqJQDkj1o-T8DW7snvFGTPQtjLod87IvatIC9aSrGaHYrOiD92fnI9LmQ.NNN-BpDndveEUVdTyOq0CA";

async function testEndpoint(name, url, options = {}) {
	try {
		console.log(`\n🧪 Testing ${name}...`);
		const response = await axios({
			url,
			method: options.method || "GET",
			headers: options.headers || {},
			data: options.data,
			validateStatus: () => true, // Don't throw on error status
		});

		console.log(`   Status: ${response.status}`);
		console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);

		if (response.data.success !== undefined) {
			console.log(`   ✅ Standardized format: ${response.data.success ? "SUCCESS" : "FAILED"}`);
			return response.data.success;
		} else {
			console.log(`   ❌ Non-standardized format detected`);
			return false;
		}
	} catch (error) {
		console.log(`   ❌ Error: ${error.message}`);
		return false;
	}
}

async function testAdManagerUpdates() {
	console.log("🧪 Testing BTDash Ad Manager API Updates...\n");

	let passedTests = 0;
	let totalTests = 0;

	console.log("=== ENVIRONMENT CONFIGURATION ===");
	console.log(`API_BASE_URL: ${API_BASE_URL}`);
	console.log(`APP_BASE_URL: ${APP_BASE_URL}`);

	console.log("\n=== DATA ENDPOINTS (Should require internal key) ===");

	// Test data endpoints that should require internal key
	const dataEndpoints = [
		["Companies", `${API_BASE_URL}/companies`],
		["Products", `${API_BASE_URL}/products`],
		["Categories", `${API_BASE_URL}/categories`],
		["Jobs", `${API_BASE_URL}/jobs`],
		["News", `${API_BASE_URL}/news`],
		["Events", `${API_BASE_URL}/events`],
	];

	for (const [name, endpoint] of dataEndpoints) {
		totalTests++;
		// Test without internal key (should fail)
		const result = await testEndpoint(`${name} (no key)`, endpoint);
		if (!result) passedTests++; // Should fail without key
	}

	console.log("\n=== ADVERTISING ENDPOINTS (Should require JWT) ===");

	// Test advertising endpoints that should require JWT
	const adEndpoints = [
		["Campaigns", `${API_BASE_URL}/campaigns`],
		["Ads", `${API_BASE_URL}/ads`],
		["User Profile", `${API_BASE_URL}/user/me`],
	];

	for (const [name, endpoint] of adEndpoints) {
		totalTests++;
		// Test without JWT (should fail with 401)
		const result = await testEndpoint(`${name} (no auth)`, endpoint);
		if (!result) passedTests++; // Should fail without auth
	}

	console.log("\n=== VALIDATION TESTS ===");

	// Test campaign creation validation
	totalTests++;
	const validationResult = await testEndpoint("Campaign Creation Validation", `${API_BASE_URL}/campaigns`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${MOCK_JWT_TOKEN}`,
		},
		data: {
			name: "", // Invalid: empty name
			start_date: "invalid-date", // Invalid: bad date format
		},
	});
	if (!validationResult) passedTests++; // Should fail validation

	console.log("\n=== FRONTEND API ROUTES ===");

	// Test frontend API routes
	const frontendEndpoints = [
		["Frontend Campaigns", `${APP_BASE_URL}/api/user/campaigns`],
		["Frontend Ads", `${APP_BASE_URL}/api/user/ads`],
		["Frontend User Profile", `${APP_BASE_URL}/api/user/me`],
	];

	for (const [name, endpoint] of frontendEndpoints) {
		totalTests++;
		const result = await testEndpoint(`${name}`, endpoint);
		// These should return standardized format (even if auth fails)
		if (result !== undefined) passedTests++;
	}

	console.log("\n=== RESULTS ===");
	console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
	console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);

	if (passedTests === totalTests) {
		console.log("🎉 All tests passed! Ad Manager is properly updated.");
	} else if (passedTests > totalTests * 0.8) {
		console.log("⚠️  Most tests passed. Check failed tests above.");
	} else {
		console.log("🚨 Many tests failed. Ad Manager needs attention.");
	}

	console.log("\n=== NEXT STEPS ===");
	console.log("1. Update MOCK_JWT_TOKEN with a real token for full testing");
	console.log("2. Test campaign creation with valid data");
	console.log("3. Test ad creation with valid data");
	console.log("4. Verify frontend components display data correctly");
	console.log("5. Test error handling in the UI");
}

// Run tests
testAdManagerUpdates().catch((error) => {
	console.error("❌ Test suite failed:", error.message);
	if (error.code === "ECONNREFUSED") {
		console.log("💡 Make sure both API server (port 3001) and Ad Manager (port 3000) are running");
	}
});
