import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card"; // Adjust the path as needed
import {
  Code,
  Briefcase,
  Newspaper,
  Users,
  Building,
  Calendar,
} from "lucide-react"; // Adjust the import path if needed

const gradients = {
  subnets: "bg-gradient-to-r from-blue-500 to-blue-700",
  applications: "bg-gradient-to-r from-green-500 to-green-700",
  news: "bg-gradient-to-r from-purple-500 to-purple-700",
  jobs: "bg-gradient-to-r from-red-500 to-red-700",
  events: "bg-gradient-to-r from-yellow-500 to-yellow-700",
  companies: "bg-gradient-to-r from-pink-500 to-pink-700",
};

function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(" ");
}

interface DashboardspringProps {
  subnetsCount: number;
  appsCount: number;
  newsCount: number;
  jobsCount: number;
  eventsCount: number;
  companiesCount: number;
}

export function Dashboardspring({
  subnetsCount,
  appsCount,
  newsCount,
  jobsCount,
  eventsCount,
  companiesCount,
}: DashboardspringProps) {
  return (
    <div className="max-w-[1600px] mx-auto">
      {/* Company Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 m-8">
        {/* Subnets Card */}
        <Link href="/subnets">
          <Card
            className={cn(
              "h-full hover:shadow-lg transition-all overflow-hidden text-white",
              gradients.subnets
            )}
          >
            <CardContent className="p-6 flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-1">Subnets</h3>
                <p className="text-white/80 text-sm">
                  {subnetsCount} active subnets
                </p>
              </div>
              <div className="bg-white/20 p-3 rounded-full">
                <Code className="h-6 w-6" />
              </div>
            </CardContent>
          </Card>
        </Link>

        {/* Company Card */}
        <Link href="/companies">
          <Card
            className={cn(
              "h-full hover:shadow-lg transition-all overflow-hidden text-white",
              gradients.companies
            )}
          >
            <CardContent className="p-6 flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-1">Companies</h3>
                <p className="text-white/80 text-sm">
                  {companiesCount} Companies
                </p>
              </div>
              <div className="bg-white/20 p-3 rounded-full">
                <Building className="h-6 w-6" />
              </div>
            </CardContent>
          </Card>
        </Link>

        {/* Applications Card */}
        <Link href="/products">
          <Card
            className={cn(
              "h-full hover:shadow-lg transition-all overflow-hidden text-white",
              gradients.applications
            )}
          >
            <CardContent className="p-6 flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-1">Products</h3>
                <p className="text-white/80 text-sm">{appsCount} products</p>
              </div>
              <div className="bg-white/20 p-3 rounded-full">
                <Briefcase className="h-6 w-6" />
              </div>
            </CardContent>
          </Card>
        </Link>

        {/* News Card */}
        <Link href="/news">
          <Card
            className={cn(
              "h-full hover:shadow-lg transition-all overflow-hidden text-white",
              gradients.news
            )}
          >
            <CardContent className="p-6 flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-1">News</h3>
                <p className="text-white/80 text-sm">
                  {newsCount} recent articles
                </p>
              </div>
              <div className="bg-white/20 p-3 rounded-full">
                <Newspaper className="h-6 w-6" />
              </div>
            </CardContent>
          </Card>
        </Link>

        {/* Jobs Card */}
        <Link href="/jobs">
          <Card
            className={cn(
              "h-full hover:shadow-lg transition-all overflow-hidden text-white",
              gradients.jobs
            )}
          >
            <CardContent className="p-6 flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-1">Jobs</h3>
                <p className="text-white/80 text-sm">
                  {jobsCount} open positions
                </p>
              </div>
              <div className="bg-white/20 p-3 rounded-full">
                <Users className="h-6 w-6" />
              </div>
            </CardContent>
          </Card>
        </Link>

        {/* Events Card */}
        <Link href="/events">
          <Card
            className={cn(
              "h-full hover:shadow-lg transition-all overflow-hidden text-white",
              gradients.events
            )}
          >
            <CardContent className="p-6 flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-1">Events</h3>
                <p className="text-white/80 text-sm">
                  {eventsCount} upcoming events
                </p>
              </div>
              <div className="bg-white/20 p-3 rounded-full">
                <Calendar className="h-6 w-6" />
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
