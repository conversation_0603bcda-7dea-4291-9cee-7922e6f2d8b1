"use client";

import ReactMarkdown from "react-markdown";

import { CategoryTag } from "@/components/category-tag";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { Category, Company, Job, Product, Subnet } from "@/lib/db/models";
import { slugify } from "@/lib/utils";
import {
  AlertCircle,
  Briefcase,
  Building,
  ChevronLeft,
  Clock,
  ExternalLink,
  MapPin,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface Props {
  job: Job | null;
  categories: Category[];
  company: Company | null | undefined;
  subnets: Subnet[] | null | undefined;
  products: Product[] | null | undefined;
}

export default function JobDetailClient({
  job,
  categories,
  company,
  subnets,
  products,
}: Props) {
  if (!job) {
    return (
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Error Loading Job</h2>
              <p className="text-red-500 mb-4">
                This job or company could not be found.
              </p>
              <Link href="/jobs">
                <Button>Back to Jobs</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const daysSincePosted = job.published_date
    ? Math.floor(
        (Date.now() - new Date(job.published_date).getTime()) /
          (1000 * 3600 * 24)
      )
    : null;

  const postedText =
    daysSincePosted === null
      ? "Date unknown"
      : daysSincePosted === 0
      ? "Posted today"
      : daysSincePosted === 1
      ? "Posted yesterday"
      : `Posted ${daysSincePosted} days ago`;

  const salaryText =
    job.min_salary || job.max_salary
      ? `${job.min_salary || ""}–${job.max_salary || ""} ${
          job.currency || ""
        }/${job.salary_time_frame || "month"}`
      : null;

  return (
    <div className="container py-8">
      <div className="max-w-4xl mx-auto">
        <Link
          href="/jobs"
          className="inline-flex items-center gap-1 text-muted-foreground hover:text-primary mb-6"
        >
          <ChevronLeft className="h-4 w-4" />
          Back to all jobs
        </Link>

        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center gap-4">
              <div className="relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                {company ? (
                  <div className="relative w-10 h-10 rounded-md overflow-hidden bg-white/20">
                    <Image
                      src={company.logo_url || "/placeholder.svg"}
                      alt={company.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : subnets && subnets[0] ? (
                  <div className="relative w-10 h-10 rounded-md overflow-hidden bg-primary flex items-center justify-center text-black text-3xl font-bold">
                    {subnets[0].subnet_symbol || subnets[0].name.charAt(0)}
                  </div>
                ) : null}
              </div>
              <div>
                <CardTitle className="text-2xl">{job.title}</CardTitle>
                <CardDescription>
                  {company ? (
                    <Link
                      href={`/companies/${slugify(company.name)}`}
                      className="inline-flex items-center gap-1 hover:text-primary transition-colors"
                    >
                      <Building className="h-4 w-4" />
                      <span>{company.name}</span>
                    </Link>
                  ) : subnets && subnets[0] ? (
                    <Link
                      href={`/subnets/${subnets[0].netuid}`}
                      className="inline-flex items-center gap-1 hover:text-primary transition-colors"
                    >
                      <Building className="h-4 w-4" />
                      <span>{subnets[0].name}</span>
                    </Link>
                  ) : null}
                </CardDescription>
              </div>
            </div>
          </CardHeader>

          <CardContent>
            <div className="flex flex-wrap gap-4 mb-6 text-sm text-muted-foreground">
              {job.location && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{job.location}</span>
                </div>
              )}
              {job.type && (
                <div className="flex items-center gap-1">
                  <Briefcase className="h-4 w-4" />
                  <span>{job.type}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{postedText}</span>
              </div>
              {salaryText && (
                <div className="font-medium text-green-600 dark:text-green-500">
                  {salaryText}
                </div>
              )}
            </div>
            <div className="flex flex-wrap gap-2 mb-4">
              {job.category_ids?.map((catId) => {
                const category = categories.find(
                  (category) => category.id === catId
                );
                return (
                  category && (
                    <span key={category.id} className="m-1 p-1 mt-1">
                      <CategoryTag category={category.name} />
                    </span>
                  )
                );
              })}
            </div>

            <div className="prose dark:prose-invert max-w-none mb-6">
              <h2 className="text-xl font-semibold mb-2">Job Description</h2>
              <div className="h-4" />
              <ReactMarkdown>{job.description}</ReactMarkdown>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-between items-center border-t pt-6 mt-6">
              <div>
                {subnets && subnets[0] && (
                  <div className="text-sm mb-2">
                    <span className="text-muted-foreground">
                      Related subnet:{" "}
                    </span>
                    <Link
                      href={`/subnets/${subnets[0].netuid}`}
                      className="text-primary hover:underline"
                    >
                      {subnets[0].name}
                    </Link>
                  </div>
                )}
                {products && products[0] && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">
                      Related product:{" "}
                    </span>
                    <Link
                      href={`/applications/${products[0].id}`}
                      className="text-primary hover:underline"
                    >
                      {products[0].name}
                    </Link>
                  </div>
                )}
              </div>

              <Button size="lg" asChild>
                <a
                  href={job.apply_link || company?.website_url || "#"}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="gap-1"
                >
                  Apply Now
                  <ExternalLink className="h-4 w-4" />
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>
              About{" "}
              {company
                ? company.name
                : subnets && subnets[0]
                ? subnets[0].name
                : ""}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <div className="relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                {company ? (
                  <Image
                    src={company.logo_url || "/placeholder.svg"}
                    alt={company.name}
                    fill
                    className="object-cover"
                  />
                ) : subnets && subnets[0] ? (
                  <div className="relative w-16 h-16 rounded-md overflow-hidden bg-primary flex items-center justify-center text-black text-3xl font-bold">
                    {subnets[0].subnet_symbol || subnets[0].name.charAt(0)}
                  </div>
                ) : null}
              </div>
              <div>
                <h3 className="font-semibold text-lg">
                  {company
                    ? company.name
                    : subnets && subnets[0]
                    ? subnets[0].name
                    : ""}
                </h3>
                <p className="text-muted-foreground">
                  {company
                    ? company.description ||
                      `${company.name} is a key contributor to the Bittensor ecosystem.`
                    : subnets && subnets[0]
                    ? subnets[0].description ||
                      `${subnets[0].name} is a key subnet in the Bittensor ecosystem.`
                    : ""}
                </p>
              </div>
            </div>

            <Button variant="outline" asChild className="w-full">
              <Link
                href={
                  company
                    ? `/companies/${slugify(company.name)}`
                    : subnets && subnets[0]
                    ? `/subnets/${subnets[0].netuid}`
                    : "#"
                }
              >
                View {company ? "Company" : "Subnet"} Profile
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
