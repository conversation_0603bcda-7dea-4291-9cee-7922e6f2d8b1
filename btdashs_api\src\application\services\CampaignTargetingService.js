const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

/**
 * Campaign Targeting Service
 *
 * Handles targeting rules at the campaign level.
 * All ads in a campaign inherit the campaign's targeting settings.
 *
 * <AUTHOR> Development Team
 * @since 2.0.0
 */
class CampaignTargetingService {
	/**
	 * Create or update targeting rules for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} targetingRules - Targeting rules object
	 * @returns {Promise<Object>} Updated targeting configuration
	 */
	async setTargetingRules(campaignId, targetingRules) {
		try {
			// Update the campaign's targeting JSONB column directly
			await db("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.update({
					targeting: JSON.stringify(targetingRules),
					updated_at: new Date(),
				});

			logger.info("Campaign targeting rules updated", {
				campaignId,
				targetingRules,
			});

			return targetingRules;
		} catch (error) {
			logger.error("Error setting campaign targeting rules", {
				error,
				campaignId,
				targetingRules,
			});
			throw error;
		}
	}

	/**
	 * Get targeting rules for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @returns {Promise<Object>} Targeting rules object
	 */
	async getTargetingRules(campaignId) {
		try {
			const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId }).select("targeting").first();

			if (!campaign || !campaign.targeting) {
				// Return default targeting structure
				return {
					countries: { mode: "all", include: [], exclude: [] },
					pageTypes: { types: [], categories: {} },
					devices: [],
					languages: [],
					interests: [],
					age: { min: null, max: null },
				};
			}

			// Parse targeting JSON
			const targetingRules =
				typeof campaign.targeting === "string" ? JSON.parse(campaign.targeting) : campaign.targeting;

			logger.info("Campaign targeting rules retrieved", {
				campaignId,
				hasTargeting: !!campaign.targeting,
			});

			return targetingRules;
		} catch (error) {
			logger.error("Error getting campaign targeting rules", {
				error,
				campaignId,
			});
			throw error;
		}
	}

	/**
	 * Check if a user/request matches campaign targeting criteria
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} userContext - User context (country, device, etc.)
	 * @returns {Promise<boolean>} Whether user matches targeting
	 */
	async matchesTargeting(campaignId, userContext) {
		try {
			const targetingRules = await this.getTargetingRules(campaignId);

			// If no targeting rules, show to everyone
			if (!targetingRules || Object.keys(targetingRules).length === 0) {
				return true;
			}

			// Check country targeting
			if (targetingRules.countries) {
				const { mode, include, exclude } = targetingRules.countries;
				const userCountry = userContext.country_code;

				if (mode === "include" && include.length > 0) {
					if (!userCountry || !include.includes(userCountry)) {
						return false;
					}
				} else if (mode === "exclude" && exclude.length > 0) {
					if (userCountry && exclude.includes(userCountry)) {
						return false;
					}
				}
			}

			// Check device targeting
			if (targetingRules.devices && targetingRules.devices.length > 0) {
				const userDevice = userContext.device_type;
				if (!userDevice || !targetingRules.devices.includes(userDevice)) {
					return false;
				}
			}

			// Check language targeting
			if (targetingRules.languages && targetingRules.languages.length > 0) {
				const userLanguage = userContext.language;
				if (!userLanguage || !targetingRules.languages.includes(userLanguage)) {
					return false;
				}
			}

			// Additional targeting checks can be added here...

			return true;
		} catch (error) {
			logger.error("Error checking campaign targeting match", {
				error,
				campaignId,
				userContext,
			});
			// On error, default to showing the ad
			return true;
		}
	}

	/**
	 * Get all ads that match targeting for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {Object} userContext - User context
	 * @returns {Promise<Array>} Array of matching ads
	 */
	async getTargetedAds(campaignId, userContext) {
		try {
			const matches = await this.matchesTargeting(campaignId, userContext);

			if (!matches) {
				return [];
			}

			// Get all active ads for this campaign
			const ads = await db("dtm_ads.ads")
				.where({
					campaign_id: campaignId,
					status: "active",
				})
				.select("*");

			return ads;
		} catch (error) {
			logger.error("Error getting targeted ads", { error, campaignId, userContext });
			throw error;
		}
	}

	/**
	 * Get targeting options for UI
	 * @returns {Promise<Object>} Available targeting options
	 */
	async getTargetingOptions() {
		try {
			return {
				countries: [
					{ code: "US", name: "United States" },
					{ code: "CA", name: "Canada" },
					{ code: "UK", name: "United Kingdom" },
					{ code: "DE", name: "Germany" },
					{ code: "FR", name: "France" },
					{ code: "AU", name: "Australia" },
					{ code: "JP", name: "Japan" },
					{ code: "BR", name: "Brazil" },
					{ code: "IN", name: "India" },
					{ code: "CN", name: "China" },
				],
				devices: ["desktop", "mobile", "tablet"],
				languages: ["en", "es", "fr", "de", "pt", "ja", "zh", "hi"],
				pageTypes: ["subnet", "companies", "products", "news"],
				interests: [
					"technology",
					"business",
					"finance",
					"healthcare",
					"education",
					"entertainment",
					"sports",
					"travel",
				],
				age_ranges: [
					{ min: 18, max: 24, name: "18-24" },
					{ min: 25, max: 34, name: "25-34" },
					{ min: 35, max: 44, name: "35-44" },
					{ min: 45, max: 54, name: "45-54" },
					{ min: 55, max: 64, name: "55-64" },
					{ min: 65, max: null, name: "65+" },
				],
			};
		} catch (error) {
			logger.error("Error getting targeting options", { error });
			throw error;
		}
	}
}

module.exports = CampaignTargetingService;
