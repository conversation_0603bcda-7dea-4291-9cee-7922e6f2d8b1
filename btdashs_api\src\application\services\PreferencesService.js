// src/application/services/PreferencesService.js

const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

/**
 * Preferences Service - Handles all user preferences business logic
 */
class PreferencesService {
	/**
	 * Create user preferences
	 * @param {number} userId - User ID
	 * @param {Object} preferencesData - Preferences data
	 * @returns {Promise<Object>} Created preferences object
	 */
	async createUserPreferences(userId, preferencesData) {
		const {
			job_types,
			industries,
			travel_availability,
			locations,
			compensation_range,
			additional_notes
		} = preferencesData;

		try {
			const [newPreferences] = await db("dtm_base.user_preferences")
				.insert({
					user_id: userId,
					job_types,
					industries,
					travel_availability,
					locations,
					compensation_range,
					additional_notes,
					created_at: new Date(),
				})
				.returning("*");

			logger.info("User preferences created", { user_id: userId });
			return newPreferences;
		} catch (error) {
			logger.error("Error creating user preferences", { error, userId });
			throw new Error(`Failed to create preferences: ${error.message}`);
		}
	}

	/**
	 * Get user preferences
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of preferences
	 */
	async getUserPreferences(userId) {
		try {
			const preferences = await db("dtm_base.user_preferences")
				.where({ user_id: userId });

			return preferences;
		} catch (error) {
			logger.error("Error getting user preferences", { error, userId });
			throw new Error(`Failed to get preferences: ${error.message}`);
		}
	}

	/**
	 * Get preferences by ID
	 * @param {number} preferencesId - Preferences ID
	 * @returns {Promise<Object|null>} Preferences object or null
	 */
	async getPreferencesById(preferencesId) {
		try {
			const preferences = await db("dtm_base.user_preferences")
				.where({ id: preferencesId })
				.first();

			return preferences || null;
		} catch (error) {
			logger.error("Error getting preferences by ID", { error, preferencesId });
			throw new Error(`Failed to get preferences: ${error.message}`);
		}
	}

	/**
	 * Update user preferences
	 * @param {number} userId - User ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated preferences object
	 */
	async updateUserPreferences(userId, updateData) {
		const {
			job_types,
			industries,
			travel_availability,
			locations,
			open_to_relocation,
			compensation_range,
			additional_notes
		} = updateData;

		try {
			const updatePayload = {
				job_types,
				industries,
				travel_availability,
				locations,
				open_to_relocation,
				compensation_range,
				additional_notes,
				updated_at: new Date(),
			};

			// Remove undefined values
			Object.keys(updatePayload).forEach(key => {
				if (updatePayload[key] === undefined) {
					delete updatePayload[key];
				}
			});

			const [updatedPreferences] = await db("dtm_base.user_preferences")
				.where({ user_id: userId })
				.update(updatePayload)
				.returning("*");

			if (!updatedPreferences) {
				throw new Error("Preferences not found");
			}

			logger.info("User preferences updated", { user_id: userId });
			return updatedPreferences;
		} catch (error) {
			logger.error("Error updating user preferences", { error, userId });
			throw error;
		}
	}

	/**
	 * Delete user preferences
	 * @param {number} userId - User ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteUserPreferences(userId) {
		try {
			const deleted = await db("dtm_base.user_preferences")
				.where({ user_id: userId })
				.del();

			if (!deleted) {
				throw new Error("Preferences not found");
			}

			logger.info("User preferences deleted", { user_id: userId });
			return true;
		} catch (error) {
			logger.error("Error deleting user preferences", { error, userId });
			throw error;
		}
	}
}

module.exports = new PreferencesService();
