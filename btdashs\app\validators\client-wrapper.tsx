"use client";

import { ValidatorsTable } from "@/components/validators-table";
import { Validator, ValidatorSubnetPerformance } from "@/lib/db/models";

interface ValidatorsClientWrapperProps {
	validators: Validator[];
	performances: ValidatorSubnetPerformance[];
}

export default function ValidatorsClientWrapper({ validators, performances }: ValidatorsClientWrapperProps) {
	return (
		<>
			<ValidatorsTable validators={validators} performances={performances} />
		</>
	);
}
