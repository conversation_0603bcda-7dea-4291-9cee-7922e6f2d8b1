// app/subnets/page.tsx
import { fetchWithFallback } from "@/lib/data/utils";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import SubnetsClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

export const metadata: Metadata = generateSEOMetadata({
	title: "Explore Bittensor's Subnets | DynamicTaoMarketCap",
	description: "Discover detailed metrics and analysis for Bittensor's subnets.",
	url: `${process.env.APP_BASE_URL}/subnets/`,
	image: "default-subnets-og.png",
});

export default async function SubnetsPage() {
	const [subnets, categories, metrics, products, networkStats] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnet-metrics`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/network-stats/latest`),
	]);

	// Handle partial failures
	if (subnets.error || categories.error || metrics.error || products.error || networkStats.error) {
		console.error("Partial data failure:", { subnets, categories, metrics, products, networkStats });
	}

	return (
		<div className="py-8 px-6 sm:px-8 lg:px-12">
			<div className="max-w-[2000px] mx-auto">
				<h1 className="text-3xl font-bold mb-4">Subnets</h1>
				<SubnetsClientWrapper
					subnets={subnets.data || []}
					categories={categories.data || []}
					metrics={metrics.data || []}
					products={products.data || []}
					networkStats={networkStats.data || {}}
				/>
			</div>
		</div>
	);
}
