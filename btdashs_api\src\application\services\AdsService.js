// src/application/services/AdsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Ads Service - Handles advertisement management operations
 *
 * This service manages advertisements in the platform, providing CRUD operations
 * and ad-related business logic for the advertising system.
 *
 * Key responsibilities:
 * - Advertisement CRUD operations
 * - Ad campaign management
 * - Ad targeting and filtering
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class AdsService extends BaseService {
	constructor() {
		super("dtm_base.ads", "Ad");
	}

	/**
	 * Get all ads with default sorting by created_at
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of ads
	 */
	async getAllAds(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "created_at", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all ads", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get ad by ID
	 * @param {number} id - Ad ID
	 * @returns {Promise<Object|null>} Ad object or null if not found
	 */
	async getAdById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting ad by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new ad
	 * @param {Object} adData - Ad data
	 * @returns {Promise<Object>} Created ad object
	 */
	async createAd(adData) {
		try {
			const newAd = await this.create({
				...adData,
				created_at: new Date(),
				updated_at: new Date(),
			});
			logger.info("Ad created", { ad_id: newAd.id });
			return newAd;
		} catch (error) {
			logger.error("Error creating ad", { error, adData });
			throw error;
		}
	}

	/**
	 * Update an ad
	 * @param {number} id - Ad ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated ad object
	 */
	async updateAd(id, updateData) {
		try {
			const updatedAd = await this.updateById(id, {
				...updateData,
				updated_at: new Date(),
			});
			logger.info("Ad updated", { ad_id: id });
			return updatedAd;
		} catch (error) {
			logger.error("Error updating ad", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete an ad
	 * @param {number} id - Ad ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteAd(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Ad deleted", { ad_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting ad", { error, id });
			throw error;
		}
	}

	/**
	 * Get active ads (currently running campaigns)
	 * @param {Object} filters - Optional filters
	 * @returns {Promise<Array>} Array of active ads
	 */
	async getActiveAds(filters = {}) {
		try {
			const now = new Date();
			const activeFilters = {
				...filters,
				is_active: true,
			};

			const db = require("../../infrastructure/database/knex");

			let query = db(this.tableName)
				.where(activeFilters)
				.where("start_date", "<=", now)
				.where(function () {
					this.whereNull("end_date").orWhere("end_date", ">=", now);
				})
				.orderBy("created_at", "desc");

			const ads = await query;

			logger.info("Active ads retrieved", {
				count: ads.length,
				filters,
			});

			return ads;
		} catch (error) {
			logger.error("Error getting active ads", { error, filters });
			throw new Error(`Failed to get active ads: ${error.message}`);
		}
	}

	/**
	 * Get ads by user ID
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of user's ads
	 */
	async getAdsByUserId(userId) {
		try {
			const ads = await this.getAll(
				{ user_id: userId },
				{
					orderBy: { column: "created_at", direction: "desc" },
				}
			);

			logger.info("User ads retrieved", {
				userId,
				count: ads.length,
			});

			return ads;
		} catch (error) {
			logger.error("Error getting ads by user ID", { error, userId });
			throw new Error(`Failed to get ads by user ID: ${error.message}`);
		}
	}

	/**
	 * Get ads by campaign ID
	 * @param {number} campaignId - Campaign ID
	 * @returns {Promise<Array>} Array of campaign ads
	 */
	async getAdsByCampaignId(campaignId) {
		try {
			const ads = await this.getAll(
				{ campaign_id: campaignId },
				{
					orderBy: { column: "created_at", direction: "desc" },
				}
			);

			logger.info("Campaign ads retrieved", {
				campaignId,
				count: ads.length,
			});

			return ads;
		} catch (error) {
			logger.error("Error getting ads by campaign ID", { error, campaignId });
			throw new Error(`Failed to get ads by campaign ID: ${error.message}`);
		}
	}

	/**
	 * Search ads by title or description
	 * @param {string} searchTerm - Search term
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of matching ads
	 */
	async searchAds(searchTerm, options = {}) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db(this.tableName)
				.where("title", "ilike", `%${searchTerm}%`)
				.orWhere("description", "ilike", `%${searchTerm}%`);

			// Apply ordering
			if (options.orderBy) {
				const { column, direction = "desc" } = options.orderBy;
				query = query.orderBy(column, direction);
			} else {
				query = query.orderBy("created_at", "desc");
			}

			// Apply pagination
			if (options.limit) {
				query = query.limit(options.limit);
			}
			if (options.offset) {
				query = query.offset(options.offset);
			}

			const ads = await query;

			logger.info("Ads searched", {
				searchTerm,
				resultCount: ads.length,
				options,
			});

			return ads;
		} catch (error) {
			logger.error("Error searching ads", { error, searchTerm, options });
			throw new Error(`Failed to search ads: ${error.message}`);
		}
	}

	// ─── CAMPAIGN MANAGEMENT ─────────────────────────────────────────────────

	/**
	 * Get all campaigns with user filtering
	 * @param {number} userId - User ID for filtering (null for admin)
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<Array>} Array of campaigns
	 */
	async getAllCampaigns(userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db("dtm_ads.ad_campaigns").select("*");

			// If not admin, only show campaigns for this manager
			if (!isAdmin && userId) {
				query = query.where({ manager_id: userId });
			}

			const campaigns = await query.orderBy("created_at", "desc");

			logger.info("Campaigns retrieved", {
				userId,
				isAdmin,
				count: campaigns.length,
			});

			return campaigns;
		} catch (error) {
			logger.error("Error getting all campaigns", { error, userId, isAdmin });
			throw new Error(`Failed to get campaigns: ${error.message}`);
		}
	}

	/**
	 * Get campaign by ID with authorization check
	 * @param {number} id - Campaign ID
	 * @param {number} userId - User ID for authorization
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<Object|null>} Campaign object or null
	 */
	async getCampaignById(id, userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			// Get campaign with enriched data for admin requests
			const campaign = await db("dtm_ads.ad_campaigns")
				.leftJoin("dtm_base.companies", "ad_campaigns.advertiser_id", "companies.id")
				.leftJoin("dtm_base.users as manager", "ad_campaigns.manager_id", "manager.id")
				.where("ad_campaigns.id", id)
				.select(
					"ad_campaigns.*",
					"companies.name as advertiser_name",
					"companies.website_url as advertiser_website",
					db.raw(
						"CONCAT(COALESCE(manager.first_name, ''), ' ', COALESCE(manager.last_name, '')) as manager_name"
					),
					"manager.email as manager_email"
				)
				.first();

			if (!campaign) {
				return null;
			}

			// Authorization check
			if (!isAdmin && userId && campaign.manager_id !== userId) {
				throw new Error("Access denied to this campaign");
			}

			// Get ads for this campaign
			const ads = await this.getAdsByCampaignId(id, userId, isAdmin);

			// Parse targeting JSON if it exists
			let targeting = null;
			if (campaign.targeting) {
				try {
					targeting =
						typeof campaign.targeting === "string" ? JSON.parse(campaign.targeting) : campaign.targeting;
				} catch (error) {
					logger.warn("Failed to parse campaign targeting JSON", { campaignId: id, error });
				}
			}

			// Format the response to match frontend expectations
			const formattedCampaign = {
				...campaign,
				startDate: campaign.start_date,
				endDate: campaign.end_date,
				createdAt: campaign.created_at,
				updatedAt: campaign.updated_at,
				targeting: targeting,
				ads: ads.map((ad) => ({
					...ad,
					imageUrl: ad.image_url,
					targetUrl: ad.target_url,
					createdAt: ad.created_at,
					updatedAt: ad.updated_at,
				})),
				// For backward compatibility, include first ad's data at campaign level
				imageUrl: ads.length > 0 ? ads[0].image_url : null,
				url: ads.length > 0 ? ads[0].target_url : null,
				slot_id: ads.length > 0 ? ads[0].slot_id : null,
			};

			logger.info("Campaign retrieved by ID", { id, userId, isAdmin });
			return formattedCampaign;
		} catch (error) {
			logger.error("Error getting campaign by ID", { error, id, userId });
			throw error;
		}
	}

	/**
	 * Create a new campaign
	 * @param {Object} campaignData - Campaign data
	 * @returns {Promise<Object>} Created campaign object
	 */
	async createCampaign(campaignData) {
		try {
			const db = require("../../infrastructure/database/knex");

			// Extract only the fields that exist in the ad_campaigns table
			const { targeting, ...validCampaignData } = campaignData;

			const [newCampaign] = await db("dtm_ads.ad_campaigns")
				.insert({
					...validCampaignData,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			logger.info("Campaign created", { campaign_id: newCampaign.id });
			return newCampaign;
		} catch (error) {
			logger.error("Error creating campaign", { error, campaignData });
			throw new Error(`Failed to create campaign: ${error.message}`);
		}
	}

	/**
	 * Update a campaign
	 * @param {number} id - Campaign ID
	 * @param {Object} updateData - Data to update
	 * @param {number} userId - User ID for authorization
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<Object>} Updated campaign object
	 */
	async updateCampaign(id, updateData, userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			// Check authorization first
			const campaign = await this.getCampaignById(id, userId, isAdmin);
			if (!campaign) {
				throw new Error("Campaign not found");
			}

			const [updatedCampaign] = await db("dtm_ads.ad_campaigns")
				.where({ id })
				.update({
					...updateData,
					updated_at: new Date(),
				})
				.returning("*");

			logger.info("Campaign updated", { campaign_id: id });
			return updatedCampaign;
		} catch (error) {
			logger.error("Error updating campaign", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a campaign
	 * @param {number} id - Campaign ID
	 * @param {number} userId - User ID for authorization
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteCampaign(id, userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			// Check authorization first
			const campaign = await this.getCampaignById(id, userId, isAdmin);
			if (!campaign) {
				throw new Error("Campaign not found");
			}

			await db("dtm_ads.ad_campaigns").where({ id }).delete();

			logger.info("Campaign deleted", { campaign_id: id });
			return true;
		} catch (error) {
			logger.error("Error deleting campaign", { error, id });
			throw error;
		}
	}

	// ─── ENHANCED ADS MANAGEMENT ─────────────────────────────────────────────────

	/**
	 * Get ads with campaign join and user filtering
	 * @param {number} userId - User ID for filtering
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<Array>} Array of ads with campaign info
	 */
	async getAdsWithCampaigns(userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db("dtm_ads.ads as a").join("dtm_ads.ad_campaigns as c", "a.campaign_id", "c.id").select("a.*");

			// If not admin, only show ads for this advertiser's campaigns
			if (!isAdmin && userId) {
				const userCampaigns = await db("dtm_ads.ad_campaigns").where({ manager_id: userId }).select("id");
				const campaignIds = userCampaigns.map((c) => c.id);
				query = query.whereIn("a.campaign_id", campaignIds);
			}

			const ads = await query.orderBy("a.created_at", "desc");

			logger.info("Ads with campaigns retrieved", {
				userId,
				isAdmin,
				count: ads.length,
			});

			return ads;
		} catch (error) {
			logger.error("Error getting ads with campaigns", { error, userId, isAdmin });
			throw new Error(`Failed to get ads with campaigns: ${error.message}`);
		}
	}

	/**
	 * Get ads for a specific campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {number} userId - User ID for authorization
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<Array>} Array of ads for the campaign
	 */
	async getAdsByCampaignId(campaignId, userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db("dtm_ads.ads").where({ campaign_id: campaignId });

			// If not admin, verify user has access to this campaign
			if (!isAdmin && userId) {
				const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId, manager_id: userId }).first();

				if (!campaign) {
					throw new Error("Access denied to this campaign");
				}
			}

			const ads = await query.orderBy("created_at", "desc");

			logger.info("Campaign ads retrieved", {
				campaignId,
				userId,
				isAdmin,
				count: ads.length,
			});

			return ads;
		} catch (error) {
			logger.error("Error getting campaign ads", { error, campaignId, userId, isAdmin });
			throw new Error(`Failed to get campaign ads: ${error.message}`);
		}
	}

	/**
	 * Get ad by ID with campaign join and authorization
	 * @param {number} id - Ad ID
	 * @param {number} userId - User ID for authorization
	 * @param {boolean} isAdmin - Whether user is admin
	 * @returns {Promise<Object|null>} Ad object with campaign info or null
	 */
	async getAdWithCampaignById(id, userId = null, isAdmin = false) {
		try {
			const db = require("../../infrastructure/database/knex");

			const ad = await db("dtm_ads.ads as a")
				.join("dtm_ads.ad_campaigns as c", "a.campaign_id", "c.id")
				.where("a.id", id)
				.select("a.*", "c.advertiser_id", "c.manager_id")
				.first();

			if (!ad) {
				return null;
			}

			// Authorization check - check if user is the manager of the campaign
			if (!isAdmin && userId && ad.manager_id !== userId) {
				throw new Error("Access denied to this ad");
			}

			logger.info("Ad with campaign retrieved by ID", { id, userId, isAdmin });
			return ad;
		} catch (error) {
			logger.error("Error getting ad with campaign by ID", { error, id, userId });
			throw error;
		}
	}

	// ─── SLOTS MANAGEMENT ─────────────────────────────────────────────────

	/**
	 * Get all active ad slots
	 * @returns {Promise<Array>} Array of active ad slots
	 */
	async getAllSlots() {
		try {
			const db = require("../../infrastructure/database/knex");

			const slots = await db("dtm_ads.ad_slots").where({ is_active: true }).select("*").orderBy("name", "asc");

			logger.info("Ad slots retrieved", { count: slots.length });
			return slots;
		} catch (error) {
			logger.error("Error getting all slots", { error });
			throw new Error(`Failed to get ad slots: ${error.message}`);
		}
	}

	/**
	 * Get slot by ID
	 * @param {number} id - Slot ID
	 * @returns {Promise<Object|null>} Slot object or null
	 */
	async getSlotById(id) {
		try {
			const db = require("../../infrastructure/database/knex");

			const slot = await db("dtm_ads.ad_slots").where({ id, is_active: true }).first();

			if (slot) {
				logger.info("Ad slot retrieved by ID", { id });
			}

			return slot;
		} catch (error) {
			logger.error("Error getting slot by ID", { error, id });
			throw new Error(`Failed to get ad slot: ${error.message}`);
		}
	}

	// ─── AD SERVING & TRACKING ─────────────────────────────────────────────────

	/**
	 * Get eligible ads for serving
	 * @param {string} slotId - Slot ID
	 * @returns {Promise<Array>} Array of eligible ads
	 */
	async getEligibleAds(slotId) {
		try {
			const db = require("../../infrastructure/database/knex");

			const eligibleAds = await db("dtm_ads.ads as a")
				.join("dtm_ads.ad_campaigns as c", "a.campaign_id", "c.id")
				.where({
					"a.slot_id": slotId,
					"a.status": "active",
					"c.status": "active",
				})
				.where("c.start_date", "<=", new Date())
				.where("c.end_date", ">=", new Date())
				.select("a.*");

			logger.info("Eligible ads retrieved", {
				slotId,
				count: eligibleAds.length,
			});

			return eligibleAds;
		} catch (error) {
			logger.error("Error getting eligible ads", { error, slotId });
			throw new Error(`Failed to get eligible ads: ${error.message}`);
		}
	}

	/**
	 * Track ad impression
	 * @param {Object} impressionData - Impression tracking data
	 * @returns {Promise<boolean>} Success status
	 */
	async trackImpression(impressionData) {
		try {
			const db = require("../../infrastructure/database/knex");

			await db("dtm_ads.ad_impressions").insert({
				...impressionData,
				created_at: new Date(),
			});

			logger.info("Impression tracked", { ad_id: impressionData.ad_id });
			return true;
		} catch (error) {
			logger.error("Error tracking impression", { error, impressionData });
			throw new Error(`Failed to track impression: ${error.message}`);
		}
	}

	/**
	 * Track ad click
	 * @param {Object} clickData - Click tracking data
	 * @returns {Promise<boolean>} Success status
	 */
	async trackClick(clickData) {
		try {
			const db = require("../../infrastructure/database/knex");

			await db("dtm_ads.ad_clicks").insert({
				...clickData,
				created_at: new Date(),
			});

			logger.info("Click tracked", { ad_id: clickData.ad_id });
			return true;
		} catch (error) {
			logger.error("Error tracking click", { error, clickData });
			throw new Error(`Failed to track click: ${error.message}`);
		}
	}

	/**
	 * Serve an ad for a specific slot with user context
	 * @param {string} slotId - Slot ID
	 * @param {Object} userContext - User context for targeting
	 * @returns {Promise<Object|null>} Selected ad or null
	 */
	async serveAd(slotId, userContext = {}) {
		try {
			const db = require("../../infrastructure/database/knex");

			// Get eligible ads for this slot
			const eligibleAds = await this.getEligibleAds(slotId);

			if (eligibleAds.length === 0) {
				logger.info("No ads available for this slot", { slotId });
				return null;
			}

			// Apply targeting filters if user context is provided
			let filteredAds = eligibleAds;

			if (userContext.country_code || userContext.device_type || userContext.language) {
				// Get campaign targeting for eligible ads
				const campaignIds = [...new Set(eligibleAds.map((ad) => ad.campaign_id))];

				const campaigns = await db("dtm_ads.ad_campaigns").whereIn("id", campaignIds).select("id", "targeting");

				// Filter ads based on campaign-level targeting
				filteredAds = eligibleAds.filter((ad) => {
					const campaign = campaigns.find((c) => c.id === ad.campaign_id);

					if (!campaign || !campaign.targeting) {
						return true; // No targeting restrictions
					}

					// Parse targeting JSON
					
					try {
						targetingRules =
							typeof campaign.targeting === "string"
								? JSON.parse(campaign.targeting)
								: campaign.targeting;
					} catch (error) {
						logger.warn("Invalid targeting JSON for campaign", { campaignId: campaign.id });
						return true; // Invalid targeting, allow ad
					}

					// Check country targeting
					if (userContext.country_code && targetingRules.countries) {
						const { mode, include, exclude } = targetingRules.countries;

						if (mode === "include" && include.length > 0) {
							if (!include.includes(userContext.country_code)) {
								return false;
							}
						} else if (mode === "exclude" && exclude.length > 0) {
							if (exclude.includes(userContext.country_code)) {
								return false;
							}
						}
					}

					// Check device targeting
					if (userContext.device_type && targetingRules.devices) {
						if (targetingRules.devices.length > 0) {
							if (!targetingRules.devices.includes(userContext.device_type)) {
								return false;
							}
						}
					}

					// Check language targeting
					if (userContext.language && targetingRules.languages) {
						if (targetingRules.languages.length > 0) {
							if (!targetingRules.languages.includes(userContext.language)) {
								return false;
							}
						}
					}

					return true;
				});
			}

			if (filteredAds.length === 0) {
				logger.info("No ads available for this user context", {
					slotId,
					userContext,
					targetingRules,
				});
				return null;
			}

			// Weighted random selection based on ad weight
			const totalWeight = filteredAds.reduce((sum, ad) => sum + (ad.weight || 1), 0);
			let random = Math.random() * totalWeight;

			for (const ad of filteredAds) {
				random -= ad.weight || 1;
				if (random <= 0) {
					logger.info("Ad served", {
						ad_id: ad.id,
						slot_id: slotId,
						user_context: userContext,
					});
					return ad;
				}
			}

			// Fallback to first ad if weighted selection fails
			logger.info("Ad served (fallback)", {
				ad_id: filteredAds[0].id,
				slot_id: slotId,
				user_context: userContext,
			});
			return filteredAds[0];
		} catch (error) {
			logger.error("Error serving ad", { error, slotId, userContext });
			throw new Error(`Failed to serve ad: ${error.message}`);
		}
	}

	/**
	 * Update campaign status
	 * @param {number} campaignId - Campaign ID
	 * @param {string} status - New status
	 * @returns {Promise<Object>} Updated campaign
	 */
	async updateCampaignStatus(campaignId, status) {
		try {
			const db = require("../../infrastructure/database/knex");

			const [updatedCampaign] = await db("dtm_ads.ad_campaigns")
				.where({ id: campaignId })
				.update({
					status,
					updated_at: new Date(),
				})
				.returning("*");

			if (!updatedCampaign) {
				throw new Error("Campaign not found");
			}

			// Ads inherit the campaign status
			await db("dtm_ads.ads").where({ campaign_id: campaignId }).update({
				status: status,
				updated_at: new Date(),
			});

			logger.info("Campaign status updated", { campaignId, status });
			return updatedCampaign;
		} catch (error) {
			logger.error("Error updating campaign status", { error, campaignId, status });
			throw new Error(`Failed to update campaign status: ${error.message}`);
		}
	}

	/**
	 * Update an ad
	 * @param {number} adId - Ad ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated ad
	 */
	async updateAd(adId, updateData) {
		try {
			const db = require("../../infrastructure/database/knex");

			const [updatedAd] = await db("dtm_ads.ads")
				.where({ id: adId })
				.update({
					...updateData,
					updated_at: new Date(),
				})
				.returning("*");

			if (!updatedAd) {
				throw new Error("Ad not found");
			}

			logger.info("Ad updated", { adId });
			return updatedAd;
		} catch (error) {
			logger.error("Error updating ad", { error, adId, updateData });
			throw new Error(`Failed to update ad: ${error.message}`);
		}
	}

	/**
	 * Get campaign by ID and advertiser
	 * @param {number} campaignId - Campaign ID
	 * @param {number} advertiserId - Advertiser ID
	 * @returns {Promise<Object|null>} Campaign or null
	 */
	async getCampaignByIdAndAdvertiser(campaignId, advertiserId) {
		try {
			const db = require("../../infrastructure/database/knex");

			const campaign = await db("dtm_ads.ad_campaigns")
				.where({ id: campaignId, advertiser_id: advertiserId })
				.first();

			return campaign || null;
		} catch (error) {
			logger.error("Error getting campaign by ID and advertiser", { error, campaignId, advertiserId });
			throw new Error(`Failed to get campaign: ${error.message}`);
		}
	}

	/**
	 * Get campaign by ID and manager
	 * @param {number} campaignId - Campaign ID
	 * @param {number} managerId - Manager ID
	 * @returns {Promise<Object|null>} Campaign or null
	 */
	async getCampaignByIdAndManager(campaignId, managerId) {
		try {
			const db = require("../../infrastructure/database/knex");

			const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId, manager_id: managerId }).first();

			return campaign || null;
		} catch (error) {
			logger.error("Error getting campaign by ID and manager", { error, campaignId, managerId });
			throw new Error(`Failed to get campaign: ${error.message}`);
		}
	}

	// ─── UTILITY METHODS ─────────────────────────────────────────────────

	/**
	 * Get user by Auth0 ID
	 * @param {string} auth0UserId - Auth0 user ID
	 * @returns {Promise<Object|null>} User object or null
	 */
	async getUserByAuth0Id(auth0UserId) {
		try {
			const db = require("../../infrastructure/database/knex");

			const user = await db("dtm_base.users").where({ auth0_user_id: auth0UserId }).first().timeout(5000);

			return user;
		} catch (error) {
			logger.error("Error getting user by Auth0 ID", { error, auth0UserId });
			throw new Error(`Failed to get user: ${error.message}`);
		}
	}
}

module.exports = new AdsService();
