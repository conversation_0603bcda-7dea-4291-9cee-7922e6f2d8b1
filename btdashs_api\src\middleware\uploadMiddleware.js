const multer = require("multer");
const path = require("path");

// Configure multer for memory storage (we'll handle file saving in the service)
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (req, file, cb) => {
	// Check file type
	const allowedMimeTypes = [
		"image/jpeg",
		"image/jpg", 
		"image/png",
		"image/gif",
		"image/webp"
	];

	if (allowedMimeTypes.includes(file.mimetype)) {
		cb(null, true);
	} else {
		cb(new Error("Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."), false);
	}
};

// Configure multer
const upload = multer({
	storage: storage,
	limits: {
		fileSize: 5 * 1024 * 1024, // 5MB limit
		files: 1 // Only allow 1 file at a time
	},
	fileFilter: fileFilter
});

// Middleware for single file upload
const uploadSingle = upload.single("image");

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
	if (error instanceof multer.MulterError) {
		if (error.code === "LIMIT_FILE_SIZE") {
			return res.status(400).json({
				success: false,
				message: "File too large. Maximum size is 5MB.",
				error: "FILE_TOO_LARGE"
			});
		}
		if (error.code === "LIMIT_FILE_COUNT") {
			return res.status(400).json({
				success: false,
				message: "Too many files. Only 1 file allowed.",
				error: "TOO_MANY_FILES"
			});
		}
		if (error.code === "LIMIT_UNEXPECTED_FILE") {
			return res.status(400).json({
				success: false,
				message: "Unexpected field name. Use 'image' as the field name.",
				error: "UNEXPECTED_FIELD"
			});
		}
	}
	
	if (error.message.includes("Invalid file type")) {
		return res.status(400).json({
			success: false,
			message: error.message,
			error: "INVALID_FILE_TYPE"
		});
	}

	// Other errors
	return res.status(500).json({
		success: false,
		message: "File upload error",
		error: error.message
	});
};

// Combined middleware that handles upload and errors
const uploadMiddleware = (req, res, next) => {
	uploadSingle(req, res, (error) => {
		if (error) {
			return handleUploadError(error, req, res, next);
		}
		next();
	});
};

module.exports = {
	uploadMiddleware,
	uploadSingle,
	handleUploadError
};
