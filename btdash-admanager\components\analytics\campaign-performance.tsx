import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface CampaignPerformanceProps {
  campaigns: {
    id: string
    name: string
    impressions: number
    clicks: number
    ctr: number
  }[]
  isAdmin?: boolean
}

export function CampaignPerformance({ campaigns, isAdmin = false }: CampaignPerformanceProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Campaign Performance</CardTitle>
        <CardDescription>Compare performance across all campaigns</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-5 font-medium text-sm">
            <div className="col-span-2">Campaign</div>
            <div className="text-right">Impressions</div>
            <div className="text-right">Clicks</div>
            <div className="text-right">CTR</div>
          </div>
          <div className="space-y-2">
            {campaigns.map((campaign) => (
              <div key={campaign.id} className="grid grid-cols-5 items-center text-sm py-2 border-b">
                <div className="col-span-2 font-medium">{campaign.name}</div>
                <div className="text-right">{campaign.impressions.toLocaleString()}</div>
                <div className="text-right">{campaign.clicks.toLocaleString()}</div>
                <div className="text-right">{(campaign.ctr * 100).toFixed(2)}%</div>
              </div>
            ))}
          </div>
          {campaigns.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">No active campaigns to display.</div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
