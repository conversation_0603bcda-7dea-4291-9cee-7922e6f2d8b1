"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Validator, ValidatorSubnetPerformance } from "@/lib/db/models";
import { formatNumber } from "@/utils/formatNumber";
import { ChevronDown, ChevronUp, Download, ExternalLink, Search } from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";

interface ValidatorsTableProps {
	validators: Validator[];
	performances: ValidatorSubnetPerformance[];
}

type SortKey = "name" | "rank" | "stake" | "stake_24h_change" | "nominators" | "subnetsCount" | "take";

export function ValidatorsTable({ validators, performances }: ValidatorsTableProps) {
	// Process validators with subnet counts (same as original hook)
	const processedValidators = useMemo(() => {
		const subnetMap = performances.reduce((acc, performance) => {
			const current = acc.get(performance.validator_id) || new Set<number>();
			current.add(performance.netuid);
			return acc.set(performance.validator_id, current);
		}, new Map<number, Set<number>>());

		return validators.map((validator) => ({
			...validator,
			subnetsCount: subnetMap.get(validator.validator_id)?.size || 0,
		}));
	}, [validators, performances]);

	const [rowsPerPage, setRowsPerPage] = useState("100");
	const [sortKey, setSortKey] = useState<SortKey>("rank");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
	const [searchQuery, setSearchQuery] = useState("");

	const handleExportCSV = () => {
		const headers = ["Rank", "Name", "Hotkey", "Stake", "Stake 24h", "Nominators", "Subnets", "Take (%)"];
		const data = filteredValidators.map((v) => [
			v.rank,
			v.name || "Unnamed",
			v.hotkey,
			formatNumber(v.stake, 1),
			v.stake_24h_change != null ? formatNumber(v.stake_24h_change, 1) : "-",
			v.nominators,
			v.subnetsCount ?? 0,
			v.take != null ? formatNumber(v.take, 1) : "-",
		]);

		const csvContent = [headers.join(","), ...data.map((row) => row.join(","))].join("\n");
		const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
		const url = URL.createObjectURL(blob);
		const link = document.createElement("a");
		link.setAttribute("href", url);
		link.setAttribute("download", "validators.csv");
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	const perPage = parseInt(rowsPerPage);

	const filteredValidators = processedValidators.filter((v) => {
		const name = v.name?.toLowerCase() || "";
		const hotkey = v.hotkey?.toLowerCase() || "";
		return name.includes(searchQuery.toLowerCase()) || hotkey.includes(searchQuery.toLowerCase());
	});

	const sortedValidators = [...filteredValidators]
		.sort((a, b) => {
			let aVal = a[sortKey];
			let bVal = b[sortKey];

			if (aVal == null) aVal = sortKey === "name" ? "" : 0;
			if (bVal == null) bVal = sortKey === "name" ? "" : 0;

			if (sortKey === "name") {
				return sortOrder === "asc"
					? String(aVal).localeCompare(String(bVal))
					: String(bVal).localeCompare(String(aVal));
			}

			const numA = typeof aVal === "number" ? aVal : parseFloat(String(aVal));
			const numB = typeof bVal === "number" ? bVal : parseFloat(String(bVal));
			if (isNaN(numA) || isNaN(numB)) return 0;

			if (numA < numB) return sortOrder === "asc" ? -1 : 1;
			if (numA > numB) return sortOrder === "asc" ? 1 : -1;
			return 0;
		})
		.slice(0, perPage);

	const toggleSort = (key: SortKey) => {
		if (sortKey === key) {
			setSortOrder(sortOrder === "asc" ? "desc" : "asc");
		} else {
			setSortKey(key);
			setSortOrder("asc");
		}
	};

	const SortIcon = ({ columnKey }: { columnKey: SortKey }) =>
		sortKey === columnKey ? (
			sortOrder === "asc" ? (
				<ChevronUp className="ml-2 h-4 w-4" />
			) : (
				<ChevronDown className="ml-2 h-4 w-4" />
			)
		) : null;

	const getRankColor = (rank: number) => {
		switch (rank) {
			case 1:
				return "bg-gradient-to-br from-yellow-400 to-yellow-600 text-white";
			case 2:
				return "bg-gradient-to-br from-gray-300 to-gray-500 text-white";
			case 3:
				return "bg-gradient-to-br from-amber-500 to-amber-700 text-white";
			default:
				return "bg-muted";
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex flex-col sm:flex-row justify-between items-center gap-4">
				<div className="relative w-full sm:w-[400px]">
					<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<Input
						type="search"
						placeholder="Search by Validator"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="pl-10 w-full"
					/>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline" onClick={handleExportCSV}>
						<Download className="mr-2 h-4 w-4" />
						Export CSV
					</Button>
					<span className="text-sm text-muted-foreground">Rows</span>
					<Select value={rowsPerPage} onValueChange={(v) => setRowsPerPage(v)}>
						<SelectTrigger className="w-[70px]">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							{["10", "25", "50", "100"].map((v) => (
								<SelectItem key={v} value={v}>
									{v}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			</div>

			<div className="rounded-lg border overflow-x-auto">
				<Table className="min-w-[800px]">
					<TableHeader className="bg-muted/50">
						<TableRow>
							<TableHead className="w-[80px] text-center">
								<Button variant="ghost" onClick={() => toggleSort("rank")} className="font-bold">
									Rank <SortIcon columnKey="rank" />
								</Button>
							</TableHead>
							<TableHead className="text-left min-w-[200px]">
								<Button variant="ghost" onClick={() => toggleSort("name")} className="font-bold">
									Name / Address <SortIcon columnKey="name" />
								</Button>
							</TableHead>
							<TableHead className="text-center">
								<Button variant="ghost" onClick={() => toggleSort("stake")} className="font-bold">
									Stake <SortIcon columnKey="stake" />
								</Button>
							</TableHead>
							<TableHead className="text-center">
								<Button
									variant="ghost"
									onClick={() => toggleSort("stake_24h_change")}
									className="font-bold"
								>
									Stake 24h Δ <SortIcon columnKey="stake_24h_change" />
								</Button>
							</TableHead>
							<TableHead className="text-center">
								<Button variant="ghost" onClick={() => toggleSort("nominators")} className="font-bold">
									Nominators <SortIcon columnKey="nominators" />
								</Button>
							</TableHead>
							<TableHead className="text-center">
								<Button
									variant="ghost"
									onClick={() => toggleSort("subnetsCount")}
									className="font-bold"
								>
									Subnets <SortIcon columnKey="subnetsCount" />
								</Button>
							</TableHead>
							<TableHead className="text-center">
								<Button variant="ghost" onClick={() => toggleSort("take")} className="font-bold">
									Take % <SortIcon columnKey="take" />
								</Button>
							</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{sortedValidators.length === 0 ? (
							<TableRow>
								<TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
									No validators match your search.
								</TableCell>
							</TableRow>
						) : (
							sortedValidators.map((v) => (
								<TableRow key={v.validator_id} className="hover:bg-muted/50">
									<TableCell className="text-center">
										<div
											className={`flex justify-center items-center mx-auto w-8 h-8 rounded-full ${getRankColor(
												v.rank
											)}`}
										>
											{v.rank}
										</div>
									</TableCell>
									<TableCell>
										<div className="space-y-1">
											<div className="flex items-center gap-2">
												<Link
													href={`/validators/${v.validator_id}`}
													className="font-medium hover:underline hover:text-primary"
												>
													{v.name || "Unnamed"}
												</Link>
												<a
													href={`https://explorer.bittensor.com/address/${v.hotkey}`}
													target="_blank"
													rel="noopener noreferrer"
													className="text-blue-500 hover:text-blue-600 transition-colors"
												>
													<ExternalLink className="h-4 w-4" />
												</a>
											</div>
											<div className="text-sm text-muted-foreground font-mono">
												{v.hotkey.slice(0, 8)}...{v.hotkey.slice(-8)}
											</div>
										</div>
									</TableCell>
									<TableCell className="text-center font-medium">
										{`${formatNumber(v.stake, 1)} τ`}
									</TableCell>
									<TableCell className="text-center">
										{v.stake_24h_change != null ? (
											<span
												className={`inline-flex items-center ${
													v.stake_24h_change >= 0 ? "text-green-500" : "text-red-500"
												}`}
											>
												{v.stake_24h_change >= 0 ? "+" : ""}
												{formatNumber(v.stake_24h_change, 1)} τ
											</span>
										) : (
											"-"
										)}
									</TableCell>
									<TableCell className="text-center">{v.nominators}</TableCell>
									<TableCell className="text-center">
										<span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-medium">
											{v.subnetsCount ?? 0}
										</span>
									</TableCell>
									<TableCell className="text-center">
										{v.take != null ? `${formatNumber(v.take, 1)}%` : "-"}
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
