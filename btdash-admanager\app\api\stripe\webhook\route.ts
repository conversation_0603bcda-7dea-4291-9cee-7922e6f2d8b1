import { stripeHelpers } from "@/lib/stripe-server";
import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

export async function POST(request: NextRequest) {
	try {
		const body = await request.text();
		const signature = request.headers.get("stripe-signature");

		if (!signature) {
			console.error("Missing Stripe signature");
			return NextResponse.json({ error: "Missing signature" }, { status: 400 });
		}

		let event: Stripe.Event;

		try {
			// Verify webhook signature
			event = stripeHelpers.verifyWebhookSignature(body, signature);
		} catch (err) {
			console.error("Webhook signature verification failed:", err);
			return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
		}

		console.log("Received Stripe webhook event:", event.type);

		// Handle different event types
		switch (event.type) {
			case "payment_intent.succeeded":
				await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
				break;

			case "payment_intent.payment_failed":
				await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
				break;

			case "payment_method.attached":
				await handlePaymentMethodAttached(event.data.object as Stripe.PaymentMethod);
				break;

			case "customer.created":
				await handleCustomerCreated(event.data.object as Stripe.Customer);
				break;

			case "invoice.payment_succeeded":
				await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
				break;

			case "invoice.payment_failed":
				await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
				break;

			default:
				console.log(`Unhandled event type: ${event.type}`);
		}

		return NextResponse.json({ received: true });
	} catch (error) {
		console.error("Webhook error:", error);
		return NextResponse.json({ error: "Webhook handler failed" }, { status: 500 });
	}
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
	try {
		console.log("Payment succeeded:", paymentIntent.id);

		const campaignId = paymentIntent.metadata.campaignId;
		const userId = paymentIntent.metadata.userId;

		if (!campaignId || !userId) {
			console.error("Missing campaign ID or user ID in payment intent metadata");
			return;
		}

		// Process payment success and activate campaign through billing API
		await fetch(`${process.env.API_BASE_URL}/billing/payment-success`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Internal-Key": process.env.INTERNAL_API_KEY || "",
			},
			body: JSON.stringify({
				payment_intent_id: paymentIntent.id,
				campaign_id: parseInt(campaignId),
				amount: paymentIntent.amount / 100, // Convert from cents
				status: "completed",
				payment_method: paymentIntent.payment_method,
			}),
		});

		console.log(`Campaign ${campaignId} payment processed and activated`);
	} catch (error) {
		console.error("Error handling payment success:", error);

		// Try to notify user of processing error
		try {
			const campaignId = paymentIntent.metadata.campaignId;
			if (campaignId) {
				await fetch(`${process.env.API_BASE_URL}/billing/payment-failed`, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"X-Internal-Key": process.env.INTERNAL_API_KEY || "",
					},
					body: JSON.stringify({
						payment_intent_id: paymentIntent.id,
						campaign_id: parseInt(campaignId),
						status: "processing_error",
						failure_reason: `Payment processing error: ${error.message}`,
					}),
				});
			}
		} catch (notificationError) {
			console.error("Failed to send error notification:", notificationError);
		}
	}
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
	try {
		console.log("Payment failed:", paymentIntent.id);

		const campaignId = paymentIntent.metadata.campaignId;

		if (!campaignId) {
			console.error("Missing campaign ID in payment intent metadata");
			return;
		}

		// Update billing transaction status
		await fetch(`${process.env.API_BASE_URL}/billing/payment-failed`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Internal-Key": process.env.INTERNAL_API_KEY || "",
			},
			body: JSON.stringify({
				payment_intent_id: paymentIntent.id,
				campaign_id: parseInt(campaignId),
				status: "failed",
				failure_reason: paymentIntent.last_payment_error?.message || "Payment failed",
			}),
		});

		console.log(`Payment failed for campaign ${campaignId}`);
	} catch (error) {
		console.error("Error handling payment failure:", error);

		// Log critical error for manual intervention
		try {
			await fetch(`${process.env.API_BASE_URL}/admin/log-critical-error`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					"X-Internal-Key": process.env.INTERNAL_API_KEY || "",
				},
				body: JSON.stringify({
					type: "webhook_payment_failure_error",
					payment_intent_id: paymentIntent.id,
					campaign_id: paymentIntent.metadata.campaignId,
					error_message: error.message,
					timestamp: new Date().toISOString(),
				}),
			});
		} catch (logError) {
			console.error("Failed to log critical error:", logError);
		}
	}
}

async function handlePaymentMethodAttached(paymentMethod: Stripe.PaymentMethod) {
	try {
		console.log("Payment method attached:", paymentMethod.id);
		// Additional logic for payment method attachment if needed
	} catch (error) {
		console.error("Error handling payment method attachment:", error);
	}
}

async function handleCustomerCreated(customer: Stripe.Customer) {
	try {
		console.log("Customer created:", customer.id);
		// Additional logic for customer creation if needed
	} catch (error) {
		console.error("Error handling customer creation:", error);
	}
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
	try {
		console.log("Invoice payment succeeded:", invoice.id);
		// Handle invoice payment success if needed
	} catch (error) {
		console.error("Error handling invoice payment success:", error);
	}
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
	try {
		console.log("Invoice payment failed:", invoice.id);
		// Handle invoice payment failure if needed
	} catch (error) {
		console.error("Error handling invoice payment failure:", error);
	}
}
