const fs = require("fs").promises;
const path = require("path");
const crypto = require("crypto");
const sharp = require("sharp");
const logger = require("../../../logger");

class FileUploadService {
	constructor() {
		this.uploadDir = path.join(process.cwd(), "uploads", "ads");
		this.maxFileSize = 5 * 1024 * 1024; // 5MB
		this.allowedMimeTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
		this.allowedExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];

		// Ensure upload directory exists
		this.ensureUploadDir();
	}

	/**
	 * Ensure upload directory exists
	 */
	async ensureUploadDir() {
		try {
			await fs.mkdir(this.uploadDir, { recursive: true });
		} catch (error) {
			logger.error("Error creating upload directory", { error, uploadDir: this.uploadDir });
		}
	}

	/**
	 * Validate uploaded file
	 * @param {Object} file - Uploaded file object
	 * @param {Object} slotRequirements - Ad slot requirements
	 * @returns {Object} Validation result
	 */
	async validateFile(file, slotRequirements = {}) {
		const errors = [];

		// Check if file exists
		if (!file) {
			errors.push("No file provided");
			return { isValid: false, errors };
		}

		// Check file size
		if (file.size > this.maxFileSize) {
			errors.push(`File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`);
		}

		// Check MIME type
		if (!this.allowedMimeTypes.includes(file.mimetype)) {
			errors.push(`Invalid file type. Allowed types: ${this.allowedMimeTypes.join(", ")}`);
		}

		// Check file extension
		const fileExtension = path.extname(file.originalname).toLowerCase();
		if (!this.allowedExtensions.includes(fileExtension)) {
			errors.push(`Invalid file extension. Allowed extensions: ${this.allowedExtensions.join(", ")}`);
		}

		// Validate image dimensions if slot requirements provided
		if (slotRequirements.width && slotRequirements.height && file.buffer) {
			try {
				const metadata = await sharp(file.buffer).metadata();

				if (metadata.width !== slotRequirements.width || metadata.height !== slotRequirements.height) {
					errors.push(
						`Image dimensions must be ${slotRequirements.width}x${slotRequirements.height}px. Current: ${metadata.width}x${metadata.height}px`
					);
				}
			} catch (error) {
				errors.push("Unable to read image metadata");
				logger.error("Error reading image metadata", { error, filename: file.originalname });
			}
		}

		// Check for malicious content (basic check)
		if (file.originalname.includes("..") || file.originalname.includes("/") || file.originalname.includes("\\")) {
			errors.push("Invalid filename");
		}

		return {
			isValid: errors.length === 0,
			errors,
		};
	}

	/**
	 * Process and save uploaded file
	 * @param {Object} file - Uploaded file object
	 * @param {Object} options - Processing options
	 * @returns {Promise<Object>} Processing result
	 */
	async processAndSaveFile(file, options = {}) {
		try {
			// Generate unique filename
			const fileExtension = path.extname(file.originalname).toLowerCase();
			const uniqueId = crypto.randomUUID();
			const timestamp = Date.now();
			const filename = `${timestamp}_${uniqueId}${fileExtension}`;
			const filepath = path.join(this.uploadDir, filename);

			let processedBuffer = file.buffer;

			// Process image if needed
			if (options.resize || options.optimize) {
				const sharpInstance = sharp(file.buffer);

				// Resize if dimensions provided
				if (options.resize && options.resize.width && options.resize.height) {
					sharpInstance.resize(options.resize.width, options.resize.height, {
						fit: options.resize.fit || "cover",
						position: options.resize.position || "center",
					});
				}

				// Optimize image
				if (options.optimize) {
					if (file.mimetype === "image/jpeg" || file.mimetype === "image/jpg") {
						sharpInstance.jpeg({ quality: options.quality || 85, progressive: true });
					} else if (file.mimetype === "image/png") {
						sharpInstance.png({ quality: options.quality || 85, progressive: true });
					} else if (file.mimetype === "image/webp") {
						sharpInstance.webp({ quality: options.quality || 85 });
					}
				}

				processedBuffer = await sharpInstance.toBuffer();
			}

			// Save file
			await fs.writeFile(filepath, processedBuffer);

			// Get file stats
			const stats = await fs.stat(filepath);

			// Get image metadata
			const metadata = await sharp(filepath).metadata();

			const result = {
				filename,
				originalName: file.originalname,
				filepath,
				url: `/uploads/ads/${filename}`,
				size: stats.size,
				mimetype: file.mimetype,
				width: metadata.width,
				height: metadata.height,
				uploadedAt: new Date(),
			};

			logger.info("File processed and saved successfully", {
				filename,
				originalName: file.originalname,
				size: stats.size,
			});

			return result;
		} catch (error) {
			logger.error("Error processing and saving file", { error, filename: file.originalname });
			throw error;
		}
	}

	/**
	 * Delete uploaded file
	 * @param {string} filename - Filename to delete
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteFile(filename) {
		try {
			const filepath = path.join(this.uploadDir, filename);
			await fs.unlink(filepath);

			logger.info("File deleted successfully", { filename });
			return true;
		} catch (error) {
			if (error.code === "ENOENT") {
				logger.warn("File not found for deletion", { filename });
				return true; // File doesn't exist, consider it deleted
			}

			logger.error("Error deleting file", { error, filename });
			return false;
		}
	}

	/**
	 * Generate thumbnail
	 * @param {string} filepath - Original file path
	 * @param {Object} options - Thumbnail options
	 * @returns {Promise<Object>} Thumbnail info
	 */
	async generateThumbnail(filepath, options = {}) {
		try {
			const { width = 150, height = 150, quality = 80 } = options;

			const originalFilename = path.basename(filepath);
			const fileExtension = path.extname(originalFilename);
			const baseName = path.basename(originalFilename, fileExtension);
			const thumbnailFilename = `${baseName}_thumb${fileExtension}`;
			const thumbnailPath = path.join(this.uploadDir, thumbnailFilename);

			await sharp(filepath)
				.resize(width, height, { fit: "cover", position: "center" })
				.jpeg({ quality })
				.toFile(thumbnailPath);

			const stats = await fs.stat(thumbnailPath);

			return {
				filename: thumbnailFilename,
				filepath: thumbnailPath,
				url: `/uploads/ads/${thumbnailFilename}`,
				size: stats.size,
				width,
				height,
			};
		} catch (error) {
			logger.error("Error generating thumbnail", { error, filepath });
			throw error;
		}
	}

	/**
	 * Get file info
	 * @param {string} filename - Filename
	 * @returns {Promise<Object>} File information
	 */
	async getFileInfo(filename) {
		try {
			const filepath = path.join(this.uploadDir, filename);
			const stats = await fs.stat(filepath);
			const metadata = await sharp(filepath).metadata();

			return {
				filename,
				filepath,
				url: `/uploads/ads/${filename}`,
				size: stats.size,
				width: metadata.width,
				height: metadata.height,
				format: metadata.format,
				createdAt: stats.birthtime,
				modifiedAt: stats.mtime,
			};
		} catch (error) {
			logger.error("Error getting file info", { error, filename });
			throw error;
		}
	}

	/**
	 * Clean up old files
	 * @param {number} maxAgeInDays - Maximum age in days
	 * @returns {Promise<number>} Number of files deleted
	 */
	async cleanupOldFiles(maxAgeInDays = 30) {
		try {
			const files = await fs.readdir(this.uploadDir);
			const cutoffDate = new Date(Date.now() - maxAgeInDays * 24 * 60 * 60 * 1000);
			let deletedCount = 0;

			for (const filename of files) {
				const filepath = path.join(this.uploadDir, filename);
				const stats = await fs.stat(filepath);

				if (stats.birthtime < cutoffDate) {
					await fs.unlink(filepath);
					deletedCount++;
					logger.info("Old file deleted during cleanup", { filename, age: stats.birthtime });
				}
			}

			logger.info("File cleanup completed", { deletedCount, maxAgeInDays });
			return deletedCount;
		} catch (error) {
			logger.error("Error during file cleanup", { error, maxAgeInDays });
			throw error;
		}
	}

	/**
	 * Validate image content (basic security check)
	 * @param {Buffer} buffer - File buffer
	 * @returns {Promise<boolean>} Whether file is safe
	 */
	async validateImageContent(buffer) {
		try {
			// Use sharp to validate that it's actually an image
			const metadata = await sharp(buffer).metadata();

			// Check if it has valid image properties
			if (!metadata.width || !metadata.height || !metadata.format) {
				return false;
			}

			// Check for suspicious metadata
			if (metadata.exif && metadata.exif.length > 10000) {
				logger.warn("Image has suspiciously large EXIF data");
				return false;
			}

			return true;
		} catch (error) {
			logger.warn("Image validation failed", { error });
			return false;
		}
	}
}

module.exports = FileUploadService;
