import { fetchWithFallback } from "@/lib/utils";
import { cookies } from "next/headers";
import SettingsClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

export default async function ApplicationsPage() {
	const cookieHeader = (await cookies()).toString();

	const [userRes, compRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/me`, { headers: { Cookie: cookieHeader } }),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/company`, { headers: { Cookie: cookieHeader } }),
	]);

	if (userRes.error) console.error("User fetch error:", userRes.error);
	if (compRes.error) console.error("Company fetch error:", compRes.error);

	return <SettingsClientWrapper profile={userRes.data || []} company={compRes.data || []} />;
}
