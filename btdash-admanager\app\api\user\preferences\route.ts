import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const GET = async function getUserPreferences(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/preferences`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const preferences = await response.json();
		return NextResponse.json(preferences);
	} catch (error) {
		console.error("Error fetching preferences:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};

export const POST = async function createUserPreferences(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const body = await request.json();

		const response = await fetch(`${process.env.API_BASE_URL}/user/preferences`, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const preferences = await response.json();
		return NextResponse.json({ data: preferences }, { status: 201 });
	} catch (error) {
		console.error("Error creating preferences:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};

export const PUT = async function updateUserPreferences(request: Request) {
	try {
		const session = await auth0.getSession();

		if (!session) {
			return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
		}

		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const body = await request.json();

		const response = await fetch(`${process.env.API_BASE_URL}/user/preferences`, {
			method: "PUT",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				...body,
				user_id: session.user.sub,
			}),
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const preferences = await response.json();
		return NextResponse.json({ data: preferences });
	} catch (error) {
		console.error("Error updating preferences:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};
