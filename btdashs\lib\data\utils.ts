import { Subnet } from "@/lib/db/models";
import { cacheManager } from "../cache/cache-manager";
import { slugify } from "../utils";

/**
 * Fetch data with retry logic and exponential backoff.
 * Handles 401 errors by redirecting to logout.
 * Returns JSON parsed response or error.
 *
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @param retries - Number of retries on failure
 * @param retryDelay - Initial delay between retries in milliseconds
 */
export async function fetchWithFallback(
	url: string,
	options: RequestInit = {},
	retries: number = 3,
	retryDelay: number = 1000
): Promise<any> {
	let lastError: Error | null = null;

	for (let i = 0; i < retries; i++) {
		try {
			const res = await fetch(url, {
				cache: "no-store",
				...options,
			});

			// Handle 401 specifically
			if (res.status === 401) {
				if (typeof window !== "undefined") {
					window.location.href = `/auth/logout?returnTo=${encodeURIComponent(window.location.pathname)}`;
				}
				// Return immediately without retrying
				return { success: false, data: null, message: "Unauthorized", error: new Error("Unauthorized") };
			}

			if (!res.ok) {
				throw new Error(`Failed to fetch ${url}: HTTP ${res.status}`);
			}

			const result = await res.json();

			// Handle standardized response format
			if (result.success) {
				return result; // Return the full response with success, data, message
			} else {
				return {
					success: false,
					data: null,
					message: result.message || "Request failed",
					error: new Error(result.message || "Request failed"),
					errors: result.errors,
				};
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));

			// Skip retries for 401 errors
			if (lastError.message.includes("401")) {
				return { success: false, data: null, message: "Unauthorized", error: lastError };
			}

			console.error(`Attempt ${i + 1} failed for ${url}:`, error);

			// Don't wait on the last attempt
			if (i < retries - 1) {
				await new Promise((resolve) => setTimeout(resolve, retryDelay));
				retryDelay *= 2; // Exponential backoff
			}
		}
	}

	console.error(`All ${retries} attempts failed for ${url}:`, lastError);

	return {
		success: false,
		data: null,
		message: `All ${retries} attempts failed`,
		error: lastError,
	};
}

/**
 * SERVER-SIDE ONLY
 * Secure fetch that injects INTERNAL_API_KEY.
 * Returns full fetch Response (not JSON parsed).
 */
export async function fetchInternal(url: string, options: RequestInit = {}): Promise<Response> {
	const headers = new Headers(options.headers);
	headers.set("x-internal-api-key", process.env.INTERNAL_API_KEY!);

	const finalOptions: RequestInit = {
		...options,
		headers,
		cache: "no-store",
	};

	return fetch(url, finalOptions);
}

const API_BASE = process.env.API_BASE_URL!;

/**
 * Get all items of a specific resource type (with caching)
 * @param resourceType - The type of resource (e.g., 'products', 'users')
 * @returns Promise with the array of items
 */
export async function getAllItems<T = any>(resourceType: string): Promise<T[]> {
	try {
		const cached = await cacheManager.getList(resourceType);
		if (cached) return cached as T[];

		const res = await fetchInternal(`${API_BASE}/${resourceType}`);
		if (!res.ok) throw new Error(`Failed to fetch ${resourceType} list`);

		const json = await res.json();

		// Handle standardized response format
		if (json.success) {
			await cacheManager.setList(resourceType, json.data);
			return json.data;
		} else {
			throw new Error(json.message || `Failed to fetch ${resourceType}`);
		}
	} catch (error) {
		console.error(`Error fetching ${resourceType}:`, error);

		// Retry once
		const res = await fetchInternal(`${API_BASE}/${resourceType}`);
		if (!res.ok) throw new Error(`Failed to fetch ${resourceType} list`);
		const json = await res.json();

		// Handle standardized response format in retry
		if (json.success) {
			return json.data;
		} else {
			throw new Error(json.message || `Failed to fetch ${resourceType}`);
		}
	}
}

/**
 * Get single item by ID (with caching)
 * @param resourceType - The type of resource (e.g., 'products', 'users')
 * @param id - The ID of the item to fetch
 * @returns Promise with the requested item
 */
export async function getItemById<T = any>(resourceType: string, id: string): Promise<T> {
	try {
		// Check single item cache
		const cached = await cacheManager.getSingle(resourceType, id);
		if (cached) return cached as T;

		// Check if item exists in list cache
		const listCache = await cacheManager.getList(resourceType);
		if (listCache) {
			const found = listCache.find((item: any) => {
				return String(item.id) === id;
			});
			if (found) return found as T;
		}

		// Fetch from API if not in cache
		const res = await fetchInternal(`${API_BASE}/${resourceType}/${id}`);
		if (!res.ok) throw new Error(`Failed to fetch ${resourceType}`);

		const json = await res.json();

		// Handle standardized response format
		if (json.success) {
			await cacheManager.setSingle(resourceType, id, json.data);
			return json.data;
		} else {
			throw new Error(json.message || `Failed to fetch ${resourceType} with ID ${id}`);
		}
	} catch (error) {
		console.error(`Error fetching ${resourceType} with ID ${id}:`, error);

		// Retry once
		const res = await fetchInternal(`${API_BASE}/${resourceType}/${id}`);
		if (!res.ok) throw new Error(`Failed to fetch ${resourceType} with ID ${id}`);
		const json = await res.json();

		// Handle standardized response format in retry
		if (json.success) {
			return json.data;
		} else {
			throw new Error(json.message || `Failed to fetch ${resourceType} with ID ${id}`);
		}
	}
}

/**
 * Get subnet information by netuid (with caching)
 * @param netuid - The network UID of the subnet to fetch
 * @returns Promise with the requested subnet
 */
export async function getSubnetByNetuid(netuid: number): Promise<Subnet> {
	const resourceType = "subnets";

	try {
		// Check single item cache (using netuid as ID)
		const cached = await cacheManager.getSingle(resourceType, String(netuid));
		if (cached) return cached as Subnet;

		// Try to find in list cache
		const listCache = await cacheManager.getList(resourceType);
		if (listCache) {
			const found = listCache.find((item: any) => {
				return Number(item.netuid) === netuid;
			});
			if (found) return found as Subnet;
		}

		// Fetch from API if not in cache
		const res = await fetchInternal(`${API_BASE}/${resourceType}/${netuid}`);
		if (!res.ok) throw new Error(`Failed to fetch ${resourceType}`);

		const json = await res.json();

		// Handle standardized response format
		if (json.success) {
			await cacheManager.setSingle(resourceType, String(netuid), json.data);
			return json.data;
		} else {
			throw new Error(json.message || `Failed to fetch subnet with netuid ${netuid}`);
		}
	} catch (error) {
		console.error(`Error fetching subnet with netuid ${netuid}:`, error);

		// Retry once
		const res = await fetchInternal(`${API_BASE}/${resourceType}/${netuid}`);
		if (!res.ok) throw new Error(`Failed to fetch ${resourceType}`);
		const json = await res.json();

		// Handle standardized response format in retry
		if (json.success) {
			return json.data;
		} else {
			throw new Error(json.message || `Failed to fetch subnet with netuid ${netuid}`);
		}
	}
}

/**
 * Get ID from slug for any resource type (using 'name' field for slug matching)
 * @param resourceType - The type of resource (e.g., 'products', 'companies')
 * @param slug - The slug to search for
 * @returns Promise with the found ID
 */
export async function getIdFromSlug(resourceType: string, slug: string): Promise<string> {
	const items = await getAllItems(resourceType);

	const found = items.find((item) => {
		return item.name && slugify(item.name) === slug;
	});

	if (!found) {
		throw new Error(`No ${resourceType} found with slug "${slug}"`);
	}

	return String(found.id);
}
