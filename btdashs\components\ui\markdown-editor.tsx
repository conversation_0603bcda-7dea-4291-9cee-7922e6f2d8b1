"use client";

import type React from "react";

import { Button } from "@/components/ui/button";
import { debounce } from "@/lib/utils";
import { Bold, Heading1, Heading2, Heading3, Italic, LinkIcon, List, X } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import rehypeParse from "rehype-parse";
import rehypeRemark from "rehype-remark";
import rehypeSanitize from "rehype-sanitize";
import rehypeStringify from "rehype-stringify";
import remarkParse from "remark-parse";
import remarkRehype from "remark-rehype";
import remarkStringify from "remark-stringify";
import { unified } from "unified";

interface MarkdownEditorProps {
	initialValue?: string;
	placeholder?: string;
	onChange?: (value: string) => void;
	height?: string;
	className?: string;
}

// Helper function to save cursor position
function saveCursorPosition(containerEl: HTMLElement) {
	const selection = window.getSelection();
	if (!selection || selection.rangeCount === 0) return null;

	const range = selection.getRangeAt(0);
	const preSelectionRange = range.cloneRange();
	preSelectionRange.selectNodeContents(containerEl);
	preSelectionRange.setEnd(range.startContainer, range.startOffset);

	const start = preSelectionRange.toString().length;
	const end = start + range.toString().length;

	return {
		start,
		end,
		selectedText: range.toString(),
		// Save the actual range for more precise restoration
		range: {
			startContainer: range.startContainer,
			startOffset: range.startOffset,
			endContainer: range.endContainer,
			endOffset: range.endOffset,
		},
	};
}

// Helper function to restore cursor position
function restoreCursorPosition(
	containerEl: HTMLElement,
	savedPosition: {
		start: number;
		end: number;
		range?: {
			startContainer: Node;
			startOffset: number;
			endContainer: Node;
			endOffset: number;
		};
	} | null
) {
	if (!savedPosition) return;

	const selection = window.getSelection();
	if (!selection) return;

	// If we have the actual range information, use it for more precise restoration
	if (savedPosition.range) {
		try {
			const range = document.createRange();
			range.setStart(savedPosition.range.startContainer, savedPosition.range.startOffset);
			range.setEnd(savedPosition.range.endContainer, savedPosition.range.endOffset);
			selection.removeAllRanges();
			selection.addRange(range);
			return;
		} catch (e) {
			console.error("Failed to restore precise cursor position, falling back to character index method");
		}
	}

	// Fall back to the character index method
	let charIndex = 0;
	let foundStart = false;
	let foundEnd = false;
	const range = document.createRange();

	function traverseNodes(node: Node) {
		if (foundEnd) return;
		if (!savedPosition) return;

		if (node.nodeType === Node.TEXT_NODE) {
			const textNode = node as Text;
			const nextCharIndex = charIndex + textNode.length;

			if (!foundStart && savedPosition.start >= charIndex && savedPosition.start <= nextCharIndex) {
				range.setStart(textNode, savedPosition.start - charIndex);
				foundStart = true;
			}

			if (!foundEnd && savedPosition.end >= charIndex && savedPosition.end <= nextCharIndex) {
				range.setEnd(textNode, savedPosition.end - charIndex);
				foundEnd = true;
			}

			charIndex = nextCharIndex;
		} else {
			for (let i = 0; i < node.childNodes.length; i++) {
				traverseNodes(node.childNodes[i]);
				if (foundEnd) break;
			}
		}
	}

	traverseNodes(containerEl);

	if (foundStart) {
		selection.removeAllRanges();
		selection.addRange(range);
	}
}

// Helper function to get the current block element
function getCurrentBlockElement() {
	const selection = window.getSelection();
	if (!selection || selection.rangeCount === 0) return null;

	let node = selection.anchorNode;
	while (node && node.nodeType !== Node.ELEMENT_NODE) {
		node = node.parentNode;
	}

	return node as HTMLElement | null;
}

export default function MarkdownEditor({
	initialValue = "",
	placeholder = "Write your markdown here...",
	onChange,
	height = "h-[500px]",
	className = "",
}: MarkdownEditorProps) {
	const [markdown, setMarkdown] = useState(initialValue);
	const [isInitialized, setIsInitialized] = useState(false);
	const [isBold, setIsBold] = useState(false);
	const [isItalic, setIsItalic] = useState(false);
	const [isList, setIsList] = useState(false);
	const [isLink, setIsLink] = useState(false);
	const [isH1, setIsH1] = useState(false);
	const [isH2, setIsH2] = useState(false);
	const [isH3, setIsH3] = useState(false);
	const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
	const [linkText, setLinkText] = useState("");
	const [linkUrl, setLinkUrl] = useState("");
	const editorRef = useRef<HTMLDivElement>(null);
	const lastHtmlRef = useRef<string>("");
	const isUpdatingRef = useRef(false);
	const savedSelectionRef = useRef<{
		start: number;
		end: number;
		selectedText: string;
		range?: {
			startContainer: Node;
			startOffset: number;
			endContainer: Node;
			endOffset: number;
		};
	} | null>(null);
	const [isParagraph, setIsParagraph] = useState(false);

	// Check current formatting state
	const checkFormatting = useCallback(() => {
		if (!document.queryCommandEnabled) return;

		setIsBold(document.queryCommandState("bold"));
		setIsItalic(document.queryCommandState("italic"));
		setIsList(document.queryCommandState("insertUnorderedList"));

		// Check if selection is within a link
		const selection = window.getSelection();
		if (selection && selection.rangeCount > 0) {
			const range = selection.getRangeAt(0);
			const parentElement = range.commonAncestorContainer.parentElement;
			setIsLink(parentElement?.tagName === "A" || parentElement?.closest("a") !== null);
		} else {
			setIsLink(false);
		}

		// Check for headings
		const currentBlock = getCurrentBlockElement();
		if (currentBlock) {
			const tagName = currentBlock.tagName.toLowerCase();
			setIsH1(tagName === "h1");
			setIsH2(tagName === "h2");
			setIsH3(tagName === "h3");
		} else {
			setIsH1(false);
			setIsH2(false);
			setIsH3(false);
		}
	}, []);

	// Convert markdown to HTML only on initial load and when markdown prop changes
	useEffect(() => {
		const processMarkdown = async () => {
			if (!editorRef.current) return;

			try {
				const result = await unified()
					.use(remarkParse)
					.use(remarkRehype)
					.use(rehypeSanitize)
					.use(rehypeStringify)
					.process(markdown || placeholder);

				const newHtml = String(result);

				// Only update if HTML has actually changed
				if (newHtml !== lastHtmlRef.current) {
					lastHtmlRef.current = newHtml;
					isUpdatingRef.current = true;

					// Save cursor position
					const savedPosition = saveCursorPosition(editorRef.current);

					// Update content
					editorRef.current.innerHTML = newHtml;

					// Restore cursor position
					restoreCursorPosition(editorRef.current, savedPosition);

					isUpdatingRef.current = false;
				}

				setIsInitialized(true);
			} catch (error) {
				console.error("Error processing markdown:", error);
			}
		};

		if (!isInitialized) {
			processMarkdown();
		}
	}, [markdown, placeholder, isInitialized]);

	// Check formatting on initialization
	useEffect(() => {
		if (isInitialized && editorRef.current) {
			checkFormatting();
		}
	}, [isInitialized, checkFormatting]);

	// Add event listeners for selection changes
	useEffect(() => {
		const editor = editorRef.current;
		if (!editor) return;

		const handleSelectionChange = () => {
			checkFormatting();
		};

		document.addEventListener("selectionchange", handleSelectionChange);
		editor.addEventListener("click", handleSelectionChange);
		editor.addEventListener("keyup", handleSelectionChange);

		return () => {
			document.removeEventListener("selectionchange", handleSelectionChange);
			editor?.removeEventListener("click", handleSelectionChange);
			editor?.removeEventListener("keyup", handleSelectionChange);
		};
	}, [checkFormatting]);

	// Debounced function to convert HTML back to markdown
	const convertHtmlToMarkdown = useCallback(
		debounce(async (htmlContent: string) => {
			try {
				const result = await unified()
					.use(rehypeParse, { fragment: true })
					.use(rehypeRemark)
					.use(remarkStringify)
					.process(htmlContent);

				const newMarkdown = String(result).trim();
				setMarkdown(newMarkdown);

				if (onChange) {
					onChange(newMarkdown);
				}
			} catch (error) {
				console.error("Error converting HTML to markdown:", error);
			}
		}, 300),
		[onChange]
	);

	// Handle content changes
	const handleInput = () => {
		if (!editorRef.current || isUpdatingRef.current) return;

		const htmlContent = editorRef.current.innerHTML;
		lastHtmlRef.current = htmlContent;
		convertHtmlToMarkdown(htmlContent);
		checkFormatting();
	};

	// Open link dialog
	const openLinkDialog = () => {
		if (!editorRef.current) return;

		// Focus the editor
		editorRef.current.focus();

		// Save the current selection with precise range information
		const savedSelection = saveCursorPosition(editorRef.current);
		savedSelectionRef.current = savedSelection;

		// If text is selected, use it as the link text
		if (savedSelection && savedSelection.selectedText) {
			setLinkText(savedSelection.selectedText);
		} else {
			setLinkText("");
		}

		setLinkUrl("");
		setIsLinkDialogOpen(true);
	};

	// Insert link
	const insertLink = () => {
		if (!editorRef.current) return;

		// Focus the editor
		editorRef.current.focus();

		// Create the link HTML
		const linkHtml = `<a href="${linkUrl}" target="_blank" rel="noopener noreferrer">${linkText}</a>`;

		// Get the current selection
		const selection = window.getSelection();

		if (selection && selection.rangeCount > 0) {
			// Restore the saved selection
			if (savedSelectionRef.current) {
				restoreCursorPosition(editorRef.current, savedSelectionRef.current);
			}

			// Get the current range
			const range = selection.getRangeAt(0);

			// Delete any selected text
			range.deleteContents();

			// Create a temporary element to hold our HTML
			const tempDiv = document.createElement("div");
			tempDiv.innerHTML = linkHtml;

			// Extract the nodes from the temporary element
			const fragment = document.createDocumentFragment();
			let node, lastNode;
			while ((node = tempDiv.firstChild)) {
				lastNode = fragment.appendChild(node);
			}

			// Insert the fragment at the current position
			range.insertNode(fragment);

			// Move the cursor to the end of the inserted content
			if (lastNode) {
				range.setStartAfter(lastNode);
				range.setEndAfter(lastNode);
				selection.removeAllRanges();
				selection.addRange(range);
			}
		}

		// Reset the dialog
		setIsLinkDialogOpen(false);
		setLinkText("");
		setLinkUrl("");
		savedSelectionRef.current = null;

		// Update the markdown
		handleInput();
	};

	// Apply formatting
	const applyFormatting = (command: string) => {
		if (!editorRef.current) return;

		// Focus the editor
		editorRef.current.focus();

		// Apply the formatting command
		switch (command) {
			case "bold":
				document.execCommand("bold", false);
				break;
			case "italic":
				document.execCommand("italic", false);
				break;
			case "list":
				document.execCommand("insertUnorderedList", false);
				break;
			case "link":
				openLinkDialog();
				return; // Return early to avoid handleInput call
			case "h1":
				if (isH1) {
					document.execCommand("formatBlock", false, "<p>");
				} else {
					document.execCommand("formatBlock", false, "<h1>");
				}
				break;
			case "h2":
				if (isH2) {
					document.execCommand("formatBlock", false, "<p>");
				} else {
					document.execCommand("formatBlock", false, "<h2>");
				}
				break;
			case "h3":
				if (isH3) {
					document.execCommand("formatBlock", false, "<p>");
				} else {
					document.execCommand("formatBlock", false, "<h3>");
				}
				break;
			default:
				break;
		}

		// Check formatting state after applying
		checkFormatting();

		// Trigger input handler to update markdown
		handleInput();
	};

	// Handle paste to strip formatting
	const handlePaste = (e: React.ClipboardEvent) => {
		e.preventDefault();
		const text = e.clipboardData.getData("text/plain");
		document.execCommand("insertText", false, text);
	};

	// Handle link dialog form submission
	const handleLinkFormSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		insertLink();
	};

	return (
		<>
			<div className={`rounded-lg overflow-hidden ${className}`} style={{ backgroundColor: "#0a0e17" }}>
				<div className="p-2 flex flex-wrap border-b border-gray-800">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("bold")}
						className={`${
							isBold ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<Bold className="h-5 w-5" />
						<span className="sr-only">Bold</span>
					</Button>

					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("italic")}
						className={`${
							isItalic ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<Italic className="h-5 w-5" />
						<span className="sr-only">Italic</span>
					</Button>

					<div className="mx-1 border-r border-gray-700"></div>

					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("h1")}
						className={`${
							isH1 ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<Heading1 className="h-5 w-5" />
						<span className="sr-only">Heading 1</span>
					</Button>

					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("h2")}
						className={`${
							isH2 ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<Heading2 className="h-5 w-5" />
						<span className="sr-only">Heading 2</span>
					</Button>

					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("h3")}
						className={`${
							isH3 ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<Heading3 className="h-5 w-5" />
						<span className="sr-only">Heading 3</span>
					</Button>

					<div className="mx-1 border-r border-gray-700"></div>

					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("list")}
						className={`${
							isList ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<List className="h-5 w-5" />
						<span className="sr-only">List</span>
					</Button>

					<Button
						variant="ghost"
						size="icon"
						onClick={() => applyFormatting("link")}
						className={`${
							isLink ? "bg-gray-700 text-white" : "text-gray-400 hover:text-white hover:bg-gray-800"
						}`}
					>
						<LinkIcon className="h-5 w-5" />
						<span className="sr-only">Link</span>
					</Button>
				</div>

				<div
					ref={editorRef}
					contentEditable
					className={`w-full ${height} overflow-auto p-4 focus:outline-none text-gray-200 markdown-body`}
					style={{ backgroundColor: "#0a0e17" }}
					onInput={handleInput}
					onPaste={handlePaste}
					suppressContentEditableWarning
				/>
			</div>

			{isLinkDialogOpen && (
				<div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
					<div className="bg-[#0a0e17] rounded-lg w-full max-w-md p-6 relative">
						<button
							onClick={() => setIsLinkDialogOpen(false)}
							className="absolute top-4 right-4 text-gray-400 hover:text-white"
						>
							<X className="h-5 w-5" />
							<span className="sr-only">Close</span>
						</button>

						<h2 className="text-xl font-semibold text-white mb-6">Insert Link</h2>

						<form onSubmit={handleLinkFormSubmit}>
							<div className="space-y-6">
								<div className="space-y-2">
									<label htmlFor="link-text" className="block text-white">
										Link Text
									</label>
									<input
										id="link-text"
										value={linkText}
										onChange={(e) => setLinkText(e.target.value)}
										className="w-full bg-[#0a0e17] border border-gray-800 rounded-md p-3 text-white focus:outline-none focus:ring-1 focus:ring-gray-600"
										placeholder="Enter link text"
										autoFocus
									/>
								</div>

								<div className="space-y-2">
									<label htmlFor="link-url" className="block text-white">
										URL
									</label>
									<input
										id="link-url"
										value={linkUrl}
										onChange={(e) => setLinkUrl(e.target.value)}
										className="w-full bg-[#0a0e17] border border-gray-800 rounded-md p-3 text-white focus:outline-none focus:ring-1 focus:ring-gray-600"
										placeholder="https://example.com"
									/>
								</div>
							</div>

							<div className="flex justify-end space-x-3 mt-8">
								<button
									type="button"
									onClick={() => setIsLinkDialogOpen(false)}
									className="px-4 py-2 rounded-md bg-[#1a1f2a] text-white hover:bg-gray-800"
								>
									Cancel
								</button>
								<button
									type="submit"
									className="px-4 py-2 rounded-md bg-gray-300 text-gray-900 hover:bg-gray-200"
								>
									Insert Link
								</button>
							</div>
						</form>
					</div>
				</div>
			)}
		</>
	);
}
