import { NextResponse } from 'next/server';
import { format, subDays } from 'date-fns';
import axios from 'axios';

const GITHUB_TOKEN = process.env.GITHUB_TOKEN;

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const owner = searchParams.get('owner');
  const repo = searchParams.get('repo');

  if (!owner || !repo) {
    return NextResponse.json({ error: 'Missing owner or repo' }, { status: 400 });
  }

  try {
    const since = format(subDays(new Date(), 365), "yyyy-MM-dd'T'HH:mm:ss'Z'");
    let page = 1;
    let allCommits: any[] = [];

    while (true) {
      const url = `https://api.github.com/repos/${owner}/${repo}/commits?since=${since}&per_page=100&page=${page}`;
      const res = await axios.get<{ length: number }[]>(url, {
        headers: {
          Authorization: GITHUB_TOKEN ? `Bearer ${GITHUB_TOKEN}` : '',
          Accept: 'application/vnd.github.v3+json',
        }
      });

      if (res.data.length === 0) break;
      allCommits = allCommits.concat(res.data);
      page++;
    }

    return NextResponse.json({ commits: allCommits });
  } catch (err) {
    console.error(err);
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}
