"use client";

import { JobsSection } from "@/components/jobs/jobs-section";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { Job } from "@/lib/db/models";
import { Briefcase, PlusCircle } from "lucide-react";
import Link from "next/link";

interface CompanyJobsProps {
	jobs: Job[];
	companyId: string | number;
	authorized_job_admin?: boolean;
	limit?: number;
}

export function CompanyJobs({ jobs = [], companyId, authorized_job_admin = false, limit = 4 }: CompanyJobsProps) {
	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold flex items-center gap-2">
					<Briefcase className="h-5 w-5 text-emerald-500" />
					Career Opportunities
				</h2>
				{authorized_job_admin && (
					<Link href="/profile#jobs">
						<Button size="sm" className="flex items-center gap-1 bg-emerald-500 hover:bg-emerald-600">
							<PlusCircle className="h-4 w-4" />
							<span>Create Job</span>
						</Button>
					</Link>
				)}
				{!authorized_job_admin && (
					<Button
						size="sm"
						className="flex items-center gap-1 bg-gray-300 text-gray-500 cursor-not-allowed"
						disabled
					>
						<PlusCircle className="h-4 w-4" />
						<span>Create Job</span>
					</Button>
				)}
			</div>

			{jobs.length === 0 ? (
				<Card className="border border-dashed">
					<CardContent className="p-6 text-center">
						<Briefcase className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
						<h3 className="text-lg font-medium mb-2">No Open Positions</h3>
						<p className="text-muted-foreground mb-4">
							This company doesn't have any open positions at the moment.
						</p>
						{authorized_job_admin ? (
							<Link href={`/jobs/create?company=${companyId}`}>
								<Button size="sm" variant="outline">
									<PlusCircle className="h-4 w-4 mr-2" />
									Post a Job
								</Button>
							</Link>
						) : (
							<Button size="sm" variant="outline" disabled className="cursor-not-allowed">
								<PlusCircle className="h-4 w-4 mr-2" />
								Post a Job
							</Button>
						)}
					</CardContent>
				</Card>
			) : (
				<JobsSection title="" jobs={jobs} limit={limit} entityType="company" entityId={companyId} />
			)}
		</div>
	);
}
