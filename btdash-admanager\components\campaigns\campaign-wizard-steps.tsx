import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import useSWR from "swr";

interface CampaignFormData {
	name: string;
	description: string;
	total_budget: number | null;
	budget_cpc: number | null;
	budget_cpm: number | null;
	budget_type: "cpc" | "cpm" | "total";
	start_date: Date | null;
	end_date: Date | null;
	ad_title: string;
	ad_description: string;
	image_url: string;
	destination_url: string;
	slot_id: number | null;
	targeting: {
		countries: string[];
		devices: string[];
		languages: string[];
		interests: string[];
		age_ranges: string[];
	};
}

export function AdCreativeStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	return (
		<div className="space-y-4">
			<div>
				<Label htmlFor="ad-title">Ad Title *</Label>
				<Input
					id="ad-title"
					value={formData.ad_title}
					onChange={(e) => updateFormData({ ad_title: e.target.value })}
					placeholder="Enter ad title"
					maxLength={60}
				/>
				<p className="text-xs text-muted-foreground mt-1">{formData.ad_title.length}/60 characters</p>
			</div>
			<div>
				<Label htmlFor="ad-description">Ad Description</Label>
				<Textarea
					id="ad-description"
					value={formData.ad_description}
					onChange={(e) => updateFormData({ ad_description: e.target.value })}
					placeholder="Enter ad description"
					rows={3}
					maxLength={150}
				/>
				<p className="text-xs text-muted-foreground mt-1">{formData.ad_description.length}/150 characters</p>
			</div>
			<div>
				<Label htmlFor="image-url">Image URL</Label>
				<Input
					id="image-url"
					value={formData.image_url}
					onChange={(e) => updateFormData({ image_url: e.target.value })}
					placeholder="https://example.com/image.jpg"
					type="url"
				/>
				<p className="text-xs text-muted-foreground mt-1">
					Enter a direct URL to your ad image (for now, file upload will be added later)
				</p>
			</div>
			<div>
				<Label htmlFor="destination-url">Destination URL *</Label>
				<Input
					id="destination-url"
					value={formData.destination_url}
					onChange={(e) => updateFormData({ destination_url: e.target.value })}
					placeholder="https://example.com"
					type="url"
				/>
			</div>
			{formData.image_url && (
				<div>
					<Label>Preview</Label>
					<div className="border rounded-lg p-4 max-w-sm">
						<img
							src={formData.image_url}
							alt="Ad preview"
							className="w-full h-32 object-cover rounded mb-2"
							onError={(e) => {
								e.currentTarget.style.display = "none";
							}}
						/>
						<h4 className="font-medium text-sm">{formData.ad_title}</h4>
						<p className="text-xs text-muted-foreground">{formData.ad_description}</p>
					</div>
				</div>
			)}
		</div>
	);
}

export function TargetingStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	const countries = ["US", "CA", "GB", "AU", "DE", "FR", "JP", "BR", "IN", "CN"];
	const devices = ["desktop", "mobile", "tablet"];
	const languages = ["en", "es", "fr", "de", "pt", "ja", "zh", "hi", "ar", "ru"];
	const interests = [
		"technology",
		"finance",
		"health",
		"education",
		"entertainment",
		"sports",
		"travel",
		"food",
		"fashion",
		"gaming",
	];
	const ageRanges = ["18-24", "25-34", "35-44", "45-54", "55-64", "65+"];

	const toggleArrayItem = (array: string[], item: string) => {
		return array.includes(item) ? array.filter((i) => i !== item) : [...array, item];
	};

	return (
		<div className="space-y-4">
			<div>
				<Label className="text-sm font-medium">Countries</Label>
				<p className="text-xs text-muted-foreground mb-2">Select target countries (all selected by default)</p>
				<div className="grid grid-cols-3 md:grid-cols-6 gap-2">
					{countries.map((country) => (
						<div key={country} className="flex items-center space-x-2">
							<Checkbox
								id={`country-${country}`}
								checked={formData.targeting.countries.includes(country)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											countries: toggleArrayItem(formData.targeting.countries, country),
										},
									});
								}}
							/>
							<Label htmlFor={`country-${country}`} className="text-sm">
								{country}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-sm font-medium">Devices</Label>
				<p className="text-xs text-muted-foreground mb-2">Select target devices (all selected by default)</p>
				<div className="flex gap-4">
					{devices.map((device) => (
						<div key={device} className="flex items-center space-x-2">
							<Checkbox
								id={`device-${device}`}
								checked={formData.targeting.devices.includes(device)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											devices: toggleArrayItem(formData.targeting.devices, device),
										},
									});
								}}
							/>
							<Label htmlFor={`device-${device}`} className="text-sm capitalize">
								{device}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-sm font-medium">Languages</Label>
				<p className="text-xs text-muted-foreground mb-2">Select target languages (all selected by default)</p>
				<div className="grid grid-cols-3 md:grid-cols-6 gap-2">
					{languages.map((language) => (
						<div key={language} className="flex items-center space-x-2">
							<Checkbox
								id={`language-${language}`}
								checked={formData.targeting.languages.includes(language)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											languages: toggleArrayItem(formData.targeting.languages, language),
										},
									});
								}}
							/>
							<Label htmlFor={`language-${language}`} className="text-sm uppercase">
								{language}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-base font-medium">Interests</Label>
				<p className="text-sm text-muted-foreground mb-3">
					Select target interests (leave empty for all interests)
				</p>
				<div className="grid grid-cols-2 md:grid-cols-3 gap-2">
					{interests.map((interest) => (
						<div key={interest} className="flex items-center space-x-2">
							<Checkbox
								id={`interest-${interest}`}
								checked={formData.targeting.interests.includes(interest)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											interests: toggleArrayItem(formData.targeting.interests, interest),
										},
									});
								}}
							/>
							<Label htmlFor={`interest-${interest}`} className="text-sm capitalize">
								{interest}
							</Label>
						</div>
					))}
				</div>
			</div>

			<div>
				<Label className="text-base font-medium">Age Ranges</Label>
				<p className="text-sm text-muted-foreground mb-3">
					Select target age ranges (leave empty for all ages)
				</p>
				<div className="flex flex-wrap gap-2">
					{ageRanges.map((ageRange) => (
						<div key={ageRange} className="flex items-center space-x-2">
							<Checkbox
								id={`age-${ageRange}`}
								checked={formData.targeting.age_ranges.includes(ageRange)}
								onCheckedChange={() => {
									updateFormData({
										targeting: {
											...formData.targeting,
											age_ranges: toggleArrayItem(formData.targeting.age_ranges, ageRange),
										},
									});
								}}
							/>
							<Label htmlFor={`age-${ageRange}`} className="text-sm">
								{ageRange}
							</Label>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}

export function ReviewStep({ formData }: { formData: CampaignFormData }) {
	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-medium mb-4">Campaign Summary</h3>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label className="text-sm font-medium">Campaign Name</Label>
						<p className="text-sm">{formData.name}</p>
					</div>
					<div>
						<Label className="text-sm font-medium">Budget</Label>
						<p className="text-sm">
							{formData.budget_type === "total" && `$${formData.total_budget} total`}
							{formData.budget_type === "cpc" && `$${formData.budget_cpc} per click`}
							{formData.budget_type === "cpm" && `$${formData.budget_cpm} per 1000 impressions`}
						</p>
					</div>
					<div>
						<Label className="text-sm font-medium">Start Date</Label>
						<p className="text-sm">{formData.start_date?.toLocaleDateString()}</p>
					</div>
					<div>
						<Label className="text-sm font-medium">End Date</Label>
						<p className="text-sm">{formData.end_date?.toLocaleDateString()}</p>
					</div>
					<div>
						<Label className="text-sm font-medium">Ad Placement</Label>
						<p className="text-sm">{formData.slot_id ? `Slot ID: ${formData.slot_id}` : "Not selected"}</p>
					</div>
				</div>
			</div>

			<div>
				<h3 className="text-lg font-medium mb-4">Ad Creative</h3>
				<div className="flex gap-4">
					{formData.image_url && (
						<img src={formData.image_url} alt="Ad preview" className="w-24 h-24 object-cover rounded" />
					)}
					<div>
						<h4 className="font-medium">{formData.ad_title}</h4>
						<p className="text-sm text-muted-foreground">{formData.ad_description}</p>
						<p className="text-xs text-muted-foreground mt-1">{formData.destination_url}</p>
					</div>
				</div>
			</div>

			<div>
				<h3 className="text-lg font-medium mb-4">Targeting</h3>
				<div className="space-y-2">
					{formData.targeting.countries.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Countries:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.countries.map((country) => (
									<Badge key={country} variant="secondary">
										{country}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.devices.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Devices:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.devices.map((device) => (
									<Badge key={device} variant="secondary">
										{device}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.languages.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Languages:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.languages.map((language) => (
									<Badge key={language} variant="secondary">
										{language}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.interests.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Interests:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.interests.map((interest) => (
									<Badge key={interest} variant="secondary">
										{interest}
									</Badge>
								))}
							</div>
						</div>
					)}
					{formData.targeting.age_ranges.length > 0 && (
						<div>
							<Label className="text-sm font-medium">Age Ranges:</Label>
							<div className="flex flex-wrap gap-1 mt-1">
								{formData.targeting.age_ranges.map((ageRange) => (
									<Badge key={ageRange} variant="secondary">
										{ageRange}
									</Badge>
								))}
							</div>
						</div>
					)}
					{Object.values(formData.targeting).every((arr) => arr.length === 0) && (
						<p className="text-sm text-muted-foreground">
							No targeting restrictions - will show to all users
						</p>
					)}
				</div>
			</div>
		</div>
	);
}

export function PlacementStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	// Fetcher function for SWR
	const fetcher = (url: string) => fetch(url).then((res) => res.json());

	// Fetch available ad slots
	const { data: slotsData, error, isLoading } = useSWR("/api/placements", fetcher);

	const slots = slotsData?.data || [];

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading ad placements...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-8">
				<p className="text-sm text-destructive">Failed to load ad placements. Please try again.</p>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div>
				<Label className="text-base font-medium">Choose Ad Placement</Label>
				<p className="text-sm text-muted-foreground mb-4">
					Select where you want your ad to appear. Note the dimensions - you'll need this info for creating
					your ad creative in the next step.
				</p>
			</div>

			<div className="grid gap-4">
				{slots.map((slot: any) => (
					<div
						key={slot.id}
						className={`border rounded-lg p-4 cursor-pointer transition-colors ${
							formData.slot_id === slot.id
								? "border-primary bg-primary/5"
								: "border-border hover:border-primary/50"
						}`}
						onClick={() => updateFormData({ slot_id: slot.id })}
					>
						<div className="flex items-start justify-between">
							<div className="flex-1">
								<div className="flex items-center gap-2 mb-2">
									<input
										type="radio"
										name="placement"
										checked={formData.slot_id === slot.id}
										onChange={() => updateFormData({ slot_id: slot.id })}
										className="text-primary"
									/>
									<h3 className="font-medium">{slot.name}</h3>
									<Badge variant="secondary" className="text-xs">
										{slot.page === "all" ? "All Pages" : slot.page}
									</Badge>
								</div>
								<p className="text-sm text-muted-foreground mb-3">
									{slot.description || `Ad slot for ${slot.page} page`}
								</p>
								<div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
									<div>
										<span className="font-medium">Size:</span>
										<br />
										{slot.width} × {slot.height}px
									</div>
									<div>
										<span className="font-medium">Est. Views:</span>
										<br />
										{slot.estimated_views?.toLocaleString() || "N/A"}
									</div>
									<div>
										<span className="font-medium">CPC:</span>
										<br />${slot.price_cpc || "N/A"}
									</div>
									<div>
										<span className="font-medium">CPM:</span>
										<br />${slot.price_cpm || "N/A"}
									</div>
								</div>
							</div>
						</div>
					</div>
				))}
			</div>

			{slots.length === 0 && (
				<div className="text-center py-8">
					<p className="text-sm text-muted-foreground">No ad placements available at the moment.</p>
				</div>
			)}
		</div>
	);
}
