openapi: 3.0.3
info:
    title: BTDash API
    description: |
        BTDash API provides comprehensive endpoints for managing users, companies, jobs, events, and advertising campaigns.

        ## Authentication
        Most endpoints require JWT authentication via Auth0. Include the JW<PERSON> token in the Authorization header:
        ```
        Authorization: Bearer <your-jwt-token>
        ```

        ## Response Format
        All API responses follow a standardized format:

        **Success Response:**
        ```json
        {
          "success": true,
          "data": {...},
          "message": "Operation completed successfully"
        }
        ```

        **Error Response:**
        ```json
        {
          "success": false,
          "error": "Error description",
          "message": "User-friendly error message"
        }
        ```

        ## Rate Limiting
        API requests are rate-limited to prevent abuse. Current limits:
        - 100 requests per minute for authenticated users
        - 20 requests per minute for unauthenticated users

    version: 1.0.0
    contact:
        name: BTDash API Support
        email: <EMAIL>
    license:
        name: MIT
        url: https://opensource.org/licenses/MIT

servers:
    - url: http://localhost:3001
      description: Development server
    - url: https://api.btdash.com
      description: Production server

security:
    - BearerAuth: []

paths:
    # Health Check
    /health:
        get:
            tags:
                - Health
            summary: Health check endpoint
            description: Returns the health status of the API
            security: []
            responses:
                "200":
                    description: API is healthy
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        example: "OK"
                                    timestamp:
                                        type: string
                                        format: date-time
                                    uptime:
                                        type: number
                                        description: Uptime in seconds

    # User Management
    /api/user/sync:
        post:
            tags:
                - Users
            summary: Sync user from Auth0
            description: Internal endpoint to sync user data from Auth0 to the database
            security:
                - SyncAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - user_id
                                - email
                            properties:
                                user_id:
                                    type: string
                                    description: Auth0 user ID
                                    example: "auth0|507f1f77bcf86cd799439011"
                                email:
                                    type: string
                                    format: email
                                    example: "<EMAIL>"
                                name:
                                    type: string
                                    example: "John Doe"
                                picture:
                                    type: string
                                    format: uri
                                    example: "https://example.com/avatar.jpg"
            responses:
                "200":
                    description: User synced successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: "Synced"
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "500":
                    $ref: "#/components/responses/InternalServerError"

    /api/user/me:
        get:
            tags:
                - Users
            summary: Get current user data
            description: Retrieve the authenticated user's profile information
            responses:
                "200":
                    description: User data retrieved successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: "#/components/schemas/SuccessResponse"
                                    - type: object
                                      properties:
                                          data:
                                              $ref: "#/components/schemas/User"
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "500":
                    $ref: "#/components/responses/InternalServerError"

        put:
            tags:
                - Users
            summary: Update current user data
            description: Update the authenticated user's profile information
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/UserUpdateRequest"
            responses:
                "200":
                    description: User updated successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                        example: "Updated"
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "500":
                    $ref: "#/components/responses/InternalServerError"

    /api/user/company:
        get:
            tags:
                - Companies
            summary: Get user's company
            description: Retrieve the company associated with the authenticated user
            responses:
                "200":
                    description: Company data retrieved successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        oneOf:
                                            - $ref: "#/components/schemas/Company"
                                            - type: "null"
                                    role:
                                        type: string
                                        enum: [owner, admin, member]
                                        example: "owner"
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "404":
                    $ref: "#/components/responses/NotFound"
                "500":
                    $ref: "#/components/responses/InternalServerError"

        post:
            tags:
                - Companies
            summary: Create company with user as owner
            description: Create a new company and link the authenticated user as the owner
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/CompanyCreateRequest"
            responses:
                "201":
                    description: Company created successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        $ref: "#/components/schemas/Company"
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "404":
                    $ref: "#/components/responses/NotFound"
                "500":
                    $ref: "#/components/responses/InternalServerError"

    /api/user/company/{id}:
        put:
            tags:
                - Companies
            summary: Update company
            description: Update company information (requires admin or owner permissions)
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                      type: integer
                  description: Company ID
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/CompanyUpdateRequest"
            responses:
                "200":
                    description: Company updated successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        $ref: "#/components/schemas/Company"
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "403":
                    $ref: "#/components/responses/Forbidden"
                "404":
                    $ref: "#/components/responses/NotFound"
                "500":
                    $ref: "#/components/responses/InternalServerError"

        delete:
            tags:
                - Companies
            summary: Delete company
            description: Delete company (requires owner permissions)
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                      type: integer
                  description: Company ID
            responses:
                "200":
                    description: Company deleted successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                        example: true
                "401":
                    $ref: "#/components/responses/Unauthorized"
                "403":
                    $ref: "#/components/responses/Forbidden"
                "404":
                    $ref: "#/components/responses/NotFound"
                "500":
                    $ref: "#/components/responses/InternalServerError"

components:
    securitySchemes:
        BearerAuth:
            type: http
            scheme: bearer
            bearerFormat: JWT
            description: JWT token from Auth0
        SyncAuth:
            type: http
            scheme: bearer
            description: Internal sync secret for Auth0 webhook

    schemas:
        SuccessResponse:
            type: object
            properties:
                success:
                    type: boolean
                    example: true
                message:
                    type: string
                    example: "Operation completed successfully"

        ErrorResponse:
            type: object
            properties:
                success:
                    type: boolean
                    example: false
                error:
                    type: string
                    example: "Error description"
                message:
                    type: string
                    example: "User-friendly error message"

        User:
            type: object
            properties:
                id:
                    type: integer
                    example: 1
                auth0_user_id:
                    type: string
                    example: "auth0|507f1f77bcf86cd799439011"
                email:
                    type: string
                    format: email
                    example: "<EMAIL>"
                username:
                    type: string
                    example: "johndoe"
                first_name:
                    type: string
                    example: "John"
                last_name:
                    type: string
                    example: "Doe"
                image_url:
                    type: string
                    format: uri
                    example: "https://example.com/avatar.jpg"
                headline:
                    type: string
                    example: "Software Engineer"
                location:
                    type: string
                    example: "San Francisco, CA"
                bio:
                    type: string
                    example: "Passionate software engineer with 5 years of experience"
                phone:
                    type: string
                    example: "******-123-4567"
                expert_job_title:
                    type: string
                    example: "Senior Software Engineer"
                website:
                    type: string
                    format: uri
                    example: "https://johndoe.dev"
                skill_ids:
                    type: array
                    items:
                        type: integer
                    example: [1, 2, 3]
                created_at:
                    type: string
                    format: date-time
                updated_at:
                    type: string
                    format: date-time

        UserUpdateRequest:
            type: object
            properties:
                first_name:
                    type: string
                    example: "John"
                last_name:
                    type: string
                    example: "Doe"
                image_url:
                    type: string
                    format: uri
                    example: "https://example.com/avatar.jpg"
                headline:
                    type: string
                    example: "Software Engineer"
                location:
                    type: string
                    example: "San Francisco, CA"
                bio:
                    type: string
                    example: "Passionate software engineer"
                phone:
                    type: string
                    example: "******-123-4567"
                expert_job_title:
                    type: string
                    example: "Senior Software Engineer"
                website:
                    type: string
                    format: uri
                    example: "https://johndoe.dev"
                skill_ids:
                    type: array
                    items:
                        type: integer
                    example: [1, 2, 3]

        Company:
            type: object
            properties:
                id:
                    type: integer
                    example: 1
                name:
                    type: string
                    example: "Tech Corp"
                description:
                    type: string
                    example: "Leading technology company"
                logo_url:
                    type: string
                    format: uri
                    example: "https://example.com/logo.png"
                header_url:
                    type: string
                    format: uri
                    example: "https://example.com/header.jpg"
                website_url:
                    type: string
                    format: uri
                    example: "https://techcorp.com"
                location:
                    type: string
                    example: "San Francisco, CA"
                foundedyear:
                    type: integer
                    example: 2010
                teamsize:
                    type: string
                    example: "50-100"
                social_media:
                    type: object
                    properties:
                        twitter:
                            type: string
                            example: "@techcorp"
                        linkedin:
                            type: string
                            example: "company/techcorp"
                created_at:
                    type: string
                    format: date-time
                updated_at:
                    type: string
                    format: date-time

        CompanyCreateRequest:
            type: object
            required:
                - name
            properties:
                name:
                    type: string
                    example: "Tech Corp"
                description:
                    type: string
                    example: "Leading technology company"
                logo_url:
                    type: string
                    format: uri
                    example: "https://example.com/logo.png"
                header_url:
                    type: string
                    format: uri
                    example: "https://example.com/header.jpg"
                website_url:
                    type: string
                    format: uri
                    example: "https://techcorp.com"
                location:
                    type: string
                    example: "San Francisco, CA"
                foundedyear:
                    type: integer
                    example: 2010
                teamsize:
                    type: string
                    example: "50-100"
                social_media:
                    type: object
                    properties:
                        twitter:
                            type: string
                            example: "@techcorp"
                        linkedin:
                            type: string
                            example: "company/techcorp"

        CompanyUpdateRequest:
            type: object
            properties:
                name:
                    type: string
                    example: "Tech Corp"
                description:
                    type: string
                    example: "Leading technology company"
                logo_url:
                    type: string
                    format: uri
                    example: "https://example.com/logo.png"
                header_url:
                    type: string
                    format: uri
                    example: "https://example.com/header.jpg"
                website_url:
                    type: string
                    format: uri
                    example: "https://techcorp.com"
                location:
                    type: string
                    example: "San Francisco, CA"
                foundedyear:
                    type: integer
                    example: 2010
                teamsize:
                    type: string
                    example: "50-100"
                social_media:
                    type: object
                    properties:
                        twitter:
                            type: string
                            example: "@techcorp"
                        linkedin:
                            type: string
                            example: "company/techcorp"

    responses:
        Unauthorized:
            description: Authentication required or invalid token
            content:
                application/json:
                    schema:
                        allOf:
                            - $ref: "#/components/schemas/ErrorResponse"
                            - type: object
                              properties:
                                  message:
                                      example: "Unauthorized"

        Forbidden:
            description: Insufficient permissions
            content:
                application/json:
                    schema:
                        allOf:
                            - $ref: "#/components/schemas/ErrorResponse"
                            - type: object
                              properties:
                                  message:
                                      example: "Forbidden: Insufficient permissions"

        NotFound:
            description: Resource not found
            content:
                application/json:
                    schema:
                        allOf:
                            - $ref: "#/components/schemas/ErrorResponse"
                            - type: object
                              properties:
                                  message:
                                      example: "Resource not found"

        InternalServerError:
            description: Internal server error
            content:
                application/json:
                    schema:
                        allOf:
                            - $ref: "#/components/schemas/ErrorResponse"
                            - type: object
                              properties:
                                  message:
                                      example: "Internal server error"
