"use client";

import { ProductsGrid } from "@/components/products/products-grid";
import type { Category, Company, Product } from "@/lib/db/models";

interface CompanyApplicationsGridProps {
	products: Product[];
	categories?: Category[];
	companies?: Company[];
}

export function CompanyApplicationsGrid({ products, categories = [], companies = [] }: CompanyApplicationsGridProps) {
	return (
		<ProductsGrid
			products={products}
			categories={categories}
			companies={companies}
			emptyMessage="No applications available."
		/>
	);
}
