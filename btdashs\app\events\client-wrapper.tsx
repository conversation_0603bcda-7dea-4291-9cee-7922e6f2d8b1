"use client";

import { EventsGrid } from "@/components/events/events-grid";
import { FeaturedCompanies } from "@/components/events/featured-companies";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import type {
  Category,
  Company,
  Event,
  Product,
  Subnet,
} from "@/lib/db/models";
import { Calendar, ExternalLink, Plus, Search } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface EventsClientProps {
  events: (Event & {
    companies?: Company[];
    categories?: Category[];
    subnets?: Subnet[];
    products?: Product[];
  })[];
  authorized_events_admin?: boolean;
}

export default function EventsClientWrapper({
  events,
  authorized_events_admin = false,
}: EventsClientProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredEvents = events.filter((event) => {
    return (
      searchQuery === "" ||
      event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (event.description ?? "")
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      (event.location ?? "").toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  return (
    <div className="container py-8">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Main content */}
        <div className="lg:w-3/4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Calendar className="h-7 w-7" />
              Events
            </h1>
            {authorized_events_admin ? (
              <Button asChild>
                <Link href="/profile#events" className="gap-1">
                  <Plus className="h-4 w-4" />
                  Create Event
                </Link>
              </Button>
            ) : (
              <Button variant="outline" disabled className="gap-1">
                <Plus className="h-4 w-4" />
                Create Event
              </Button>
            )}
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <p className="text-muted-foreground">
              Discover conferences, meetups, and workshops in the Bittensor
              ecosystem
            </p>
          </div>

          {/* Search input */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search events..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="mb-4">
            <p className="text-muted-foreground">
              Showing {filteredEvents.length}{" "}
              {filteredEvents.length === 1 ? "event" : "events"}
            </p>
          </div>

          <EventsGrid events={filteredEvents} />
        </div>

        {/* Sidebar */}
        <div className="lg:w-1/4">
          <div className="sticky top-24">
            <Card className="bg-gradient-to-b from-purple-500 to-indigo-600 border-0 text-white overflow-hidden mb-6">
              <CardContent className="p-6">
                <div className="font-semibold text-sm mb-2 bg-white/20 w-fit px-2 py-0.5 rounded-sm">
                  SPONSORED
                </div>
                <h3 className="text-xl font-bold mb-3">Become a Validator</h3>
                <p className="text-sm mb-4 text-white/90">
                  Earn rewards by running a validator node on the Bittensor
                  network.
                </p>
                <Button
                  variant="secondary"
                  size="sm"
                  asChild
                  className="w-full"
                >
                  <a href="#" className="gap-1">
                    Learn More
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </Button>
              </CardContent>
            </Card>

            <FeaturedCompanies
              events={events}
              title="Featured Companies"
              maxCompanies={5}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
