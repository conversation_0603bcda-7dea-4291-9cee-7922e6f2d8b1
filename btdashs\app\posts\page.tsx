"use client";

import { redirect } from "next/navigation";
import { useState } from "react";

export default function PostsPage() {
	const [sortBy, setSortBy] = useState("trending");

	// redirect to home page
	redirect("/");

	// return (
	//   <div className="py-6 max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
	//     <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
	//       {/* Left Sidebar */}
	//       <div className="md:col-span-3">
	//         <JoinConversation />
	//       </div>

	//       {/* Main Content */}
	//       <div className="md:col-span-6">
	//         <div className="flex items-center justify-between mb-6">
	//           <h1 className="text-2xl font-bold">All Bittensor Posts</h1>
	//           <Select value={sortBy} onValueChange={setSortBy}>
	//             <SelectTrigger className="w-[140px]">
	//               <SelectValue />
	//             </SelectTrigger>
	//             <SelectContent>
	//               <SelectItem value="trending">Trending</SelectItem>
	//               <SelectItem value="latest">Latest</SelectItem>
	//               <SelectItem value="top">Top</SelectItem>
	//             </SelectContent>
	//           </Select>
	//         </div>
	//         <PostsList sortBy={sortBy} />
	//       </div>

	//       {/* Right Sidebar */}
	//       <div className="md:col-span-3">
	//         <ActiveUsers />
	//       </div>
	//     </div>
	//   </div>
	// )
}
