import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card"
import { <PERSON><PERSON>hart<PERSON>, Mouse<PERSON>ointer<PERSON>lick, TrendingUp } from "lucide-react"

interface MetricsSummaryProps {
  impressions: number
  clicks: number
  ctr: number
}

export function MetricsSummary({ impressions, clicks, ctr }: MetricsSummaryProps) {
  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Impressions</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{impressions.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">Total ad views</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Clicks</CardTitle>
          <MousePointerClick className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{clicks.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">Total ad clicks</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">CTR</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{(ctr * 100).toFixed(2)}%</div>
          <p className="text-xs text-muted-foreground">Click-through rate</p>
        </CardContent>
      </Card>
    </div>
  )
}
