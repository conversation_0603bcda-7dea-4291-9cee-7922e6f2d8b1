"use client";

import { Badge } from "@/components/ui/badge";
import { Company, Event } from "@/lib/db/models";
import { cn, slugify } from "@/lib/utils";
import { Calendar, ChevronRight, Heart, MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

interface eventsClientProps {
	events: Event[];
	company: Company[] | null | undefined;
}

export function EventsRow({ events, company }: eventsClientProps) {
	const scrollRef = useRef<HTMLDivElement>(null);
	const [isHovered, setIsHovered] = useState(false);

	useEffect(() => {
		if (isHovered || !scrollRef.current) return;

		const scrollContainer = scrollRef.current;
		const scrollWidth = scrollContainer.scrollWidth;
		const clientWidth = scrollContainer.clientWidth;

		if (scrollWidth <= clientWidth) return;

		let scrollPos = 0;
		const scrollSpeed = 1;
		const gap = 20; // Gap between end and start when looping

		const scroll = () => {
			if (!scrollContainer || isHovered) return;

			scrollPos += scrollSpeed;

			// Reset position when we've scrolled all the way
			if (scrollPos >= scrollWidth - clientWidth + gap) {
				scrollPos = 0;
			}

			scrollContainer.scrollLeft = scrollPos;
		};

		const intervalId = setInterval(scroll, 30);

		return () => clearInterval(intervalId);
	}, [isHovered]);

	const formatDate = (date: Date) => {
		return new Date(date).toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			year: "numeric",
		});
	};

	const getDaysAgo = (date: Date) => {
		const today = new Date();
		const eventDate = new Date(date);
		const diffTime = Math.abs(today.getTime() - eventDate.getTime());
		return Math.floor(diffTime / (1000 * 60 * 60 * 24));
	};

	return (
		<div className="w-full bg-gradient-to-r overflow-hidden relative">
			{/* Container with relative positioning for the fixed elements */}
			<div className="relative px-4">
				{/* Fixed "Upcoming Events" on the left */}
				<div className="absolute left-0 top-1/2 -translate-y-1/2 z-10 flex items-center bg-black/80 backdrop-blur-sm px-3 my-0 shadow-lg h-full max-h-full">
					<Calendar className="h-6 w-6 text-white mr-2" />
					<div className="flex flex-col justify-center">
						<span className="text-xs font-medium text-white/80 leading-none">Upcoming</span>
						<span className="text-sm font-bold text-white leading-tight mt-0.5">Events</span>
					</div>
				</div>

				{/* Fixed "View all" on the right */}
				<Link
					href="/events"
					className="absolute right-0 top-1/2 -translate-y-1/2 z-10 flex flex-col items-end justify-center bg-black/80 backdrop-blur-sm px-3 shadow-lg group h-full max-h-full"
				>
					<span className="text-xs text-white/80 group-hover:text-white leading-none">View</span>
					<div className="flex items-center mt-0.5">
						<span className="text-sm font-medium text-white/80 group-hover:text-white leading-tight">
							all
						</span>
						<ChevronRight className="h-4 w-4 text-white/80 group-hover:text-white ml-0.5" />
					</div>
				</Link>

				{/* Scrollable cards with padding to accommodate fixed elements */}
				<div
					className="flex items-center gap-4 overflow-x-auto scrollbar-hide pl-32 pr-24"
					ref={scrollRef}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
					style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
				>
					{events.length === 0
						? Array.from({ length: 5 }).map((_, index) => (
								<div
									key={index}
									className="flex-shrink-0 w-[400px] h-[100px] rounded-md bg-gray-200 animate-pulse"
								/>
						  ))
						: events
								.filter((event) => event.end_date && new Date(event.end_date) > new Date())
								.map((event) => (
									<Link
										key={event.id}
										href={`/events/${slugify(event.name)}-${event.id}`}
										className="flex-shrink-0 w-[400px] rounded-xl overflow-hidden transition-colors shadow-lg"
									>
										<div
											className={cn(
												"flex h-[100px] bg-gradient-to-r",
												"from-blue-500 to-purple-600"
											)}
										>
											{/* Left side - Image */}
											<div className="relative w-[100px] h-full flex-shrink-0">
												<Image
													src={event.image_url || "/placeholder.svg"}
													alt={event.name}
													fill
													className="object-cover"
												/>
												<div className="absolute inset-0 bg-black/30" />

												{/* Status badge */}
												<Badge
													className={`absolute bottom-2 left-2 bg-black/40 backdrop-blur-sm text-white border-0 ${
														new Date(event.start_date) > new Date() ? "" : "text-green-500"
													}`}
												>
													{new Date(event.start_date) > new Date() ? "Upcoming" : "Running"}
												</Badge>
											</div>

											{/* Right side - Content */}
											<div className="flex-1 p-3 flex flex-col justify-between">
												<div>
													{/* Title and likes */}
													<div className="flex justify-between items-start">
														<h3 className="font-bold text-white text-base line-clamp-1 pr-4 flex-1">
															{event.name}
														</h3>
														<div className="flex items-center bg-black/30 backdrop-blur-sm rounded-full px-1.5 py-0.5 flex-shrink-0">
															<Heart className="h-3 w-3 mr-0.5 fill-white" />
															<span className="text-xs text-white">0</span>
														</div>
													</div>

													{/* Date and location */}
													<div className="flex gap-4 mt-1">
														<div className="flex items-center text-white/90 text-xs">
															<Calendar className="h-3 w-3 mr-1" />
															<span>{formatDate(event.start_date)}</span>
														</div>
														{event.location && (
															<div className="flex items-center text-white/90 text-xs">
																<MapPin className="h-3 w-3 mr-1" />
																<span>{event.location}</span>
															</div>
														)}
													</div>
												</div>

												{/* Organizer and days ago */}
												<div className="flex items-center justify-between">
													<div className="flex items-center">
														<div className="flex -space-x-2 pt-1 pl-1">
															{Array.isArray(company) &&
																company
																	.filter((comp) =>
																		event.company_ids?.includes(comp.id)
																	)
																	.map((filteredCompany, index) => (
																		<a
																			key={filteredCompany.id || index}
																			href={filteredCompany.website_url || "#"}
																			target="_blank"
																			rel="noopener noreferrer"
																			className="relative h-6 w-6 rounded-full border-2 border-white bg-background overflow-hidden"
																			title={filteredCompany.name}
																			onClick={(e) => e.stopPropagation()}
																		>
																			<Image
																				src={
																					filteredCompany.logo_url ||
																					"/placeholder.svg"
																				}
																				alt={
																					filteredCompany.name ||
																					"Company logo"
																				}
																				fill
																				className="object-cover"
																			/>
																		</a>
																	))}
														</div>
													</div>
													<span className="text-xs text-white/70">
														{(() => {
															const daysDiff = Math.ceil(
																(new Date(event.start_date).getTime() -
																	new Date().getTime()) /
																	(1000 * 60 * 60 * 24)
															);
															if (daysDiff > 0) {
																return `in ${daysDiff} day${daysDiff > 1 ? "s" : ""}`;
															} else if (daysDiff === 0) {
																return "Today";
															}
															return "";
														})()}
													</span>
												</div>
											</div>
										</div>
									</Link>
								))}

					{/* Duplicate the first few events to create a seamless loop */}
					{events
						.filter((event) => new Date(event.start_date) > new Date())
						.slice(0, 3)
						.map((event) => (
							<Link
								key={`dup-${event.id}`}
								href={`/events/${slugify(event.name)}-${event.id}`}
								className="flex-shrink-0 w-[400px] rounded-md overflow-hidden transition-colors shadow-lg"
							>
								<div className={cn("flex h-[100px] bg-gradient-to-r", "from-blue-500 to-purple-600")}>
									{/* Left side - Image */}
									<div className="relative w-[100px] h-full flex-shrink-0">
										<Image
											src={event.image_url || "/placeholder.svg"}
											alt={event.name}
											fill
											className="object-cover"
										/>
										<div className="absolute inset-0 bg-black/30" />

										{/* Status badge */}
										<Badge className="absolute bottom-2 left-2 bg-black/40 backdrop-blur-sm text-white border-0">
											{new Date(event.start_date) > new Date() ? "Upcoming" : "Running"}
										</Badge>
									</div>

									{/* Right side - Content */}
									<div className="flex-1 p-3 flex flex-col justify-between">
										<div>
											{/* Title and likes */}
											<div className="flex justify-between items-start">
												<h3 className="font-bold text-white text-base line-clamp-1 pr-4 flex-1">
													{event.name}
												</h3>
												<div className="flex items-center bg-black/30 backdrop-blur-sm rounded-full px-1.5 py-0.5 flex-shrink-0">
													<Heart className="h-3 w-3 mr-0.5 fill-white" />
													<span className="text-xs text-white">0</span>
												</div>
											</div>

											{/* Date and location */}
											<div className="flex gap-4 mt-1">
												<div className="flex items-center text-white/90 text-xs">
													<Calendar className="h-3 w-3 mr-1" />
													<span>{formatDate(event.start_date)}</span>
												</div>
												{event.location && (
													<div className="flex items-center text-white/90 text-xs">
														<MapPin className="h-3 w-3 mr-1" />
														<span>{event.location}</span>
													</div>
												)}
											</div>
										</div>

										{/* Organizer and days ago */}
										<div className="flex items-center justify-between">
											<div className="flex items-center">
												<div className="flex -space-x-2 pt-1 pl-1">
													{Array.isArray(company) &&
														company
															.filter((comp) => event.company_ids?.includes(comp.id))
															.map((filteredCompany, index) => (
																<a
																	key={filteredCompany.id || index}
																	href={filteredCompany.website_url || "#"}
																	target="_blank"
																	rel="noopener noreferrer"
																	className="relative h-6 w-6 rounded-full border-2 border-white bg-background overflow-hidden"
																	title={filteredCompany.name}
																	onClick={(e) => e.stopPropagation()}
																>
																	<Image
																		src={
																			filteredCompany.logo_url ||
																			"/placeholder.svg"
																		}
																		alt={filteredCompany.name || "Company logo"}
																		fill
																		className="object-cover"
																	/>
																</a>
															))}
												</div>
											</div>
											<span className="text-xs text-white/70">
												{getDaysAgo(event.start_date)}d ago
											</span>
										</div>
									</div>
								</div>
							</Link>
						))}
				</div>
			</div>
		</div>
	);
}
