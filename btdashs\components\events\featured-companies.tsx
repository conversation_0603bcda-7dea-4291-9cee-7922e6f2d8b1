// components/events/featured-companies.tsx
"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Category, Company, Event, Product, Subnet } from "@/lib/db/models";

interface FeaturedCompaniesProps {
	events: (Event & {
		companies?: Company[];
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	title?: string;
	maxCompanies?: number;
}

export function FeaturedCompanies({ events, title = "Featured Companies", maxCompanies = 4 }: FeaturedCompaniesProps) {
	// Extract unique companies from events
	const companies = events
		.map((event) => event.companies)
		.flat()
		.filter((company): company is Company => !!company && !!company.id)
		.filter((company, index, self) => self.findIndex((c) => c.id === company.id) === index);

	// Sort companies by some criteria (here we'll just take first few)
	const featuredCompanies = companies.slice(0, maxCompanies);

	// If no companies found, use fallback data
	if (featuredCompanies.length === 0) {
		return (
			<Card>
				<CardContent className="p-4">
					<h3 className="font-medium mb-3">{title}</h3>
					<div className="space-y-3">No featured companies found.</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardContent className="p-4">
				<h3 className="font-medium mb-3">{title}</h3>
				<div className="space-y-3">
					{featuredCompanies.map((company) => (
						<div key={company.id} className="flex items-center gap-2">
							{company.logo_url ? (
								<img
									src={company.logo_url}
									alt={company.name}
									className="w-8 h-8 rounded-full object-cover"
								/>
							) : (
								<div className="w-8 h-8 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center text-xs">
									{company.name.charAt(0).toUpperCase()}
								</div>
							)}
							<span className="truncate">{company.name}</span>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
