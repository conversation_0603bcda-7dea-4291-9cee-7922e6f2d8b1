"use client";

import {
	Category,
	Company,
	Event,
	Job,
	Product,
	Skills,
	Subnet,
	User,
	UserEducation,
	UserExperience,
	UserPreferences,
	UserSkills,
} from "@/lib/db/models";

import ProfileDashboardClient from "@/components/profile/profile-dashboard";

export default function ProfileDashboardClientWrapper({
	profile,
	skills,
	userSkills,
	preferences,
	educations,
	experiences,
	jobs,
	events,
	companies,
	categories,
	subnets,
	products,
	allEvents,
}: {
	profile: User;
	skills: Skills[];
	userSkills: UserSkills[];
	preferences: UserPreferences;
	educations: UserEducation[];
	experiences: UserExperience[];
	jobs: (Job & {
		company?: Company;
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	events: (Event & {
		companies?: Company[];
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	companies: Company[];
	categories: Category[];
	subnets: Subnet[];
	products: Product[];
	allEvents: Event[];
}) {
	return (
		<ProfileDashboardClient
			profile={profile}
			skills={skills}
			userSkills={userSkills}
			preferences={preferences}
			educations={educations}
			experiences={experiences}
			jobs={jobs}
			events={events}
			companies={companies}
			categories={categories}
			subnets={subnets}
			products={products}
			allEvents={allEvents}
		/>
	);
}
