"use client";

import type { Category, Company, Product } from "@/lib/db/models";
import { ProductCard } from "./product-card";

interface ProductsGridProps {
	products: Product[];
	categories: Category[];
	companies: Company[];
	emptyMessage?: string;
}

export function ProductsGrid({
	products,
	categories,
	companies,
	emptyMessage = "No products found",
}: ProductsGridProps) {
	if (products.length === 0) {
		return (
			<div className="text-center py-12 border rounded-lg bg-muted/50">
				<p className="text-muted-foreground">{emptyMessage}</p>
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
			{products.map((product) => (
				<ProductCard key={product.id} product={product} categories={categories} companies={companies} />
			))}
		</div>
	);
}
