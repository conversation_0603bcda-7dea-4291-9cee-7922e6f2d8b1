import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Github, Book, Code, Terminal, Video } from "lucide-react"

export function SubnetDocumentation() {
  return (
    <div className="space-y-6">
      {/* Quick Start Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Start Guide</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <Terminal className="h-6 w-6" />
              <span className="font-medium">Installation</span>
              <span className="text-sm text-muted-foreground">Set up your validator node</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <Code className="h-6 w-6" />
              <span className="font-medium">Integration</span>
              <span className="text-sm text-muted-foreground">Integrate with your app</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
              <Video className="h-6 w-6" />
              <span className="font-medium">Tutorial</span>
              <span className="text-sm text-muted-foreground">Watch step-by-step guide</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Documentation Resources */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Technical Documentation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">API Reference</h4>
              <p className="text-sm text-muted-foreground">Complete API documentation with examples and use cases.</p>
              <Button variant="link" className="p-0">
                View Documentation →
              </Button>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">SDK Guide</h4>
              <p className="text-sm text-muted-foreground">Learn how to use our SDK for seamless integration.</p>
              <Button variant="link" className="p-0">
                Explore SDK →
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Developer Resources</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Example Projects</h4>
              <p className="text-sm text-muted-foreground">Ready-to-use example projects and templates.</p>
              <Button variant="link" className="p-0">
                Browse Examples →
              </Button>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">GitHub Repository</h4>
              <p className="text-sm text-muted-foreground">Access our open-source code and contribute.</p>
              <Button variant="link" className="p-0">
                View on GitHub →
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Community Resources */}
      <Card>
        <CardHeader>
          <CardTitle>Community Resources</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Book className="h-6 w-6" />
              <h4 className="font-medium">Knowledge Base</h4>
              <p className="text-sm text-muted-foreground">Browse FAQs and troubleshooting guides.</p>
            </div>
            <div className="space-y-2">
              <FileText className="h-6 w-6" />
              <h4 className="font-medium">Blog Posts</h4>
              <p className="text-sm text-muted-foreground">Read technical articles and updates.</p>
            </div>
            <div className="space-y-2">
              <Github className="h-6 w-6" />
              <h4 className="font-medium">GitHub Discussions</h4>
              <p className="text-sm text-muted-foreground">Join technical discussions.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

