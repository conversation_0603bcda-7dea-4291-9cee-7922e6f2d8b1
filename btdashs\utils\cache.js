const CACHE_KEY = "btdashs_cache";

export const getCachedData = (key) => {
	const cachedData = localStorage.getItem(`${CACHE_KEY}_${key}`);
	if (cachedData) {
		return JSON.parse(cachedData);
	}
	return null;
};

export const setCachedData = (key, data) => {
	localStorage.setItem(`${CACHE_KEY}_${key}`, JSON.stringify(data));
};

export const isCacheValid = (key, maxAge) => {
	const cachedData = localStorage.getItem(`${CACHE_KEY}_${key}`);
	if (!cachedData) return false;

	const { timestamp } = JSON.parse(cachedData);
	return Date.now() - timestamp < maxAge;
};
