import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const adPlacements = [
  {
    position: "Homepage Billboard",
    size: "970x250",
    placement: "Top of homepage",
    visibility: "High",
    price: "Contact for pricing",
  },
  {
    position: "Leaderboard",
    size: "728x90",
    placement: "Between content sections",
    visibility: "High",
    price: "Contact for pricing",
  },
  {
    position: "Sidebar Rectangle",
    size: "300x250",
    placement: "Right sidebar",
    visibility: "Medium",
    price: "Contact for pricing",
  },
  {
    position: "Subnet Page Banner",
    size: "970x90",
    placement: "Top of subnet pages",
    visibility: "High",
    price: "Contact for pricing",
  },
]

export function AdPlacementTable() {
  return (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Position</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Placement</TableHead>
            <TableHead>Visibility</TableHead>
            <TableHead>Price</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {adPlacements.map((placement) => (
            <TableRow key={placement.position}>
              <TableCell className="font-medium">{placement.position}</TableCell>
              <TableCell>{placement.size}</TableCell>
              <TableCell>{placement.placement}</TableCell>
              <TableCell>{placement.visibility}</TableCell>
              <TableCell>{placement.price}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

