"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { Category, News } from "@/lib/db/models";
import { Calendar, ExternalLink } from "lucide-react";
import Link from "next/link";

interface CompanyNewsProps {
	news: News[];
	categories: Category[];
}

export function CompanyNews({ news, categories }: CompanyNewsProps) {
	if (!news || news.length === 0) {
		return (
			<div className="text-center py-12 border rounded-lg bg-muted/50">
				<p className="text-muted-foreground">No news available</p>
			</div>
		);
	}

	// Function to truncate content to a snippet
	const getContentSnippet = (content?: string, maxLength: number = 120) => {
		if (!content) return "";
		if (content.length <= maxLength) return content;
		return content.substring(0, maxLength) + "...";
	};

	return (
		<div className="space-y-4">
			{news.map((article) => {
				const articleCategories = categories.filter((category) => article.category_ids?.includes(category.id));

				return (
					<Link href={`/news/${article.id}`} key={article.id} className="block group">
						<Card className="overflow-hidden transition-all hover:shadow-lg group-hover:border-primary">
							<CardContent className="p-6">
								<div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-3">
									<div className="flex flex-wrap gap-2">
										{articleCategories.map((cat) => (
											<Badge variant="secondary" key={cat.id} className="whitespace-nowrap">
												{cat.name}
											</Badge>
										))}
									</div>
									<div className="flex items-center text-sm text-muted-foreground">
										<Calendar className="h-4 w-4 mr-1 flex-shrink-0" />
										{article.publication_date &&
											new Date(article.publication_date).toLocaleDateString("en-US", {
												year: "numeric",
												month: "short",
												day: "numeric",
											})}
									</div>
								</div>

								<CardTitle className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
									{article.title}
								</CardTitle>

								<p className="text-muted-foreground mb-4 line-clamp-2">
									{getContentSnippet(article.content)}
								</p>

								<div className="flex items-center text-sm text-primary font-medium">
									Read more
									<ExternalLink className="h-4 w-4 ml-1" />
								</div>
							</CardContent>
						</Card>
					</Link>
				);
			})}
		</div>
	);
}
