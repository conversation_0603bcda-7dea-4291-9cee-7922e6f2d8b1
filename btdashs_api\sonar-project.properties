# SonarCloud configuration for BTDash API

# Project identification
sonar.projectKey=btdash_api
sonar.organization=btdash
sonar.projectName=BTDash API
sonar.projectVersion=1.0.0

# Source code configuration
sonar.sources=src
sonar.tests=tests
sonar.sourceEncoding=UTF-8

# Language configuration
sonar.javascript.lcov.reportPaths=coverage/lcov.info

# Exclusions
sonar.exclusions=**/node_modules/**,**/coverage/**,**/dist/**,**/*.test.js,**/*.spec.js

# Test exclusions
sonar.test.exclusions=**/node_modules/**,**/coverage/**

# Coverage exclusions
sonar.coverage.exclusions=**/tests/**,**/migrations/**,**/seeds/**,server.js

# Duplication exclusions
sonar.cpd.exclusions=**/migrations/**,**/seeds/**

# Quality gate
sonar.qualitygate.wait=true

# Analysis parameters
sonar.javascript.environments=node
sonar.nodejs.executable=node
