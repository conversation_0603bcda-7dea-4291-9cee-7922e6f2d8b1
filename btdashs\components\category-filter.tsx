"use client";
import { Button } from "@/components/ui/button";
import { getCategoryGradient } from "@/components/ui/category-gradients";
import { Category } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import * as Icons from "lucide-react";
import { ChevronLeft, ChevronRight, type LucideIcon } from "lucide-react";
import { useRef } from "react";

interface CategoryFilterProps {
	selectedCategory_id: number | null;
	onSelectCategory: (category: number | null) => void;
	categories: Category[];
	isLoading?: boolean;
	error?: string | null;
}

export function CategoryFilter({
	selectedCategory_id,
	onSelectCategory,
	categories,
	isLoading = false,
	error = null,
}: CategoryFilterProps) {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	const scroll = (direction: "left" | "right") => {
		if (scrollContainerRef.current) {
			const scrollAmount = direction === "left" ? -300 : 300;
			scrollContainerRef.current.scrollBy({
				left: scrollAmount,
				behavior: "smooth",
			});
		}
	};

	if (isLoading) {
		return <div>Loading...</div>;
	}

	if (error) {
		return <div className="text-red-500 p-4">Error loading categories: {error}</div>;
	}

	return (
		<div className="relative">
			<div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
				<Button
					variant="ghost"
					size="icon"
					className="h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
					onClick={() => scroll("left")}
				>
					<ChevronLeft className="h-6 w-6" />
				</Button>
			</div>
			<div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
				<Button
					variant="ghost"
					size="icon"
					className="h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
					onClick={() => scroll("right")}
				>
					<ChevronRight className="h-6 w-6" />
				</Button>
			</div>
			<div
				ref={scrollContainerRef}
				className="overflow-x-auto hide-scrollbar px-12"
				style={{
					scrollbarWidth: "none",
					msOverflowStyle: "none",
				}}
			>
				<div className="flex space-x-6 py-6">
					{categories.map((category) => {
						const iconName = category.icon || "Globe";
						const Icon = (Icons[iconName as keyof typeof Icons] as LucideIcon) || Icons.Globe;
						const gradient = getCategoryGradient(category.name);

						return (
							<Button
								key={category.id}
								variant="ghost"
								className={cn(
									"flex flex-col items-center justify-center h-28 w-28 gap-3 rounded-xl hover:bg-accent",
									selectedCategory_id === category.id && "bg-accent"
								)}
								onClick={() =>
									onSelectCategory(selectedCategory_id === category.id ? null : category.id)
								}
							>
								<Icon
									className={cn(
										"h-10 w-10 transition-colors",
										selectedCategory_id === category.id
											? `text-transparent bg-clip-text bg-gradient-to-br ${gradient}`
											: "text-muted-foreground hover:text-primary"
									)}
								/>
								<span
									className={cn(
										"text-sm text-center whitespace-normal leading-tight transition-all",
										selectedCategory_id === category.id
											? `text-transparent bg-clip-text bg-gradient-to-br ${gradient} text-lg font-bold`
											: "text-muted-foreground hover:text-primary"
									)}
								>
									{category.name}
								</span>
							</Button>
						);
					})}
				</div>
			</div>
		</div>
	);
}
