// app/api/subnets/route.ts

import { getAllItems } from "@/lib/data/utils";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const data = await getAllItems("subnets");
		return NextResponse.json({
			success: true,
			data,
			message: "Subnets fetched successfully",
		});
	} catch (err) {
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch subnets",
				errors: err instanceof Error ? err.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
