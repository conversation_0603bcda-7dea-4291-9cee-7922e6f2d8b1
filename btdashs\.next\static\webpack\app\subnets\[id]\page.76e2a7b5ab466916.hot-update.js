"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./lib/api/ad-serving.ts":
/*!*******************************!*\
  !*** ./lib/api/ad-serving.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   fetchPaidAd: () => (/* binding */ fetchPaidAd),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense fallback slots mapped by slot ID\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"7230250579\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.SUBNET_BANNER]: \"8814794983\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"8814794983\"\n};\n/**\n * SERVER-SIDE ONLY\n * Fetch a paid ad from the backend API\n */ async function fetchPaidAd(slotId, userContext) {\n    try {\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return null;\n        }\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.country_code) && {\n                country_code: userContext.country_code\n            },\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.device_type) && {\n                device_type: userContext.device_type\n            },\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.language) && {\n                language: userContext.language\n            },\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.user_agent) && {\n                user_agent: userContext.user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const response = await fetch(\"\".concat(API_BASE, \"/serve-ad?\").concat(params), {\n            headers,\n            cache: \"no-store\"\n        });\n        if (!response.ok) {\n            if (response.status === 404) {\n                // No ads available for this slot\n                return null;\n            }\n            throw new Error(\"Ad serving failed: \".concat(response.status));\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return result.data;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching paid ad:\", error);\n        return null;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    return \"\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (true) {\n        return navigator.language || \"en-US\";\n    }\n    return \"en-US\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/ad-serving.ts\n"));

/***/ })

});