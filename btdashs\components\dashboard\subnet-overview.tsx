"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Brain, Database, Zap, Users, Activity, Clock, HardDrive, Cpu, MessageSquare } from "lucide-react"

const subnets = [
  {
    id: "subnet-1",
    name: "AI Inference Subnet",
    category: "ai",
    description: "High-performance AI model inference for various applications",
    icon: Brain,
    metrics: {
      staking: "1.2M TAO",
      rewards: "50K TAO/day",
      tps: "1000",
      responseTime: "100ms",
      uptime: "99.99%",
      participants: 500,
    },
  },
  {
    id: "subnet-2",
    name: "Data Validation Subnet",
    category: "data",
    description: "Decentralized data validation and verification",
    icon: Database,
    metrics: {
      staking: "800K TAO",
      rewards: "30K TAO/day",
      tps: "5000",
      responseTime: "50ms",
      uptime: "99.95%",
      participants: 300,
    },
  },
  {
    id: "subnet-3",
    name: "Distributed Storage Subnet",
    category: "storage",
    description: "Decentralized file storage and retrieval",
    icon: HardDrive,
    metrics: {
      staking: "1.5M TAO",
      rewards: "45K TAO/day",
      tps: "2000",
      responseTime: "200ms",
      uptime: "99.90%",
      participants: 400,
    },
  },
  {
    id: "subnet-4",
    name: "Edge Computing Subnet",
    category: "compute",
    description: "Distributed computing at the network edge",
    icon: Cpu,
    metrics: {
      staking: "1M TAO",
      rewards: "40K TAO/day",
      tps: "3000",
      responseTime: "75ms",
      uptime: "99.98%",
      participants: 350,
    },
  },
  {
    id: "subnet-5",
    name: "NLP Processing Subnet",
    category: "nlp",
    description: "Natural language processing and text analysis",
    icon: MessageSquare,
    metrics: {
      staking: "900K TAO",
      rewards: "35K TAO/day",
      tps: "1500",
      responseTime: "150ms",
      uptime: "99.92%",
      participants: 250,
    },
  },
]

export function SubnetOverview() {
  const [selectedSubnet, setSelectedSubnet] = useState(subnets[0])
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const filteredSubnets = selectedCategory ? subnets.filter((subnet) => subnet.category === selectedCategory) : subnets

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subnet Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue={filteredSubnets[0]?.id}
          onValueChange={(value) => setSelectedSubnet(subnets.find((s) => s.id === value)!)}
        >
          <TabsList className="grid grid-cols-2 lg:grid-cols-4 mb-4">
            {filteredSubnets.map((subnet) => (
              <TabsTrigger key={subnet.id} value={subnet.id} className="text-sm">
                {subnet.name}
              </TabsTrigger>
            ))}
          </TabsList>
          {filteredSubnets.map((subnet) => (
            <TabsContent key={subnet.id} value={subnet.id}>
              <div className="flex items-center gap-4 mb-4">
                <subnet.icon className="h-12 w-12 text-primary" />
                <div>
                  <h3 className="text-xl font-bold">{subnet.name}</h3>
                  <Badge variant="secondary">{subnet.category}</Badge>
                </div>
              </div>
              <p className="text-muted-foreground mb-6">{subnet.description}</p>
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="p-3">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Zap className="h-4 w-4" /> Staking & Rewards
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-3">
                    <div className="text-xl font-bold">{subnet.metrics.staking}</div>
                    <div className="text-sm text-muted-foreground">{subnet.metrics.rewards}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-3">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Activity className="h-4 w-4" /> Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-3">
                    <div className="text-xl font-bold">{subnet.metrics.tps} TPS</div>
                    <div className="text-sm text-muted-foreground">{subnet.metrics.responseTime} response time</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-3">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Clock className="h-4 w-4" /> Uptime
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-3">
                    <div className="text-xl font-bold">{subnet.metrics.uptime}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="p-3">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Users className="h-4 w-4" /> Participants
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-3">
                    <div className="text-xl font-bold">{subnet.metrics.participants}</div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}

