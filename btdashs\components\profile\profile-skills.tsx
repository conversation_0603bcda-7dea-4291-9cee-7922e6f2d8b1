"use client";

import { Skills, UserSkills } from "@/lib/db/models";
import { Plus, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ProfileSkillsClientProps {
	availableSkills: Skills[];
	userSkills: UserSkills[];
}

const levelMap = {
	Beginner: 25,
	Intermediate: 50,
	Advanced: 75,
	Expert: 100,
};

export default function ProfileSkillsClient({
	availableSkills,
	userSkills: initialUserSkills,
}: ProfileSkillsClientProps) {
	const [userSkills, setUserSkills] = useState<UserSkills[]>(initialUserSkills);
	const [selectedSkillId, setSelectedSkillId] = useState<number | null>(null);
	const [selectedLevel, setSelectedLevel] = useState<UserSkills["level"]>("Beginner");
	const [searchQuery, setSearchQuery] = useState("");
	const [isSelectOpen, setIsSelectOpen] = useState(false);
	const searchInputRef = useRef<HTMLInputElement>(null);

	// Focus the search input when dropdown opens
	useEffect(() => {
		if (isSelectOpen && searchInputRef.current) {
			searchInputRef.current.focus();
		}
	}, [isSelectOpen]);

	// Keep focus on search input while typing
	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchQuery(e.target.value);
		// Keep the dropdown open during typing
		setIsSelectOpen(true);
	};

	const handleAddSkill = async () => {
		if (selectedSkillId === null) return;

		try {
			const res = await fetch("/api/user/skills", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ skill_id: selectedSkillId, level: selectedLevel }),
			});

			if (!res.ok) {
				const err = await res.json();
				throw new Error(err.error || "Failed to add skill");
			}

			const json = await res.json();
			const newUserSkill = json.data.data as UserSkills;

			setUserSkills((prev) => [...prev, newUserSkill]);
			setSelectedSkillId(null);
			setSelectedLevel("Beginner");
			setSearchQuery("");
		} catch (err) {
			console.error("Add skill failed:", err);
		}
	};

	const handleRemoveSkill = async (id: number) => {
		try {
			const res = await fetch(`/api/user/skills/${id}`, { method: "DELETE" });
			if (!res.ok) {
				const text = await res.text();
				throw new Error(text || "Failed to delete skill");
			}
			setUserSkills((prev) => prev.filter((s) => s.id !== id));
		} catch (err) {
			console.error("Remove skill failed:", err);
		}
	};

	const handleUpdateLevel = async (userSkillId: number, newLevel: UserSkills["level"]) => {
		try {
			const res = await fetch(`/api/user/skills/${userSkillId}`, {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ level: newLevel }),
			});

			if (!res.ok) throw new Error("Failed to update skill level");

			setUserSkills((prev) => prev.map((us) => (us.id === userSkillId ? { ...us, level: newLevel } : us)));
		} catch (err) {
			console.error("Update skill level failed:", err);
		}
	};

	const filteredSkills = availableSkills.filter((skill) =>
		skill.name.toLowerCase().includes(searchQuery.toLowerCase())
	);

	const selectedSkillName = availableSkills.find((s) => s.id === selectedSkillId)?.name;

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Add Skill</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex flex-col sm:flex-row gap-4">
						<div className="flex-1">
							<Select
								open={isSelectOpen}
								onOpenChange={(open) => {
									setIsSelectOpen(open);
									if (open) {
										setTimeout(() => {
											searchInputRef.current?.focus();
										}, 0);
									}
								}}
								onValueChange={(val) => {
									setSelectedSkillId(Number(val));
									setSearchQuery("");
									setIsSelectOpen(false);
								}}
								value={selectedSkillId !== null ? String(selectedSkillId) : ""}
							>
								<SelectTrigger className="w-full">
									<SelectValue placeholder="Select a skill...">
										{selectedSkillName || "Select a skill..."}
									</SelectValue>
								</SelectTrigger>
								<SelectContent className="p-0">
									<div className="p-2 border-b">
										<Input
											ref={searchInputRef}
											type="text"
											placeholder="Search skills..."
											value={searchQuery}
											onChange={handleSearchChange}
											className="w-full border-0 shadow-none focus-visible:ring-0"
											onClick={(e) => e.stopPropagation()}
											onKeyDown={(e) => e.stopPropagation()}
										/>
									</div>
									<div className="max-h-[200px] overflow-y-auto">
										{filteredSkills.length > 0 ? (
											filteredSkills.map((skill) => (
												<SelectItem key={skill.id} value={String(skill.id)}>
													{skill.name}
												</SelectItem>
											))
										) : (
											<div className="py-2 text-center text-sm text-muted-foreground">
												No skills found
											</div>
										)}
									</div>
								</SelectContent>
							</Select>
						</div>

						<div className="flex-1">
							<Select
								onValueChange={(val) => setSelectedLevel(val as UserSkills["level"])}
								value={selectedLevel}
							>
								<SelectTrigger className="w-full">
									<SelectValue placeholder="Select level" />
								</SelectTrigger>
								<SelectContent>
									{(["Beginner", "Intermediate", "Advanced", "Expert"] as const).map((level) => (
										<SelectItem key={level} value={level}>
											{level}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<Button
							onClick={handleAddSkill}
							disabled={selectedSkillId === null}
							className="sm:w-auto w-full"
						>
							<Plus className="w-4 h-4 mr-1" /> Add
						</Button>
					</div>
				</CardContent>
			</Card>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{userSkills.map((us: UserSkills) => {
					const skill = availableSkills.find((s) => s.id === us.skill_id);
					return (
						<Card key={us.id} className="relative bg-white text-black rounded-lg shadow-md">
							<CardHeader>
								<CardTitle className="flex items-center justify-between">
									{skill?.name ?? "Unknown Skill"}
									<Button
										variant="ghost"
										size="icon"
										className="text-red-500 hover:text-red-700"
										onClick={() => handleRemoveSkill(us.id)}
									>
										<X className="w-4 h-4" />
									</Button>
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="flex flex-col gap-2">
									<div className="flex justify-between items-center">
										<p>Level: </p>
										<Select
											onValueChange={(val) =>
												handleUpdateLevel(us.id, val as UserSkills["level"])
											}
											value={us.level}
										>
											<SelectTrigger>
												<SelectValue placeholder="Select level" />
											</SelectTrigger>
											<SelectContent>
												{(["Beginner", "Intermediate", "Advanced", "Expert"] as const).map(
													(level) => (
														<SelectItem key={level} value={level}>
															{level}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
									</div>
									<div className="w-full bg-gray-200 h-2 rounded-full">
										<div
											className="h-2 rounded-full"
											style={{
												width: `${levelMap[us.level]}%`,
												backgroundColor: "#4caf50",
											}}
										/>
									</div>
									<p className="text-sm mt-2">Endorsements: {us.endorsements}</p>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
