// src/middleware/adminAuth.js
const UserService = require("../application/services/UserService");
const { sendUnauthorized, sendForbidden } = require("../utils/responseWrapper");
const logger = require("../../logger");

/**
 * Middleware to check if the authenticated user is an admin
 * Must be used after checkJwt middleware
 */
const checkAdminAuth = async (req, res, next) => {
	try {
		const auth0_id = req.auth?.sub;

		if (!auth0_id) {
			logger.warn("Admin access attempted without authentication", {
				ip: req.ip,
				path: req.path,
				method: req.method,
			});
			return sendUnauthorized(res, "Authentication required");
		}

		// Check if user exists and is admin
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			logger.warn("Admin access attempted with non-existent user", {
				auth0_id,
				ip: req.ip,
				path: req.path,
				method: req.method,
			});
			return sendForbidden(res, "User not found");
		}

		// Check if user is in admin_users table
		const isAdmin = await UserService.isAdminUser(auth0_id);
		if (!isAdmin) {
			logger.warn("Non-admin user attempted to access admin endpoint", {
				auth0_id,
				ip: req.ip,
				path: req.path,
				method: req.method,
				userId: user.id,
			});
			return sendForbidden(res, "Admin access required");
		}

		// Add user info to request for use in controllers
		req.adminUser = user;
		next();
	} catch (error) {
		logger.error("Error in admin authentication middleware", {
			error: error.message,
			stack: error.stack,
			auth0_id: req.auth?.sub,
			ip: req.ip,
			path: req.path,
		});
		return sendForbidden(res, "Admin access verification failed");
	}
};

module.exports = checkAdminAuth;
