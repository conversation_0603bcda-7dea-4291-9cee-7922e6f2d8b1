# BTDash Database Schema Documentation

## Overview

The BTDash database uses PostgreSQL and is organized into several schemas to separate different functional areas:

- **dtm_base**: Core application data (users, companies, jobs, events)
- **dtm_ads**: Advertising system data (campaigns, ads, analytics)
- **dtm_analytics**: Analytics and reporting data

## Schema: dtm_base

### Core Entities

#### users
Primary user table storing Auth0-synced user data.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing user ID |
| auth0_user_id | VARCHAR(255) | UNIQUE, NOT NULL | Auth0 user identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User email address |
| username | VARCHAR(100) | | Display username |
| first_name | VARCHAR(100) | | User's first name |
| last_name | VARCHAR(100) | | User's last name |
| image_url | TEXT | | Profile picture URL |
| headline | VARCHAR(255) | | Professional headline |
| location | VARCHAR(255) | | User's location |
| bio | TEXT | | User biography |
| phone | VARCHAR(50) | | Phone number |
| expert_job_title | VARCHAR(255) | | Expert job title |
| website | VARCHAR(255) | | Personal website URL |
| skill_ids | INTEGER[] | | Array of skill IDs |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_users_auth0_user_id` on `auth0_user_id`
- `idx_users_email` on `email`

#### companies
Company information and profiles.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing company ID |
| name | VARCHAR(255) | NOT NULL | Company name |
| description | TEXT | | Company description |
| logo_url | TEXT | | Company logo URL |
| header_url | TEXT | | Header image URL |
| website_url | VARCHAR(255) | | Company website |
| location | VARCHAR(255) | | Company location |
| foundedyear | INTEGER | | Year company was founded |
| teamsize | VARCHAR(50) | | Team size range |
| social_media | JSONB | | Social media links |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_companies_name` on `name`

#### user_company
Junction table linking users to companies with roles.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing ID |
| user_id | INTEGER | FOREIGN KEY → users.id | User reference |
| company_id | INTEGER | FOREIGN KEY → companies.id | Company reference |
| role | VARCHAR(50) | NOT NULL | User role (owner, admin, member) |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |

**Indexes:**
- `idx_user_company_user_id` on `user_id`
- `idx_user_company_company_id` on `company_id`
- `unique_user_company` UNIQUE on `(user_id, company_id)`

#### jobs
Job postings and opportunities.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing job ID |
| owner_id | INTEGER | FOREIGN KEY → users.id | Job poster |
| title | VARCHAR(255) | NOT NULL | Job title |
| description | TEXT | | Job description |
| location | VARCHAR(255) | | Job location |
| remote | BOOLEAN | DEFAULT FALSE | Remote work option |
| type | VARCHAR(50) | | Job type (full-time, part-time, etc.) |
| currency | VARCHAR(10) | | Salary currency |
| salary_time_frame | VARCHAR(50) | | Salary time frame |
| min_salary | INTEGER | | Minimum salary |
| max_salary | INTEGER | | Maximum salary |
| published_date | TIMESTAMP | | Publication date |
| company_id | INTEGER | FOREIGN KEY → companies.id | Associated company |
| subnet_ids | INTEGER[] | | Related subnet IDs |
| product_ids | INTEGER[] | | Related product IDs |
| category_ids | INTEGER[] | | Category IDs |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_jobs_owner_id` on `owner_id`
- `idx_jobs_company_id` on `company_id`
- `idx_jobs_published_date` on `published_date`

#### events
Events and conferences.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing event ID |
| created_by_id | INTEGER | FOREIGN KEY → users.id | Event creator |
| name | VARCHAR(255) | NOT NULL | Event name |
| description | TEXT | | Event description |
| start_date | TIMESTAMP | | Event start date |
| end_date | TIMESTAMP | | Event end date |
| location | VARCHAR(255) | | Event location |
| is_virtual | BOOLEAN | DEFAULT FALSE | Virtual event flag |
| event_type | VARCHAR(100) | | Event type |
| website_url | VARCHAR(255) | | Event website |
| registration_url | VARCHAR(255) | | Registration URL |
| image_url | TEXT | | Event image |
| is_published | BOOLEAN | DEFAULT FALSE | Publication status |
| published_at | TIMESTAMP | | Publication date |
| is_featured | BOOLEAN | DEFAULT FALSE | Featured event flag |
| subnet_ids | INTEGER[] | | Related subnet IDs |
| product_ids | INTEGER[] | | Related product IDs |
| company_ids | INTEGER[] | | Related company IDs |
| category_ids | INTEGER[] | | Category IDs |
| event_ids | INTEGER[] | | Related event IDs |
| organizer_ids | INTEGER[] | | Organizer IDs |
| desc_about_this_event | TEXT | | About section |
| desc_what_u_will_learn | TEXT | | Learning outcomes |
| desc_who_should_attend | TEXT | | Target audience |
| image_url_banner | TEXT | | Banner image URL |
| speakers | JSONB | | Speaker information |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_events_created_by_id` on `created_by_id`
- `idx_events_start_date` on `start_date`
- `idx_events_is_published` on `is_published`

### User Profile Data

#### user_preferences
User job and career preferences.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing ID |
| user_id | INTEGER | FOREIGN KEY → users.id | User reference |
| job_types | VARCHAR(255)[] | | Preferred job types |
| industries | VARCHAR(255)[] | | Preferred industries |
| travel_availability | VARCHAR(100) | | Travel availability |
| locations | VARCHAR(255)[] | | Preferred locations |
| open_to_relocation | BOOLEAN | DEFAULT FALSE | Relocation willingness |
| compensation_range | VARCHAR(100) | | Salary expectations |
| additional_notes | TEXT | | Additional preferences |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### user_skills
User skills and endorsements.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing ID |
| user_id | INTEGER | FOREIGN KEY → users.id | User reference |
| skill_id | INTEGER | | Skill identifier |
| endorsements | INTEGER | DEFAULT 0 | Number of endorsements |
| level | VARCHAR(50) | | Skill level |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### user_educations
User education history.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing ID |
| user_id | INTEGER | FOREIGN KEY → users.id | User reference |
| institution_name | VARCHAR(255) | | Educational institution |
| start_date | DATE | | Start date |
| end_date | DATE | | End date |
| degree | VARCHAR(255) | | Degree obtained |
| description | TEXT | | Education description |
| field_of_study | VARCHAR(255) | | Field of study |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

#### user_experiences
User work experience history.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Auto-incrementing ID |
| user_id | INTEGER | FOREIGN KEY → users.id | User reference |
| company_name | VARCHAR(255) | | Company name |
| role | VARCHAR(255) | | Job role/title |
| location | VARCHAR(255) | | Job location |
| start_date | DATE | | Start date |
| end_date | DATE | | End date (NULL if current) |
| description | TEXT | | Job description |
| current_job | BOOLEAN | DEFAULT FALSE | Current position flag |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

## Relationships

### Entity Relationship Diagram

```
users (1) ←→ (M) user_company (M) ←→ (1) companies
users (1) ←→ (M) jobs
users (1) ←→ (M) events
users (1) ←→ (M) user_preferences
users (1) ←→ (M) user_skills
users (1) ←→ (M) user_educations
users (1) ←→ (M) user_experiences
companies (1) ←→ (M) jobs
```

### Key Relationships

1. **Users ↔ Companies**: Many-to-many through `user_company` with roles
2. **Users ↔ Jobs**: One-to-many (users can create multiple jobs)
3. **Companies ↔ Jobs**: One-to-many (companies can have multiple jobs)
4. **Users ↔ Profile Data**: One-to-many for all profile tables

## Constraints and Business Rules

### Data Integrity
- All foreign keys have CASCADE DELETE for dependent data
- Email addresses must be unique across users
- Auth0 user IDs must be unique
- User-company relationships are unique per pair

### Business Logic
- Only company owners can delete companies
- Only owners and admins can update company information
- Users can only modify their own profile data
- Job postings require valid company association

## Performance Considerations

### Indexing Strategy
- Primary keys are automatically indexed
- Foreign keys have dedicated indexes
- Frequently queried columns (email, auth0_user_id) are indexed
- Composite indexes for common query patterns

### Query Optimization
- Use LIMIT and OFFSET for pagination
- Avoid N+1 queries with proper JOIN strategies
- Use array operations for skill_ids and category arrays
- Consider materialized views for complex analytics

## Migration Strategy

### Version Control
- All schema changes are versioned through Knex migrations
- Migrations are stored in `src/infrastructure/database/migrations/`
- Each migration includes both up and down operations

### Deployment Process
1. Test migrations on development database
2. Backup production database
3. Run migrations during maintenance window
4. Verify data integrity post-migration

## Security Considerations

### Access Control
- Row-level security (RLS) policies for multi-tenant data
- User data is isolated by Auth0 user ID
- Company data access controlled by user-company relationships

### Data Protection
- Sensitive data (emails, phone numbers) should be encrypted at rest
- PII data follows GDPR compliance requirements
- Audit trails for sensitive operations
