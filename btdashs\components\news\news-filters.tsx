"use client";

import type React from "react";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

const categories = [
  "All",
  "New Subnet",
  "Milestone",
  "Protocol Update",
  "Community",
  "Development",
  "Governance",
  "Research",
];

interface NewsFiltersProps {
  selectedCategory: string | null;
  onCategoryChange: (category: string | null) => void;
}

export function NewsFilters({
  selectedCategory,
  onCategoryChange,
}: NewsFiltersProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleCategoryClick = (category: string) => {
    if (category === "All") {
      onCategoryChange(null);
    } else {
      onCategoryChange(category);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality here
    //console.log("Searching for:", searchQuery);
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <form onSubmit={handleSearch} className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search news articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>

        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={
                (category === "All" && selectedCategory === null) ||
                category === selectedCategory
                  ? "default"
                  : "outline"
              }
              size="sm"
              onClick={() => handleCategoryClick(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
