"use client";

import CompanyInfo from "@/components/profile/company-info";
import ProfileBasicInfoClient from "@/components/profile/profile-basic-info";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { Company, User } from "@/lib/db/models";
import { CreditCard, ExternalLink } from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useState } from "react";

export const revalidate = 600; // 10 minutes in seconds

interface SettingsClientWrapperProps {
	profile: User;
	company: Company;
}

export default function SettingsClientWrapper({ profile, company }: SettingsClientWrapperProps) {
	const searchParams = useSearchParams();
	const defaultTab = searchParams.get("tab") || "account";

	const [loading, setLoading] = useState(false);
	const [customerId, setCustomerId] = useState<string | null>(null);

	const handleSave = (section: string) => {
		setLoading(true);
		setTimeout(() => {
			setLoading(false);
			toast({
				title: "Settings saved",
				description: `Your ${section} settings have been updated successfully.`,
			});
		}, 1000);
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Settings</h1>
			</div>

			<Tabs defaultValue={defaultTab} className="space-y-4">
				<div className="flex items-center justify-between mb-2">
					<TabsList>
						<TabsTrigger value="account">Account</TabsTrigger>
						<TabsTrigger value="notifications">Notifications</TabsTrigger>
						<TabsTrigger value="billing">Billing</TabsTrigger>
						<TabsTrigger value="company">Company</TabsTrigger>
					</TabsList>
					<Link href="/auth/logout" prefetch={false}>
						<Button variant="destructive">Logout</Button>
					</Link>
				</div>

				<TabsContent value="account" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Profile Information</CardTitle>
							<CardDescription>Update your account profile information</CardDescription>
						</CardHeader>
						<CardContent>
							<ProfileBasicInfoClient initialProfile={profile} />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="notifications" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Notification Preferences</CardTitle>
							<CardDescription>Manage how you receive notifications</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="campaign-notifications">Campaign Status Updates</Label>
									<p className="text-sm text-muted-foreground">
										Receive notifications when your campaign status changes
									</p>
								</div>
								<Switch id="campaign-notifications" defaultChecked />
							</div>
							<Separator />
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="performance-notifications">Performance Reports</Label>
									<p className="text-sm text-muted-foreground">
										Receive weekly performance reports for your campaigns
									</p>
								</div>
								<Switch id="performance-notifications" defaultChecked />
							</div>
							<Separator />
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="billing-notifications">Billing Notifications</Label>
									<p className="text-sm text-muted-foreground">
										Receive notifications about billing and payments
									</p>
								</div>
								<Switch id="billing-notifications" defaultChecked />
							</div>
							<Separator />
							<div className="flex items-center justify-between">
								<div className="space-y-0.5">
									<Label htmlFor="marketing-notifications">Marketing Updates</Label>
									<p className="text-sm text-muted-foreground">
										Receive marketing updates and new feature announcements
									</p>
								</div>
								<Switch id="marketing-notifications" />
							</div>
						</CardContent>
						<CardFooter>
							<Button onClick={() => handleSave("notifications")} disabled={loading}>
								{loading ? "Saving..." : "Save Preferences"}
							</Button>
						</CardFooter>
					</Card>
				</TabsContent>

				<TabsContent value="billing" className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<CreditCard className="h-5 w-5" />
									Payment Methods
								</CardTitle>
								<CardDescription>Manage your payment methods for campaigns</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="text-center py-8">
									<CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
									<h3 className="text-lg font-medium mb-2">No payment methods</h3>
									<p className="text-sm text-muted-foreground mb-4">
										Add a payment method to get started with campaign billing.
									</p>
									<Link href="/dashboard/billing?tab=payment-methods">
										<Button>Manage Payment Methods</Button>
									</Link>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Billing Overview</CardTitle>
								<CardDescription>Quick overview of your billing status</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="space-y-2">
									<div className="flex justify-between text-sm">
										<span className="text-muted-foreground">Total Spent</span>
										<span className="font-medium">$0.00</span>
									</div>
									<div className="flex justify-between text-sm">
										<span className="text-muted-foreground">Active Campaigns</span>
										<span className="font-medium">0</span>
									</div>
									<div className="flex justify-between text-sm">
										<span className="text-muted-foreground">Payment Status</span>
										<span className="font-medium text-green-600">Current</span>
									</div>
								</div>
								<div className="pt-4 border-t">
									<Link href="/dashboard/billing">
										<Button className="w-full">
											View Full Billing Dashboard
											<ExternalLink className="ml-2 h-4 w-4" />
										</Button>
									</Link>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="company" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>Company Information</CardTitle>
							<CardDescription>Manage your company details</CardDescription>
						</CardHeader>
						<CardContent>
							<CompanyInfo initialCompany={company} />
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
