"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { AlertCircle } from "lucide-react";
import { useState } from "react";

interface RawContribution {
  date: string;
  count: number;
}

interface ContributionDay extends RawContribution {
  level: 0 | 1 | 2 | 3 | 4;
}

interface Props {
  contributions: RawContribution[];
  className?: string;
}

export function SubnetGithubContributionGraph({
  contributions,
  className,
}: Props) {
  const [tooltip, setTooltip] = useState<{
    content: string;
    x: number;
    y: number;
  } | null>(null);

  const calculateLevel = (count: number): 0 | 1 | 2 | 3 | 4 => {
    if (count === 0) return 0;
    if (count <= 3) return 1;
    if (count <= 8) return 2;
    if (count <= 15) return 3;
    return 4;
  };

  const addDay = (dateStr: string) => {
    const date = new Date(dateStr);
    date.setDate(date.getDate() + 1);
    return date.toISOString().split("T")[0];
  };

  const transformData = (): ContributionDay[] => {
    const days: ContributionDay[] = [];
    const today = new Date();

    for (let i = 0; i < 365; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      const dateStr = date.toISOString().slice(0, 10);
      const raw = contributions.find((d) => d.date === dateStr);
      days.unshift({
        date: addDay(dateStr),
        count: raw?.count || 0,
        level: calculateLevel(raw?.count || 0),
      });
    }

    return days;
  };

  const fullYearData = transformData();
  const totalContributions = fullYearData.reduce((sum, d) => sum + d.count, 0);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleCellInteraction = (
    event: React.MouseEvent,
    day: ContributionDay
  ) => {
    setTooltip({
      content: `${day.count} commit${
        day.count !== 1 ? "s" : ""
      } on ${formatDate(day.date)}`,
      x: event.clientX,
      y: event.clientY - 20,
    });
  };

  return (
    <>
      <Card className={className}>
        <div className="flex justify-between items-center px-6 py-5">
          <CardHeader>
            <CardTitle>GitHub Contribution Activity</CardTitle>
          </CardHeader>
          <div className="ml-auto">
            <strong>{totalContributions}</strong> contributions in the last year
          </div>
        </div>

        <CardContent>
          {contributions.length === 0 ? (
            <div className="flex items-center gap-2 text-amber-500">
              <AlertCircle className="h-5 w-5" />
              <p>No contribution data available</p>
            </div>
          ) : (
            <div className="overflow-x-auto pb-2">
              <div className="min-w-[750px]">
                <div className="flex text-xs text-muted-foreground mb-1">
                  <div className="w-8"></div>
                  <div className="flex-1 flex justify-between">
                    {Array.from({ length: 12 }).map((_, i) => {
                      const date = new Date();
                      date.setMonth(date.getMonth() - (11 - i));
                      return (
                        <div key={i} className="text-center w-[48px]">
                          {date.toLocaleString("default", { month: "short" })}
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="flex">
                  <div className="w-8 flex flex-col justify-between text-xs text-muted-foreground py-1">
                    <div>Mon</div>
                    <div>Wed</div>
                    <div>Fri</div>
                  </div>

                  <div
                    className="flex-1 grid"
                    style={{
                      gridTemplateColumns: "repeat(52, minmax(0, 1fr))",
                      gridTemplateRows: "repeat(7, 1fr)",
                      gap: "4px", // optional for better scaling
                      height: "100%", // fill parent Card
                    }}
                  >
                    {fullYearData.map((day, i) => {
                      const weekIndex = Math.floor(i / 7);
                      const dayOfWeekIndex = i % 7;
                      return (
                        <div
                          key={`${day.date}-${i}`}
                          onMouseEnter={(e) => handleCellInteraction(e, day)}
                          onMouseLeave={() => setTooltip(null)}
                          className={cn(
                            "cursor-pointer transition-colors",
                            day.level === 0 &&
                              "bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700",
                            day.level === 1 &&
                              "bg-green-200 dark:bg-green-900 hover:bg-green-300 dark:hover:bg-green-800",
                            day.level === 2 &&
                              "bg-green-400 dark:bg-green-700 hover:bg-green-500 dark:hover:bg-green-600",
                            day.level === 3 &&
                              "bg-green-600 dark:bg-green-500 hover:bg-green-700 dark:hover:bg-green-400",
                            day.level === 4 &&
                              "bg-green-800 dark:bg-green-300 hover:bg-green-900 dark:hover:bg-green-200"
                          )}
                          style={{
                            gridColumn: weekIndex + 1,
                            gridRow: dayOfWeekIndex + 1,
                            width: "16px",
                            height: "16px",
                            borderRadius: "2px",
                          }}
                        />
                      );
                    })}
                  </div>
                </div>

                <div className="flex justify-end items-center mt-2 text-xs text-muted-foreground">
                  <span className="mr-2">Less</span>
                  <div className="flex gap-1">
                    {[0, 1, 2, 3, 4].map((level) => (
                      <div
                        key={level}
                        className={cn(
                          "w-3 h-3 rounded-sm",
                          level === 0 && "bg-gray-200 dark:bg-gray-800",
                          level === 1 && "bg-green-200 dark:bg-green-900",
                          level === 2 && "bg-green-400 dark:bg-green-700",
                          level === 3 && "bg-green-600 dark:bg-green-500",
                          level === 4 && "bg-green-800 dark:bg-green-300"
                        )}
                      />
                    ))}
                  </div>
                  <span className="ml-2">More</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {tooltip && (
        <div
          className="fixed bg-gray-900 text-white px-3 py-2 rounded-md text-sm shadow-lg z-50 pointer-events-none"
          style={{
            left: `${tooltip.x}px`,
            top: `${tooltip.y}px`,
            transform: "translate(-50%, -100%)",
          }}
        >
          {tooltip.content}
          <div className="absolute bottom-0 left-1/2 w-3 h-3 bg-gray-900 transform translate-y-1/2 -translate-x-1/2 rotate-45" />
        </div>
      )}
    </>
  );
}
