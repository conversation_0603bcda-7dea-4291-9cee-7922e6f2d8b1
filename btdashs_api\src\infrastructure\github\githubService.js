const axios = require("axios");
const dotenv = require("dotenv");

dotenv.config();

// Configuration
const GITHUB_API_BASE_URL = "https://api.github.com";
const GITHUB_API_TOKEN = process.env.GITHUB_API_TOKEN; // Optional
const DEFAULT_DELAY_MS = 1000; // 1 second base delay
const MAX_RETRIES = 3;

// Helper function to delay execution
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Rate limit tracker
let rateLimit = {
	remaining: Number.MAX_SAFE_INTEGER,
	reset: 0,
};

// Create axios instance with common headers
const githubApi = axios.create({
	baseURL: GITHUB_API_BASE_URL,
	headers: {
		Accept: "application/vnd.github.v3+json",
		"User-Agent": "Bittensor-Subnets-API",
		...(GITHUB_API_TOKEN && { Authorization: `token ${GITHUB_API_TOKEN}` }),
	},
});

// Interceptor to track rate limits
githubApi.interceptors.response.use(
	(response) => {
		if (response.headers) {
			rateLimit = {
				remaining: parseInt(response.headers["x-ratelimit-remaining"] || rateLimit.remaining),
				reset: parseInt(response.headers["x-ratelimit-reset"] || rateLimit.reset) * 1000,
			};
		}
		return response;
	},
	(error) => {
		if (error.response && error.response.headers) {
			rateLimit = {
				remaining: parseInt(error.response.headers["x-ratelimit-remaining"] || rateLimit.remaining),
				reset: parseInt(error.response.headers["x-ratelimit-reset"] || rateLimit.reset) * 1000,
			};
		}
		return Promise.reject(error);
	}
);

const fetchWithRetry = async (url, params = {}, retries = MAX_RETRIES, delayMs = DEFAULT_DELAY_MS) => {
	for (let attempt = 1; attempt <= retries; attempt++) {
		try {
			// Check rate limits before making request
			const now = Date.now();
			if (rateLimit.remaining <= 0 && now < rateLimit.reset) {
				const waitTime = rateLimit.reset - now + 1000; // Add 1 second buffer
				console.warn(`GitHub rate limit exceeded. Waiting ${Math.ceil(waitTime / 1000)} seconds...`);
				await delay(waitTime);
			}

			const response = await githubApi.get(url, { params });
			return response.data;
		} catch (error) {
			if (attempt === retries) throw error;

			if (error.response) {
				// Handle rate limits
				if (error.response.status === 403 && error.response.headers["x-ratelimit-remaining"] === "0") {
					const resetTime = parseInt(error.response.headers["x-ratelimit-reset"]) * 1000;
					const waitTime = resetTime - Date.now() + 1000; // Add 1 second buffer
					console.warn(`GitHub rate limit hit. Waiting ${Math.ceil(waitTime / 1000)} seconds...`);
					await delay(waitTime);
					continue;
				}

				// Handle secondary rate limits
				if (error.response.status === 403 && error.response.data?.message?.includes("secondary rate limit")) {
					const waitTime = delayMs * Math.pow(2, attempt - 1); // Exponential backoff
					console.warn(`Secondary rate limit hit. Retrying in ${waitTime}ms...`);
					await delay(waitTime);
					continue;
				}
			}

			// For other errors, use exponential backoff
			const waitTime = delayMs * Math.pow(2, attempt - 1);
			console.warn(`Attempt ${attempt} failed. Retrying in ${waitTime}ms...`, error.message);
			await delay(waitTime);
		}
	}
	throw new Error(`Failed to fetch ${url} after ${retries} retries`);
};

// Fetch all pages of a paginated endpoint
exports.fetchAllPages = async (endpoint, params = {}) => {
	let results = [];
	let page = 1;
	let hasMore = true;

	while (hasMore) {
		try {
			console.log(`Fetching ${endpoint}, page ${page}...`);
			const response = await fetchWithRetry(endpoint, {
				...params,
				page,
				per_page: 100, // Max items per page
			});

			if (Array.isArray(response) && response.length > 0) {
				results = results.concat(response);
				page++;
			} else {
				hasMore = false;
			}
		} catch (error) {
			console.error(`Error fetching page ${page} of ${endpoint}:`, error.message);
			hasMore = false;
		}
	}

	return results;
};

// Get repository contributors
exports.fetchRepoContributors = async (owner, repo) => {
	const endpoint = `/repos/${owner}/${repo}/contributors`;
	return fetchWithRetry(endpoint);
};

// Get repository details
exports.fetchRepoDetails = async (owner, repo) => {
	const endpoint = `/repos/${owner}/${repo}`;
	return fetchWithRetry(endpoint);
};

// Get repository commits
exports.getCommitCountsSince = async (owner, repo, since = null) => {
	let sinceDate;
	if (since == null) {
		// Handles both `null` and `undefined`
		const oneYearAgo = new Date();
		oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
		sinceDate = oneYearAgo.toISOString();
	} else {
		sinceDate = new Date(since).toISOString();
	}

	const endpoint = `/repos/${owner}/${repo}/commits`;
	const params = {
		since: sinceDate,
	};

	let commits;
	try {
		commits = await this.fetchAllPages(endpoint, params);
	} catch (error) {
		if (error.response && error.response.status === 409) {
			// Empty repository
			commits = [];
		} else {
			throw error;
		}
	}

	const commitCounts = {};

	for (const commit of commits) {
		const commitDate = new Date(commit.commit.author.date);
		const dateStr = commitDate.toISOString().split("T")[0]; // YYYY-MM-DD format
		commitCounts[dateStr] = (commitCounts[dateStr] || 0) + 1;
	}

	const data = Object.entries(commitCounts)
		.map(([date, count]) => ({ date, count }))
		.sort((a, b) => a.date.localeCompare(b.date));

	const lastUpdateDay = new Date().toISOString().split("T")[0];

	return {
		data,
		last_update_day: lastUpdateDay,
	};
};

// Check rate limit status
exports.getRateLimitStatus = async () => {
	try {
		const response = await githubApi.get("/rate_limit");
		return {
			limit: response.data.resources.core.limit,
			remaining: response.data.resources.core.remaining,
			reset: new Date(response.data.resources.core.reset * 1000).toISOString(),
		};
	} catch (error) {
		console.error("Error checking GitHub rate limits:", error.message);
		return rateLimit;
	}
};
