// app/actions/create-campaign.ts
"use server";

import { cookies } from "next/headers";

export async function createCampaign(campaignData: any) {
	const cookieHeader = (await cookies()).toString();

	try {
		const response = await fetch(`${process.env.APP_BASE_URL}/api/user/campaigns`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Cookie: cookieHeader,
			},
			body: JSON.stringify(campaignData),
		});

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return { success: true, data: result.data, message: result.message };
		} else {
			return { success: false, message: result.message, errors: result.errors };
		}
	} catch (error: any) {
		return { success: false, message: error.message || "Failed to create campaign" };
	}
}
