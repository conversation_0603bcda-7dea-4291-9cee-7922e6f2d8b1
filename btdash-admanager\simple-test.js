// simple-test.js - Simple manual test for API integration
console.log('🧪 Testing BTDash API Integration...\n');

// Test 1: API Health Check
console.log('1. Testing API Health Check...');
fetch('http://localhost:3001/health')
  .then(res => res.json())
  .then(data => {
    console.log('   ✅ API Health:', data.success ? 'SUCCESS' : 'FAILED');
    console.log('   Response:', JSON.stringify(data, null, 2));
  })
  .catch(err => console.log('   ❌ API Health Error:', err.message));

// Test 2: Data Endpoint (should require internal key)
console.log('\n2. Testing Data Endpoint (without key)...');
fetch('http://localhost:3001/api/companies')
  .then(res => res.json())
  .then(data => {
    console.log('   ✅ Companies Endpoint:', data.success ? 'UNEXPECTED SUCCESS' : 'CORRECTLY FAILED');
    console.log('   Response:', JSON.stringify(data, null, 2));
  })
  .catch(err => console.log('   ❌ Companies Error:', err.message));

// Test 3: Advertising Endpoint (should require JWT)
console.log('\n3. Testing Advertising Endpoint (without auth)...');
fetch('http://localhost:3001/api/campaigns')
  .then(res => res.json())
  .then(data => {
    console.log('   ✅ Campaigns Endpoint:', data.success ? 'UNEXPECTED SUCCESS' : 'CORRECTLY FAILED');
    console.log('   Response:', JSON.stringify(data, null, 2));
  })
  .catch(err => console.log('   ❌ Campaigns Error:', err.message));

// Test 4: Frontend Health Check
console.log('\n4. Testing Frontend...');
fetch('http://localhost:3000')
  .then(res => {
    console.log('   ✅ Frontend Status:', res.status === 200 ? 'RUNNING' : 'FAILED');
    console.log('   Status Code:', res.status);
  })
  .catch(err => console.log('   ❌ Frontend Error:', err.message));

console.log('\n🎯 Test Summary:');
console.log('- API should be running on port 3001');
console.log('- Frontend should be running on port 3000');
console.log('- Data endpoints should require internal key');
console.log('- Advertising endpoints should require JWT auth');
console.log('- All responses should use standardized format');
