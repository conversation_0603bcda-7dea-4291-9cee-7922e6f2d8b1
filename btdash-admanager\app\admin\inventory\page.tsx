"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Plus } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function AdminInventoryPage() {
	const { toast } = useToast();

	// Fetch ad slots/placements data
	const {
		data: placements,
		error: placementsError,
		isLoading: placementsLoading,
	} = useSWR("/api/placements", fetcher, {
		refreshInterval: 60000, // Refresh every minute
	});

	useEffect(() => {
		if (placementsError) {
			toast({
				title: "Error",
				description: "Failed to load ad placements. Please try again.",
				variant: "destructive",
			});
		}
	}, [placementsError, toast]);

	if (placementsLoading) {
		return (
			<div className="flex h-64 items-center justify-center">
				<div className="text-center">
					<Loader2 className="mx-auto h-8 w-8 animate-spin" />
					<p className="mt-4 text-lg">Loading ad inventory...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Ad Inventory</h1>
				<Button>
					<Plus className="mr-2 h-4 w-4" />
					Add Placement
				</Button>
			</div>

			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				{placements && placements.length > 0 ? (
					placements.map((placement: any) => (
						<Card key={placement.id}>
							<CardHeader>
								<div className="flex justify-between items-center">
									<Badge>{placement.type || "Ad Slot"}</Badge>
									<Badge variant="outline">${placement.base_price || 0}/month</Badge>
								</div>
								<CardTitle className="mt-2">{placement.name}</CardTitle>
								<CardDescription>{placement.description || "No description available"}</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="flex justify-between">
										<span className="text-sm">Dimensions:</span>
										<span className="text-sm font-medium">
											{placement.width}x{placement.height}px
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm">Status:</span>
										<span className="text-sm font-medium">
											{placement.is_active ? "Active" : "Inactive"}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm">Max File Size:</span>
										<span className="text-sm font-medium">
											{placement.max_file_size
												? `${Math.round(placement.max_file_size / 1024)}KB`
												: "N/A"}
										</span>
									</div>
								</div>
								<div className="flex gap-2 mt-4">
									<Link href={`/admin/inventory/${placement.id}`}>
										<Button variant="outline" size="sm" className="w-full">
											Edit
										</Button>
									</Link>
								</div>
							</CardContent>
						</Card>
					))
				) : (
					<div className="col-span-full flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
						<h3 className="text-lg font-medium">No ad placements found</h3>
						<p className="text-sm text-muted-foreground">Create your first ad placement to get started.</p>
						<Button className="mt-4">
							<Plus className="mr-2 h-4 w-4" />
							Add Placement
						</Button>
					</div>
				)}
			</div>
		</div>
	);
}
