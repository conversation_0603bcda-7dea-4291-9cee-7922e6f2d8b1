-- ============================================================================
-- BTDash API - Phase 2 Database Schema Updates
-- ============================================================================
-- This script updates the existing schema to support Phase 2 features:
-- - Budget tracking and billing
-- - Admin workflow and notifications
-- - File upload management
-- - Targeting system enhancements
-- - Analytics improvements
-- ============================================================================

-- Create dtm_ads schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dtm_ads;

-- ============================================================================
-- BUDGET TRACKING & BILLING TABLES
-- ============================================================================

-- Advertiser balance tracking
CREATE TABLE IF NOT EXISTS dtm_ads.advertiser_balances (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    last_transaction_id INTEGER,
    last_updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_advertiser_balances_user_id 
        FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT chk_advertiser_balances_balance 
        CHECK (balance >= 0),
    CONSTRAINT uq_advertiser_balances_user_id 
        UNIQUE (user_id)
);

-- Billing transactions for all financial activities
CREATE TABLE IF NOT EXISTS dtm_ads.billing_transactions (
    id SERIAL PRIMARY KEY,
    advertiser_id INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_method VARCHAR(50),
    invoice_id VARCHAR(100),
    campaign_id INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_billing_transactions_advertiser_id 
        FOREIGN KEY (advertiser_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT fk_billing_transactions_campaign_id 
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE SET NULL,
    CONSTRAINT chk_billing_transactions_status 
        CHECK (status IN ('pending', 'completed', 'failed', 'refunded'))
);

-- ============================================================================
-- ADMIN WORKFLOW & NOTIFICATIONS
-- ============================================================================

-- Notifications for users (campaign approvals, rejections, etc.)
CREATE TABLE IF NOT EXISTS dtm_ads.ad_notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    related_id INTEGER,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_ad_notifications_user_id 
        FOREIGN KEY (user_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT chk_ad_notifications_type 
        CHECK (type IN ('campaign_approval', 'campaign_rejection', 'ad_approval', 
                       'ad_rejection', 'payment_receipt', 'low_balance', 'budget_threshold'))
);

-- Admin action logging
CREATE TABLE IF NOT EXISTS dtm_ads.admin_actions (
    id SERIAL PRIMARY KEY,
    admin_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_admin_actions_admin_id 
        FOREIGN KEY (admin_id) REFERENCES dtm_base.users(id) ON DELETE CASCADE,
    CONSTRAINT chk_admin_actions_action 
        CHECK (action IN ('campaign_approval', 'campaign_rejection', 'ad_approval', 
                         'ad_rejection', 'campaign_pause', 'campaign_resume'))
);

-- Rejection reasons lookup table
CREATE TABLE IF NOT EXISTS dtm_ads.rejection_reasons (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_rejection_reasons_entity_type 
        CHECK (entity_type IN ('campaign', 'ad')),
    CONSTRAINT uq_rejection_reasons_code 
        UNIQUE (entity_type, code)
);

-- ============================================================================
-- TARGETING SYSTEM
-- ============================================================================

-- Ad targeting rules
CREATE TABLE IF NOT EXISTS dtm_ads.ad_targets (
    id SERIAL PRIMARY KEY,
    ad_id INTEGER NOT NULL,
    key VARCHAR(50) NOT NULL,
    value VARCHAR(255) NOT NULL,
    operator VARCHAR(20) NOT NULL DEFAULT 'equals',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_ad_targets_ad_id 
        FOREIGN KEY (ad_id) REFERENCES dtm_ads.ads(id) ON DELETE CASCADE,
    CONSTRAINT chk_ad_targets_operator 
        CHECK (operator IN ('equals', 'contains', 'startsWith', 'endsWith', 'in', 'notIn', 'gte', 'lte', 'gt', 'lt')),
    CONSTRAINT chk_ad_targets_key 
        CHECK (key IN ('country_code', 'region', 'device_type', 'language', 'age', 'gender', 'interest', 'timezone'))
);

-- ============================================================================
-- ANALYTICS ENHANCEMENTS
-- ============================================================================

-- Daily aggregated reports for ads
CREATE TABLE IF NOT EXISTS dtm_ads.daily_ad_reports (
    id SERIAL PRIMARY KEY,
    ad_id INTEGER NOT NULL,
    date DATE NOT NULL,
    impressions INTEGER NOT NULL DEFAULT 0,
    clicks INTEGER NOT NULL DEFAULT 0,
    spend DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    avg_view_time INTEGER, -- in seconds
    unique_users INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_daily_ad_reports_ad_id 
        FOREIGN KEY (ad_id) REFERENCES dtm_ads.ads(id) ON DELETE CASCADE,
    CONSTRAINT uq_daily_ad_reports_ad_date 
        UNIQUE (ad_id, date),
    CONSTRAINT chk_daily_ad_reports_metrics 
        CHECK (impressions >= 0 AND clicks >= 0 AND spend >= 0 AND unique_users >= 0)
);

-- Daily aggregated reports for campaigns
CREATE TABLE IF NOT EXISTS dtm_ads.campaign_daily_reports (
    id SERIAL PRIMARY KEY,
    campaign_id INTEGER NOT NULL,
    date DATE NOT NULL,
    impressions INTEGER NOT NULL DEFAULT 0,
    clicks INTEGER NOT NULL DEFAULT 0,
    spend DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    ctr DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    avg_cpc DECIMAL(8,4) NOT NULL DEFAULT 0.0000,
    avg_cpm DECIMAL(8,4) NOT NULL DEFAULT 0.0000,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_campaign_daily_reports_campaign_id 
        FOREIGN KEY (campaign_id) REFERENCES dtm_ads.ad_campaigns(id) ON DELETE CASCADE,
    CONSTRAINT uq_campaign_daily_reports_campaign_date 
        UNIQUE (campaign_id, date),
    CONSTRAINT chk_campaign_daily_reports_metrics 
        CHECK (impressions >= 0 AND clicks >= 0 AND spend >= 0 AND ctr >= 0)
);

-- ============================================================================
-- ENHANCE EXISTING TABLES
-- ============================================================================

-- 1. Enhance ad_impressions table
DO $$
BEGIN
    -- Add country_code column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_impressions'
                   AND column_name = 'country_code') THEN
        ALTER TABLE dtm_ads.ad_impressions ADD COLUMN country_code VARCHAR(2);
    END IF;

    -- Add device_type column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_impressions'
                   AND column_name = 'device_type') THEN
        ALTER TABLE dtm_ads.ad_impressions ADD COLUMN device_type VARCHAR(20);
    END IF;

    -- Add viewed_time column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_impressions'
                   AND column_name = 'viewed_time') THEN
        ALTER TABLE dtm_ads.ad_impressions ADD COLUMN viewed_time INTEGER;
    END IF;
END $$;

-- 2. Enhance ad_clicks table
DO $$
BEGIN
    -- Add country_code column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_clicks'
                   AND column_name = 'country_code') THEN
        ALTER TABLE dtm_ads.ad_clicks ADD COLUMN country_code VARCHAR(2);
    END IF;

    -- Add device_type column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_clicks'
                   AND column_name = 'device_type') THEN
        ALTER TABLE dtm_ads.ad_clicks ADD COLUMN device_type VARCHAR(20);
    END IF;

    -- Add referrer_url column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_clicks'
                   AND column_name = 'referrer_url') THEN
        ALTER TABLE dtm_ads.ad_clicks ADD COLUMN referrer_url TEXT;
    END IF;
END $$;

-- 3. Enhance ads table
DO $$
BEGIN
    -- Add rejection_reason column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ads'
                   AND column_name = 'rejection_reason') THEN
        ALTER TABLE dtm_ads.ads ADD COLUMN rejection_reason TEXT;
    END IF;
END $$;

-- 4. Enhance ad_targets table (add missing columns)
DO $$
BEGIN
    -- Add operator column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_targets'
                   AND column_name = 'operator') THEN
        ALTER TABLE dtm_ads.ad_targets ADD COLUMN operator VARCHAR(20) NOT NULL DEFAULT 'equals';
    END IF;

    -- Add created_at column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_targets'
                   AND column_name = 'created_at') THEN
        ALTER TABLE dtm_ads.ad_targets ADD COLUMN created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
    END IF;

    -- Add updated_at column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_ads'
                   AND table_name = 'ad_targets'
                   AND column_name = 'updated_at') THEN
        ALTER TABLE dtm_ads.ad_targets ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- 5. Rename and enhance ad_reports_daily table
DO $$
BEGIN
    -- Check if the old table exists and new one doesn't
    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_schema = 'dtm_ads'
               AND table_name = 'ad_reports_daily')
    AND NOT EXISTS (SELECT 1 FROM information_schema.tables
                    WHERE table_schema = 'dtm_ads'
                    AND table_name = 'daily_ad_reports') THEN

        -- Rename the table
        ALTER TABLE dtm_ads.ad_reports_daily RENAME TO daily_ad_reports;

        -- Add missing columns
        ALTER TABLE dtm_ads.daily_ad_reports ADD COLUMN IF NOT EXISTS spend DECIMAL(10,2) NOT NULL DEFAULT 0.00;
        ALTER TABLE dtm_ads.daily_ad_reports ADD COLUMN IF NOT EXISTS avg_view_time INTEGER;
        ALTER TABLE dtm_ads.daily_ad_reports ADD COLUMN IF NOT EXISTS unique_users INTEGER NOT NULL DEFAULT 0;
        ALTER TABLE dtm_ads.daily_ad_reports ADD COLUMN IF NOT EXISTS created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
        ALTER TABLE dtm_ads.daily_ad_reports ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
    END IF;
END $$;

-- 6. Add is_admin column to users table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'dtm_base'
                   AND table_name = 'users'
                   AND column_name = 'is_admin') THEN
        ALTER TABLE dtm_base.users ADD COLUMN is_admin BOOLEAN NOT NULL DEFAULT FALSE;
    END IF;
END $$;

-- ============================================================================
-- FRAUD DETECTION TABLES
-- ============================================================================

-- Fraud analysis results
CREATE TABLE IF NOT EXISTS dtm_ads.fraud_analysis (
    id SERIAL PRIMARY KEY,
    type VARCHAR(20) NOT NULL,
    entity_id INTEGER NOT NULL,
    ad_id INTEGER NOT NULL,
    ip_address INET,
    risk_score INTEGER NOT NULL DEFAULT 0,
    risk_level VARCHAR(20) NOT NULL DEFAULT 'minimal',
    suspicious_factors JSONB,
    action_recommended VARCHAR(20) NOT NULL DEFAULT 'allow',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_fraud_analysis_ad_id
        FOREIGN KEY (ad_id) REFERENCES dtm_ads.ads(id) ON DELETE CASCADE,
    CONSTRAINT chk_fraud_analysis_type
        CHECK (type IN ('click', 'impression')),
    CONSTRAINT chk_fraud_analysis_risk_score
        CHECK (risk_score >= 0 AND risk_score <= 100),
    CONSTRAINT chk_fraud_analysis_risk_level
        CHECK (risk_level IN ('minimal', 'low', 'medium', 'high', 'critical')),
    CONSTRAINT chk_fraud_analysis_action
        CHECK (action_recommended IN ('allow', 'monitor', 'flag', 'block'))
);

-- ============================================================================
-- ADD CONSTRAINTS TO EXISTING TABLES
-- ============================================================================

-- Add constraints to ad_targets table
DO $$
BEGIN
    -- Add operator constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'chk_ad_targets_operator') THEN
        ALTER TABLE dtm_ads.ad_targets
        ADD CONSTRAINT chk_ad_targets_operator
        CHECK (operator IN ('equals', 'contains', 'startsWith', 'endsWith', 'in', 'notIn', 'gte', 'lte', 'gt', 'lt'));
    END IF;

    -- Add key constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'chk_ad_targets_key') THEN
        ALTER TABLE dtm_ads.ad_targets
        ADD CONSTRAINT chk_ad_targets_key
        CHECK (key IN ('country_code', 'region', 'device_type', 'language', 'age', 'gender', 'interest', 'timezone'));
    END IF;
END $$;

-- Add constraints to daily_ad_reports table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints
                   WHERE constraint_name = 'chk_daily_ad_reports_metrics') THEN
        ALTER TABLE dtm_ads.daily_ad_reports
        ADD CONSTRAINT chk_daily_ad_reports_metrics
        CHECK (impressions >= 0 AND clicks >= 0 AND spend >= 0 AND unique_users >= 0);
    END IF;
END $$;
