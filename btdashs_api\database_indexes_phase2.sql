-- ============================================================================
-- BTDash API - Phase 2 Database Indexes and Constraints
-- ============================================================================
-- This script creates indexes and additional constraints for optimal performance
-- of Phase 2 features
-- ============================================================================

-- ============================================================================
-- BUDGET TRACKING INDEXES
-- ============================================================================

-- Advertiser balances indexes
CREATE INDEX IF NOT EXISTS idx_advertiser_balances_user_id 
    ON dtm_ads.advertiser_balances(user_id);
CREATE INDEX IF NOT EXISTS idx_advertiser_balances_last_updated 
    ON dtm_ads.advertiser_balances(last_updated DESC);

-- Billing transactions indexes
CREATE INDEX IF NOT EXISTS idx_billing_transactions_advertiser_id 
    ON dtm_ads.billing_transactions(advertiser_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_campaign_id 
    ON dtm_ads.billing_transactions(campaign_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_status 
    ON dtm_ads.billing_transactions(status);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_created_at 
    ON dtm_ads.billing_transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_amount 
    ON dtm_ads.billing_transactions(amount);

-- Composite index for spend queries
CREATE INDEX IF NOT EXISTS idx_billing_transactions_spend_queries 
    ON dtm_ads.billing_transactions(advertiser_id, created_at DESC) 
    WHERE amount < 0; -- Negative amounts are spend transactions

-- ============================================================================
-- NOTIFICATIONS & ADMIN INDEXES
-- ============================================================================

-- Ad notifications indexes
CREATE INDEX IF NOT EXISTS idx_ad_notifications_user_id 
    ON dtm_ads.ad_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_notifications_type 
    ON dtm_ads.ad_notifications(type);
CREATE INDEX IF NOT EXISTS idx_ad_notifications_is_read 
    ON dtm_ads.ad_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_ad_notifications_created_at 
    ON dtm_ads.ad_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ad_notifications_related_id 
    ON dtm_ads.ad_notifications(related_id);

-- Composite index for user notifications
CREATE INDEX IF NOT EXISTS idx_ad_notifications_user_unread 
    ON dtm_ads.ad_notifications(user_id, is_read, created_at DESC);

-- Admin actions indexes
CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_id 
    ON dtm_ads.admin_actions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_action 
    ON dtm_ads.admin_actions(action);
CREATE INDEX IF NOT EXISTS idx_admin_actions_entity_id 
    ON dtm_ads.admin_actions(entity_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_created_at 
    ON dtm_ads.admin_actions(created_at DESC);

-- Rejection reasons indexes
CREATE INDEX IF NOT EXISTS idx_rejection_reasons_entity_type 
    ON dtm_ads.rejection_reasons(entity_type);
CREATE INDEX IF NOT EXISTS idx_rejection_reasons_is_active 
    ON dtm_ads.rejection_reasons(is_active);

-- ============================================================================
-- TARGETING SYSTEM INDEXES
-- ============================================================================

-- Ad targets indexes
CREATE INDEX IF NOT EXISTS idx_ad_targets_ad_id 
    ON dtm_ads.ad_targets(ad_id);
CREATE INDEX IF NOT EXISTS idx_ad_targets_key 
    ON dtm_ads.ad_targets(key);
CREATE INDEX IF NOT EXISTS idx_ad_targets_value 
    ON dtm_ads.ad_targets(value);

-- Composite index for targeting queries
CREATE INDEX IF NOT EXISTS idx_ad_targets_key_value 
    ON dtm_ads.ad_targets(key, value);

-- ============================================================================
-- ANALYTICS INDEXES
-- ============================================================================

-- Daily ad reports indexes (works with both old and new table names)
DO $$
BEGIN
    -- Check which table name exists and create indexes accordingly
    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_schema = 'dtm_ads'
               AND table_name = 'daily_ad_reports') THEN

        CREATE INDEX IF NOT EXISTS idx_daily_ad_reports_ad_id
            ON dtm_ads.daily_ad_reports(ad_id);
        CREATE INDEX IF NOT EXISTS idx_daily_ad_reports_date
            ON dtm_ads.daily_ad_reports(date DESC);
        CREATE INDEX IF NOT EXISTS idx_daily_ad_reports_impressions
            ON dtm_ads.daily_ad_reports(impressions DESC);
        CREATE INDEX IF NOT EXISTS idx_daily_ad_reports_clicks
            ON dtm_ads.daily_ad_reports(clicks DESC);
        CREATE INDEX IF NOT EXISTS idx_daily_ad_reports_analytics
            ON dtm_ads.daily_ad_reports(ad_id, date DESC);

    ELSIF EXISTS (SELECT 1 FROM information_schema.tables
                  WHERE table_schema = 'dtm_ads'
                  AND table_name = 'ad_reports_daily') THEN

        CREATE INDEX IF NOT EXISTS idx_ad_reports_daily_ad_id
            ON dtm_ads.ad_reports_daily(ad_id);
        CREATE INDEX IF NOT EXISTS idx_ad_reports_daily_date
            ON dtm_ads.ad_reports_daily(date DESC);
        CREATE INDEX IF NOT EXISTS idx_ad_reports_daily_impressions
            ON dtm_ads.ad_reports_daily(impressions DESC);
        CREATE INDEX IF NOT EXISTS idx_ad_reports_daily_clicks
            ON dtm_ads.ad_reports_daily(clicks DESC);
        CREATE INDEX IF NOT EXISTS idx_ad_reports_daily_analytics
            ON dtm_ads.ad_reports_daily(ad_id, date DESC);
    END IF;
END $$;

-- Campaign daily reports indexes
CREATE INDEX IF NOT EXISTS idx_campaign_daily_reports_campaign_id 
    ON dtm_ads.campaign_daily_reports(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_daily_reports_date 
    ON dtm_ads.campaign_daily_reports(date DESC);
CREATE INDEX IF NOT EXISTS idx_campaign_daily_reports_spend 
    ON dtm_ads.campaign_daily_reports(spend DESC);

-- Composite index for campaign analytics queries
CREATE INDEX IF NOT EXISTS idx_campaign_daily_reports_analytics 
    ON dtm_ads.campaign_daily_reports(campaign_id, date DESC);

-- ============================================================================
-- ENHANCED EXISTING TABLE INDEXES
-- ============================================================================

-- Additional indexes for ad_impressions (country and device targeting)
CREATE INDEX IF NOT EXISTS idx_ad_impressions_country_code 
    ON dtm_ads.ad_impressions(country_code);
CREATE INDEX IF NOT EXISTS idx_ad_impressions_device_type 
    ON dtm_ads.ad_impressions(device_type);

-- Composite index for impression analytics with targeting
CREATE INDEX IF NOT EXISTS idx_ad_impressions_targeting_analytics 
    ON dtm_ads.ad_impressions(ad_id, country_code, device_type, created_at DESC);

-- Additional indexes for ad_clicks (country and device targeting)
CREATE INDEX IF NOT EXISTS idx_ad_clicks_country_code 
    ON dtm_ads.ad_clicks(country_code);
CREATE INDEX IF NOT EXISTS idx_ad_clicks_device_type 
    ON dtm_ads.ad_clicks(device_type);

-- Composite index for click analytics with targeting
CREATE INDEX IF NOT EXISTS idx_ad_clicks_targeting_analytics 
    ON dtm_ads.ad_clicks(ad_id, country_code, device_type, created_at DESC);

-- Index for admin users
CREATE INDEX IF NOT EXISTS idx_users_is_admin 
    ON dtm_base.users(is_admin) WHERE is_admin = TRUE;

-- ============================================================================
-- FOREIGN KEY CONSTRAINTS (if not already added)
-- ============================================================================

-- Add foreign key for advertiser_balances.last_transaction_id
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'fk_advertiser_balances_last_transaction_id') THEN
        ALTER TABLE dtm_ads.advertiser_balances 
        ADD CONSTRAINT fk_advertiser_balances_last_transaction_id 
        FOREIGN KEY (last_transaction_id) REFERENCES dtm_ads.billing_transactions(id) ON DELETE SET NULL;
    END IF;
END $$;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Create or replace the update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to new tables
DROP TRIGGER IF EXISTS update_advertiser_balances_updated_at ON dtm_ads.advertiser_balances;
CREATE TRIGGER update_advertiser_balances_updated_at 
    BEFORE UPDATE ON dtm_ads.advertiser_balances 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_billing_transactions_updated_at ON dtm_ads.billing_transactions;
CREATE TRIGGER update_billing_transactions_updated_at 
    BEFORE UPDATE ON dtm_ads.billing_transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_ad_notifications_updated_at ON dtm_ads.ad_notifications;
CREATE TRIGGER update_ad_notifications_updated_at 
    BEFORE UPDATE ON dtm_ads.ad_notifications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_rejection_reasons_updated_at ON dtm_ads.rejection_reasons;
CREATE TRIGGER update_rejection_reasons_updated_at 
    BEFORE UPDATE ON dtm_ads.rejection_reasons 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_ad_targets_updated_at ON dtm_ads.ad_targets;
CREATE TRIGGER update_ad_targets_updated_at 
    BEFORE UPDATE ON dtm_ads.ad_targets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add triggers for daily ad reports (handle both table names)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_schema = 'dtm_ads'
               AND table_name = 'daily_ad_reports') THEN

        DROP TRIGGER IF EXISTS update_daily_ad_reports_updated_at ON dtm_ads.daily_ad_reports;
        CREATE TRIGGER update_daily_ad_reports_updated_at
            BEFORE UPDATE ON dtm_ads.daily_ad_reports
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

DROP TRIGGER IF EXISTS update_campaign_daily_reports_updated_at ON dtm_ads.campaign_daily_reports;
CREATE TRIGGER update_campaign_daily_reports_updated_at
    BEFORE UPDATE ON dtm_ads.campaign_daily_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- PERFORMANCE OPTIMIZATION VIEWS
-- ============================================================================

-- View for active campaigns with spend summary
DO $$
BEGIN
    -- Create view based on which daily reports table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_schema = 'dtm_ads'
               AND table_name = 'daily_ad_reports') THEN

        CREATE OR REPLACE VIEW dtm_ads.v_active_campaigns_summary AS
        SELECT
            c.id,
            c.name,
            c.advertiser_id,
            c.total_budget,
            c.start_date,
            c.end_date,
            c.status,
            COALESCE(SUM(ABS(bt.amount)), 0) as total_spend,
            COUNT(DISTINCT a.id) as ad_count,
            COALESCE(SUM(dai.impressions), 0) as total_impressions,
            COALESCE(SUM(dai.clicks), 0) as total_clicks
        FROM dtm_ads.ad_campaigns c
        LEFT JOIN dtm_ads.billing_transactions bt ON c.id = bt.campaign_id AND bt.amount < 0
        LEFT JOIN dtm_ads.ads a ON c.id = a.campaign_id
        LEFT JOIN dtm_ads.daily_ad_reports dai ON a.id = dai.ad_id
        WHERE c.status = 'active'
        GROUP BY c.id, c.name, c.advertiser_id, c.total_budget, c.start_date, c.end_date, c.status;

    ELSIF EXISTS (SELECT 1 FROM information_schema.tables
                  WHERE table_schema = 'dtm_ads'
                  AND table_name = 'ad_reports_daily') THEN

        CREATE OR REPLACE VIEW dtm_ads.v_active_campaigns_summary AS
        SELECT
            c.id,
            c.name,
            c.advertiser_id,
            c.total_budget,
            c.start_date,
            c.end_date,
            c.status,
            COALESCE(SUM(ABS(bt.amount)), 0) as total_spend,
            COUNT(DISTINCT a.id) as ad_count,
            COALESCE(SUM(ard.impressions), 0) as total_impressions,
            COALESCE(SUM(ard.clicks), 0) as total_clicks
        FROM dtm_ads.ad_campaigns c
        LEFT JOIN dtm_ads.billing_transactions bt ON c.id = bt.campaign_id AND bt.amount < 0
        LEFT JOIN dtm_ads.ads a ON c.id = a.campaign_id
        LEFT JOIN dtm_ads.ad_reports_daily ard ON a.id = ard.ad_id
        WHERE c.status = 'active'
        GROUP BY c.id, c.name, c.advertiser_id, c.total_budget, c.start_date, c.end_date, c.status;
    ELSE
        -- Create basic view without daily reports if table doesn't exist
        CREATE OR REPLACE VIEW dtm_ads.v_active_campaigns_summary AS
        SELECT
            c.id,
            c.name,
            c.advertiser_id,
            c.total_budget,
            c.start_date,
            c.end_date,
            c.status,
            COALESCE(SUM(ABS(bt.amount)), 0) as total_spend,
            COUNT(DISTINCT a.id) as ad_count,
            0 as total_impressions,
            0 as total_clicks
        FROM dtm_ads.ad_campaigns c
        LEFT JOIN dtm_ads.billing_transactions bt ON c.id = bt.campaign_id AND bt.amount < 0
        LEFT JOIN dtm_ads.ads a ON c.id = a.campaign_id
        WHERE c.status = 'active'
        GROUP BY c.id, c.name, c.advertiser_id, c.total_budget, c.start_date, c.end_date, c.status;
    END IF;
END $$;

-- View for user balance with recent activity
CREATE OR REPLACE VIEW dtm_ads.v_user_balance_summary AS
SELECT 
    ab.user_id,
    ab.balance,
    ab.currency,
    ab.last_updated,
    COUNT(bt.id) as transaction_count,
    COALESCE(SUM(CASE WHEN bt.amount > 0 THEN bt.amount ELSE 0 END), 0) as total_deposits,
    COALESCE(SUM(CASE WHEN bt.amount < 0 THEN ABS(bt.amount) ELSE 0 END), 0) as total_spend
FROM dtm_ads.advertiser_balances ab
LEFT JOIN dtm_ads.billing_transactions bt ON ab.user_id = bt.advertiser_id
GROUP BY ab.user_id, ab.balance, ab.currency, ab.last_updated;
