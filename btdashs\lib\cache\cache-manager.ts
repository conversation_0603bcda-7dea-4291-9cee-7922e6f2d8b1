// lib/cache/cache-manager.ts
import Redis from "ioredis";

const redisUrl = process.env.REDIS_URL;
if (!redisUrl) {
	throw new Error("REDIS_URL environment variable is not defined");
}

// Global state for error suppression
const errorState = {
	lastErrorLogged: 0,
	errorThrottleMs: 30000, // 30 seconds
	maxRetriesErrorLogged: false,
	connectionErrorsLogged: 0,
	maxConnectionErrorsBeforeSilence: 3,
};

function logError(error: Error, context?: string) {
	const now = Date.now();

	// Special handling for MaxRetriesPerRequestError
	if (error.message.includes("MaxRetriesPerRequestError")) {
		if (!errorState.maxRetriesErrorLogged) {
			//console.error("[Redis] Operation failed after retries:", context || "");
			errorState.maxRetriesErrorLogged = true;
		}
		return;
	}

	// General error throttling
	if (now - errorState.lastErrorLogged > errorState.errorThrottleMs) {
		//console.error(`[Redis] ${context || "Error"}:`, error.message);
		if (process.env.NODE_ENV !== "production") {
			//console.error(error.stack);
		}
		errorState.lastErrorLogged = now;
		errorState.connectionErrorsLogged++;

		// After several connection errors, stop logging until recovery
		if (errorState.connectionErrorsLogged >= errorState.maxConnectionErrorsBeforeSilence) {
			//console.error("[Redis] Silencing further connection errors until recovery");
		}
	}
}

// Enhanced Redis client configuration
const redis = new Redis(redisUrl, {
	tls: {
		rejectUnauthorized: false,
	},
	retryStrategy: (times) => {
		if (times > 5) {
			// Reduced from 10 to fail faster
			return null;
		}
		return Math.min(times * 1000, 5000);
	},
	maxRetriesPerRequest: 1, // Reduced from 3 to fail faster
	reconnectOnError: (err) => {
		const targetErrors = ["ECONNRESET", "ETIMEDOUT", "ECONNREFUSED"];
		return targetErrors.some((target) => err.message.includes(target));
	},
	enableOfflineQueue: false, // Disabled to fail fast when offline
	connectTimeout: 10000,
	commandTimeout: 5000,
	showFriendlyErrorStack: false,
	autoResendUnfulfilledCommands: false,
});

// Connection event handlers
redis.on("ready", () => {
	//console.log("[Redis] Connected");
	errorState.maxRetriesErrorLogged = false;
	errorState.connectionErrorsLogged = 0;
});

redis.on("error", (err) => {
	if (errorState.connectionErrorsLogged < errorState.maxConnectionErrorsBeforeSilence) {
		logError(err, "Connection error");
	}
});

redis.on("end", () => {
	//console.log("[Redis] Connection ended");
});

const TTL = 10 * 60; // 10 minutes in seconds

function listKey(table: string): string {
	return `cache:${table}:list`;
}

function singleKey(table: string, id: string): string {
	return `cache:${table}:single:${id}`;
}

class RedisCacheManager {
	private isAvailable = true;

	constructor() {
		this.checkConnection();
		// Periodic health check every 30 seconds
		setInterval(() => this.checkConnection(), 30000);
	}

	private async checkConnection() {
		try {
			await redis.ping();
			if (!this.isAvailable) {
				this.isAvailable = true;
				//console.log("[Redis] Connection restored");
				errorState.maxRetriesErrorLogged = false;
				errorState.connectionErrorsLogged = 0;
			}
		} catch (error) {
			if (this.isAvailable) {
				this.isAvailable = false;
				//console.error("[Redis] Connection lost");
			}
		}
	}

	private async execute<T>(fn: () => Promise<T>, context: string): Promise<T | null> {
		if (!this.isAvailable) {
			return null;
		}

		try {
			return await fn();
		} catch (error) {
			logError(error as Error, context);

			// Mark as unavailable if it's a connection error
			if ((error as Error).message.includes("ECONNRESET") || (error as Error).message.includes("ETIMEDOUT")) {
				this.isAvailable = false;
			}

			return null;
		}
	}

	// List cache
	async getList<T>(table: string): Promise<T[] | null> {
		return this.execute(async () => {
			const data = await redis.get(listKey(table));
			return data ? JSON.parse(data) : null;
		}, `getList for ${table}`);
	}

	async setList<T>(table: string, data: T[]): Promise<void> {
		await this.execute(async () => {
			await redis.set(listKey(table), JSON.stringify(data), "EX", TTL);
		}, `setList for ${table}`);
	}

	// Single item cache
	async getSingle<T>(table: string, id: string): Promise<T | null> {
		return this.execute(async () => {
			const data = await redis.get(singleKey(table, id));
			return data ? JSON.parse(data) : null;
		}, `getSingle for ${table}/${id}`);
	}

	async setSingle<T>(table: string, id: string, data: T): Promise<void> {
		await this.execute(async () => {
			await redis.set(singleKey(table, id), JSON.stringify(data), "EX", TTL);
		}, `setSingle for ${table}/${id}`);
	}

	// Clear cache
	async clearCache(table?: string): Promise<void> {
		await this.execute(async () => {
			const pattern = table ? `cache:${table}:*` : "cache:*";
			let cursor = "0";
			let keys: string[] = [];

			do {
				const reply = await redis.scan(cursor, "MATCH", pattern, "COUNT", 100);
				cursor = reply[0];
				keys = [...keys, ...reply[1]];

				if (keys.length >= 100) {
					await redis.del(...keys);
					keys = [];
				}
			} while (cursor !== "0");

			if (keys.length > 0) {
				await redis.del(...keys);
			}
		}, `clearCache for ${table || "all"}`);
	}

	// Get cache stats
	async getStats(): Promise<{
		lists: Array<{
			table: string;
			itemCount: number;
			key: string;
			ttl: number;
		}>;
		totalLists: number;
		totalItems: number;
		summary: string;
		status: string;
	}> {
		const result = await this.execute(async () => {
			let cursor = "0";
			let listKeys: string[] = [];

			do {
				const reply = await redis.scan(cursor, "MATCH", "cache:*:list", "COUNT", 100);
				cursor = reply[0];
				listKeys = [...listKeys, ...reply[1]];
			} while (cursor !== "0");

			const stats = {
				lists: [] as Array<{
					table: string;
					itemCount: number;
					key: string;
					ttl: number;
				}>,
				totalLists: 0,
				totalItems: 0,
				summary: "",
				status: this.isAvailable ? "connected" : "disconnected",
			};

			for (const key of listKeys) {
				try {
					const [_, table] = key.split(":");
					const data = await redis.get(key);
					let itemCount = 0;

					if (data) {
						try {
							const parsed = JSON.parse(data);
							itemCount = Array.isArray(parsed) ? parsed.length : 0;
						} catch (error) {
							console.error(`Error parsing cache data for ${key}:`, error);
						}
					}

					const ttl = await redis.ttl(key);

					stats.lists.push({
						table,
						itemCount,
						key,
						ttl,
					});

					stats.totalItems += itemCount;
				} catch (error) {
					console.error(`Error processing key ${key}:`, error);
				}
			}

			stats.totalLists = stats.lists.length;
			stats.lists.sort((a, b) => b.itemCount - a.itemCount);
			stats.summary =
				stats.lists.map((item) => ` [${item.table} -> ${item.itemCount}]`).join("") +
				` [Total lists: ${stats.totalLists}] ` +
				`[Total items: ${stats.totalItems}]`;

			return stats;
		}, "getStats");

		return (
			result || {
				lists: [],
				totalLists: 0,
				totalItems: 0,
				summary: "Cache unavailable",
				status: "disconnected",
			}
		);
	}

	async disconnect(): Promise<void> {
		try {
			await redis.quit();
		} catch (error) {
			logError(error as Error, "disconnect");
		}
	}
}

export const cacheManager = new RedisCacheManager();
