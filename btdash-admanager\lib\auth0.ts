// lib/auth0.js

import { Auth0Client } from "@auth0/nextjs-auth0/server";

// Initialize the Auth0 client
export const auth0 = new Auth0Client({
	session: {
		rolling: true,
		cookie: {
			name: "app_session",
			path: "/",
			sameSite: "lax",
			secure: process.env.NODE_ENV === "production",
		},
	},
	authorizationParameters: {
		scope: process.env.AUTH0_SCOPE,
		audience: process.env.AUTH0_AUDIENCE,
	},
});
