"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import type { UserPreferences } from "@/lib/db/models";
import { Save, X } from "lucide-react";
import { useEffect, useState } from "react";

interface ProfilePreferencesClientProps {
	initialPreferences: UserPreferences;
}

export default function ProfilePreferencesClient({ initialPreferences }: ProfilePreferencesClientProps) {
	const [preferences, setPreferences] = useState<UserPreferences>(initialPreferences);

	// **Sync state whenever initialPreferences changes**
	useEffect(() => {
		setPreferences(initialPreferences);
	}, [initialPreferences]);

	const [newLocation, setNewLocation] = useState("");
	const [newIndustry, setNewIndustry] = useState("");
	const [newJobType, setNewJobType] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSwitchChange = (name: keyof UserPreferences) => (checked: boolean) =>
		setPreferences((prev) => ({ ...prev, [name]: checked }));

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
		const { name, value } = e.target;
		setPreferences((prev) => ({ ...prev, [name]: value }));
	};

	const handleAdd = (
		listKey: "locations" | "industries" | "job_types",
		newValue: string,
		setNew: (s: string) => void
	) => {
		const trimmed = newValue.trim();
		if (!trimmed || preferences[listKey]?.includes(trimmed)) return;
		setPreferences((prev) => ({
			...prev,
			[listKey]: [...(prev[listKey] || []), trimmed],
		}));
		setNew("");
	};

	const handleRemove = (listKey: "locations" | "industries" | "job_types", value: string) =>
		setPreferences((prev) => ({
			...prev,
			[listKey]: prev[listKey]?.filter((item) => item !== value) || [],
		}));

	const handleSave = async () => {
		setIsLoading(true);
		setError(null);
		try {
			const res = await fetch("/api/user/preferences", {
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(preferences),
			});
			if (!res.ok) {
				const err = await res.json();
				throw new Error(err.error || "Failed to save preferences");
			}
			const json = await res.json();

			const data = json.data.data as UserPreferences;

			setPreferences(data);
		} catch (err: any) {
			setError(err.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Job Preferences</h2>
				<Button onClick={handleSave} disabled={isLoading}>
					<Save className="mr-2 h-4 w-4" />
					{isLoading ? "Saving..." : "Save Preferences"}
				</Button>
			</div>

			{error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>}

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Job Types */}
				<Card>
					<CardHeader>
						<CardTitle>Job Types</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex flex-wrap gap-2 mb-2">
							{(preferences?.job_types || []).map((jt) => (
								<Badge key={jt} variant="secondary" className="flex items-center gap-1">
									{jt}
									<button
										onClick={() => handleRemove("job_types", jt)}
										disabled={isLoading}
										className="ml-1 rounded-full hover:bg-gray-200 p-0.5"
									>
										<X className="h-3 w-3" />
										<span className="sr-only">Remove {jt}</span>
									</button>
								</Badge>
							))}
						</div>
						<div className="flex gap-2">
							<Input
								placeholder="Add job type..."
								value={newJobType}
								onChange={(e) => setNewJobType(e.target.value)}
								disabled={isLoading}
								className="flex-1"
							/>
							<Button
								onClick={() => handleAdd("job_types", newJobType, setNewJobType)}
								disabled={isLoading}
							>
								Add
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* Locations */}
				<Card>
					<CardHeader>
						<CardTitle>Locations</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex flex-wrap gap-2 mb-2">
							{(preferences?.locations || []).map((loc) => (
								<Badge key={loc} variant="secondary" className="flex items-center gap-1">
									{loc}
									<button
										onClick={() => handleRemove("locations", loc)}
										disabled={isLoading}
										className="ml-1 rounded-full hover:bg-gray-200 p-0.5"
									>
										<X className="h-3 w-3" />
										<span className="sr-only">Remove {loc}</span>
									</button>
								</Badge>
							))}
						</div>
						<div className="flex gap-2">
							<Input
								placeholder="Add location..."
								value={newLocation}
								onChange={(e) => setNewLocation(e.target.value)}
								disabled={isLoading}
								className="flex-1"
							/>
							<Button
								onClick={() => handleAdd("locations", newLocation, setNewLocation)}
								disabled={isLoading}
							>
								Add
							</Button>
						</div>
						<div className="flex items-center justify-between">
							<Label htmlFor="open_to_relocation">Open to Relocation</Label>
							<Switch
								id="open_to_relocation"
								checked={preferences?.open_to_relocation || false}
								onCheckedChange={handleSwitchChange("open_to_relocation")}
								disabled={isLoading}
							/>
						</div>
					</CardContent>
				</Card>

				{/* Industries */}
				<Card>
					<CardHeader>
						<CardTitle>Industries</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex flex-wrap gap-2 mb-2">
							{(preferences?.industries || []).map((ind) => (
								<Badge key={ind} variant="secondary" className="flex items-center gap-1">
									{ind}
									<button
										onClick={() => handleRemove("industries", ind)}
										disabled={isLoading}
										className="ml-1 rounded-full hover:bg-gray-200 p-0.5"
									>
										<X className="h-3 w-3" />
										<span className="sr-only">Remove {ind}</span>
									</button>
								</Badge>
							))}
						</div>
						<div className="flex gap-2">
							<Input
								placeholder="Add industry..."
								value={newIndustry}
								onChange={(e) => setNewIndustry(e.target.value)}
								disabled={isLoading}
								className="flex-1"
							/>
							<Button
								onClick={() => handleAdd("industries", newIndustry, setNewIndustry)}
								disabled={isLoading}
							>
								Add
							</Button>
						</div>
					</CardContent>
				</Card>

				{/* Compensation */}
				<Card>
					<CardHeader>
						<CardTitle>Compensation</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="compensation_range">Compensation Range</Label>
							<select
								id="compensation_range"
								name="compensation_range"
								value={preferences?.compensation_range || ""}
								onChange={handleInputChange}
								disabled={isLoading}
								className="w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm"
							>
								<option value="">Select range</option>
								<option value="50k-100k">$50k - $100k</option>
								<option value="100k-150k">$100k - $150k</option>
								<option value="150k-200k">$150k - $200k</option>
								<option value="200k+">$200k+</option>
							</select>
						</div>
					</CardContent>
				</Card>

				{/* Travel */}
				<Card>
					<CardHeader>
						<CardTitle>Travel Availability</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="travel_availability">Travel Requirements</Label>
							<select
								id="travel_availability"
								name="travel_availability"
								value={preferences?.travel_availability || ""}
								onChange={handleInputChange}
								disabled={isLoading}
								className="w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm"
							>
								<option value="">Select travel requirements</option>
								<option value="none">No travel</option>
								<option value="some">Some travel required</option>
								<option value="extensive">Extensive travel required</option>
							</select>
						</div>
					</CardContent>
				</Card>

				{/* Notes */}
				<Card className="md:col-span-2">
					<CardHeader>
						<CardTitle>Additional Notes</CardTitle>
					</CardHeader>
					<CardContent>
						<Textarea
							id="additional_notes"
							name="additional_notes"
							rows={4}
							value={preferences?.additional_notes || ""}
							onChange={handleInputChange}
							disabled={isLoading}
							placeholder="Add any additional information about your job preferences..."
						/>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
