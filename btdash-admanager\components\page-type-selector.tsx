"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { pageTypesWithCategories } from "@/lib/page-categories";
import * as React from "react";

interface PageTypeSelectorProps {
	selectedTypes: string[];
	selectedCategories: { [pageType: string]: string[] | "all" };
	onTypeChange: (types: string[]) => void;
	onCategoryChange: (pageType: string, categories: string[] | "all") => void;
}

export function PageTypeSelector({
	selectedTypes,
	selectedCategories,
	onTypeChange,
	onCategoryChange,
}: PageTypeSelectorProps) {
	const [open, setOpen] = React.useState(false);
	const [activePageType, setActivePageType] = React.useState<string | null>(null);

	const handleTypeToggle = (type: string) => {
		const newSelectedTypes = selectedTypes.includes(type)
			? selectedTypes.filter((t) => t !== type)
			: [...selectedTypes, type];

		onTypeChange(newSelectedTypes);

		// If we're adding a new type, initialize its categories as "all"
		if (!selectedTypes.includes(type)) {
			onCategoryChange(type, "all");
		}
	};

	const handleCategoryToggle = (pageType: string, categoryId: string) => {
		const currentCategories = selectedCategories[pageType];

		// If currently set to "all", switch to specific selection with all items except the toggled one
		if (currentCategories === "all") {
			const pageTypeObj = pageTypesWithCategories.find((pt) => pt.type === pageType);
			if (!pageTypeObj) return;

			// Get all category IDs except the one being toggled
			const allCategoryIds = pageTypeObj.categories.map((cat) => cat.id);
			const allCategoriesExceptToggled = allCategoryIds.filter((id) => id !== categoryId);

			onCategoryChange(pageType, allCategoriesExceptToggled);
			return;
		}

		// Otherwise, toggle the specific category
		const newCategories = Array.isArray(currentCategories)
			? currentCategories.includes(categoryId)
				? currentCategories.filter((id) => id !== categoryId)
				: [...currentCategories, categoryId]
			: [categoryId];

		// If all categories are now selected, switch to "all" mode
		const pageTypeObj = pageTypesWithCategories.find((pt) => pt.type === pageType);
		if (pageTypeObj && newCategories.length === pageTypeObj.categories.length) {
			onCategoryChange(pageType, "all");
			return;
		}

		onCategoryChange(pageType, newCategories);
	};

	const handleSelectAllCategories = (pageType: string) => {
		onCategoryChange(pageType, "all");
	};

	const getSelectedCategoriesCount = (pageType: string) => {
		const categories = selectedCategories[pageType];
		if (categories === "all") return "All";
		if (!categories || !Array.isArray(categories)) return "0";

		const pageTypeObj = pageTypesWithCategories.find((pt) => pt.type === pageType);
		if (!pageTypeObj) return "0";

		if (categories.length === pageTypeObj.categories.length) return "All";
		return categories.length.toString();
	};

	return (
		<div className="space-y-4">
			<div className="space-y-2">
				<Label>Select Page Types</Label>
				<div className="flex flex-wrap gap-2">
					{pageTypesWithCategories.map((pageType) => (
						<div key={pageType.type} className="flex items-center space-x-2">
							<Checkbox
								id={`page-type-${pageType.type}`}
								checked={selectedTypes.includes(pageType.type)}
								onCheckedChange={() => handleTypeToggle(pageType.type)}
							/>
							<Label
								htmlFor={`page-type-${pageType.type}`}
								className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
							>
								{pageType.label}
							</Label>
						</div>
					))}
				</div>
			</div>

			{selectedTypes.length > 0 && (
				<div className="space-y-2">
					<Label>Configure Categories</Label>
					<div className="space-y-2">
						{selectedTypes.map((type) => {
							const pageType = pageTypesWithCategories.find((pt) => pt.type === type);

							// Skip if pageType is not found (invalid type)
							if (!pageType) {
								console.warn(`Invalid page type: ${type}`);
								return null;
							}

							return (
								<div key={type} className="rounded-md border p-4">
									<div className="flex items-center justify-between mb-2">
										<h4 className="font-medium">{pageType.label}</h4>
										<div className="flex items-center gap-2">
											<span className="text-xs text-muted-foreground">
												Selected: {getSelectedCategoriesCount(type)}
											</span>
											<Button
												variant="outline"
												size="sm"
												onClick={() => {
													setActivePageType(activePageType === type ? null : type);
												}}
											>
												{activePageType === type ? "Hide" : "Configure"}
											</Button>
										</div>
									</div>
									{activePageType === type && (
										<div className="mt-2 space-y-2 pl-4 border-l-2">
											<div className="flex items-center space-x-2">
												<Checkbox
													id={`${type}-all`}
													checked={selectedCategories[type] === "all"}
													onCheckedChange={() => handleSelectAllCategories(type)}
												/>
												<Label
													htmlFor={`${type}-all`}
													className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
												>
													All Categories
												</Label>
											</div>
											<div className="mt-2 pl-2 space-y-1">
												{selectedCategories[type] !== "all" &&
													pageType.categories.map((category) => (
														<div key={category.id} className="flex items-center space-x-2">
															<Checkbox
																id={category.id}
																checked={
																	selectedCategories[type] === "all" ||
																	(Array.isArray(selectedCategories[type]) &&
																		(selectedCategories[type] as string[]).includes(
																			category.id
																		))
																}
																onCheckedChange={() =>
																	handleCategoryToggle(type, category.id)
																}
															/>
															<Label
																htmlFor={category.id}
																className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
															>
																{category.name}
															</Label>
														</div>
													))}
											</div>
										</div>
									)}
								</div>
							);
						})}
					</div>
				</div>
			)}
		</div>
	);
}
