import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar"

const subnets = [
  { name: "Dashboard", icon: Home, url: "/" },
  { name: "Subnet 1", icon: Cpu, url: "/subnet-1" },
  { name: "Subnet 2", icon: Bar<PERSON><PERSON>, url: "/subnet-2" },
  { name: "Subnet 3", icon: Users, url: "/subnet-3" },
]

export function AppSidebar() {
  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Bittensor Subnets</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {subnets.map((subnet) => (
                <SidebarMenuItem key={subnet.name}>
                  <SidebarMenuButton asChild>
                    <a href={subnet.url}>
                      <subnet.icon className="mr-2 h-4 w-4" />
                      <span>{subnet.name}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}

