// src/presentation/routes/docs.js - API Documentation routes

const express = require('express');
const path = require('path');
const fs = require('fs');
const yaml = require('js-yaml');

const router = express.Router();

/**
 * Serve Swagger UI for API documentation
 */
router.get('/', (req, res) => {
  const swaggerUiHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>BTDash API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
    .swagger-ui .topbar {
      background-color: #2c3e50;
    }
    .swagger-ui .topbar .download-url-wrapper {
      display: none;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: '/api/docs/openapi.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        validatorUrl: null,
        tryItOutEnabled: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        onComplete: function() {
          console.log('Swagger UI loaded successfully');
        },
        onFailure: function(data) {
          console.error('Failed to load Swagger UI:', data);
        }
      });
    };
  </script>
</body>
</html>`;

  res.send(swaggerUiHtml);
});

/**
 * Serve OpenAPI specification as JSON
 */
router.get('/openapi.json', (req, res) => {
  try {
    const openApiPath = path.join(__dirname, '../../../docs/openapi.yaml');
    const openApiYaml = fs.readFileSync(openApiPath, 'utf8');
    const openApiJson = yaml.load(openApiYaml);
    
    // Update server URLs based on request
    const protocol = req.get('X-Forwarded-Proto') || req.protocol;
    const host = req.get('Host');
    const baseUrl = `${protocol}://${host}`;
    
    openApiJson.servers = [
      {
        url: baseUrl,
        description: 'Current server'
      },
      ...openApiJson.servers.filter(server => server.url !== baseUrl)
    ];
    
    res.json(openApiJson);
  } catch (error) {
    console.error('Error loading OpenAPI specification:', error);
    res.status(500).json({
      error: 'Failed to load API documentation',
      message: 'The OpenAPI specification could not be loaded'
    });
  }
});

/**
 * Serve OpenAPI specification as YAML
 */
router.get('/openapi.yaml', (req, res) => {
  try {
    const openApiPath = path.join(__dirname, '../../../docs/openapi.yaml');
    const openApiYaml = fs.readFileSync(openApiPath, 'utf8');
    
    res.setHeader('Content-Type', 'application/x-yaml');
    res.send(openApiYaml);
  } catch (error) {
    console.error('Error loading OpenAPI specification:', error);
    res.status(500).json({
      error: 'Failed to load API documentation',
      message: 'The OpenAPI specification could not be loaded'
    });
  }
});

/**
 * API documentation landing page with links to different formats
 */
router.get('/info', (req, res) => {
  const infoHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BTDash API Documentation</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      line-height: 1.6;
      color: #333;
    }
    .header {
      text-align: center;
      margin-bottom: 3rem;
      padding-bottom: 2rem;
      border-bottom: 2px solid #eee;
    }
    .header h1 {
      color: #2c3e50;
      margin-bottom: 0.5rem;
    }
    .header p {
      color: #666;
      font-size: 1.1rem;
    }
    .links {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 3rem;
    }
    .link-card {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 1.5rem;
      text-decoration: none;
      color: inherit;
      transition: all 0.2s ease;
    }
    .link-card:hover {
      background: #e9ecef;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .link-card h3 {
      margin: 0 0 0.5rem 0;
      color: #2c3e50;
    }
    .link-card p {
      margin: 0;
      color: #666;
      font-size: 0.9rem;
    }
    .info-section {
      background: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 1rem 1.5rem;
      margin: 2rem 0;
    }
    .info-section h3 {
      margin-top: 0;
      color: #2c3e50;
    }
    code {
      background: #e9ecef;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-family: 'Monaco', 'Consolas', monospace;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>BTDash API Documentation</h1>
    <p>Comprehensive API documentation for the BTDash platform</p>
  </div>

  <div class="links">
    <a href="/api/docs" class="link-card">
      <h3>📖 Interactive Documentation</h3>
      <p>Explore and test API endpoints with Swagger UI</p>
    </a>
    
    <a href="/api/docs/openapi.json" class="link-card">
      <h3>📄 OpenAPI JSON</h3>
      <p>Download the OpenAPI 3.0 specification in JSON format</p>
    </a>
    
    <a href="/api/docs/openapi.yaml" class="link-card">
      <h3>📝 OpenAPI YAML</h3>
      <p>Download the OpenAPI 3.0 specification in YAML format</p>
    </a>
    
    <a href="/health" class="link-card">
      <h3>💚 Health Check</h3>
      <p>Check the current status of the API</p>
    </a>
  </div>

  <div class="info-section">
    <h3>🔐 Authentication</h3>
    <p>Most endpoints require JWT authentication via Auth0. Include your token in the Authorization header:</p>
    <p><code>Authorization: Bearer &lt;your-jwt-token&gt;</code></p>
  </div>

  <div class="info-section">
    <h3>📊 Response Format</h3>
    <p>All API responses follow a standardized format with <code>success</code>, <code>data</code>, and <code>message</code> fields.</p>
  </div>

  <div class="info-section">
    <h3>🚦 Rate Limiting</h3>
    <p>API requests are rate-limited to prevent abuse:</p>
    <ul>
      <li>100 requests per minute for authenticated users</li>
      <li>20 requests per minute for unauthenticated users</li>
    </ul>
  </div>
</body>
</html>`;

  res.send(infoHtml);
});

module.exports = router;
