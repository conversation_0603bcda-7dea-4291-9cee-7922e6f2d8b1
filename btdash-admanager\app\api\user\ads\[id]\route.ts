// app/api/user/ads/[id]/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

// GET /api/user/ads/[id] - Get specific ad
export async function GET(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/ads/${id}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) throw new Error("Failed to fetch ad details");

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}

// PUT /api/user/ads/[id] - Update ad
export async function PUT(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;
		const body = await request.json();

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/ads/${id}`, {
			method: "PUT",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${token}`,
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Failed to update ad: ${errorText}`);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}

// DELETE /api/user/ads/[id] - Delete ad
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/ads/${id}`, {
			method: "DELETE",
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Failed to delete ad: ${errorText}`);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}
