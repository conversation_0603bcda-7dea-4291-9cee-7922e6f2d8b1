// app/dashboard/ads/[id]/analytics/page.tsx
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, BarChart3, Eye, MousePointer, TrendingUp } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Ad {
	id: number;
	campaign_id: number;
	title: string;
	status: string;
	image_url: string;
	target_url: string;
}

interface Analytics {
	impressions: number;
	clicks: number;
	ctr: number;
	spend: number;
	avgCpc: number;
	avgCpm: number;
	conversions: number;
	conversionRate: number;
}

export default function AdAnalyticsPage() {
	const router = useRouter();
	const params = useParams();
	const { toast } = useToast();
	const adId = params.id as string;

	const [ad, setAd] = useState<Ad | null>(null);
	const [analytics, setAnalytics] = useState<Analytics | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [dateRange, setDateRange] = useState<"7d" | "30d" | "90d">("30d");

	useEffect(() => {
		if (adId) {
			fetchAdAndAnalytics();
		}
	}, [adId, dateRange]);

	const fetchAdAndAnalytics = async () => {
		try {
			setLoading(true);

			// Fetch ad details
			const adResponse = await fetch(`/api/user/ads/${adId}`);
			if (!adResponse.ok) {
				throw new Error(`Failed to fetch ad: ${adResponse.status}`);
			}

			const adResult = await adResponse.json();
			if (adResult.success) {
				setAd(adResult.data);
			} else {
				throw new Error(adResult.message || "Failed to fetch ad");
			}

			// Fetch analytics data
			const analyticsResponse = await fetch(`/api/user/ads/${adId}/analytics?range=${dateRange}`);
			if (analyticsResponse.ok) {
				const analyticsResult = await analyticsResponse.json();
				if (analyticsResult.success) {
					setAnalytics(analyticsResult.data);
				} else {
					// If no analytics data, set default values
					setAnalytics({
						impressions: 0,
						clicks: 0,
						ctr: 0,
						spend: 0,
						avgCpc: 0,
						avgCpm: 0,
						conversions: 0,
						conversionRate: 0,
					});
				}
			} else {
				// If analytics endpoint doesn't exist, set empty analytics
				setAnalytics({
					impressions: 0,
					clicks: 0,
					ctr: 0,
					spend: 0,
					avgCpc: 0,
					avgCpm: 0,
					conversions: 0,
					conversionRate: 0,
				});
			}
		} catch (error) {
			console.error("Error fetching ad and analytics:", error);
			setError(error instanceof Error ? error.message : "Failed to fetch ad");
		} finally {
			setLoading(false);
		}
	};

	const formatNumber = (num: number) => {
		return new Intl.NumberFormat().format(num);
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	const formatPercentage = (value: number) => {
		return `${value.toFixed(2)}%`;
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-background flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading analytics...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="min-h-screen bg-background flex items-center justify-center">
				<div className="text-center">
					<p className="text-destructive mb-4">{error}</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	if (!ad || !analytics) {
		return (
			<div className="min-h-screen bg-background flex items-center justify-center">
				<div className="text-center">
					<p className="text-muted-foreground mb-4">Ad or analytics data not found</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-background py-8">
			<div className="max-w-6xl mx-auto px-4">
				<div className="mb-6">
					<Button variant="ghost" onClick={() => router.back()} className="mb-4">
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Campaign
					</Button>
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold text-gray-900">Ad Analytics</h1>
							<p className="text-gray-600">Performance metrics for "{ad.title}"</p>
						</div>
						<div className="flex items-center gap-2">
							<Label htmlFor="date-range">Date Range:</Label>
							<Select value={dateRange} onValueChange={(value) => setDateRange(value as any)}>
								<SelectTrigger id="date-range" className="w-[120px]">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="7d">Last 7 days</SelectItem>
									<SelectItem value="30d">Last 30 days</SelectItem>
									<SelectItem value="90d">Last 90 days</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>
				</div>

				<div className="grid gap-6">
					{/* Ad Overview */}
					<Card>
						<CardHeader>
							<CardTitle>Ad Overview</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="flex items-start gap-4">
								<div className="relative w-24 h-16 rounded-md overflow-hidden border">
									<img
										src={ad.image_url || "/placeholder.svg"}
										alt="Ad creative"
										className="object-cover w-full h-full"
									/>
								</div>
								<div className="flex-1">
									<h3 className="font-medium">{ad.title}</h3>
									<p className="text-sm text-muted-foreground">{ad.target_url}</p>
									<Badge variant={ad.status === "active" ? "default" : "secondary"} className="mt-2">
										{ad.status}
									</Badge>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Key Metrics */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<Card>
							<CardContent className="p-4">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium text-muted-foreground">Impressions</p>
										<p className="text-2xl font-bold">{formatNumber(analytics.impressions)}</p>
									</div>
									<Eye className="h-8 w-8 text-muted-foreground" />
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium text-muted-foreground">Clicks</p>
										<p className="text-2xl font-bold">{formatNumber(analytics.clicks)}</p>
									</div>
									<MousePointer className="h-8 w-8 text-muted-foreground" />
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium text-muted-foreground">CTR</p>
										<p className="text-2xl font-bold">{formatPercentage(analytics.ctr)}</p>
									</div>
									<TrendingUp className="h-8 w-8 text-muted-foreground" />
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4">
								<div className="flex items-center justify-between">
									<div>
										<p className="text-sm font-medium text-muted-foreground">Spend</p>
										<p className="text-2xl font-bold">{formatCurrency(analytics.spend)}</p>
									</div>
									<BarChart3 className="h-8 w-8 text-muted-foreground" />
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Detailed Metrics */}
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<Card>
							<CardHeader>
								<CardTitle>Performance Metrics</CardTitle>
								<CardDescription>Detailed performance breakdown</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex justify-between">
									<span className="text-sm font-medium">Average CPC:</span>
									<span className="text-sm">{formatCurrency(analytics.avgCpc)}</span>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">Average CPM:</span>
									<span className="text-sm">{formatCurrency(analytics.avgCpm)}</span>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">Conversions:</span>
									<span className="text-sm">{formatNumber(analytics.conversions)}</span>
								</div>
								<div className="flex justify-between">
									<span className="text-sm font-medium">Conversion Rate:</span>
									<span className="text-sm">{formatPercentage(analytics.conversionRate)}</span>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Quick Actions</CardTitle>
								<CardDescription>Manage your ad performance</CardDescription>
							</CardHeader>
							<CardContent className="space-y-3">
								<Button
									variant="outline"
									className="w-full justify-start"
									onClick={() => router.push(`/dashboard/ads/${adId}/edit`)}
								>
									Edit Ad Details
								</Button>
								<Button
									variant="outline"
									className="w-full justify-start"
									onClick={() => router.push(`/dashboard/ads/${adId}/targeting`)}
								>
									Update Targeting
								</Button>
								<Button
									variant="outline"
									className="w-full justify-start"
									onClick={() => router.push(`/dashboard/campaigns/${ad.campaign_id}`)}
								>
									View Campaign
								</Button>
							</CardContent>
						</Card>
					</div>

					{/* Performance Tips */}
					<Card>
						<CardHeader>
							<CardTitle>Performance Tips</CardTitle>
							<CardDescription>Suggestions to improve your ad performance</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-3 text-sm">
								{analytics.ctr < 1 && (
									<div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
										<p className="font-medium text-yellow-800">Low Click-Through Rate</p>
										<p className="text-yellow-700">
											Consider updating your ad creative or targeting to improve engagement.
										</p>
									</div>
								)}
								{analytics.conversions === 0 && (
									<div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
										<p className="font-medium text-blue-800">No Conversions Yet</p>
										<p className="text-blue-700">
											Make sure your landing page is optimized and tracking is properly set up.
										</p>
									</div>
								)}
								{analytics.ctr > 3 && (
									<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
										<p className="font-medium text-green-800">Great Performance!</p>
										<p className="text-green-700">
											Your ad is performing well. Consider increasing the budget to scale up.
										</p>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
