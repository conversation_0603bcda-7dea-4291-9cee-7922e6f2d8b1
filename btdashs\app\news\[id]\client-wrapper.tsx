"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import type { Category, Company, News, Product, Subnet } from "@/lib/db/models";
import { AlertCircle, ArrowLeft } from "lucide-react";
import Link from "next/link";
import ReactMarkdown from "react-markdown";

interface Props {
	article: News | null;
	categories: Category[];
	companies: Company[];
	subnets: Subnet[];
	products: Product[];
}

export default function NewsArticleClient({ article, categories, companies, subnets, products }: Props) {
	if (!article) {
		return (
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				<div className="max-w-[1200px] mx-auto">
					<div className="p-8 text-center border rounded-lg">
						<AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
						<h2 className="text-2xl font-bold mb-2">Error Loading Article</h2>
						<p className="text-red-500 mb-4">The article could not be found.</p>
						<Link href="/news">
							<Button variant="outline" className="gap-2">
								<ArrowLeft className="h-4 w-4" />
								Back to News
							</Button>
						</Link>
					</div>
				</div>
			</div>
		);
	}

	const markdown = article.article_part_1 || article.content || "";

	return (
		<>
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				<div className="max-w-[1200px] mx-auto">
					<div className="mb-6">
						<Link href="/news">
							<Button variant="ghost" className="gap-2 -ml-2">
								<ArrowLeft className="h-4 w-4" />
								Back to News
							</Button>
						</Link>
					</div>

					<div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
						<div className="lg:col-span-3">
							<article className="space-y-6">
								<h1 className="text-4xl font-bold">{article.title}</h1>

								<div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
									<div>Published: {new Date(article.publication_date).toLocaleDateString()}</div>
								</div>

								<div className="w-full mb-8" style={{ minHeight: "90px" }}>
									<SmartAdBanner variant="horizontal" className="flex justify-center w-full" />
								</div>

								<article className="prose lg:prose-xl dark:prose-invert prose-h1:font-bold prose-a:text-blue-600 prose-p:text-justify prose-img:rounded-xl prose-p:text-lg prose:leading-loose">
									<ReactMarkdown>{markdown}</ReactMarkdown>
								</article>

								{Array.isArray(article.netuids) && article.netuids.length > 0 && (
									<div className="flex flex-wrap gap-2 pt-4">
										{article.netuids.map((subnetId) => (
											<Badge key={subnetId} variant="secondary">
												Subnet {subnetId}
											</Badge>
										))}
									</div>
								)}
							</article>

							<Separator className="my-8" />
							<Badge variant="outline" className="mb-2">
								{article.source || "Unknown Source"}
							</Badge>
						</div>

						<div className="space-y-6">
							<SmartAdBanner slotId={15} />

							{/* Sticky Footer Ad */}
							<div className="mt-8">
								<SmartAdBanner slotId={16} />
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
