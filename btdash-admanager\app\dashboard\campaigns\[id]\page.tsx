// app/dashboard/campaigns/[id]/page.tsx
import { fetchWithFallback } from "@/lib/utils";
import { cookies } from "next/headers";
import CampaignDetailsClientWrapper from "./client-wrapper";

export const revalidate = 60; // Revalidate every 60 seconds

export default async function CampaignDetailsPage({ params }: { params: { id: string } }) {
	const paramsData = await params;
	const id = paramsData.id;
	const cookieHeader = (await cookies()).toString();

	// Fetch campaign details
	const campaignRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/campaigns/${id}`, {
		headers: { Cookie: cookieHeader },
	});

	// Fetch all ads
	const adsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/ads`, {
		headers: { Cookie: cookieHeader },
	});

	// Handle new response format for campaign
	let campaign = null;
	if (campaignRes.success) {
		campaign = campaignRes.data;
	} else {
		console.error("Failed to fetch campaign details:", campaignRes.message || campaignRes.error);
	}

	// Handle new response format for ads
	let ads = [];
	if (adsRes.success) {
		ads = adsRes.data || [];
	} else {
		console.error("Failed to fetch ads:", adsRes.message || adsRes.error);
	}

	// Filter ads for this campaign
	const campaignAds = ads.filter((ad: any) => ad.campaign_id === parseInt(id));

	return <CampaignDetailsClientWrapper campaign={campaign} ads={campaignAds} />;
}
