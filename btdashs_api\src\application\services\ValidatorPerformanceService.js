// src/application/services/ValidatorPerformanceService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Validator Performance Service - Handles validator performance metrics operations
 *
 * This service manages validator performance data and analytics,
 * providing performance tracking and analysis for validator monitoring.
 *
 * Key responsibilities:
 * - Validator performance CRUD operations
 * - Performance analytics calculation
 * - Historical performance tracking
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class ValidatorPerformanceService extends BaseService {
	constructor() {
		super("dtm_base.validator_subnet_performance", "ValidatorPerformance");
	}

	/**
	 * Get all validator performance records with default sorting by id
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of validator performance records
	 */
	async getAllValidatorPerformance(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "id", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all validator performance", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get validator performance by ID
	 * @param {number} id - Validator performance ID
	 * @returns {Promise<Object|null>} Validator performance object or null if not found
	 */
	async getValidatorPerformanceById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting validator performance by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Get performance records by validator ID
	 * @param {number} validatorId - Validator ID
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of performance records for the validator
	 */
	async getPerformanceByValidatorId(validatorId, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "id", direction: "desc" },
				...options,
			};

			const performance = await this.getAll({ validator_id: validatorId }, queryOptions);
			logger.info("Validator performance retrieved by validator ID", {
				validatorId,
				count: performance.length,
			});
			return performance;
		} catch (error) {
			logger.error("Error getting performance by validator ID", { error, validatorId });
			throw new Error(`Failed to get performance by validator ID: ${error.message}`);
		}
	}

	/**
	 * Get performance records by subnet ID (netuid)
	 * @param {number} netuid - Subnet network UID
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of performance records for the subnet
	 */
	async getPerformanceBySubnetId(netuid, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "id", direction: "desc" },
				...options,
			};

			const performance = await this.getAll({ netuid }, queryOptions);
			logger.info("Validator performance retrieved by subnet ID", {
				netuid,
				count: performance.length,
			});
			return performance;
		} catch (error) {
			logger.error("Error getting performance by subnet ID", { error, netuid });
			throw new Error(`Failed to get performance by subnet ID: ${error.message}`);
		}
	}

	/**
	 * Create a new validator performance record
	 * @param {Object} performanceData - Validator performance data
	 * @returns {Promise<Object>} Created validator performance object
	 */
	async createValidatorPerformance(performanceData) {
		try {
			const newPerformance = await this.create({
				...performanceData,
			});
			logger.info("Validator performance created", { performance_id: newPerformance.id });
			return newPerformance;
		} catch (error) {
			logger.error("Error creating validator performance", { error, performanceData });
			throw error;
		}
	}

	/**
	 * Update a validator performance record
	 * @param {number} id - Validator performance ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated validator performance object
	 */
	async updateValidatorPerformance(id, updateData) {
		try {
			const updatedPerformance = await this.updateById(id, updateData);
			logger.info("Validator performance updated", { performance_id: id });
			return updatedPerformance;
		} catch (error) {
			logger.error("Error updating validator performance", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a validator performance record
	 * @param {number} id - Validator performance ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteValidatorPerformance(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Validator performance deleted", { performance_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting validator performance", { error, id });
			throw error;
		}
	}

	/**
	 * Get latest performance for a validator
	 * @param {number} validatorId - Validator ID
	 * @param {number} limit - Number of latest records to return
	 * @returns {Promise<Array>} Array of latest validator performance records
	 */
	async getLatestValidatorPerformance(validatorId, limit = 10) {
		try {
			const performance = await this.getAll(
				{ validator_id: validatorId },
				{
					orderBy: { column: "id", direction: "desc" },
					limit,
				}
			);

			logger.info("Latest validator performance retrieved", {
				validatorId,
				count: performance.length,
				limit,
			});

			return performance;
		} catch (error) {
			logger.error("Error getting latest validator performance", { error, validatorId, limit });
			throw new Error(`Failed to get latest validator performance: ${error.message}`);
		}
	}

	/**
	 * Get validator performance analytics
	 * @param {number} validatorId - Validator ID
	 * @param {number} days - Number of days to analyze
	 * @returns {Promise<Object>} Performance analytics
	 */
	async getValidatorPerformanceAnalytics(validatorId, days = 7) {
		try {
			const db = require("../../infrastructure/database/knex");
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const analytics = await db(this.tableName)
				.where("validator_id", validatorId)
				.select(
					db.raw("AVG(vtrust) as avg_vtrust"),
					db.raw("AVG(emissions) as avg_emissions"),
					db.raw("COUNT(*) as data_points")
				)
				.first();

			logger.info("Validator performance analytics retrieved", {
				validatorId,
				days,
				analytics,
			});

			return analytics;
		} catch (error) {
			logger.error("Error getting validator performance analytics", { error, validatorId, days });
			throw new Error(`Failed to get validator performance analytics: ${error.message}`);
		}
	}

	/**
	 * Get top performing validators
	 * @param {string} metric - Metric to sort by (stake, rank, trust, etc.)
	 * @param {number} netuid - Optional subnet filter
	 * @param {number} limit - Number of top validators to return
	 * @returns {Promise<Array>} Array of top performing validators
	 */
	async getTopPerformingValidators(metric = "stake", netuid = null, limit = 10) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db(this.tableName)
				.select(
					"validator_id",
					"netuid",
					db.raw(`AVG(${metric}) as avg_${metric}`),
					db.raw("COUNT(*) as data_points")
				)
				.groupBy("validator_id", "netuid");

			if (netuid) {
				query = query.where("netuid", netuid);
			}

			const topValidators = await query.orderBy(`avg_${metric}`, "desc").limit(limit);

			logger.info("Top performing validators retrieved", {
				metric,
				netuid,
				limit,
				count: topValidators.length,
			});

			return topValidators;
		} catch (error) {
			logger.error("Error getting top performing validators", { error, metric, netuid, limit });
			throw new Error(`Failed to get top performing validators: ${error.message}`);
		}
	}

	/**
	 * Get validator performance comparison
	 * @param {Array} validatorIds - Array of validator IDs to compare
	 * @param {number} days - Number of days to analyze
	 * @returns {Promise<Array>} Array of comparison metrics
	 */
	async getValidatorPerformanceComparison(validatorIds, days = 7) {
		try {
			const db = require("../../infrastructure/database/knex");
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const comparison = await db(this.tableName)
				.whereIn("validator_id", validatorIds)
				.select(
					"validator_id",
					db.raw("AVG(vtrust) as avg_vtrust"),
					db.raw("AVG(emissions) as avg_emissions"),
					db.raw("COUNT(*) as data_points")
				)
				.groupBy("validator_id")
				.orderBy("avg_emissions", "desc");

			logger.info("Validator performance comparison retrieved", {
				validatorIds,
				days,
				count: comparison.length,
			});

			return comparison;
		} catch (error) {
			logger.error("Error getting validator performance comparison", { error, validatorIds, days });
			throw new Error(`Failed to get validator performance comparison: ${error.message}`);
		}
	}
}

module.exports = new ValidatorPerformanceService();
