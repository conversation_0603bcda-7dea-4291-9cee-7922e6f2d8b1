"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { User } from "lib/db/models";
import { Moon, Search, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

interface Props {
	user: User | null;
}

export function TopNav({ user }: Props) {
	const { theme, setTheme } = useTheme();
	const [mounted, setMounted] = useState(false);
	const pathname = usePathname();

	const displayName =
		((user?.first_name ?? "") + " " + (user?.last_name ?? "") || user?.username || user?.email) ?? "";

	useEffect(() => {
		setMounted(true);
	}, []);

	function getInitials(name: string | undefined) {
		if (!name) return "U";
		const names = name.split(" ");
		return names
			.map((n) => n[0])
			.join("")
			.toUpperCase();
	}

	return (
		<nav className="border-b">
			<div className="max-w-[2000px] mx-auto px-6 sm:px-8 lg:px-12">
				<div className="flex h-16 items-center justify-between">
					<div className="flex items-center space-x-8">
						<Link href="/">
							<div className="relative h-8 w-24 md:w-32">
								<Image
									src="/dtm_logo.png"
									alt="DTM Logo"
									fill
									sizes="(max-width: 768px) 96px, 128px"
									className="object-contain"
									priority
								/>
							</div>
						</Link>
						<div className="hidden md:flex space-x-6">
							<Link
								href="/subnets"
								className={`text-sm transition-colors ${
									pathname === "/subnets"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-purple-500 to-indigo-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-purple-500 hover:to-indigo-600"
								}`}
							>
								Subnets
							</Link>
							<Link
								href="/validators"
								className={`text-sm transition-colors ${
									pathname === "/validators"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-blue-500 to-cyan-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-blue-500 hover:to-cyan-600"
								}`}
							>
								Validators
							</Link>
							<Link
								href="/companies"
								className={`text-sm transition-colors ${
									pathname === "/companies"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-emerald-500 to-teal-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-emerald-500 hover:to-teal-600"
								}`}
							>
								Companies
							</Link>
							<Link
								href="/products"
								className={`text-sm transition-colors ${
									pathname === "/products"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-rose-500 to-pink-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-rose-500 hover:to-pink-600"
								}`}
							>
								Products
							</Link>
							{/* <Link
                href="/posts"
                className={`text-sm transition-colors ${
                  pathname === "/posts"
                    ? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-300 to-orange-400"
                    : "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-yellow-300 hover:to-orange-400"
                }`}
              >
                Posts
              </Link> */}
							<Link
								href="/events"
								className={`text-sm transition-colors ${
									pathname === "/events"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-amber-500 to-orange-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-amber-500 hover:to-orange-600"
								}`}
							>
								Events
							</Link>
							<Link
								href="/news"
								className={`text-sm transition-colors ${
									pathname === "/news"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-violet-500 to-purple-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-violet-500 hover:to-purple-600"
								}`}
							>
								News
							</Link>
							<Link
								href="/jobs"
								className={`text-sm transition-colors ${
									pathname === "/jobs"
										? "font-bold text-transparent bg-clip-text bg-gradient-to-br from-green-500 to-emerald-600"
										: "hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-br hover:from-green-500 hover:to-emerald-600"
								}`}
							>
								Jobs
							</Link>
						</div>
					</div>
					{mounted && (
						<div className="flex items-center space-x-4">
							<div className="relative w-64">
								<Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
								<Input placeholder="Search..." className="pl-8" />
							</div>
							<Button
								variant="ghost"
								size="icon"
								onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
							>
								<Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
								<Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
								<span className="sr-only">Toggle theme</span>
							</Button>

							{user ? (
								<>
									<span className="text-sm mr-2">Hello, {displayName}</span>
									<Link href="/profile">
										{user.image_url ? (
											<div className="relative h-8 w-8 rounded-full overflow-hidden">
												<Image
													src={user.image_url}
													alt={displayName || "User Avatar"}
													fill
													sizes="32px"
													className="object-cover"
												/>
											</div>
										) : (
											<div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-xs font-bold">
												{getInitials(displayName)}
											</div>
										)}
									</Link>
									{/* <SupportPopover /> */}
								</>
							) : (
								<>
									<a href="/auth/login">
										<Button variant="ghost">Sign in</Button>
									</a>
									<a href="/auth/login?screen_hint=signup">
										<Button>Sign up</Button>
									</a>
									{/* <SupportPopover /> */}
								</>
							)}
						</div>
					)}
				</div>
			</div>
		</nav>
	);
}
