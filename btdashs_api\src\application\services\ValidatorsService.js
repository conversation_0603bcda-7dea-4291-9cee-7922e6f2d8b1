// src/application/services/ValidatorsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Validators Service - Handles validator data operations
 *
 * This service manages validator information from the TaoStats API,
 * providing CRUD operations and data management for validator metrics.
 *
 * Key responsibilities:
 * - Validator data CRUD operations
 * - Validator metrics management
 * - Subnet-specific validator operations
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class ValidatorsService extends BaseService {
	constructor() {
		super("dtm_base.validators", "Validator");
	}

	/**
	 * Get all validators with default sorting
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of validators
	 */
	async getAllValidators(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "validator_id", direction: "asc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all validators", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get validator by ID
	 * @param {number} id - Validator ID
	 * @returns {Promise<Object|null>} Validator object or null if not found
	 */
	async getValidatorById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting validator by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Get validators by subnet ID (netuid)
	 * @param {number} netuid - Subnet network UID
	 * @returns {Promise<Array>} Array of validators for the subnet
	 */
	async getValidatorsBySubnetId(netuid) {
		try {
			const validators = await this.getAll({ netuid });
			logger.info("Validators retrieved by subnet ID", {
				netuid,
				count: validators.length,
			});
			return validators;
		} catch (error) {
			logger.error("Error getting validators by subnet ID", { error, netuid });
			throw new Error(`Failed to get validators by subnet ID: ${error.message}`);
		}
	}

	/**
	 * Get validator by UID and subnet
	 * @param {number} uid - Validator UID
	 * @param {number} netuid - Subnet network UID
	 * @returns {Promise<Object|null>} Validator object or null if not found
	 */
	async getValidatorByUidAndSubnet(uid, netuid) {
		try {
			const validators = await this.getAll({ uid, netuid });
			return validators.length > 0 ? validators[0] : null;
		} catch (error) {
			logger.error("Error getting validator by UID and subnet", { error, uid, netuid });
			throw new Error(`Failed to get validator by UID and subnet: ${error.message}`);
		}
	}

	/**
	 * Create a new validator
	 * @param {Object} validatorData - Validator data
	 * @returns {Promise<Object>} Created validator object
	 */
	async createValidator(validatorData) {
		try {
			const newValidator = await this.create(validatorData);
			logger.info("Validator created", { validator_id: newValidator.id });
			return newValidator;
		} catch (error) {
			logger.error("Error creating validator", { error, validatorData });
			throw error;
		}
	}

	/**
	 * Update a validator
	 * @param {number} id - Validator ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated validator object
	 */
	async updateValidator(id, updateData) {
		try {
			const updatedValidator = await this.updateById(id, updateData);
			logger.info("Validator updated", { validator_id: id });
			return updatedValidator;
		} catch (error) {
			logger.error("Error updating validator", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a validator
	 * @param {number} id - Validator ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteValidator(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Validator deleted", { validator_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting validator", { error, id });
			throw error;
		}
	}

	/**
	 * Get top validators by stake
	 * @param {number} limit - Number of validators to return
	 * @param {number} netuid - Optional subnet filter
	 * @returns {Promise<Array>} Array of top validators
	 */
	async getTopValidatorsByStake(limit = 10, netuid = null) {
		try {
			const filters = netuid ? { netuid } : {};
			const validators = await this.getAll(filters, {
				orderBy: { column: "stake", direction: "desc" },
				limit,
			});

			logger.info("Top validators by stake retrieved", {
				count: validators.length,
				limit,
				netuid,
			});

			return validators;
		} catch (error) {
			logger.error("Error getting top validators by stake", { error, limit, netuid });
			throw new Error(`Failed to get top validators by stake: ${error.message}`);
		}
	}

	/**
	 * Get active validators (those with recent activity)
	 * @param {number} netuid - Optional subnet filter
	 * @returns {Promise<Array>} Array of active validators
	 */
	async getActiveValidators(netuid = null) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db(this.tableName).where("active", true);

			if (netuid) {
				query = query.where("netuid", netuid);
			}

			query = query.orderBy("stake", "desc");

			const validators = await query;

			logger.info("Active validators retrieved", {
				count: validators.length,
				netuid,
			});

			return validators;
		} catch (error) {
			logger.error("Error getting active validators", { error, netuid });
			throw new Error(`Failed to get active validators: ${error.message}`);
		}
	}
}

module.exports = new ValidatorsService();
