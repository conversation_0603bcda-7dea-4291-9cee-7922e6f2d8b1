import { fetchWithFallback } from "@/lib/data/utils";
import type { Category, Company, Event, Job, News, Product, Subnet, SubnetMetric } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import SubnetClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

interface Params {
	params: { id: string };
}

export async function generateMetadata({ params }: Params): Promise<Metadata> {
	const paramsData = await params;
	const netuid = Number(paramsData.id);

	const subnetsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets/${netuid}`);
	const subnet: Subnet = subnetsRes.data;

	return generateSEOMetadata({
		title: `${subnet.name} | DynamicTaoMarketCap`,
		description: subnet.description || `Detailed metrics and analysis for Subnet ${subnet.netuid}`,
		url: `${process.env.APP_BASE_URL}/subnets/${netuid}/`,
		image: subnet.image_url || `default-subnet-og.png`,
	});
}

// Main page render
export default async function SubnetPage({ params }: Params) {
	const paramsData = await params;
	const netuid = Number(paramsData.id);

	const [subnetRes, metricsRes, categoriesRes, newsRes, productsRes, companiesRes, jobsRes, eventsRes] =
		await Promise.all([
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets/${netuid}`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnet-metrics/${netuid}`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/news`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/jobs`),
			fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`),
		]);

	if (subnetRes.error) console.error("Subnet fetch error", subnetRes.error);
	if (metricsRes.error) console.error("Subnet metrics fetch error", metricsRes.error);
	if (categoriesRes.error) console.error("Categories fetch error", categoriesRes.error);
	if (newsRes.error) console.error("News fetch error", newsRes.error);
	if (productsRes.error) console.error("Products fetch error", productsRes.error);
	if (companiesRes.error) console.error("Companies fetch error", companiesRes.error);
	if (jobsRes.error) console.error("Jobs fetch error", jobsRes.error);
	if (eventsRes.error) console.error("News fetch error", eventsRes.error);

	const subnet: Subnet = subnetRes.data;
	const metrics: SubnetMetric = metricsRes.data;

	const filteredCategories = categoriesRes.data.filter((category: Category) =>
		subnet.category_ids?.includes(category.id)
	);

	const filteredNews = newsRes.data.filter(
		(newsItem: News) => newsItem.netuids?.includes(netuid) || subnet.news_ids?.includes(newsItem.id)
	);

	const filteredProducts = productsRes.data.filter(
		(product: Product) => product.subnet_ids?.includes(netuid) || subnet.product_ids?.includes(product.id)
	);

	const filteredCompanies = companiesRes.data.filter(
		(company: Company) => company.subnet_ids?.includes(netuid) || subnet.company_ids?.includes(company.id)
	);

	const filteredJobs = jobsRes.data.filter(
		(job: Job) => job.subnet_ids?.includes(netuid) || subnet.job_ids?.includes(job.id)
	);

	const filteredEvents = eventsRes.data.filter(
		(event: Event) => event.subnet_ids?.includes(netuid) || subnet.event_ids?.includes(event.id)
	);

	return (
		<SubnetClientWrapper
			subnet={subnet}
			metrics={metrics}
			categories={filteredCategories}
			news={filteredNews}
			products={filteredProducts}
			companies={filteredCompanies}
			jobs={filteredJobs}
			events={filteredEvents}
		/>
	);
}
