// src/application/services/ProductsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Products Service - Handles product management operations
 * 
 * This service manages products in the platform, providing CRUD operations
 * and product-related business logic.
 * 
 * Key responsibilities:
 * - Product CRUD operations
 * - Product search and filtering
 * - Subnet-specific product management
 * - Data validation and sanitization
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class ProductsService extends BaseService {
  constructor() {
    super("dtm_base.products", "Product");
  }

  /**
   * Get all products with default sorting by created_at
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of products
   */
  async getAllProducts(filters = {}, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'created_at', direction: 'desc' },
        ...options
      };

      return await this.getAll(filters, queryOptions);
    } catch (error) {
      logger.error("Error getting all products", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get product by ID
   * @param {number} id - Product ID
   * @returns {Promise<Object|null>} Product object or null if not found
   */
  async getProductById(id) {
    try {
      return await this.getById(id);
    } catch (error) {
      logger.error("Error getting product by ID", { error, id });
      throw error;
    }
  }

  /**
   * Get products by subnet ID (netuid)
   * @param {number} netuid - Subnet network UID
   * @returns {Promise<Array>} Array of products for the subnet
   */
  async getProductsBySubnetId(netuid) {
    try {
      const products = await this.getAll({ netuid });
      logger.info("Products retrieved by subnet ID", { 
        netuid, 
        count: products.length 
      });
      return products;
    } catch (error) {
      logger.error("Error getting products by subnet ID", { error, netuid });
      throw new Error(`Failed to get products by subnet ID: ${error.message}`);
    }
  }

  /**
   * Create a new product
   * @param {Object} productData - Product data
   * @returns {Promise<Object>} Created product object
   */
  async createProduct(productData) {
    try {
      const newProduct = await this.create(productData);
      logger.info("Product created", { product_id: newProduct.id });
      return newProduct;
    } catch (error) {
      logger.error("Error creating product", { error, productData });
      throw error;
    }
  }

  /**
   * Update a product
   * @param {number} id - Product ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated product object
   */
  async updateProduct(id, updateData) {
    try {
      const updatedProduct = await this.updateById(id, updateData);
      logger.info("Product updated", { product_id: id });
      return updatedProduct;
    } catch (error) {
      logger.error("Error updating product", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete a product
   * @param {number} id - Product ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteProduct(id) {
    try {
      const result = await this.deleteById(id);
      logger.info("Product deleted", { product_id: id });
      return result;
    } catch (error) {
      logger.error("Error deleting product", { error, id });
      throw error;
    }
  }

  /**
   * Search products by name or description
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of matching products
   */
  async searchProducts(searchTerm, options = {}) {
    try {
      const db = require("../../infrastructure/database/knex");
      
      let query = db(this.tableName)
        .where('name', 'ilike', `%${searchTerm}%`)
        .orWhere('description', 'ilike', `%${searchTerm}%`);

      // Apply ordering
      if (options.orderBy) {
        const { column, direction = 'desc' } = options.orderBy;
        query = query.orderBy(column, direction);
      } else {
        query = query.orderBy('created_at', 'desc');
      }

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.offset(options.offset);
      }

      const products = await query;
      
      logger.info("Products searched", { 
        searchTerm, 
        resultCount: products.length,
        options 
      });
      
      return products;
    } catch (error) {
      logger.error("Error searching products", { error, searchTerm, options });
      throw new Error(`Failed to search products: ${error.message}`);
    }
  }
}

module.exports = new ProductsService();
