"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx":
/*!*******************************************************!*\
  !*** ./components/ads-placements/smart-ad-banner.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartAdBanner: () => (/* binding */ SmartAdBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(app-pages-browser)/./lib/api/ad-serving.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SmartAdBanner auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SmartAdBanner(param) {\n    let { slotId, className, googleAdSlotOverride, enablePaidAds = true } = param;\n    _s();\n    const [paidAd, setPaidAd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>(0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.generateSessionId)()\n    }[\"SmartAdBanner.useState\"]);\n    const [hasTrackedImpression, setHasTrackedImpression] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const adContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const googleAdInitialized = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const maxRetries = 3;\n    // Get dimensions for this slot\n    const dimensions = _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.SLOT_DIMENSIONS[slotId];\n    if (!dimensions) {\n        console.error(\"No dimensions found for slot ID: \".concat(slotId));\n        return null;\n    }\n    // Fetch paid ad on component mount with retry logic\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!enablePaidAds) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchPaidAd = {\n                \"SmartAdBanner.useEffect.fetchPaidAd\": async function() {\n                    let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setError(null);\n                        const userContext = {\n                            country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                            device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent),\n                            language: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getLanguage)(),\n                            user_agent: navigator.userAgent\n                        };\n                        const controller = new AbortController();\n                        const timeoutId = setTimeout({\n                            \"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\": ()=>controller.abort()\n                        }[\"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\"], 5000); // 5 second timeout\n                        const response = await fetch(\"/api/ads/serve\", {\n                            method: \"POST\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                slotId,\n                                ...userContext\n                            }),\n                            signal: controller.signal\n                        });\n                        clearTimeout(timeoutId);\n                        if (response.ok) {\n                            const result = await response.json();\n                            if (result.success && result.data) {\n                                setPaidAd(result.data);\n                                setRetryCount(0);\n                                return;\n                            }\n                        }\n                        // If we get here, no paid ads were available (404) or other non-critical error\n                        if (response.status === 404) {\n                            // No ads available - this is expected, fall back to Google\n                            setRetryCount(0);\n                            return;\n                        }\n                        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    } catch (error) {\n                        console.error(\"Error fetching paid ad (attempt \".concat(attempt + 1, \"):\"), error);\n                        if (attempt < maxRetries - 1) {\n                            // Exponential backoff: 1s, 2s, 4s\n                            const delay = Math.pow(2, attempt) * 1000;\n                            setTimeout({\n                                \"SmartAdBanner.useEffect.fetchPaidAd\": ()=>{\n                                    setRetryCount(attempt + 1);\n                                    fetchPaidAd(attempt + 1);\n                                }\n                            }[\"SmartAdBanner.useEffect.fetchPaidAd\"], delay);\n                        } else {\n                            setError(\"Failed to load paid ads after multiple attempts\");\n                            setRetryCount(0);\n                        }\n                    } finally{\n                        if (attempt === 0 || attempt >= maxRetries - 1) {\n                            setIsLoading(false);\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.fetchPaidAd\"];\n            fetchPaidAd();\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        enablePaidAds,\n        maxRetries\n    ]);\n    // Track impression when ad becomes visible with error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!paidAd || hasTrackedImpression) return;\n            const observer = new IntersectionObserver({\n                \"SmartAdBanner.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"SmartAdBanner.useEffect\": (entry)=>{\n                            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {\n                                (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdImpression)({\n                                    ad_id: paidAd.id,\n                                    session_id: sessionId,\n                                    country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                                    device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n                                }).catch({\n                                    \"SmartAdBanner.useEffect\": (error)=>{\n                                        console.error(\"Failed to track impression:\", error);\n                                    // Don't block the user experience for tracking failures\n                                    }\n                                }[\"SmartAdBanner.useEffect\"]);\n                                setHasTrackedImpression(true);\n                                observer.disconnect();\n                            }\n                        }\n                    }[\"SmartAdBanner.useEffect\"]);\n                }\n            }[\"SmartAdBanner.useEffect\"], {\n                threshold: 0.5\n            });\n            if (adContainerRef.current) {\n                observer.observe(adContainerRef.current);\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>observer.disconnect()\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        paidAd,\n        sessionId,\n        hasTrackedImpression\n    ]);\n    // Initialize Google AdSense when no paid ad is available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (isLoading || paidAd || googleAdInitialized.current) return;\n            const container = adContainerRef.current;\n            const adEl = container === null || container === void 0 ? void 0 : container.querySelector(\"ins.adsbygoogle\");\n            if (!adEl) return;\n            // Clear any previous ad content\n            adEl.innerHTML = \"\";\n            const { width, height } = dimensions;\n            // Apply sizing rules based on slot type\n            const isHorizontal = width > height; // Leaderboard, Billboard, Banner types\n            if (isHorizontal && container) {\n                var _container_parentElement;\n                const parentWidth = ((_container_parentElement = container.parentElement) === null || _container_parentElement === void 0 ? void 0 : _container_parentElement.clientWidth) || width;\n                const calculatedWidth = Math.min(parentWidth, width);\n                const calculatedHeight = height / width * calculatedWidth;\n                container.style.width = \"\".concat(calculatedWidth, \"px\");\n                container.style.height = \"\".concat(calculatedHeight, \"px\");\n            } else if (container) {\n                container.style.width = \"\".concat(width, \"px\");\n                container.style.height = \"\".concat(height, \"px\");\n            }\n            if (container) {\n                container.style.overflow = \"hidden\";\n                container.style.position = \"relative\";\n            }\n            // Attempt to load Google ad with retry\n            let retryCount = 0;\n            const loadGoogleAd = {\n                \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                    try {\n                        (window.adsbygoogle = window.adsbygoogle || []).push({});\n                        googleAdInitialized.current = true;\n                    } catch (e) {\n                        retryCount++;\n                        if (retryCount < 3) {\n                            setTimeout(loadGoogleAd, 1000);\n                        } else {\n                            console.error(\"Google AdSense load failed:\", e);\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.loadGoogleAd\"];\n            loadGoogleAd();\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>{\n                    if (adEl) adEl.innerHTML = \"\";\n                }\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        isLoading,\n        paidAd\n    ]);\n    // Handle paid ad click with error handling\n    const handlePaidAdClick = async ()=>{\n        if (!paidAd) return;\n        try {\n            // Track the click (don't wait for it to complete)\n            (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdClick)({\n                ad_id: paidAd.id,\n                session_id: sessionId,\n                country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n            }).catch((error)=>{\n                console.error(\"Failed to track click:\", error);\n            // Don't block the redirect for tracking failures\n            });\n            // Redirect to target URL immediately\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        } catch (error) {\n            console.error(\"Error handling ad click:\", error);\n            // Still try to redirect even if tracking fails\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    // Get the appropriate Google AdSense slot\n    const googleAdSlot = googleAdSlotOverride || _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.GOOGLE_AD_SLOTS[slotId];\n    // Show loading state\n    if (isLoading && enablePaidAds) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f3f4f6\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: retryCount > 0 ? \"Loading ads... (\".concat(retryCount, \"/\").concat(maxRetries, \")\") : \"Loading ads...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 239,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 238,\n            columnNumber: 4\n        }, this);\n    }\n    // Show error state (only if we have an error and no fallback)\n    if (error && !enablePaidAds) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#fef2f2\",\n                    border: \"1px solid #fecaca\",\n                    borderRadius: \"4px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-red-600 text-center px-4\",\n                    children: \"Ad loading failed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 265,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 264,\n            columnNumber: 4\n        }, this);\n    }\n    // Render paid ad\n    if (paidAd) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            ref: adContainerRef,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    position: \"relative\",\n                    cursor: \"pointer\",\n                    overflow: \"hidden\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                onClick: handlePaidAdClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: paidAd.image_url,\n                        alt: paidAd.title,\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: \"4px\",\n                            right: \"4px\",\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"white\",\n                            fontSize: \"10px\",\n                            padding: \"2px 4px\",\n                            borderRadius: \"2px\"\n                        },\n                        children: \"Sponsored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 290,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 289,\n            columnNumber: 4\n        }, this);\n    }\n    // Render Google AdSense fallback\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-block \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: adContainerRef,\n            style: {\n                minWidth: \"\".concat(dimensions.width, \"px\"),\n                minHeight: \"\".concat(dimensions.height, \"px\"),\n                margin: \"0 auto\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                className: \"adsbygoogle\",\n                style: {\n                    display: \"block\"\n                },\n                \"data-ad-client\": \"ca-pub-5681407322305640\",\n                \"data-ad-slot\": googleAdSlot,\n                \"data-ad-format\": dimensions.width > dimensions.height ? \"horizontal\" : dimensions.width === dimensions.height ? \"rectangle\" : \"auto\",\n                \"data-full-width-responsive\": \"true\"\n            }, \"\".concat(slotId, \"-\").concat(googleAdSlot), false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 342,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 334,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n        lineNumber: 333,\n        columnNumber: 3\n    }, this);\n}\n_s(SmartAdBanner, \"p/OpB8YznAamvRPQDODqMMvsOqw=\");\n_c = SmartAdBanner;\nvar _c;\n$RefreshReg$(_c, \"SmartAdBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/subnets/subnet-profile.tsx":
/*!***********************************************!*\
  !*** ./components/subnets/subnet-profile.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubnetProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var _components_category_tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/category-tag */ \"(app-pages-browser)/./components/category-tag.tsx\");\n/* harmony import */ var _components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/subnet-documentation */ \"(app-pages-browser)/./components/subnet-documentation.tsx\");\n/* harmony import */ var _components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/subnet-github-contribution-graph */ \"(app-pages-browser)/./components/subnet-github-contribution-graph.tsx\");\n/* harmony import */ var _components_subnet_news__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/subnet-news */ \"(app-pages-browser)/./components/subnet-news.tsx\");\n/* harmony import */ var _components_subnet_team__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/subnet-team */ \"(app-pages-browser)/./components/subnet-team.tsx\");\n/* harmony import */ var _components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/subnet-validators */ \"(app-pages-browser)/./components/subnet-validators.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/public/tao-logo.svg */ \"(app-pages-browser)/./public/tao-logo.svg\");\n/* harmony import */ var _components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ads-placements/smart-ad-banner */ \"(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\");\n/* harmony import */ var _components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/subnets/subnet-relationship-chart */ \"(app-pages-browser)/./components/subnets/subnet-relationship-chart.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _subnet_applications__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../subnet-applications */ \"(app-pages-browser)/./components/subnet-applications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/* import { useRef, useState } from \"react\"; */ \n\n\n\n\n\n\n\n\n\n\n\n// Import the new chart component\n\n\n\n\nfunction SubnetProfile(param) {\n    let { subnet, metrics, categories, news, products, jobs, events, companies } = param;\n    var _subnet_subnet_ids, _subnet_images, _metrics_github_contributions;\n    /* const [imageError, setImageError] = useState(false);\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [isMuted, setIsMuted] = useState(false); */ const netuid = subnet.netuid;\n    var _companies_length, _products_length, _events_length, _jobs_length, _categories_length, _metrics_validators_count, _news_length, _subnet_subnet_ids_length;\n    /* const images = subnet.images?.length\r\n    ? subnet.images\r\n    : [\r\n        \"https://via.placeholder.com/800x400?text=Image+1\",\r\n        \"https://via.placeholder.com/800x400?text=Image+2\",\r\n        \"https://via.placeholder.com/800x400?text=Image+3\",\r\n      ]; */ const data = {\n        companyCount: (_companies_length = companies === null || companies === void 0 ? void 0 : companies.length) !== null && _companies_length !== void 0 ? _companies_length : 0,\n        productCount: (_products_length = products === null || products === void 0 ? void 0 : products.length) !== null && _products_length !== void 0 ? _products_length : 0,\n        eventCount: (_events_length = events === null || events === void 0 ? void 0 : events.length) !== null && _events_length !== void 0 ? _events_length : 0,\n        jobCount: (_jobs_length = jobs === null || jobs === void 0 ? void 0 : jobs.length) !== null && _jobs_length !== void 0 ? _jobs_length : 0,\n        categoryCount: (_categories_length = categories === null || categories === void 0 ? void 0 : categories.length) !== null && _categories_length !== void 0 ? _categories_length : 0,\n        validatorCount: (_metrics_validators_count = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count !== void 0 ? _metrics_validators_count : 0,\n        newsCount: (_news_length = news.length) !== null && _news_length !== void 0 ? _news_length : 0,\n        subnetCount: (_subnet_subnet_ids_length = subnet === null || subnet === void 0 ? void 0 : (_subnet_subnet_ids = subnet.subnet_ids) === null || _subnet_subnet_ids === void 0 ? void 0 : _subnet_subnet_ids.length) !== null && _subnet_subnet_ids_length !== void 0 ? _subnet_subnet_ids_length : 0\n    };\n    var _metrics_validators_count1, _metrics_emission;\n    const metricsCards = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Price\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    alt: \"TAO\",\n                                    width: 24,\n                                    height: 24,\n                                    className: \"inline-block pr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 7\n                                }, this),\n                                (metrics === null || metrics === void 0 ? void 0 : metrics.alpha_price_tao) != null && !isNaN(metrics.alpha_price_tao) ? Number(metrics.alpha_price_tao) < 0.01 ? Number(metrics.alpha_price_tao).toFixed(3) : Number(metrics.alpha_price_tao).toFixed(2) : \"0.00\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 79,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Validators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: (_metrics_validators_count1 = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count1 !== void 0 ? _metrics_validators_count1 : 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 95,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Emission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                (((_metrics_emission = metrics === null || metrics === void 0 ? void 0 : metrics.emission) !== null && _metrics_emission !== void 0 ? _metrics_emission : 0) / 1e7).toFixed(2),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 104,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Miners\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold \".concat(subnet.active_miners <= 5 ? \"text-red-500\" : subnet.active_miners <= 15 ? \"text-orange-500\" : \"text-green-500\"),\n                            children: subnet.active_miners || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 113,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8\",\n                    style: {\n                        minHeight: \"90px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 7,\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold\",\n                                            children: subnet.subnet_symbol || subnet.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: subnet.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: [\n                                                        \"Subnet ID: \",\n                                                        netuid\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Coldkey:\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://taostats.io/account/\".concat(subnet.sub_address_pkey, \"/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile\"),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-xs text-muted-foreground underline hover:text-primary\",\n                                                            children: subnet.sub_address_pkey\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2 text-muted-foreground\",\n                                                    children: categories && categories.length > 0 ? categories.map((category, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_category_tag__WEBPACK_IMPORTED_MODULE_4__.CategoryTag, {\n                                                            category: category.name\n                                                        }, id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 13\n                                                        }, this)) : null\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_17__.Markdown, {\n                                        children: subnet.description_short\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 mb-8\",\n                                    children: subnet.white_paper ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        asChild: true,\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        variant: \"default\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.white_paper,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 11\n                                                }, this),\n                                                \"Read White Paper\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 10\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 9\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        disabled: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 10\n                                            }, this),\n                                            \"White Paper Unavailable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 7\n                                }, this),\n                                subnet.images || subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 8\n                                }, this) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 6\n                        }, this),\n                        subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                        className: \"absolute left-0 w-full h-full\",\n                                        src: subnet.main_video_url,\n                                        title: \"Subnet video\",\n                                        frameBorder: \"0\",\n                                        allow: \"autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                        allowFullScreen: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 7\n                        }, this) : !subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                subnet.images ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-lg overflow-hidden shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Pagination\n                                        ],\n                                        navigation: true,\n                                        pagination: {\n                                            clickable: true\n                                        },\n                                        spaceBetween: 10,\n                                        slidesPerView: 1,\n                                        className: \"w-full h-full\",\n                                        children: (_subnet_images = subnet.images) === null || _subnet_images === void 0 ? void 0 : _subnet_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"Subnet Image \".concat(index + 1),\n                                                    width: 800,\n                                                    height: 400,\n                                                    className: \"w-full h-auto object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 12\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 11\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 7\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" gap-4 overflow-hidden max-[60px]\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                                slotId: 9\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__.SubnetGithubContributionGraph, {\n                                className: \"h-[360px]\",\n                                contributions: (metrics === null || metrics === void 0 ? void 0 : (_metrics_github_contributions = metrics.github_contributions) === null || _metrics_github_contributions === void 0 ? void 0 : _metrics_github_contributions.data) || []\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[360px] col-span-2 overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__.SubnetRelationshipChart, {\n                                subnetId: subnet.name,\n                                data: data,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-0 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_subnet_applications__WEBPACK_IMPORTED_MODULE_16__.SubnetApplications, {\n                        products: products\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 10,\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                            className: \"flex flex-wrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"team\",\n                                    children: \"Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"documentation\",\n                                    children: \"Documentation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"validators\",\n                                    children: \"Validators\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"news\",\n                                    children: \"News\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-1 gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"overview\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-4\",\n                                                        children: subnet.key_features && subnet.key_features.length > 0 ? subnet.key_features[0].map((feature, id)=>feature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 16\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No key features available.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"team\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_team__WEBPACK_IMPORTED_MODULE_8__.SubnetTeam, {\n                                            subnet: subnet\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"documentation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__.SubnetDocumentation, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"validators\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__.SubnetValidators, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"news\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_news__WEBPACK_IMPORTED_MODULE_7__.SubnetNews, {\n                                            news: news\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 mt-8 mb-8 min-h-[90px] w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 8,\n                        className: \"w-full h-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n            lineNumber: 136,\n            columnNumber: 4\n        }, this)\n    }, void 0, false);\n}\n_c = SubnetProfile;\nvar _c;\n$RefreshReg$(_c, \"SubnetProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/subnets/subnet-profile.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/ad-serving.ts":
/*!*******************************!*\
  !*** ./lib/api/ad-serving.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   fetchPaidAd: () => (/* binding */ fetchPaidAd),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense fallback slots mapped by slot ID\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"7230250579\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.SUBNET_BANNER]: \"8814794983\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"8814794983\"\n};\n/**\n * SERVER-SIDE ONLY\n * Fetch a paid ad from the backend API\n */ async function fetchPaidAd(slotId, userContext) {\n    try {\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return null;\n        }\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.country_code) && {\n                country_code: userContext.country_code\n            },\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.device_type) && {\n                device_type: userContext.device_type\n            },\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.language) && {\n                language: userContext.language\n            },\n            ...(userContext === null || userContext === void 0 ? void 0 : userContext.user_agent) && {\n                user_agent: userContext.user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const response = await fetch(\"\".concat(API_BASE, \"/api/serve-ad?\").concat(params), {\n            headers,\n            cache: \"no-store\"\n        });\n        if (!response.ok) {\n            if (response.status === 404) {\n                // No ads available for this slot\n                return null;\n            }\n            throw new Error(\"Ad serving failed: \".concat(response.status));\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return result.data;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching paid ad:\", error);\n        return null;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    return \"\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (true) {\n        return navigator.language || \"en-US\";\n    }\n    return \"en-US\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/ad-serving.ts\n"));

/***/ })

});