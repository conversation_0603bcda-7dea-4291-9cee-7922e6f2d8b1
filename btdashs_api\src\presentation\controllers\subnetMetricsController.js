const SubnetMetricsService = require("../../application/services/SubnetMetricsService");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllSubnetMetrics = asyncHandler(async (req, res) => {
	const metrics = await SubnetMetricsService.getAllSubnetMetrics();
	return sendSuccess(res, metrics, "Subnet metrics retrieved successfully");
});

const getSubnetMetricById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const metric = await SubnetMetricsService.getSubnetMetricById(id);
	if (!metric) {
		return sendNotFound(res, "Subnet metric not found");
	}
	return sendSuccess(res, metric, "Subnet metric retrieved successfully");
});

const createSubnetMetric = asyncHandler(async (req, res) => {
	const metricData = {
		...req.body,
		recorded_at: new Date(),
	};

	const newMetric = await SubnetMetricsService.createSubnetMetric(metricData);
	return sendSuccess(res, newMetric, "Subnet metric created successfully", 201);
});

const updateSubnetMetric = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const updateData = {
		...req.body,
		recorded_at: new Date(),
	};

	const updatedMetric = await SubnetMetricsService.updateSubnetMetric(id, updateData);
	return sendSuccess(res, updatedMetric, "Subnet metric updated successfully");
});

const deleteSubnetMetric = asyncHandler(async (req, res) => {
	const { id } = req.params;
	await SubnetMetricsService.deleteSubnetMetric(id);
	return sendSuccess(res, null, "Subnet metric deleted successfully");
});

/* --- TAO STATS INTERACTIONS --- */

// Update subnets metrics with data fetched from TaoStats API
const updateSubnetsMetricsWithTaoStats = async (req, res) => {
	try {
		const subnetData = await fetchAllPages("/subnet/latest/v1");
		const registrationData = await fetchAllPages("/subnet/registration/v1");
		const poolData = await fetchAllPages("/dtao/pool/latest/v1");

		await updateSubnetsMetrics(subnetData, registrationData, poolData);

		updateEndpointStatus("/update/subnets-metrics", true, "Subnets metrics updated successfully");

		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("Error updating subnet metrics:", error);

		updateEndpointStatus("/update/subnets-metrics", false, error.message);
		res.status(500).json({ error: error.message });
	}
};

// Update subnet metrics with given data
const updateSubnetsMetrics = async (subnetData, registrationData, poolData) => {
	logger.info("Updating subnets metrics with TaoStats data...");

	const metricsToInsert = [];
	const metricsToUpdate = [];

	for (const subnet of subnetData) {
		const { netuid, timestamp, recycled_since_registration, validators, emission, max_neurons } = subnet;

		// Skip if the subnet is missing in the subnets table
		const existingSubnet = await db("dtm_base.subnets").where({ netuid }).first();
		if (!existingSubnet) {
			logger.info(`Missing netuid ${netuid} in subnets. Skipping...`);
			continue;
		}

		// Find corresponding data
		const reg = registrationData.find((r) => r.netuid === netuid);
		const pool = poolData.find((p) => p.netuid === netuid);

		// Process price data
		let sevenDayPrices = null;

		if (pool?.seven_day_prices) {
			// Group prices by day and get one price per day
			const pricesByDay = {};
			pool.seven_day_prices.forEach((item) => {
				const date = new Date(item.timestamp).toISOString().split("T")[0]; // Extract YYYY-MM-DD
				if (!pricesByDay[date] || new Date(item.timestamp) > new Date(pricesByDay[date].timestamp)) {
					pricesByDay[date] = item;
				}
			});

			// Sort by date and get the most recent 7 days
			sevenDayPrices = Object.values(pricesByDay)
				.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
				.slice(0, 7)
				.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
				.map((item) => item.price);
		}

		// Map data
		const metricsToInsertOrUpdate = {
			netuid,
			recorded_at: timestamp || new Date().toISOString(),
			max_neurons: max_neurons || null,
			alpha_token: pool?.total_alpha ? pool.total_alpha.toString() : null,
			emission: emission || null,
			emission_percentage: null, // (TODO)
			recycled_tao: recycled_since_registration || null,
			registration_cost_tao: reg?.registration_cost || null,
			alpha_price_tao: pool?.price || null,
			market_cap_tao: pool?.market_cap || null,
			volume_tao: pool?.tao_volume_24_hr || null,
			liquidity_tao: pool?.liquidity || null,
			staked_tao: pool?.alpha_staked || null,
			validators_count: validators || null,
			active_miners: pool?.active_miners || null,
			price_change_1_week: pool?.price_change_1_week || null,
			seven_day_prices: sevenDayPrices ? JSON.stringify(sevenDayPrices) : null,
		};

		// Check if the metric already exists
		const existingMetric = await db("dtm_base.subnet_metrics").where({ netuid }).first();

		if (existingMetric) {
			metricsToUpdate.push(metricsToInsertOrUpdate);
		} else {
			metricsToInsert.push(metricsToInsertOrUpdate);
		}
	}

	// Perform bulk insert for new metrics
	if (metricsToInsert.length > 0) {
		await db("dtm_base.subnet_metrics").insert(metricsToInsert);
		logger.info(`Inserted ${metricsToInsert.length} new metrics`);
	}

	// Perform bulk update for existing metrics
	if (metricsToUpdate.length > 0) {
		await Promise.all(
			metricsToUpdate.map(async (metric) => {
				await db("dtm_base.subnet_metrics").where({ netuid: metric.netuid }).update(metric);
			})
		);
		logger.info(`Updated ${metricsToUpdate.length} metrics`);
	}
};

module.exports = {
	getAllSubnetMetrics,
	getSubnetMetricById,
	createSubnetMetric,
	updateSubnetMetric,
	deleteSubnetMetric,
	updateSubnetsMetricsWithTaoStats,
	updateSubnetsMetrics,
};
