import { getAllItems } from "@/lib/data/utils";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const data = await getAllItems("subnet-metrics");
		return NextResponse.json({
			success: true,
			data,
			message: "Subnet metrics fetched successfully",
		});
	} catch (err) {
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch subnet metrics",
				errors: err instanceof Error ? err.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
