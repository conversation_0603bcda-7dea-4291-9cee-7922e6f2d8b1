/** @type {import('next').NextConfig} */
const nextConfig = {
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	},
	images: {
		domains: [
			"dynamictaomarketcap.com",
			"bittensormarketcap.com",
			"camo.githubusercontent.com", // GitHub profile pics
			"lh3.googleusercontent.com", // Google OAuth
			"avatars.githubusercontent.com", // GitHub OAuth
			"pbs.twimg.com", // Twitter profile pics
			"media.licdn.com", // LinkedIn profile pics
			"s.gravatar.com", // Gravatar profile pics
			"sjc.microlink.io",
			"dmtbittensor.s3.us-east-1.amazonaws.com",
			"rayonlabs.ai",
			"googleads.g.doubleclick.net",
			"x.com",
			"analytics.twitter.com",
			"res.cloudinary.com",
		],
	},
};

export default nextConfig;
