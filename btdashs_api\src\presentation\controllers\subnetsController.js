const SubnetsService = require("../../application/services/SubnetsService");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { validateUrl } = require("../../../utils/urlValidator");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllSubnets = asyncHandler(async (req, res) => {
	// Fetch all subnets from the database
	// Exclude subnets with names containing "unknown" or "unnamed"
	// and where github_repo is not null
	const subnets = await SubnetsService.getFilteredSubnets();

	return sendSuccess(res, subnets, "Subnets retrieved successfully");
});

const getSubnetById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const subnet = await SubnetsService.getSubnetByNetuid(id);
	if (!subnet) {
		return sendNotFound(res, "Subnet not found");
	}
	return sendSuccess(res, subnet, "Subnet retrieved successfully");
});

const createSubnet = async (req, res) => {
	try {
		const {
			netuid,
			name,
			owner_address,
			description,
			subnet_symbol,
			github_repo,
			website,
			is_active,
			image_url,
			category_ids,
			likes_count,
			main_video_url,
			main_video_type,
			description_short,
			key_features,
			sub_address_pkey,
			active_validators,
			active_miners,
			alpha_high,
			alpha_low,
		} = req.body;

		const [newSubnet] = await db("dtm_base.subnets")
			.insert({
				netuid,
				name,
				owner_address,
				description,
				subnet_symbol,
				github_repo,
				website,
				is_active,
				image_url,
				category_ids,
				likes_count,
				main_video_url,
				main_video_type,
				description_short,
				key_features,
				sub_address_pkey,
				active_validators,
				active_miners,
				alpha_high,
				alpha_low,
			})
			.returning("*");

		res.status(201).json({ data: newSubnet });
	} catch (error) {
		logger.error("Error creating subnet:", error);
		res.status(500).json({ message: "Error creating subnet" });
	}
};

const updateSubnet = async (req, res) => {
	try {
		const { id } = req.params;
		const {
			name,
			owner_address,
			description,
			subnet_symbol,
			github_repo,
			website,
			is_active,
			image_url,
			category_ids,
			likes_count,
			main_video_url,
			main_video_type,
			description_short,
			key_features,
			sub_address_pkey,
			active_validators,
			active_miners,
			alpha_high,
			alpha_low,
		} = req.body;

		const [updatedSubnet] = await db("dtm_base.subnets")
			.where({ netuid: id })
			.update({
				name,
				owner_address,
				description,
				subnet_symbol,
				github_repo,
				website,
				is_active,
				image_url,
				category_ids,
				likes_count,
				main_video_url,
				main_video_type,
				description_short,
				key_features,
				sub_address_pkey,
				active_validators,
				active_miners,
				alpha_high,
				alpha_low,
			})
			.returning("*");

		if (!updatedSubnet) {
			return res.status(404).json({ message: "Subnet not found" });
		}

		res.status(200).json({ data: updatedSubnet });
	} catch (error) {
		logger.error("Error updating subnet:", error);
		res.status(500).json({ message: "Error updating subnet" });
	}
};

const deleteSubnet = async (req, res) => {
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.subnets").where({ netuid: id }).del();
		if (!deleted) {
			return res.status(404).json({ message: "Subnet not found" });
		}
		res.status(204).send();
	} catch (error) {
		logger.error("Error deleting subnet:", error);
		res.status(500).json({ message: "Error deleting subnet" });
	}
};

/* --- TAO STATS INTERACTIONS --- */

// Update subnets with data fetched from TaoStats API
const updateSubnetsWithTaoStats = async (req, res) => {
	try {
		const subnetsData = await fetchAllPages("/subnet/latest/v1");
		const poolsData = await fetchAllPages("/dtao/pool/latest/v1");
		const identityData = await fetchAllPages("/subnet/identity/v1");

		await updateSubnets(subnetsData, poolsData, identityData);

		updateEndpointStatus("/update/subnets", true, "Subnets updated successfully");
		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("Error updating subnets:", error);
		updateEndpointStatus("/update/subnets", false, error.message);
		res.status(500).json({ error: error.message });
	}
};

const updateSubnets = async (subnetsData, poolsData, identityData) => {
	logger.info("Updating subnets with data from TaoStats API...");

	const subnetsToInsert = [];
	const subnetsToUpdate = [];
	const publicSubnetsToSync = [];

	for (const subnet of subnetsData) {
		const { timestamp, netuid, owner } = subnet;

		// Find corresponding pool and description
		const pool = poolsData.find((p) => p.netuid === netuid);
		const identity = identityData.find((d) => d.netuid === netuid);

		// Map data to our schema
		const subnetToInsertOrUpdate = {
			netuid,
			name: identity?.subnet_name || "Unnamed Subnet",
			updated_at: timestamp || new Date().toISOString(),
			owner_address: owner?.hex || null,
			description: identity?.description || null,
			subnet_symbol: pool?.symbol || null,
			github_repo: validateUrl(identity?.github_repo),
			website: validateUrl(identity?.subnet_url),
			is_active: true,
			sub_address_pkey: owner?.ss58 || null,
			active_validators: subnet?.active_validators || null,
			active_miners: subnet?.active_miners || null,
			alpha_high: pool?.alpha_high || null,
			alpha_low: pool?.alpha_low || null,
		};

		// Prepare public subnet record (minimal data)
		publicSubnetsToSync.push({
			netuid,
			name: subnetToInsertOrUpdate.name,
		});

		// Check if the subnet already exists in dtm_base
		const existingSubnet = await db("dtm_base.subnets").where({ netuid }).first();

		if (existingSubnet) {
			subnetsToUpdate.push(subnetToInsertOrUpdate);
		} else {
			subnetsToInsert.push(subnetToInsertOrUpdate);
		}
	}

	// Perform operations in a transaction
	await db.transaction(async (trx) => {
		try {
			// 1. Handle dtm_base.subnets operations
			if (subnetsToInsert.length > 0) {
				await trx("dtm_base.subnets").insert(subnetsToInsert);
				logger.info(`Inserted ${subnetsToInsert.length} new subnets to dtm_base`);
			}

			if (subnetsToUpdate.length > 0) {
				await Promise.all(
					subnetsToUpdate.map(async (subnet) => {
						await trx("dtm_base.subnets").where({ netuid: subnet.netuid }).update(subnet);
					})
				);
				logger.info(`Updated ${subnetsToUpdate.length} subnets in dtm_base`);
			}

			// 2. Sync public.subnets (upsert operation)
			if (publicSubnetsToSync.length > 0) {
				// First delete any existing entries that might be stale
				await trx("public.subnets").delete();

				// Then insert all current records
				await trx("public.subnets").insert(publicSubnetsToSync.map(({ netuid, name }) => ({ netuid, name })));

				logger.info(`Synced ${publicSubnetsToSync.length} subnets to public schema`);
			}

			await trx.commit();
		} catch (error) {
			await trx.rollback();
			logger.error("Transaction failed:", error);
			throw error;
		}
	});
};

module.exports = {
	getAllSubnets,
	getSubnetById,
	createSubnet,
	updateSubnet,
	deleteSubnet,
	updateSubnetsWithTaoStats,
	updateSubnets,
};
