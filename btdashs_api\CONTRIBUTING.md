# Contributing to BTDash API

Thank you for your interest in contributing to the BTDash API! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)
- [Documentation](#documentation)

## Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- PostgreSQL 13.x or higher
- Git
- Auth0 account (for authentication testing)

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/btdashs_api.git
   cd btdashs_api
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database Setup**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

6. **Verify Setup**
   ```bash
   curl http://localhost:3001/health
   npm test
   ```

## Development Workflow

### Branch Strategy

We use a Git flow-inspired branching strategy:

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Feature development branches
- **hotfix/**: Critical bug fixes
- **release/**: Release preparation branches

### Creating a Feature Branch

```bash
# Start from develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature-name

# Make your changes
# ...

# Commit your changes
git add .
git commit -m "feat: add your feature description"

# Push to your fork
git push origin feature/your-feature-name
```

### Commit Message Format

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(auth): add JWT token refresh functionality
fix(database): resolve connection pool leak
docs(api): update OpenAPI specification
test(users): add integration tests for user service
```

## Coding Standards

### Code Style

- Follow the [Code Style Guide](docs/CODE_STYLE_GUIDE.md)
- Use ESLint and Prettier for consistent formatting
- Write self-documenting code with clear variable names
- Add comments for complex business logic

### Architecture Guidelines

- Follow the established service layer pattern
- Keep controllers thin - business logic belongs in services
- Use dependency injection for better testability
- Maintain clear separation between layers

### Error Handling

- Use specific error types for different scenarios
- Include relevant context in error messages
- Log errors with appropriate detail level
- Handle errors gracefully without exposing sensitive information

## Testing Guidelines

### Test Requirements

All contributions must include appropriate tests:

- **Unit Tests**: For service layer and utility functions
- **Integration Tests**: For API endpoints and database operations
- **Documentation**: Update API documentation for new endpoints

### Running Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration

# Run with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Coverage

Maintain test coverage above:
- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

### Writing Tests

```javascript
describe('UserService', () => {
  describe('createUser', () => {
    it('should create a user with valid data', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', password: 'password123' };
      
      // Act
      const user = await UserService.createUser(userData);
      
      // Assert
      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
    });
  });
});
```

## Pull Request Process

### Before Submitting

1. **Code Quality**
   ```bash
   npm run lint
   npm run format
   npm test
   ```

2. **Documentation**
   - Update API documentation if adding/changing endpoints
   - Update README if changing setup or usage
   - Add JSDoc comments for new functions

3. **Testing**
   - Ensure all tests pass
   - Add tests for new functionality
   - Verify test coverage meets requirements

### Pull Request Template

When creating a pull request, include:

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
- [ ] No breaking changes (or documented)
```

### Review Process

1. **Automated Checks**: All CI checks must pass
2. **Code Review**: At least one team member review required
3. **Testing**: Verify tests cover new functionality
4. **Documentation**: Ensure documentation is updated
5. **Approval**: Maintainer approval required for merge

## Issue Reporting

### Bug Reports

When reporting bugs, include:

1. **Environment Information**
   - Node.js version
   - Operating system
   - Database version

2. **Steps to Reproduce**
   - Clear, numbered steps
   - Expected vs actual behavior
   - Error messages or logs

3. **Additional Context**
   - Screenshots if applicable
   - Related issues or PRs
   - Possible solutions

### Feature Requests

For feature requests, include:

1. **Problem Description**
   - What problem does this solve?
   - Who would benefit from this feature?

2. **Proposed Solution**
   - Detailed description of the feature
   - API design if applicable
   - Implementation considerations

3. **Alternatives Considered**
   - Other solutions you've considered
   - Why this approach is preferred

## Documentation

### API Documentation

- Update OpenAPI specification for new endpoints
- Include request/response examples
- Document error responses
- Add authentication requirements

### Code Documentation

- Use JSDoc for all public functions
- Include parameter types and descriptions
- Add usage examples for complex functions
- Document business logic and algorithms

### Architecture Documentation

- Update architecture docs for significant changes
- Document design decisions and trade-offs
- Include diagrams for complex flows
- Maintain database schema documentation

## Development Tools

### Recommended IDE Setup

**VS Code Extensions:**
- ESLint
- Prettier
- REST Client
- GitLens
- Thunder Client (for API testing)

### Debugging

```bash
# Debug mode
npm run dev:debug

# Debug tests
npm run test:debug
```

### Database Tools

```bash
# Database migrations
npm run db:migrate
npm run db:rollback

# Database seeding
npm run db:seed

# Database status
npm run db:status
```

## Getting Help

### Resources

- [Architecture Documentation](docs/ARCHITECTURE.md)
- [Code Style Guide](docs/CODE_STYLE_GUIDE.md)
- [API Documentation](http://localhost:3001/api/docs)
- [Database Schema](docs/database-schema.md)

### Communication

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Pull Request Comments**: For code-specific discussions

### Mentorship

New contributors are welcome! If you're new to the project:

1. Start with issues labeled `good first issue`
2. Ask questions in GitHub Discussions
3. Request code review feedback
4. Pair with experienced contributors

## Recognition

Contributors are recognized in:

- **README**: Major contributors listed
- **Release Notes**: Contributors credited for each release
- **GitHub**: Contributor graphs and statistics

## License

By contributing to BTDash API, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to BTDash API! 🚀
