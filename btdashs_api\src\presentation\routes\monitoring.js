// src/presentation/routes/monitoring.js - Monitoring and metrics routes

const express = require("express");
const {
	metricsEndpointMiddleware,
	healthMetricsMiddleware,
	prometheusMetricsMiddleware,
} = require("../../middleware/metricsMiddleware");
const metricsCollector = require("../../infrastructure/monitoring/MetricsCollector");

const router = express.Router();

/**
 * GET /metrics - Application metrics in JSON format
 */
router.get("/metrics", metricsEndpointMiddleware);

/**
 * GET /health - Health check with metrics
 */
router.get("/health", healthMetricsMiddleware);

/**
 * GET /metrics/prometheus - Prometheus-compatible metrics
 */
router.get("/metrics/prometheus", prometheusMetricsMiddleware);

/**
 * POST /reset-metrics - Reset metrics (for testing)
 */
router.post("/reset-metrics", (req, res) => {
	try {
		metricsCollector.reset();
		res.json({
			success: true,
			message: "Metrics reset successfully",
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		res.status(500).json({
			success: false,
			message: "Failed to reset metrics",
			error: error.message,
		});
	}
});

/**
 * GET /dashboard - Simple monitoring dashboard
 */
router.get("/dashboard", (req, res) => {
	const dashboardHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BTDash API Monitoring Dashboard</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
    }
    
    .header {
      background: #2c3e50;
      color: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .header h1 {
      margin: 0;
      font-size: 1.5rem;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .metric-card {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-left: 4px solid #3498db;
    }
    
    .metric-card.warning {
      border-left-color: #f39c12;
    }
    
    .metric-card.error {
      border-left-color: #e74c3c;
    }
    
    .metric-card h3 {
      margin-bottom: 1rem;
      color: #2c3e50;
      font-size: 1.1rem;
    }
    
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #3498db;
      margin-bottom: 0.5rem;
    }
    
    .metric-label {
      color: #666;
      font-size: 0.9rem;
    }
    
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }
    
    .status-healthy { background: #27ae60; }
    .status-degraded { background: #f39c12; }
    .status-unhealthy { background: #e74c3c; }
    
    .chart-container {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 2rem;
    }
    
    .refresh-btn {
      background: #3498db;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.9rem;
      margin-bottom: 1rem;
    }
    
    .refresh-btn:hover {
      background: #2980b9;
    }
    
    .endpoint-list {
      max-height: 300px;
      overflow-y: auto;
    }
    
    .endpoint-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid #eee;
    }
    
    .endpoint-item:last-child {
      border-bottom: none;
    }
    
    .endpoint-name {
      font-family: monospace;
      font-size: 0.9rem;
    }
    
    .endpoint-stats {
      font-size: 0.8rem;
      color: #666;
    }
    
    .auto-refresh {
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      font-size: 0.8rem;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>BTDash API Monitoring Dashboard</h1>
  </div>
  
  <div class="auto-refresh" id="autoRefresh">
    Auto-refresh: <span id="countdown">30</span>s
  </div>
  
  <div class="container">
    <button class="refresh-btn" onclick="loadMetrics()">Refresh Metrics</button>
    
    <div class="metrics-grid" id="metricsGrid">
      <div class="metric-card">
        <h3>Loading...</h3>
        <div class="metric-value">-</div>
        <div class="metric-label">Please wait</div>
      </div>
    </div>
    
    <div class="chart-container">
      <h3>Top Endpoints</h3>
      <div class="endpoint-list" id="endpointList">
        Loading...
      </div>
    </div>
  </div>

  <script>
    let countdownTimer;
    let refreshTimer;
    
    async function loadMetrics() {
      try {
        const [healthResponse, metricsResponse] = await Promise.all([
          fetch('/monitoring/health'),
          fetch('/monitoring/metrics')
        ]);
        
        const health = await healthResponse.json();
        const metrics = await metricsResponse.json();
        
        updateDashboard(health, metrics);
      } catch (error) {
        console.error('Failed to load metrics:', error);
        document.getElementById('metricsGrid').innerHTML = 
          '<div class="metric-card error"><h3>Error</h3><div class="metric-value">Failed to load metrics</div></div>';
      }
    }
    
    function updateDashboard(health, metrics) {
      const grid = document.getElementById('metricsGrid');
      const statusClass = health.status === 'healthy' ? 'status-healthy' : 
                         health.status === 'degraded' ? 'status-degraded' : 'status-unhealthy';
      
      grid.innerHTML = \`
        <div class="metric-card">
          <h3><span class="status-indicator \${statusClass}"></span>System Status</h3>
          <div class="metric-value">\${health.status.toUpperCase()}</div>
          <div class="metric-label">Overall health</div>
        </div>
        
        <div class="metric-card">
          <h3>Total Requests</h3>
          <div class="metric-value">\${metrics.requests.total.toLocaleString()}</div>
          <div class="metric-label">Since startup</div>
        </div>
        
        <div class="metric-card \${parseFloat(health.requests.errorRate) > 5 ? 'warning' : ''}">
          <h3>Error Rate</h3>
          <div class="metric-value">\${health.requests.errorRate}</div>
          <div class="metric-label">Percentage of failed requests</div>
        </div>
        
        <div class="metric-card">
          <h3>Avg Response Time</h3>
          <div class="metric-value">\${metrics.avgResponseTime}ms</div>
          <div class="metric-label">Average across all requests</div>
        </div>
        
        <div class="metric-card \${parseFloat(health.memory.usage) > 80 ? 'warning' : ''}">
          <h3>Memory Usage</h3>
          <div class="metric-value">\${health.memory.usage}</div>
          <div class="metric-label">System memory utilization</div>
        </div>
        
        <div class="metric-card">
          <h3>Database Queries</h3>
          <div class="metric-value">\${metrics.database.queries.total.toLocaleString()}</div>
          <div class="metric-label">Total queries executed</div>
        </div>
        
        <div class="metric-card">
          <h3>Uptime</h3>
          <div class="metric-value">\${formatUptime(health.uptime)}</div>
          <div class="metric-label">Server uptime</div>
        </div>
        
        <div class="metric-card">
          <h3>Request Rate</h3>
          <div class="metric-value">\${metrics.requestRate}</div>
          <div class="metric-label">Requests per minute</div>
        </div>
      \`;
      
      // Update endpoints list
      const endpointList = document.getElementById('endpointList');
      const endpoints = Object.entries(metrics.requests.byEndpoint)
        .sort(([,a], [,b]) => b.count - a.count)
        .slice(0, 10);
      
      endpointList.innerHTML = endpoints.map(([endpoint, stats]) => \`
        <div class="endpoint-item">
          <div class="endpoint-name">\${endpoint}</div>
          <div class="endpoint-stats">
            \${stats.count} requests | \${stats.avgTime.toFixed(0)}ms avg | \${stats.errors} errors
          </div>
        </div>
      \`).join('');
    }
    
    function formatUptime(seconds) {
      const days = Math.floor(seconds / 86400);
      const hours = Math.floor((seconds % 86400) / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      
      if (days > 0) return \`\${days}d \${hours}h\`;
      if (hours > 0) return \`\${hours}h \${minutes}m\`;
      return \`\${minutes}m\`;
    }
    
    function startCountdown() {
      let count = 30;
      const countdownEl = document.getElementById('countdown');
      
      countdownTimer = setInterval(() => {
        count--;
        countdownEl.textContent = count;
        
        if (count <= 0) {
          loadMetrics();
          count = 30;
        }
      }, 1000);
    }
    
    // Initialize dashboard
    loadMetrics();
    startCountdown();
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      if (countdownTimer) clearInterval(countdownTimer);
      if (refreshTimer) clearInterval(refreshTimer);
    });
  </script>
</body>
</html>`;

	res.send(dashboardHtml);
});

module.exports = router;
