// app/dashboard/placements/[id]/create/client-wrapper.tsx
"use client";

import { CountrySelector } from "@/components/country-selector";
import { DatePickerWithRange } from "@/components/date-range-picker";
import { PageTypeSelector } from "@/components/page-type-selector";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import type { AdSlot, Company } from "@/lib/db/models";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import type { DateRange } from "react-day-picker";

export default function CreateCampaignClientWrapper({ slot, company }: { slot: AdSlot; company: Company }) {
	const router = useRouter();
	const { toast } = useToast();
	const [isSubmitting, setIsSubmitting] = useState(false);

	const [formData, setFormData] = useState({
		name: "",
		url: "",
		imageUrl: "",
		budget: "",
		maxImpressions: "",
		maxClicks: "",
	});

	const [dateRange, setDateRange] = useState<DateRange>({
		from: new Date(),
		to: new Date(new Date().setMonth(new Date().getMonth() + 1)),
	});

	const [targetingMode, setTargetingMode] = useState<"all" | "include" | "exclude">("all");
	const [includedCountries, setIncludedCountries] = useState<string[]>([]);
	const [excludedCountries, setExcludedCountries] = useState<string[]>([]);
	const [selectedPageTypes, setSelectedPageTypes] = useState<string[]>([]);
	const [selectedCategories, setSelectedCategories] = useState<{ [key: string]: string[] | "all" }>({});

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData((prev) => ({ ...prev, [name]: value }));
	};

	const handlePageTypeChange = (types: string[]) => {
		setSelectedPageTypes(types);
		setSelectedCategories((prev) => {
			const updated = { ...prev };
			Object.keys(updated).forEach((type) => {
				if (!types.includes(type)) delete updated[type];
			});
			types.forEach((type) => {
				if (!updated[type]) updated[type] = "all";
			});
			return updated;
		});
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);

		try {
			// Validate required fields
			if (!formData.name || !formData.url || !formData.imageUrl || !formData.budget) {
				throw new Error("Please fill all required fields");
			}

			if (!dateRange.from || !dateRange.to) {
				throw new Error("Please select a valid date range");
			}

			if (new Date(dateRange.from) >= new Date(dateRange.to)) {
				throw new Error("End date must be after start date");
			}

			// Prepare campaign data
			const campaignData = {
				advertiser_id: company.id,
				slot_id: slot.id,
				name: formData.name,
				target_url: formData.url,
				image_url: formData.imageUrl,
				start_date: dateRange.from.toISOString(),
				end_date: dateRange.to.toISOString(),
				total_budget: Number(formData.budget),
				max_impressions: formData.maxImpressions ? Number(formData.maxImpressions) : null,
				max_clicks: formData.maxClicks ? Number(formData.maxClicks) : null,
				weight: 1, // Default weight
				targeting: {
					countries: {
						mode: targetingMode,
						...(targetingMode === "include" && { include: includedCountries }),
						...(targetingMode === "exclude" && { exclude: excludedCountries }),
					},
					page_types: {
						types: selectedPageTypes,
						categories: selectedCategories,
					},
				},
			};

			const response = await fetch("/api/user/campaigns", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(campaignData),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to create campaign");
			}

			const { campaign } = await response.json();
			router.push(`/dashboard/checkout?campaign=${campaign.id}`);
		} catch (error: unknown) {
			toast({
				title: "Error",
				description: error instanceof Error ? error.message : "An unknown error occurred",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Create Campaign</h1>
			</div>

			<div className="grid gap-6 lg:grid-cols-3">
				<Card className="lg:col-span-2">
					<CardContent className="p-6">
						<form onSubmit={handleSubmit} className="space-y-6">
							<div className="space-y-2">
								<Label htmlFor="name">Campaign Name *</Label>
								<Input
									id="name"
									name="name"
									value={formData.name}
									onChange={handleInputChange}
									placeholder="Enter campaign name"
									required
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="url">Destination URL *</Label>
								<Input
									id="url"
									name="url"
									type="url"
									value={formData.url}
									onChange={handleInputChange}
									placeholder="https://example.com"
									required
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="budget">Total Budget ($) *</Label>
								<Input
									id="budget"
									name="budget"
									type="number"
									value={formData.budget}
									onChange={handleInputChange}
									placeholder="500"
									required
								/>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="maxImpressions">Max Impressions (optional)</Label>
									<Input
										id="maxImpressions"
										name="maxImpressions"
										type="number"
										value={formData.maxImpressions}
										onChange={handleInputChange}
										placeholder="10000"
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="maxClicks">Max Clicks (optional)</Label>
									<Input
										id="maxClicks"
										name="maxClicks"
										type="number"
										value={formData.maxClicks}
										onChange={handleInputChange}
										placeholder="500"
									/>
								</div>
							</div>

							<div className="space-y-2">
								<Label>Campaign Duration *</Label>
								<DatePickerWithRange
									date={dateRange}
									setDate={(range) => {
										if (range) setDateRange(range);
									}}
								/>
							</div>

							<Separator className="my-6" />

							<div className="space-y-4">
								<h3 className="text-lg font-medium">Geographic Targeting</h3>
								<RadioGroup
									value={targetingMode}
									onValueChange={(v) => setTargetingMode(v as "all" | "include" | "exclude")}
									className="flex flex-col space-y-2"
								>
									<div className="flex items-center space-x-2">
										<RadioGroupItem value="all" id="all" />
										<Label htmlFor="all">Show ads to all countries</Label>
									</div>
									<div className="flex items-center space-x-2">
										<RadioGroupItem value="include" id="include" />
										<Label htmlFor="include">Include specific countries only</Label>
									</div>
									<div className="flex items-center space-x-2">
										<RadioGroupItem value="exclude" id="exclude" />
										<Label htmlFor="exclude">Exclude specific countries</Label>
									</div>
								</RadioGroup>

								{targetingMode === "include" && (
									<div className="pl-6 space-y-2">
										<Label>Countries to include</Label>
										<CountrySelector
											selected={includedCountries}
											onChange={setIncludedCountries}
											placeholder="Select countries to include..."
										/>
									</div>
								)}

								{targetingMode === "exclude" && (
									<div className="pl-6 space-y-2">
										<Label>Countries to exclude</Label>
										<CountrySelector
											selected={excludedCountries}
											onChange={setExcludedCountries}
											placeholder="Select countries to exclude..."
										/>
									</div>
								)}
							</div>

							<Separator className="my-6" />

							<div className="space-y-4">
								<h3 className="text-lg font-medium">Page Targeting</h3>
								<PageTypeSelector
									selectedTypes={selectedPageTypes}
									selectedCategories={selectedCategories}
									onTypeChange={handlePageTypeChange}
									onCategoryChange={(type, cats) =>
										setSelectedCategories((prev) => ({ ...prev, [type]: cats }))
									}
								/>
							</div>

							<Separator className="my-6" />

							<div className="space-y-2">
								<Label htmlFor="imageUrl">Image URL *</Label>
								<Input
									id="imageUrl"
									name="imageUrl"
									type="url"
									value={formData.imageUrl}
									onChange={handleInputChange}
									placeholder="https://example.com/ad-image.jpg"
									required
								/>
								<p className="text-xs text-muted-foreground">
									Dimensions: {slot.width}x{slot.height}px • Formats:{" "}
									{slot.allowed_ad_types?.join(", ") || "JPEG, PNG"}
								</p>
							</div>

							<Button type="submit" className="w-full" disabled={isSubmitting}>
								{isSubmitting ? "Creating..." : "Continue to Checkout"}
							</Button>
						</form>
					</CardContent>
				</Card>

				<div className="space-y-6">
					<Card>
						<CardContent className="p-6">
							<h3 className="text-lg font-medium mb-2">Selected Placement</h3>
							<div className="space-y-2">
								<p className="text-sm font-medium">{slot.name}</p>
								<p className="text-sm text-muted-foreground">{slot.description}</p>
								<div className="flex justify-between">
									<p className="text-sm font-medium">Type:</p>
									<p className="text-sm">{slot.page}</p>
								</div>
								<div className="flex justify-between">
									<p className="text-sm font-medium">Dimensions:</p>
									<p className="text-sm">
										{slot.width}x{slot.height}px
									</p>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-6">
							<h3 className="text-lg font-medium mb-4">Preview</h3>
							{formData.imageUrl ? (
								<div className="overflow-hidden rounded-lg border">
									<Image
										src={formData.imageUrl}
										alt="Ad preview"
										width={slot.width}
										height={slot.height}
										className="w-full h-auto"
										style={{ aspectRatio: `${slot.width}/${slot.height}` }}
									/>
								</div>
							) : (
								<div
									className="flex flex-col items-center justify-center rounded-lg border border-dashed"
									style={{
										aspectRatio: `${slot.width}/${slot.height}`,
										background: "#f5f5f5",
									}}
								>
									<p className="text-sm text-muted-foreground">Enter image URL to see preview</p>
								</div>
							)}
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
