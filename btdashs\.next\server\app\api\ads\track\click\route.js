/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/track/click/route";
exports.ids = ["app/api/ads/track/click/route"];
exports.modules = {

/***/ "(rsc)/./app/api/ads/track/click/route.ts":
/*!******************************************!*\
  !*** ./app/api/ads/track/click/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        // Validate request body\n        let body;\n        try {\n            body = await request.json();\n        } catch (parseError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        const { ad_id, session_id, user_id, country_code, device_type } = body;\n        // Validate required fields\n        if (!ad_id || !session_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Missing required fields: ad_id and session_id\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate field types\n        if (typeof ad_id !== \"number\" || typeof session_id !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Invalid field types: ad_id must be number, session_id must be string\"\n            }, {\n                status: 400\n            });\n        }\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        if (!process.env.INTERNAL_API_KEY) {\n            console.error(\"INTERNAL_API_KEY not configured\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Forward to backend API with timeout\n        const headers = new Headers();\n        headers.set(\"Content-Type\", \"application/json\");\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000);\n        try {\n            const response = await fetch(`${API_BASE}/track/click`, {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify({\n                    ad_id,\n                    session_id,\n                    user_id,\n                    ip_address: request.ip || \"unknown\",\n                    user_agent: request.headers.get(\"user-agent\") || \"unknown\",\n                    country_code,\n                    device_type\n                }),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const result = await response.json();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n            } else {\n                const errorText = await response.text().catch(()=>\"Unknown error\");\n                console.error(`Backend API error ${response.status}: ${errorText}`);\n                // Don't expose backend errors to client\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: \"Failed to track click\"\n                }, {\n                    status: 500\n                });\n            }\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        if (error instanceof Error) {\n            if (error.name === \"AbortError\") {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: \"Request timeout\"\n                }, {\n                    status: 504\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Failed to track click\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ads/track/click/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fclick%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fclick%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_track_click_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ads/track/click/route.ts */ \"(rsc)/./app/api/ads/track/click/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/track/click/route\",\n        pathname: \"/api/ads/track/click\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/track/click/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\ads\\\\track\\\\click\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_track_click_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fclick%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fclick%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fclick%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();