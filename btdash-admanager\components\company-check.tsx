"use client";

import OnboardingWizard from "@/components/onboarding-wizard";
import { useEffect, useState } from "react";

interface CompanyCheckProps {
	children: React.ReactNode;
}

export default function CompanyCheck({ children }: CompanyCheckProps) {
	const [showWizard, setShowWizard] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [hasCompany, setHasCompany] = useState(false);

	useEffect(() => {
		checkCompanyStatus();
	}, []);

	const checkCompanyStatus = async () => {
		try {
			const response = await fetch("/api/user/company");

			if (response.ok) {
				const result = await response.json();
				if (result.data?.id) {
					setHasCompany(true);
					setShowWizard(false);
				} else {
					setHasCompany(false);
					setShowWizard(true);
				}
			} else {
				// API error (500, 404, etc.) - assume no company and show wizard
				console.warn("Company status check failed with status:", response.status);
				setHasCompany(false);
				setShowWizard(true);
			}
		} catch (error) {
			console.error("Error checking company status:", error);
			// Network error or backend unavailable - assume no company and show wizard
			setHasCompany(false);
			setShowWizard(true);
		} finally {
			setIsLoading(false);
		}
	};

	const handleWizardComplete = () => {
		setShowWizard(false);
		setHasCompany(true);
		// Optionally refresh the page or update the UI
		window.location.reload();
	};

	// Show loading state while checking company status
	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[200px]">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
			</div>
		);
	}

	return (
		<>
			{children}
			<OnboardingWizard
				isOpen={showWizard}
				onClose={() => setShowWizard(false)}
				onComplete={handleWizardComplete}
			/>
		</>
	);
}
