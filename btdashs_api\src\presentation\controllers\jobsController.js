const JobService = require("../../application/services/JobService");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

// Get all jobs sorted by published_date
const getAllJobs = asyncHandler(async (req, res) => {
	// Fetch jobs and order them by published_date in descending order
	const jobs = await JobService.getAllJobs();
	return sendSuccess(res, jobs, "Jobs retrieved successfully");
});

// Get a job by ID
const getJobById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const job = await JobService.getJobById(id);
	if (!job) {
		return sendNotFound(res, "Job not found");
	}
	return sendSuccess(res, job, "Job retrieved successfully");
});

// Create a new job
const createJob = asyncHandler(async (req, res) => {
	const {
		title,
		description,
		location,
		remote,
		type,
		category,
		industry,
		currency,
		salary_time_frame,
		min_salary,
		max_salary,
		company_id,
		owner_id,
		subnet_ids,
		product_ids,
		company_ids,
	} = req.body;

	const newJob = await JobService.createJob({
		title,
		description,
		location,
		remote,
		type,
		category,
		industry,
		currency,
		salary_time_frame,
		min_salary,
		max_salary,
		company_id,
		owner_id,
		subnet_ids,
		product_ids,
		company_ids,
		published_date: new Date(), // Automatically set the published date
	});

	return sendSuccess(res, newJob, "Job created successfully", 201);
});

// Update a job
const updateJob = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const {
		title,
		description,
		location,
		remote,
		type,
		category,
		industry,
		currency,
		salary_time_frame,
		min_salary,
		max_salary,
		company_id,
		owner_id,
		subnet_ids,
		product_ids,
		company_ids,
	} = req.body;

	const updatedJob = await JobService.updateJob(id, {
		title,
		description,
		location,
		remote,
		type,
		category,
		industry,
		currency,
		salary_time_frame,
		min_salary,
		max_salary,
		company_id,
		owner_id,
		subnet_ids,
		product_ids,
		company_ids,
	});

	if (!updatedJob) {
		return sendNotFound(res, "Job not found");
	}
	return sendSuccess(res, updatedJob, "Job updated successfully");
});

// Delete a job
const deleteJob = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const deleted = await JobService.deleteJob(id);
	if (!deleted) {
		return sendNotFound(res, "Job not found");
	}
	return sendSuccess(res, null, "Job deleted successfully");
});

module.exports = {
	getAllJobs,
	getJobById,
	createJob,
	updateJob,
	deleteJob,
};
