// src/application/services/UpdateService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Update Service - Handles system update operations
 * 
 * This service manages system updates, version tracking,
 * and update-related operations for the platform.
 * 
 * Key responsibilities:
 * - Update records CRUD operations
 * - Version management
 * - Update status tracking
 * - Data validation and sanitization
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class UpdateService extends BaseService {
  constructor() {
    super("dtm_base.updates", "Update");
  }

  /**
   * Get all updates with default sorting by timestamp
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of updates
   */
  async getAllUpdates(filters = {}, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'timestamp', direction: 'desc' },
        ...options
      };

      return await this.getAll(filters, queryOptions);
    } catch (error) {
      logger.error("Error getting all updates", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get update by ID
   * @param {number} id - Update ID
   * @returns {Promise<Object|null>} Update object or null if not found
   */
  async getUpdateById(id) {
    try {
      return await this.getById(id);
    } catch (error) {
      logger.error("Error getting update by ID", { error, id });
      throw error;
    }
  }

  /**
   * Create a new update record
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Created update object
   */
  async createUpdate(updateData) {
    try {
      const newUpdate = await this.create({
        ...updateData,
        timestamp: updateData.timestamp || new Date()
      });
      logger.info("Update created", { update_id: newUpdate.id });
      return newUpdate;
    } catch (error) {
      logger.error("Error creating update", { error, updateData });
      throw error;
    }
  }

  /**
   * Update an update record
   * @param {number} id - Update ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated update object
   */
  async updateUpdate(id, updateData) {
    try {
      const updatedUpdate = await this.updateById(id, updateData);
      logger.info("Update updated", { update_id: id });
      return updatedUpdate;
    } catch (error) {
      logger.error("Error updating update", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete an update record
   * @param {number} id - Update ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteUpdate(id) {
    try {
      const result = await this.deleteById(id);
      logger.info("Update deleted", { update_id: id });
      return result;
    } catch (error) {
      logger.error("Error deleting update", { error, id });
      throw error;
    }
  }

  /**
   * Get latest updates
   * @param {number} limit - Number of latest updates to return
   * @returns {Promise<Array>} Array of latest updates
   */
  async getLatestUpdates(limit = 10) {
    try {
      const updates = await this.getAll({}, {
        orderBy: { column: 'timestamp', direction: 'desc' },
        limit
      });

      logger.info("Latest updates retrieved", { 
        count: updates.length,
        limit 
      });
      
      return updates;
    } catch (error) {
      logger.error("Error getting latest updates", { error, limit });
      throw new Error(`Failed to get latest updates: ${error.message}`);
    }
  }

  /**
   * Get updates by version
   * @param {string} version - Version string
   * @returns {Promise<Array>} Array of updates for the version
   */
  async getUpdatesByVersion(version) {
    try {
      const updates = await this.getAll({ version }, {
        orderBy: { column: 'timestamp', direction: 'desc' }
      });

      logger.info("Updates retrieved by version", { 
        version,
        count: updates.length 
      });
      
      return updates;
    } catch (error) {
      logger.error("Error getting updates by version", { error, version });
      throw new Error(`Failed to get updates by version: ${error.message}`);
    }
  }

  /**
   * Get updates by status
   * @param {string} status - Update status (pending, completed, failed, etc.)
   * @returns {Promise<Array>} Array of updates with the specified status
   */
  async getUpdatesByStatus(status) {
    try {
      const updates = await this.getAll({ status }, {
        orderBy: { column: 'timestamp', direction: 'desc' }
      });

      logger.info("Updates retrieved by status", { 
        status,
        count: updates.length 
      });
      
      return updates;
    } catch (error) {
      logger.error("Error getting updates by status", { error, status });
      throw new Error(`Failed to get updates by status: ${error.message}`);
    }
  }

  /**
   * Get updates by date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} Array of updates in date range
   */
  async getUpdatesByDateRange(startDate, endDate) {
    try {
      const db = require("../../infrastructure/database/knex");
      
      const updates = await db(this.tableName)
        .where('timestamp', '>=', startDate)
        .where('timestamp', '<=', endDate)
        .orderBy('timestamp', 'desc');

      logger.info("Updates retrieved by date range", { 
        startDate,
        endDate,
        count: updates.length 
      });
      
      return updates;
    } catch (error) {
      logger.error("Error getting updates by date range", { error, startDate, endDate });
      throw new Error(`Failed to get updates by date range: ${error.message}`);
    }
  }

  /**
   * Get update statistics
   * @param {number} days - Number of days to analyze
   * @returns {Promise<Object>} Update statistics
   */
  async getUpdateStatistics(days = 30) {
    try {
      const db = require("../../infrastructure/database/knex");
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const stats = await db(this.tableName)
        .where('timestamp', '>=', startDate)
        .select(
          db.raw('COUNT(*) as total_updates'),
          db.raw('COUNT(CASE WHEN status = \'completed\' THEN 1 END) as completed_updates'),
          db.raw('COUNT(CASE WHEN status = \'failed\' THEN 1 END) as failed_updates'),
          db.raw('COUNT(CASE WHEN status = \'pending\' THEN 1 END) as pending_updates')
        )
        .first();

      // Calculate success rate
      if (stats.total_updates > 0) {
        stats.success_rate = (stats.completed_updates / stats.total_updates) * 100;
      } else {
        stats.success_rate = 0;
      }

      logger.info("Update statistics retrieved", { 
        days,
        stats 
      });
      
      return stats;
    } catch (error) {
      logger.error("Error getting update statistics", { error, days });
      throw new Error(`Failed to get update statistics: ${error.message}`);
    }
  }

  /**
   * Get current system version
   * @returns {Promise<Object|null>} Current version info or null if not found
   */
  async getCurrentVersion() {
    try {
      const db = require("../../infrastructure/database/knex");
      
      const currentVersion = await db(this.tableName)
        .where('status', 'completed')
        .orderBy('timestamp', 'desc')
        .first();

      if (currentVersion) {
        logger.info("Current version retrieved", { version: currentVersion.version });
      }
      
      return currentVersion;
    } catch (error) {
      logger.error("Error getting current version", { error });
      throw new Error(`Failed to get current version: ${error.message}`);
    }
  }

  /**
   * Mark update as completed
   * @param {number} id - Update ID
   * @param {Object} completionData - Completion data
   * @returns {Promise<Object>} Updated update object
   */
  async markUpdateCompleted(id, completionData = {}) {
    try {
      const updatedUpdate = await this.updateById(id, {
        status: 'completed',
        completed_at: new Date(),
        ...completionData
      });
      
      logger.info("Update marked as completed", { update_id: id });
      return updatedUpdate;
    } catch (error) {
      logger.error("Error marking update as completed", { error, id });
      throw error;
    }
  }

  /**
   * Mark update as failed
   * @param {number} id - Update ID
   * @param {string} errorMessage - Error message
   * @returns {Promise<Object>} Updated update object
   */
  async markUpdateFailed(id, errorMessage) {
    try {
      const updatedUpdate = await this.updateById(id, {
        status: 'failed',
        error_message: errorMessage,
        failed_at: new Date()
      });
      
      logger.info("Update marked as failed", { update_id: id, errorMessage });
      return updatedUpdate;
    } catch (error) {
      logger.error("Error marking update as failed", { error, id });
      throw error;
    }
  }
}

module.exports = new UpdateService();
