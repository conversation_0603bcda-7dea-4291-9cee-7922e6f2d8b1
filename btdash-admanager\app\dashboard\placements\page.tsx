// app/dashboard/placements/page.tsx
import { fetchWithFallback } from "@/lib/utils";
import { cookies } from "next/headers";
import PlacementsClientWrapper from "./client-wrapper";

export const revalidate = 3600; // 1 hour

export default async function PlacementsPage() {
	const cookieHeader = (await cookies()).toString();

	const placementsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/placements`, {
		headers: { Cookie: cookieHeader },
		next: { tags: ["placements"] },
	});

	if (placementsRes.error) console.error("Failed to fetch placements:", placementsRes.error);

	return (
		<div className="flex flex-col gap-4">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Ad Placements</h1>
			</div>
			<PlacementsClientWrapper placements={placementsRes.data || []} />
		</div>
	);
}
