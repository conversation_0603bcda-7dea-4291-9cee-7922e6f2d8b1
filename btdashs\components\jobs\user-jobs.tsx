"use client";

import { JobsGrid } from "@/components/jobs/jobs-grid";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MarkdownEditor from "@/components/ui/markdown-editor";
import { MultiSelect } from "@/components/ui/multi-select";
import { Category, Company, Job, Product, Subnet } from "@/lib/db/models";
import { Plus } from "lucide-react";
import { useState } from "react";

interface UserJobsClientProps {
	initialJobs: (Job & {
		company?: Company;
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	companies?: Company[];
	categories?: Category[];
	subnets?: Subnet[];
	products?: Product[];
}

export default function UserJobsClient({
	initialJobs,
	companies = [],
	categories = [],
	subnets = [],
	products = [],
}: UserJobsClientProps) {
	const [jobs, setJobs] = useState(initialJobs);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [currentJob, setCurrentJob] = useState<
		| (Partial<Job> & {
				company?: Company;
				categories?: Category[];
				subnets?: Subnet[];
				products?: Product[];
		  })
		| null
	>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const jobTypes = ["Full-time", "Part-time", "Contract", "Freelance", "Internship"];
	const salaryTimeFrames = ["hour", "day", "week", "month", "year"];
	const currencies = ["USD", "EUR", "GBP", "JPY", "CAD", "AUD"];

	const handleSave = async () => {
		if (!currentJob) return;

		// Basic validation
		if (!currentJob.title) {
			setError("Job title is required");
			return;
		}
		if (!currentJob.description) {
			setError("Job description is required");
			return;
		}

		setIsLoading(true);
		setError(null);

		try {
			const method = currentJob.id ? "PUT" : "POST";
			const url = currentJob.id ? `/api/user/jobs/${currentJob.id}` : "/api/user/jobs";

			const payload = {
				...currentJob,
				min_salary: currentJob.min_salary ? Number(currentJob.min_salary) : null,
				max_salary: currentJob.max_salary ? Number(currentJob.max_salary) : null,
				subnet_ids: currentJob.subnet_ids || [],
				category_ids: currentJob.category_ids || [],
				product_ids: currentJob.product_ids || [],
				company_id: currentJob.company_id ? Number(currentJob.company_id) : null,
			};

			const response = await fetch(url, {
				method,
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || "Failed to save job");
			}

			const { data } = await response.json();
			const savedJob = (method === "PUT" ? data : data.data) as Job;

			// Update jobs state
			setJobs((prev) => {
				if (currentJob.id) {
					return prev.map((j) =>
						j.id === savedJob.id
							? {
									...savedJob,
									company: companies.find((c) => c.id === savedJob.company_id),
									categories: categories.filter((c) => currentJob.category_ids?.includes(c.id)),
									subnets: subnets.filter((s) => currentJob.subnet_ids?.includes(s.netuid)),
									products: products.filter((p) => currentJob.product_ids?.includes(p.id)),
							  }
							: j
					);
				} else {
					return [
						{
							...savedJob,
							company: companies.find((c) => c.id === savedJob.company_id),
							categories: categories.filter((c) => currentJob.category_ids?.includes(c.id)),
							subnets: subnets.filter((s) => currentJob.subnet_ids?.includes(s.netuid)),
							products: products.filter((p) => currentJob.product_ids?.includes(p.id)),
						},
						...prev,
					];
				}
			});

			handleCloseDialog();
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleOpenDialog = (
		job?: Job & {
			company?: Company;
			categories?: Category[];
			subnets?: Subnet[];
			products?: Product[];
		}
	) => {
		setCurrentJob(
			job || {
				title: "",
				description: "",
				location: "",
				type: "Full-time",
				currency: "USD",
				min_salary: undefined,
				max_salary: undefined,
				salary_time_frame: "year",
				remote: false,
				subnet_ids: [],
				category_ids: [],
				product_ids: [],
				company_id: undefined,
			}
		);
		setError(null);
		setIsDialogOpen(true);
	};

	const handleCloseDialog = () => {
		setIsDialogOpen(false);
		setCurrentJob(null);
		setError(null);
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
		const { name, value, type } = e.target;
		const checked = type === "checkbox" ? (e.target as HTMLInputElement).checked : undefined;

		setCurrentJob((prev) => ({
			...prev!,
			[name]: checked !== undefined ? checked : value,
		}));
	};

	const handleMultiSelectChange = (name: string, values: number[]) => {
		setCurrentJob((prev) => ({
			...prev!,
			[name]: values,
		}));
	};

	const handleDelete = async (id: number) => {
		if (!confirm("Are you sure you want to delete this job?")) return;

		setIsLoading(true);
		try {
			const response = await fetch(`/api/user/jobs/${id}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to delete job");
			}

			setJobs((prev) => prev.filter((j) => j.id !== id));
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Manage Jobs</h2>
				<Button onClick={() => handleOpenDialog()}>
					<Plus className="mr-2 h-4 w-4" />
					Post New Job
				</Button>
			</div>

			{error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>}

			<JobsGrid
				jobs={jobs}
				editMode={true}
				onEdit={(job) => handleOpenDialog(job)}
				onDelete={handleDelete}
				loading={isLoading}
			/>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>{currentJob?.id ? "Edit Job" : "Create New Job"}</DialogTitle>
					</DialogHeader>

					<div className="grid gap-4 py-4">
						<div className="space-y-2">
							<Label htmlFor="title">Job Title*</Label>
							<Input
								id="title"
								name="title"
								value={currentJob?.title || ""}
								onChange={handleChange}
								placeholder="e.g. Senior Frontend Developer"
								required
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="description">Job Description*</Label>
							<MarkdownEditor
								initialValue={currentJob?.description || ""}
								placeholder="Enter detailed job description..."
								onChange={(markdown) => setCurrentJob((prev) => ({ ...prev!, description: markdown }))}
								height="300px"
							/>
						</div>

						<div className="space-y-2">
							<Label>Company</Label>
							<MultiSelect
								options={companies.map((c) => ({ value: c.id, label: c.name }))}
								selected={currentJob?.company_id ? [currentJob.company_id] : []}
								onChange={(values) =>
									setCurrentJob((prev) => ({
										...prev!,
										company_id: values.length > 0 ? values[0] : undefined,
									}))
								}
								placeholder="Select company..."
								single={true}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="type">Job Type*</Label>
								<select
									id="type"
									name="type"
									value={currentJob?.type || ""}
									onChange={handleChange}
									className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
									required
								>
									<option value="">Select job type</option>
									{jobTypes.map((type) => (
										<option key={type} value={type}>
											{type}
										</option>
									))}
								</select>
							</div>

							<div className="space-y-2">
								<Label htmlFor="location">Location</Label>
								<Input
									id="location"
									name="location"
									value={currentJob?.location || ""}
									onChange={handleChange}
									placeholder="e.g. New York, NY or Remote"
								/>
							</div>
						</div>

						<div className="grid grid-cols-3 gap-4">
							<div className="space-y-2">
								<Label htmlFor="min_salary">Minimum Salary</Label>
								<div className="flex items-center">
									<select
										name="currency"
										value={currentJob?.currency || "USD"}
										onChange={handleChange}
										className="flex h-10 rounded-l-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
									>
										{currencies.map((curr) => (
											<option key={curr} value={curr}>
												{curr}
											</option>
										))}
									</select>
									<Input
										id="min_salary"
										name="min_salary"
										type="number"
										value={currentJob?.min_salary || ""}
										onChange={handleChange}
										className="rounded-l-none"
										placeholder="e.g. 50000"
									/>
								</div>
							</div>

							<div className="space-y-2">
								<Label htmlFor="max_salary">Maximum Salary</Label>
								<Input
									id="max_salary"
									name="max_salary"
									type="number"
									value={currentJob?.max_salary || ""}
									onChange={handleChange}
									placeholder="e.g. 80000"
								/>
							</div>

							<div className="space-y-2">
								<Label htmlFor="salary_time_frame">Salary Time Frame</Label>
								<select
									id="salary_time_frame"
									name="salary_time_frame"
									value={currentJob?.salary_time_frame || "year"}
									onChange={handleChange}
									className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
								>
									{salaryTimeFrames.map((frame) => (
										<option key={frame} value={frame}>
											Per {frame}
										</option>
									))}
								</select>
							</div>
						</div>

						<div className="grid grid-cols-3 gap-4">
							<div className="space-y-2">
								<Label>Subnets</Label>
								<MultiSelect
									options={subnets.map((s) => ({ value: s.netuid, label: s.name }))}
									selected={currentJob?.subnet_ids || []}
									onChange={(values) => handleMultiSelectChange("subnet_ids", values)}
									placeholder="Select subnets..."
								/>
							</div>

							<div className="space-y-2">
								<Label>Categories</Label>
								<MultiSelect
									options={categories.map((c) => ({ value: c.id, label: c.name }))}
									selected={currentJob?.category_ids || []}
									onChange={(values) => handleMultiSelectChange("category_ids", values)}
									placeholder="Select categories..."
								/>
							</div>

							<div className="space-y-2">
								<Label>Products</Label>
								<MultiSelect
									options={products.map((p) => ({ value: p.id, label: p.name }))}
									selected={currentJob?.product_ids || []}
									onChange={(values) => handleMultiSelectChange("product_ids", values)}
									placeholder="Select products..."
								/>
							</div>
						</div>

						<div className="flex items-center space-x-2">
							<input
								type="checkbox"
								id="remote"
								name="remote"
								checked={currentJob?.remote || false}
								onChange={handleChange}
								className="rounded border-gray-300"
							/>
							<Label htmlFor="remote">Remote Position</Label>
						</div>
					</div>

					{error && <div className="text-red-500 text-sm">{error}</div>}

					<div className="flex justify-end space-x-2">
						<Button variant="outline" onClick={handleCloseDialog} disabled={isLoading}>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={isLoading}>
							{isLoading ? "Saving..." : "Save Job"}
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
