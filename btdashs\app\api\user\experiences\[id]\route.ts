import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { auth0 } from "../../../../../lib/auth0";

export async function GET(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const res = await fetch(`${process.env.API_BASE_URL}/user/experiences/${id}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		return handleResponse(res);
	} catch (error) {
		console.error("Experience fetch error:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
}

export async function PUT(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const body = await req.json();

		const res = await fetch(`${process.env.API_BASE_URL}/user/experiences/${id}`, {
			method: "PUT",
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		return handleResponse(res);
	} catch (error) {
		console.error("Experience update error:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
}

export async function DELETE(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const res = await fetch(`${process.env.API_BASE_URL}/user/experiences/${id}`, {
			method: "DELETE",
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		return handleResponse(res);
	} catch (error) {
		console.error("Experience delete error:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
}

// Shared response handler
function handleResponse(response: Response) {
	const contentType = response.headers.get("content-type");

	if (contentType?.includes("application/json")) {
		return response.json().then(
			(data) =>
				new NextResponse(JSON.stringify(data), {
					status: response.status,
					headers: { "Content-Type": "application/json" },
				})
		);
	}

	return response.text().then(
		(text) =>
			new NextResponse(text, {
				status: response.status,
				headers: { "Content-Type": "text/plain" },
			})
	);
}
