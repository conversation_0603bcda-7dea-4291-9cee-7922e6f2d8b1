const Redis = require("ioredis");
const logger = require("../../../logger");

class RedisClient {
	constructor() {
		this.client = null;
		this.isConnected = false;
		this.connect();
	}

	connect() {
		try {
			// Check if Redis is disabled
			if (process.env.REDIS_ENABLED === "false") {
				logger.info("Redis is disabled via environment variable");
				return;
			}

			const redisConfig = {
				host: process.env.REDIS_HOST || "localhost",
				port: process.env.REDIS_PORT || 6379,
				password: process.env.REDIS_PASSWORD || undefined,
				db: process.env.REDIS_DB || 0,
				retryDelayOnFailover: 100,
				maxRetriesPerRequest: 1, // Reduce retries
				lazyConnect: true,
				keepAlive: 30000,
				connectTimeout: 10000,
				commandTimeout: 5000,
			};

			this.client = new Redis(redisConfig);
			this.connectionAttempts = 0;
			this.maxConnectionAttempts = 3;

			this.client.on("connect", () => {
				this.isConnected = true;
				this.connectionAttempts = 0;
				logger.info("Redis connected successfully");
			});

			this.client.on("error", (error) => {
				this.isConnected = false;
				this.connectionAttempts++;

				// Only log first few attempts to avoid spam
				if (this.connectionAttempts <= this.maxConnectionAttempts) {
					logger.error("Redis connection error", { error, attempt: this.connectionAttempts });
				}

				// Stop trying after max attempts
				if (this.connectionAttempts >= this.maxConnectionAttempts) {
					logger.warn("Redis connection failed after maximum attempts, disabling Redis");
					this.client.disconnect();
					this.client = null;
				}
			});

			this.client.on("close", () => {
				this.isConnected = false;
				if (this.connectionAttempts <= this.maxConnectionAttempts) {
					logger.warn("Redis connection closed");
				}
			});

			this.client.on("reconnecting", () => {
				if (this.connectionAttempts <= this.maxConnectionAttempts) {
					logger.info("Redis reconnecting...");
				}
			});
		} catch (error) {
			logger.error("Failed to initialize Redis client", { error });
		}
	}

	async get(key) {
		try {
			if (!this.client || !this.isConnected) {
				return null; // Return null if Redis is not available
			}
			return await this.client.get(key);
		} catch (error) {
			logger.error("Redis GET error", { error, key });
			return null;
		}
	}

	async set(key, value, ttl = null) {
		try {
			if (!this.client || !this.isConnected) {
				return null; // Return null if Redis is not available
			}

			if (ttl) {
				return await this.client.setex(key, ttl, value);
			} else {
				return await this.client.set(key, value);
			}
		} catch (error) {
			logger.error("Redis SET error", { error, key, value, ttl });
			return null;
		}
	}

	async incr(key) {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.incr(key);
		} catch (error) {
			logger.error("Redis INCR error", { error, key });
			return null;
		}
	}

	async expire(key, ttl) {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.expire(key, ttl);
		} catch (error) {
			logger.error("Redis EXPIRE error", { error, key, ttl });
			return null;
		}
	}

	async del(key) {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.del(key);
		} catch (error) {
			logger.error("Redis DEL error", { error, key });
			return null;
		}
	}

	async exists(key) {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.exists(key);
		} catch (error) {
			logger.error("Redis EXISTS error", { error, key });
			return null;
		}
	}

	async ttl(key) {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.ttl(key);
		} catch (error) {
			logger.error("Redis TTL error", { error, key });
			return null;
		}
	}

	async multi() {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return this.client.multi();
		} catch (error) {
			logger.error("Redis MULTI error", { error });
			return null;
		}
	}

	async eval(script, numKeys, ...args) {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.eval(script, numKeys, ...args);
		} catch (error) {
			logger.error("Redis EVAL error", { error, script });
			return null;
		}
	}

	async disconnect() {
		try {
			if (this.client) {
				await this.client.disconnect();
				this.isConnected = false;
				logger.info("Redis disconnected");
			}
		} catch (error) {
			logger.error("Redis disconnect error", { error });
		}
	}

	// Health check method
	async ping() {
		try {
			// Skip ping if Redis is disabled
			if (process.env.REDIS_ENABLED === "false" || !this.client) {
				return false;
			}

			if (!this.isConnected) {
				await this.client.connect();
			}
			const result = await this.client.ping();
			return result === "PONG";
		} catch (error) {
			// Only log error if Redis is supposed to be enabled
			if (process.env.REDIS_ENABLED !== "false") {
				logger.error("Redis PING error", { error });
			}
			return false;
		}
	}

	// Get Redis info
	async info() {
		try {
			if (!this.isConnected) {
				await this.client.connect();
			}
			return await this.client.info();
		} catch (error) {
			logger.error("Redis INFO error", { error });
			return null;
		}
	}
}

// Create singleton instance
const redisClient = new RedisClient();

module.exports = redisClient;
