"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Camera, Save } from "lucide-react";
import { useEffect, useState } from "react";

export default function ProfileBasicInfoClient({ initialProfile }: { initialProfile: any }) {
	const [editMode, setEditMode] = useState(false);
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const [form, setForm] = useState({
		username: "",
		first_name: "",
		last_name: "",
		headline: "",
		email: "",
		phone: "",
		location: "",
		website: "",
		bio: "",
		image_url: "",
	});

	useEffect(() => {
		if (initialProfile) {
			setForm({
				username: initialProfile.username || "",
				first_name: initialProfile.first_name || "",
				last_name: initialProfile.last_name || "",
				headline: initialProfile.headline || "",
				email: initialProfile.email || "",
				phone: initialProfile.phone || "",
				location: initialProfile.location || "",
				website: initialProfile.website || "",
				bio: initialProfile.bio || "",
				image_url: initialProfile.image_url || "",
			});
		}
	}, [initialProfile]);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = e.target;
		setForm((prev) => ({ ...prev, [name]: value }));
	};

	const handleSave = async () => {
		setSaving(true);
		setError(null);
		try {
			const res = await fetch("/api/user/me", {
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(form),
			});

			if (!res.ok) {
				const err = await res.json();
				throw new Error(err.error || "Failed to update profile");
			}

			setEditMode(false);
		} catch (err: any) {
			console.error("Save failed:", err);
			setError(err.message);
		} finally {
			setSaving(false);
		}
	};

	const displayName =
		[form.first_name, form.last_name].filter(Boolean).join(" ") || form.username || "Anonymous User";
	const fields: { id: keyof typeof form; label: string; type?: string }[] = [
		{ id: "email", label: "Email", type: "email" },
		{ id: "phone", label: "Phone", type: "tel" },
		{ id: "location", label: "Location" },
		{ id: "website", label: "Website", type: "url" },
		{ id: "headline", label: "Headline" },
	];

	return (
		<div className="space-y-6 p-4">
			<div className="flex flex-col md:flex-row gap-6 items-start">
				<div className="relative">
					<Avatar className="h-32 w-32">
						{form.image_url ? (
							<AvatarImage src={form.image_url} alt="Profile picture" />
						) : (
							<AvatarFallback className="text-2xl">
								{displayName
									.split(" ")
									.map((n) => n[0])
									.join("")
									.toUpperCase()}
							</AvatarFallback>
						)}
					</Avatar>
					{editMode && (
						<Button size="icon" variant="secondary" className="absolute bottom-0 right-0 rounded-full">
							<Camera className="h-4 w-4" />
							<span className="sr-only">Change profile picture</span>
						</Button>
					)}
				</div>

				<div className="flex-1 space-y-4">
					{editMode ? (
						<>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="first_name">
										First Name <span className="text-red-500">*</span>
									</Label>
									<Input
										id="first_name"
										name="first_name"
										value={form.first_name}
										onChange={handleChange}
										required
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="last_name">
										Last Name <span className="text-red-500">*</span>
									</Label>
									<Input
										id="last_name"
										name="last_name"
										value={form.last_name}
										onChange={handleChange}
										required
									/>
								</div>

								{fields.map(({ id, label, type }) => (
									<div key={id} className="space-y-2">
										<Label htmlFor={id}>{label}</Label>
										<Input
											id={id}
											name={id}
											type={type || "text"}
											value={form[id]}
											onChange={handleChange}
										/>
									</div>
								))}
							</div>
							<div className="space-y-2">
								<Label htmlFor="bio">Bio</Label>
								<Textarea
									id="bio"
									name="bio"
									rows={4}
									value={form.bio}
									onChange={handleChange}
									placeholder="Tell us about yourself..."
								/>
							</div>
							{error && <p className="text-red-500">{error}</p>}
						</>
					) : (
						<>
							<h2 className="text-2xl font-bold text-foreground">{displayName}</h2>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{fields.map(({ id, label }) => (
									<div key={id} className="space-y-1">
										<p className="text-sm font-medium text-muted-foreground">{label}</p>
										<p className="text-foreground">
											{form[id] ? (
												id === "website" ? (
													<a
														href={form[id]}
														target="_blank"
														rel="noopener noreferrer"
														className="text-blue-600 hover:underline"
													>
														{form[id]}
													</a>
												) : (
													form[id]
												)
											) : (
												<span className="text-gray-400 italic">Not specified</span>
											)}
										</p>
									</div>
								))}
							</div>

							<div className="space-y-1">
								<p className="text-sm font-medium text-muted-foreground">Bio</p>
								<p className="whitespace-pre-wrap text-foreground">
									{form.bio || <span className="text-gray-400 italic">No bio provided</span>}
								</p>
							</div>
						</>
					)}
				</div>
			</div>

			<div className="flex justify-end">
				{editMode ? (
					<Button onClick={handleSave} disabled={saving}>
						<Save className="mr-2 h-4 w-4" />
						{saving ? "Saving..." : "Save Changes"}
					</Button>
				) : (
					<Button onClick={() => setEditMode(true)}>Edit Profile</Button>
				)}
			</div>
		</div>
	);
}
