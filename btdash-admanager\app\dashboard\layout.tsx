// app/dashboard/layout.tsx
import CompanyCheck from "@/components/company-check";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardNav } from "@/components/dashboard-nav";
import { auth0 } from "@/lib/auth0";
import { redirect } from "next/navigation";

export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
	const session = await auth0.getSession();

	// If user is not logged in, redirect to home (login) page
	if (!session?.user) {
		redirect("/");
	}

	return (
		<CompanyCheck>
			<div className="flex min-h-screen flex-col">
				<DashboardHeader />
				<div className="flex flex-1">
					<aside className="hidden w-64 border-r bg-muted/40 md:block">
						<div className="fixed h-full w-64 overflow-y-auto py-6 pr-6 lg:py-8">
							<DashboardNav />
						</div>
					</aside>
					<main className="flex-1 overflow-auto p-6">{children}</main>
				</div>
			</div>
		</CompanyCheck>
	);
}
