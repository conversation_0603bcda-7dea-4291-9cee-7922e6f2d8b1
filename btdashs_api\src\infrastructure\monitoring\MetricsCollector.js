// src/infrastructure/monitoring/MetricsCollector.js - Application metrics collection

const os = require('os');
const process = require('process');
const logger = require('../../../logger');

/**
 * MetricsCollector - Collects and tracks application performance metrics
 */
class MetricsCollector {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        byEndpoint: new Map(),
        byStatusCode: new Map(),
        responseTimes: []
      },
      system: {
        memory: {
          used: 0,
          free: 0,
          total: 0,
          heapUsed: 0,
          heapTotal: 0
        },
        cpu: {
          usage: 0,
          loadAverage: []
        },
        uptime: 0
      },
      database: {
        connections: {
          active: 0,
          idle: 0,
          total: 0
        },
        queries: {
          total: 0,
          slow: 0,
          errors: 0,
          avgDuration: 0
        }
      },
      errors: {
        total: 0,
        byType: new Map(),
        recent: []
      }
    };

    this.startTime = Date.now();
    this.lastCollectionTime = Date.now();
    
    // Start periodic collection
    this.startPeriodicCollection();
  }

  /**
   * Record HTTP request metrics
   */
  recordRequest(req, res, responseTime) {
    const endpoint = `${req.method} ${req.route?.path || req.path}`;
    const statusCode = res.statusCode;
    const isError = statusCode >= 400;

    // Update request counters
    this.metrics.requests.total++;
    if (isError) {
      this.metrics.requests.errors++;
    } else {
      this.metrics.requests.success++;
    }

    // Track by endpoint
    const endpointStats = this.metrics.requests.byEndpoint.get(endpoint) || {
      count: 0,
      errors: 0,
      totalTime: 0,
      avgTime: 0
    };
    endpointStats.count++;
    endpointStats.totalTime += responseTime;
    endpointStats.avgTime = endpointStats.totalTime / endpointStats.count;
    if (isError) endpointStats.errors++;
    this.metrics.requests.byEndpoint.set(endpoint, endpointStats);

    // Track by status code
    const statusStats = this.metrics.requests.byStatusCode.get(statusCode) || 0;
    this.metrics.requests.byStatusCode.set(statusCode, statusStats + 1);

    // Track response times (keep last 1000)
    this.metrics.requests.responseTimes.push({
      time: Date.now(),
      duration: responseTime,
      endpoint,
      statusCode
    });
    if (this.metrics.requests.responseTimes.length > 1000) {
      this.metrics.requests.responseTimes.shift();
    }

    // Log slow requests
    if (responseTime > 1000) {
      logger.warn('Slow request detected', {
        endpoint,
        responseTime,
        statusCode,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
  }

  /**
   * Record database query metrics
   */
  recordDatabaseQuery(query, duration, error = null) {
    this.metrics.database.queries.total++;
    
    if (error) {
      this.metrics.database.queries.errors++;
      this.recordError('DatabaseError', error, { query });
    }

    // Update average duration
    const totalQueries = this.metrics.database.queries.total;
    const currentAvg = this.metrics.database.queries.avgDuration;
    this.metrics.database.queries.avgDuration = 
      ((currentAvg * (totalQueries - 1)) + duration) / totalQueries;

    // Track slow queries (>500ms)
    if (duration > 500) {
      this.metrics.database.queries.slow++;
      logger.warn('Slow database query detected', {
        query: query.substring(0, 100) + '...',
        duration,
        error: error?.message
      });
    }
  }

  /**
   * Record application errors
   */
  recordError(type, error, context = {}) {
    this.metrics.errors.total++;
    
    // Track by error type
    const typeCount = this.metrics.errors.byType.get(type) || 0;
    this.metrics.errors.byType.set(type, typeCount + 1);

    // Keep recent errors (last 100)
    this.metrics.errors.recent.push({
      time: Date.now(),
      type,
      message: error.message || error,
      stack: error.stack,
      context
    });
    if (this.metrics.errors.recent.length > 100) {
      this.metrics.errors.recent.shift();
    }

    logger.error('Application error recorded', {
      type,
      message: error.message || error,
      context
    });
  }

  /**
   * Update system metrics
   */
  updateSystemMetrics() {
    const memUsage = process.memoryUsage();
    const systemMem = {
      total: os.totalmem(),
      free: os.freemem()
    };

    this.metrics.system.memory = {
      used: systemMem.total - systemMem.free,
      free: systemMem.free,
      total: systemMem.total,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal
    };

    this.metrics.system.cpu.loadAverage = os.loadavg();
    this.metrics.system.uptime = process.uptime();
  }

  /**
   * Get current metrics snapshot
   */
  getMetrics() {
    this.updateSystemMetrics();
    
    return {
      ...this.metrics,
      timestamp: Date.now(),
      uptime: Date.now() - this.startTime,
      requestRate: this.calculateRequestRate(),
      errorRate: this.calculateErrorRate(),
      avgResponseTime: this.calculateAvgResponseTime()
    };
  }

  /**
   * Get metrics summary for health checks
   */
  getHealthMetrics() {
    const now = Date.now();
    const recentRequests = this.metrics.requests.responseTimes.filter(
      r => now - r.time < 60000 // Last minute
    );

    return {
      status: this.getHealthStatus(),
      uptime: process.uptime(),
      memory: {
        usage: (this.metrics.system.memory.used / this.metrics.system.memory.total * 100).toFixed(2) + '%',
        heap: (this.metrics.system.memory.heapUsed / this.metrics.system.memory.heapTotal * 100).toFixed(2) + '%'
      },
      requests: {
        total: this.metrics.requests.total,
        lastMinute: recentRequests.length,
        errorRate: this.calculateErrorRate() + '%'
      },
      database: {
        queries: this.metrics.database.queries.total,
        avgDuration: this.metrics.database.queries.avgDuration.toFixed(2) + 'ms',
        errors: this.metrics.database.queries.errors
      }
    };
  }

  /**
   * Calculate overall health status
   */
  getHealthStatus() {
    const errorRate = this.calculateErrorRate();
    const memoryUsage = this.metrics.system.memory.used / this.metrics.system.memory.total * 100;
    const avgResponseTime = this.calculateAvgResponseTime();

    if (errorRate > 10 || memoryUsage > 90 || avgResponseTime > 2000) {
      return 'unhealthy';
    } else if (errorRate > 5 || memoryUsage > 80 || avgResponseTime > 1000) {
      return 'degraded';
    } else {
      return 'healthy';
    }
  }

  /**
   * Calculate request rate (requests per minute)
   */
  calculateRequestRate() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const recentRequests = this.metrics.requests.responseTimes.filter(
      r => r.time > oneMinuteAgo
    );
    return recentRequests.length;
  }

  /**
   * Calculate error rate percentage
   */
  calculateErrorRate() {
    if (this.metrics.requests.total === 0) return 0;
    return ((this.metrics.requests.errors / this.metrics.requests.total) * 100).toFixed(2);
  }

  /**
   * Calculate average response time
   */
  calculateAvgResponseTime() {
    const responseTimes = this.metrics.requests.responseTimes;
    if (responseTimes.length === 0) return 0;
    
    const total = responseTimes.reduce((sum, r) => sum + r.duration, 0);
    return (total / responseTimes.length).toFixed(2);
  }

  /**
   * Start periodic metrics collection
   */
  startPeriodicCollection() {
    // Update system metrics every 30 seconds
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000);

    // Log metrics summary every 5 minutes
    setInterval(() => {
      const summary = this.getHealthMetrics();
      logger.info('Metrics summary', summary);
    }, 300000);

    // Clean old data every hour
    setInterval(() => {
      this.cleanOldData();
    }, 3600000);
  }

  /**
   * Clean old metrics data to prevent memory leaks
   */
  cleanOldData() {
    const oneHourAgo = Date.now() - 3600000;
    
    // Clean old response times
    this.metrics.requests.responseTimes = this.metrics.requests.responseTimes.filter(
      r => r.time > oneHourAgo
    );

    // Clean old errors
    this.metrics.errors.recent = this.metrics.errors.recent.filter(
      e => e.time > oneHourAgo
    );

    logger.debug('Cleaned old metrics data');
  }

  /**
   * Reset all metrics (useful for testing)
   */
  reset() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        errors: 0,
        byEndpoint: new Map(),
        byStatusCode: new Map(),
        responseTimes: []
      },
      system: {
        memory: { used: 0, free: 0, total: 0, heapUsed: 0, heapTotal: 0 },
        cpu: { usage: 0, loadAverage: [] },
        uptime: 0
      },
      database: {
        connections: { active: 0, idle: 0, total: 0 },
        queries: { total: 0, slow: 0, errors: 0, avgDuration: 0 }
      },
      errors: {
        total: 0,
        byType: new Map(),
        recent: []
      }
    };
    this.startTime = Date.now();
  }
}

// Export singleton instance
module.exports = new MetricsCollector();
