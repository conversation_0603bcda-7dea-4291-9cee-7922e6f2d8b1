"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Category } from "@/lib/db/models";
import { formatNumber } from "@/utils/formatNumber";
import * as LucideIcons from "lucide-react";
import { ChevronDown, ChevronUp, Globe } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const ROW_HEIGHT = "h-10";
const CELL_PADDING = "px-4";
const IMAGE_SIZE = "h-5 w-5";
const ICON_SIZE = "h-4 w-4";

type SortKey =
  | "name"
  | "category"
  | "validators"
  | "emission"
  | "lockup"
  | "recycled"
  | "status";

const CATEGORY_COLORS: Record<string, string> = {
  Text: "text-blue-400 drop-shadow-[0_0_8px_rgba(96,165,250,0.7)]",
  Image: "text-orange-400 drop-shadow-[0_0_8px_rgba(251,146,60,0.7)]",
  "Text Processing and NLP":
    "text-green-400 drop-shadow-[0_0_8px_rgba(74,222,128,0.7)]",
  "Image Generation":
    "text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.7)]",
  "General AI and Multimodal":
    "text-purple-400 drop-shadow-[0_0_8px_rgba(192,132,252,0.7)]",
  "Decentralized Compute":
    "text-teal-400 drop-shadow-[0_0_8px_rgba(45,212,191,0.7)]",
  "Data Collection":
    "text-rose-400 drop-shadow-[0_0_8px_rgba(251,113,133,0.7)]",
  Pretraining: "text-indigo-400 drop-shadow-[0_0_8px_rgba(129,140,248,0.7)]",
  "Text to Speech (TTS)":
    "text-cyan-400 drop-shadow-[0_0_8px_rgba(34,211,238,0.7)]",
  "Blockchain Analytics":
    "text-blue-500 drop-shadow-[0_0_8px_rgba(59,130,246,0.7)]",
  "Decentralized Marketing":
    "text-teal-500 drop-shadow-[0_0_8px_rgba(20,184,166,0.7)]",
  "Financial Prediction":
    "text-green-500 drop-shadow-[0_0_8px_rgba(34,197,94,0.7)]",
  "3D Asset Creation":
    "text-emerald-400 drop-shadow-[0_0_8px_rgba(16,185,129,0.7)]",
  "Security and Verification":
    "text-red-400 drop-shadow-[0_0_8px_rgba(248,113,113,0.7)]",
  "Machine Translation":
    "text-cyan-500 drop-shadow-[0_0_8px_rgba(6,182,212,0.7)]",
  Governance: "text-blue-500 drop-shadow-[0_0_8px_rgba(59,130,246,0.7)]",
  "AI Data Networks":
    "text-fuchsia-400 drop-shadow-[0_0_8px_rgba(240,171,252,0.7)]",
  "AI-Detection": "text-red-500 drop-shadow-[0_0_8px_rgba(239,68,68,0.7)]",
  "Defi-Lending": "text-yellow-500 drop-shadow-[0_0_8px_rgba(234,179,8,0.7)]",
  "Decentralized Training":
    "text-violet-500 drop-shadow-[0_0_8px_rgba(139,92,246,0.7)]",
  "AI Drug Discovery":
    "text-emerald-500 drop-shadow-[0_0_8px_rgba(5,150,105,0.7)]",
  Agent: "text-purple-500 drop-shadow-[0_0_8px_rgba(168,85,247,0.7)]",
  "Time-Series Prediction":
    "text-teal-500 drop-shadow-[0_0_8px_rgba(20,184,166,0.7)]",
  Inference: "text-indigo-500 drop-shadow-[0_0_8px_rgba(99,102,241,0.7)]",
  "AI Model Development":
    "text-cyan-500 drop-shadow-[0_0_8px_rgba(6,182,212,0.7)]",
  "AI Data Pipeline": "text-rose-500 drop-shadow-[0_0_8px_rgba(244,63,94,0.7)]",
  "Distributed Training":
    "text-teal-500 drop-shadow-[0_0_8px_rgba(20,184,166,0.7)]",
  "Generative AI": "text-pink-500 drop-shadow-[0_0_8px_rgba(236,72,153,0.7)]",
  "Components & Tooling":
    "text-gray-400 drop-shadow-[0_0_8px_rgba(156,163,175,0.7)]",
  "Scientific Research":
    "text-violet-500 drop-shadow-[0_0_8px_rgba(139,92,246,0.7)]",
  "General Infrastructure":
    "text-slate-500 drop-shadow-[0_0_8px_rgba(100,116,139,0.7)]",
  "AI Powered Tools":
    "text-orange-500 drop-shadow-[0_0_8px_rgba(249,115,22,0.7)]",
  "Model Hosting": "text-blue-400 drop-shadow-[0_0_8px_rgba(96,165,250,0.7)]",
  "Predictive System":
    "text-green-500 drop-shadow-[0_0_8px_rgba(34,197,94,0.7)]",
  "Agentic AI": "text-fuchsia-500 drop-shadow-[0_0_8px_rgba(217,70,239,0.7)]",
};

export function SubnetsTable({
  subnets,
  categories,
  metrics,
  category,
  status,
  searchQuery,
}: {
  subnets: Array<{
    netuid: number;
    name: string;
    subnet_symbol?: string;
    image_url?: string;
    category_ids?: number[];
    is_active: boolean;
  }>;
  categories: Category[];
  metrics: Array<{
    netuid: number;
    validators_count: number;
    emission: number;
    staked_tao: number;
    recycled_tao: number;
  }>;
  category: string;
  status: string;
  searchQuery: string;
}) {
  const [sortKey, setSortKey] = useState<SortKey>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  const toggleSort = (key: SortKey) => {
    if (key === sortKey) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortKey(key);
      setSortOrder("asc");
    }
  };

  // Enrich each subnet with its metrics and categories
  const enriched = subnets.map((s) => {
    const m = metrics.find((m) => m.netuid === s.netuid);
    const cats =
      s.category_ids
        ?.map((id) => categories.find((c) => c.id === id))
        ?.filter(Boolean) ?? [];

    // Find the selected category if filtering by a specific category
    const selectedCategory =
      category !== "all"
        ? cats.find((c) => c?.id.toString() === category)
        : null;

    // Use selected category if filtering, otherwise use primary category
    const displayCategory = selectedCategory ||
      cats[0] || { name: "Uncategorized", icon: "Globe" };

    return {
      ...s,
      metrics: m,
      categories: cats,
      displayCategory,
    };
  });

  // Sort
  const sorted = [...enriched].sort((a, b) => {
    let aVal: any, bVal: any;
    switch (sortKey) {
      case "name":
        aVal = a.name;
        bVal = b.name;
        break;
      case "category":
        aVal = a.displayCategory.name;
        bVal = b.displayCategory.name;
        break;
      case "validators":
        aVal = a.metrics?.validators_count ?? 0;
        bVal = b.metrics?.validators_count ?? 0;
        break;
      case "emission":
        aVal = a.metrics?.emission ?? 0;
        bVal = b.metrics?.emission ?? 0;
        break;
      case "lockup":
        aVal = a.metrics?.staked_tao ?? 0;
        bVal = b.metrics?.staked_tao ?? 0;
        break;
      case "recycled":
        aVal = a.metrics?.recycled_tao ?? 0;
        bVal = b.metrics?.recycled_tao ?? 0;
        break;
      case "status":
        aVal = a.is_active ? "active" : "inactive";
        bVal = b.is_active ? "active" : "inactive";
        break;
      default:
        return 0;
    }
    if (aVal < bVal) return sortOrder === "asc" ? -1 : 1;
    if (aVal > bVal) return sortOrder === "asc" ? 1 : -1;
    return 0;
  });

  // Filter
  const filtered = sorted.filter((s) => {
    const matchCat =
      category === "all" ||
      s.categories.some((c) => (c?.id ?? "").toString() === category);
    const matchStatus =
      status === "all" || (s.is_active ? "active" : "inactive") === status;
    const matchSearch =
      !searchQuery || s.name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchCat && matchStatus && matchSearch;
  });

  return (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow className={ROW_HEIGHT}>
            {(
              [
                { key: "name", label: "Name" },
                { key: "category", label: "Category" },
                { key: "validators", label: "Validators" },
                { key: "emission", label: "Emission" },
                { key: "lockup", label: "Total Lockup" },
                { key: "recycled", label: "Recycled" },
                { key: "status", label: "Status" },
              ] as const
            ).map(({ key, label }) => (
              <TableHead
                key={key}
                className={key === "name" ? "" : "text-center"}
              >
                <Button
                  variant="ghost"
                  onClick={() => toggleSort(key)}
                  className="font-bold"
                >
                  {label}
                  {sortKey === key &&
                    (sortOrder === "asc" ? (
                      <ChevronUp className="ml-2 h-4 w-4" />
                    ) : (
                      <ChevronDown className="ml-2 h-4 w-4" />
                    ))}
                </Button>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {filtered.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={7}
                className={`text-center py-2 ${ROW_HEIGHT}`}
              >
                No subnets match your filters
              </TableCell>
            </TableRow>
          ) : (
            filtered.map((s) => {
              const { displayCategory } = s;
              const Icon = (LucideIcons[
                displayCategory.icon as keyof typeof LucideIcons
              ] ?? Globe) as React.ComponentType<{ className?: string }>;
              const colorClass =
                CATEGORY_COLORS[displayCategory.name] ??
                CATEGORY_COLORS["Uncategorized"];

              return (
                <TableRow
                  key={s.netuid}
                  className={`group hover:bg-accent cursor-pointer ${ROW_HEIGHT}`}
                >
                  {/* Name */}
                  <TableCell className="p-0">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`flex items-center gap-2 ${CELL_PADDING} ${ROW_HEIGHT}`}
                    >
                      {s.image_url ? (
                        <div className={`relative ${IMAGE_SIZE}`}>
                          <Image
                            src={s.image_url}
                            alt={s.name}
                            fill
                            className="object-cover rounded-full"
                            unoptimized
                          />
                        </div>
                      ) : (
                        <div
                          className={`${IMAGE_SIZE} bg-muted rounded-full flex items-center justify-center text-xs`}
                        >
                          {s.subnet_symbol ?? s.name.charAt(0)}
                        </div>
                      )}
                      <span className="font-medium">{s.name}</span>
                    </Link>
                  </TableCell>

                  {/* Category */}
                  <TableCell className="p-0 text-center">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`${CELL_PADDING} ${ROW_HEIGHT} flex items-center justify-center`}
                    >
                      <Badge variant="secondary" className="gap-2">
                        <Icon className={`${ICON_SIZE} ${colorClass}`} />
                        <span>{displayCategory.name}</span>
                      </Badge>
                    </Link>
                  </TableCell>

                  {/* Validators */}
                  <TableCell className="p-0 text-center">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`${CELL_PADDING} ${ROW_HEIGHT} flex items-center justify-center`}
                    >
                      {s.metrics?.validators_count ?? 0}
                    </Link>
                  </TableCell>

                  {/* Emission */}
                  <TableCell className="p-0 text-center">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`${CELL_PADDING} ${ROW_HEIGHT} flex items-center justify-center`}
                    >
                      {s.metrics?.emission != null
                        ? `${formatNumber(s.metrics.emission)} τ`
                        : "-"}
                    </Link>
                  </TableCell>

                  {/* Total Lockup */}
                  <TableCell className="p-0 text-center">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`${CELL_PADDING} ${ROW_HEIGHT} flex items-center justify-center`}
                    >
                      {s.metrics?.staked_tao != null
                        ? `${formatNumber(
                            s.metrics.staked_tao / 1_000_000,
                            1
                          )} τ`
                        : "-"}
                    </Link>
                  </TableCell>

                  {/* Recycled */}
                  <TableCell className="p-0 text-center">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`${CELL_PADDING} ${ROW_HEIGHT} flex items-center justify-center`}
                    >
                      {s.metrics?.recycled_tao != null
                        ? `${formatNumber(
                            s.metrics.recycled_tao / 1_000_000,
                            1
                          )} τ`
                        : "-"}
                    </Link>
                  </TableCell>

                  {/* Status */}
                  <TableCell className="p-0 text-center">
                    <Link
                      href={`/subnets/${s.netuid}`}
                      className={`${CELL_PADDING} ${ROW_HEIGHT} flex items-center justify-center`}
                    >
                      <Badge
                        variant={s.is_active ? "default" : "outline"}
                        className="capitalize"
                      >
                        {s.is_active ? "active" : "inactive"}
                      </Badge>
                    </Link>
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>
    </div>
  );
}
