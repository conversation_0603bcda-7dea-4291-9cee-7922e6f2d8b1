const NewsService = require("../../application/services/NewsService");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllNews = asyncHandler(async (req, res) => {
	const news = await NewsService.getAllNews();
	return sendSuccess(res, news, "News retrieved successfully");
});

const getNewsById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const article = await NewsService.getNewsById(id);
	if (!article) {
		return sendNotFound(res, "News article not found");
	}
	return sendSuccess(res, article, "News article retrieved successfully");
});

const createNews = asyncHandler(async (req, res) => {
	const { title, content, publication_date, source, article_link, subnet_netuid } = req.body;
	const newArticle = await NewsService.createNews({
		title,
		content,
		publication_date,
		source,
		article_link,
		subnet_netuid,
	});
	return sendSuccess(res, newArticle, "News article created successfully", 201);
});

const updateNews = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const { title, content, publication_date, source, article_link, subnet_netuid } = req.body;
	const updatedArticle = await NewsService.updateNews(id, {
		title,
		content,
		publication_date,
		source,
		article_link,
		subnet_netuid,
	});
	if (!updatedArticle) {
		return sendNotFound(res, "News article not found");
	}
	return sendSuccess(res, updatedArticle, "News article updated successfully");
});

const deleteNews = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const deleted = await NewsService.deleteNews(id);
	if (!deleted) {
		return sendNotFound(res, "News article not found");
	}
	return sendSuccess(res, null, "News article deleted successfully");
});

module.exports = {
	getAllNews,
	getNewsById,
	createNews,
	updateNews,
	deleteNews,
};
