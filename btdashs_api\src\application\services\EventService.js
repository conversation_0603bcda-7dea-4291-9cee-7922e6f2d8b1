// src/application/services/EventService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * Event Service - Handles user event creation and management business logic
 */
class EventService extends BaseService {
	constructor() {
		super("dtm_base.events", "Event");
	}

	/**
	 * Create an event for a user
	 * @param {number} userId - User ID (creator)
	 * @param {Object} eventData - Event data
	 * @returns {Promise<Object>} Created event object
	 */
	async createUserEvent(userId, eventData) {
		const {
			name,
			description,
			start_date,
			end_date,
			location,
			is_virtual,
			event_type,
			website_url,
			registration_url,
			image_url,
			is_published,
			published_at,
			is_featured,
			subnet_ids,
			product_ids,
			company_ids,
			category_ids,
			event_ids,
			organizer_ids,
			desc_about_this_event,
			desc_what_u_will_learn,
			desc_who_should_attend,
			image_url_banner,
			speakers,
		} = eventData;

		try {
			return await this.create({
				name,
				description,
				start_date,
				end_date,
				location,
				is_virtual,
				event_type,
				website_url,
				registration_url,
				image_url,
				is_published,
				published_at,
				is_featured,
				created_by_id: userId,
				subnet_ids,
				product_ids,
				company_ids,
				category_ids,
				event_ids,
				organizer_ids,
				desc_about_this_event,
				desc_what_u_will_learn,
				desc_who_should_attend,
				image_url_banner,
				speakers,
			});
		} catch (error) {
			logger.error("Error creating user event", { error, userId });
			throw error;
		}
	}

	/**
	 * Get all events for a user
	 * @param {number} userId - User ID (creator)
	 * @returns {Promise<Array>} Array of event records
	 */
	async getUserEvents(userId) {
		try {
			const events = await this.getAll({ created_by_id: userId });
			return events;
		} catch (error) {
			logger.error("Error getting user events", { error, userId });
			throw error;
		}
	}

	/**
	 * Update an event
	 * @param {number} eventId - Event ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated event object
	 */
	async updateUserEvent(eventId, updateData) {
		const {
			name,
			description,
			start_date,
			end_date,
			location,
			is_virtual,
			event_type,
			website_url,
			registration_url,
			image_url,
			is_published,
			published_at,
			is_featured,
			subnet_ids,
			product_ids,
			company_ids,
			category_ids,
			event_ids,
			organizer_ids,
			desc_about_this_event,
			desc_what_u_will_learn,
			desc_who_should_attend,
			image_url_banner,
			speakers,
		} = updateData;

		try {
			return await this.updateById(eventId, {
				name,
				description,
				start_date,
				end_date,
				location,
				is_virtual,
				event_type,
				website_url,
				registration_url,
				image_url,
				is_published,
				published_at,
				is_featured,
				subnet_ids,
				product_ids,
				company_ids,
				category_ids,
				event_ids,
				organizer_ids,
				desc_about_this_event,
				desc_what_u_will_learn,
				desc_who_should_attend,
				image_url_banner,
				speakers,
			});
		} catch (error) {
			logger.error("Error updating user event", { error, eventId });
			throw error;
		}
	}

	/**
	 * Delete an event
	 * @param {number} eventId - Event ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteUserEvent(eventId) {
		try {
			return await this.deleteById(eventId);
		} catch (error) {
			logger.error("Error deleting user event", { error, eventId });
			throw error;
		}
	}

	/**
	 * Get all public events with filtering and pagination
	 * @param {Object} filters - Filter options
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of event records
	 */
	async getPublicEvents(filters = {}, options = {}) {
		try {
			// Default ordering by start_date desc
			const queryOptions = {
				orderBy: { column: "start_date", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting public events", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get all events (public events board)
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of events
	 */
	async getAllEvents(filters = {}, options = {}) {
		return await this.getPublicEvents(filters, options);
	}

	/**
	 * Get event by ID (public access)
	 * @param {number} id - Event ID
	 * @returns {Promise<Object|null>} Event object or null if not found
	 */
	async getEventById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting event by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new event (public events board)
	 * @param {Object} eventData - Event data
	 * @returns {Promise<Object>} Created event object
	 */
	async createEvent(eventData) {
		try {
			const newEvent = await this.create(eventData);
			logger.info("Event created", { event_id: newEvent.id });
			return newEvent;
		} catch (error) {
			logger.error("Error creating event", { error, eventData });
			throw error;
		}
	}

	/**
	 * Update an event (public events board)
	 * @param {number} id - Event ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated event object
	 */
	async updateEvent(id, updateData) {
		try {
			const updatedEvent = await this.updateById(id, updateData);
			logger.info("Event updated", { event_id: id });
			return updatedEvent;
		} catch (error) {
			logger.error("Error updating event", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete an event (public events board)
	 * @param {number} id - Event ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteEvent(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Event deleted", { event_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting event", { error, id });
			throw error;
		}
	}
}

module.exports = new EventService();
