import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { NetworkStats, Subnet, SubnetMetric } from "@/lib/db/models";
import { ArrowDown, ArrowUp, TrendingUp } from "lucide-react";
import { useMemo } from "react";

interface TrendingWidgetsProps {
	subnets: Subnet[];
	subnetMetrics: SubnetMetric[];
	networkStats: NetworkStats[] | null;
}

export function TrendingWidgets({ subnets, subnetMetrics, networkStats }: TrendingWidgetsProps) {
	// Calculate trending subnets
	const trendingSubnets = useMemo(() => {
		const metricsMap = new Map<number, SubnetMetric>(subnetMetrics.map((metric) => [metric.netuid, metric]));

		return subnets
			.map((subnet) => ({
				...subnet,
				metrics: metricsMap.get(subnet.netuid),
				growthPercentage: calculateGrowthPercentage(metricsMap.get(subnet.netuid)),
			}))
			.sort((a, b) => b.growthPercentage - a.growthPercentage)
			.slice(0, 4);
	}, [subnets, subnetMetrics]);

	// Find most recent network stats
	const mostRecentStats = useMemo(() => {
		if (!networkStats || networkStats.length === 0) return null;

		return networkStats.reduce((latest: NetworkStats | null, current: NetworkStats) => {
			if (!latest) return current;
			return new Date(current.recorded_at) > new Date(latest.recorded_at) ? current : latest;
		}, null);
	}, [networkStats]);

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Trending Subnets</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{trendingSubnets.map((subnet) => (
							<div key={subnet.netuid} className="flex justify-between items-center">
								<div className="flex items-center space-x-2">
									{subnet.image_url ? (
										<img
											src={subnet.image_url}
											alt={subnet.name}
											className="w-6 h-6 rounded-full object-cover"
										/>
									) : (
										<div className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold bg-gray-500">
											{subnet.subnet_symbol || subnet.name.charAt(0)}
										</div>
									)}
									<span className="font-medium">{subnet.name}</span>
								</div>
								<div
									className={`flex items-center ${
										subnet.growthPercentage >= 0 ? "text-green-600" : "text-red-600"
									}`}
								>
									{subnet.growthPercentage >= 0 ? (
										<ArrowUp className="h-4 w-4 mr-1" />
									) : (
										<ArrowDown className="h-4 w-4 mr-1" />
									)}
									{Math.abs(subnet.growthPercentage).toFixed(1)}%
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Network Stats</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-2">
						<div className="flex justify-between">
							<span className="text-sm text-muted-foreground">Total Validators</span>
							<span className="font-medium">
								{mostRecentStats?.total_validators?.toLocaleString() ?? "N/A"}
							</span>
						</div>
						<div className="flex justify-between">
							<span className="text-sm text-muted-foreground">Active Subnets</span>
							<span className="font-medium">{mostRecentStats?.active_subnets ?? "N/A"}</span>
						</div>
						<div className="flex justify-between">
							<span className="text-sm text-muted-foreground">24h Transactions</span>
							<div className="flex items-center text-green-600">
								<TrendingUp className="h-4 w-4 mr-1" />
								<span className="font-medium">
									{mostRecentStats?.transactions_24h?.toLocaleString() ?? "N/A"}
								</span>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* <Card>
				<CardHeader>
					<CardTitle>Hot Topics</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex flex-wrap gap-2">
						{[
							"AI Integration",
							"Validator Rewards",
							"Protocol Upgrade",
							"New Partnerships",
							"Subnet Scalability",
							"Community Governance",
						].map((topic, index) => (
							<Badge key={index} variant="secondary">
								{topic}
							</Badge>
						))}
					</div>
				</CardContent>
			</Card> */}
		</div>
	);
}

function calculateGrowthPercentage(metrics?: SubnetMetric): number {
	return metrics?.price_change_1_week || 0;
}
