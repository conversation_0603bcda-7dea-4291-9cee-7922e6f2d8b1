"use client";

import { CategoryTag } from "@/components/category-tag";
import { Card, CardContent } from "@/components/ui/card";
import { Category, Company, News, Subnet } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";

interface NewsArticleRowProps {
  news: News[];
  subnets: Subnet[];
  categories: Category[];
  companies: Company[];
  isLoading?: boolean;
  error?: string | null;
}

export function NewsArticleRow({
  news,
  subnets,
  categories,
  companies,
  isLoading = false,
  error = null,
}: NewsArticleRowProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Assemble extended news data (same logic as original hook)
  const extendedNews = useMemo(() => {
    return news.map((article) => {
      // Get related subnets
      const relatedSubnets =
        (article.netuids
          ?.map((netuid) => subnets.find((subnet) => subnet.netuid === netuid))
          .filter(Boolean) as Subnet[]) || [];

      // Get related categories
      const relatedCategories =
        (article.category_ids
          ?.map((catId) => categories.find((category) => category.id === catId))
          .filter(Boolean) as Category[]) || [];

      // Get related companies
      const relatedCompanies =
        (article.company_ids
          ?.map((companyId) =>
            companies.find((company) => company.id === companyId)
          )
          .filter(Boolean) as Company[]) || [];

      return {
        ...article,
        subnets: relatedSubnets,
        categories: relatedCategories,
        companies: relatedCompanies,
      };
    });
  }, [news, subnets, categories, companies]);

  useEffect(() => {
    if (extendedNews.length === 0) return;

    const timer = setInterval(() => {
      setCurrentIndex(
        (prevIndex) => (prevIndex + 1) % (extendedNews.length - 2)
      );
    }, 5000);

    return () => clearInterval(timer);
  }, [extendedNews.length]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </div>
    );
  }

  if (error || extendedNews.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-10">
        {error || "No news available."}
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden py-4">
      <div
        className="flex transition-transform duration-500 ease-in-out space-x-4"
        style={{ transform: `translateX(-${currentIndex * 308}px)` }}
      >
        {extendedNews.map((article) => (
          <Link href={`/news/${article.id}`} key={article.id}>
            <Card className="flex-shrink-0 w-[300px] mx-2 hover:shadow-lg hover:bg-primary/10 transition-all">
              <CardContent className="p-4">
                {/* Show subnet names and fallback to symbol */}
                <div className="flex items-center mb-2">
                  {article.subnets.slice(0, 2).map((subnet) => (
                    <div key={subnet.id} className="flex items-center mr-2">
                      {subnet.image_url ? (
                        <img
                          src={subnet.image_url}
                          alt={subnet.name}
                          className="h-6 w-6 mr-2 rounded-full"
                        />
                      ) : (
                        subnet.subnet_symbol && (
                          <div className="h-6 w-6 bg-black text-white flex justify-center items-center rounded-full mr-2">
                            <span className="text-sm">
                              {subnet.subnet_symbol}
                            </span>
                          </div>
                        )
                      )}
                      <span className="font-semibold text-sm">
                        {subnet.name}
                      </span>
                    </div>
                  ))}
                  {article.subnets.length > 1 && (
                    <span className="text-sm">...</span>
                  )}
                </div>

                <h3 className="font-semibold text-lg mb-2 line-clamp-2">
                  {article.title}
                </h3>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {article.content || "No content available."}
                </p>
              </CardContent>

              <div className="p-4 mt-auto text-sm text-muted-foreground">
                <div className="mb-2">
                  {article.categories.slice(0, 2).map((category) => (
                    <span key={category.id} className="m-1 p-2 mt-2">
                      <CategoryTag category={category.name} />
                    </span>
                  ))}
                  {article.categories.length > 2 && <span>...</span>}
                </div>

                <div>
                  {article.companies.slice(0, 2).map((company) => (
                    <span key={company.id} className="m-1 p-1">
                      <CategoryTag category={company.name} />
                    </span>
                  ))}
                  {article.companies.length > 1 && <span>...</span>}
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </div>

      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {extendedNews.map((_, index) => (
          <button
            key={index}
            className={cn(
              "w-2 h-2 rounded-full transition-colors duration-300",
              index === currentIndex ? "bg-primary" : "bg-gray-300"
            )}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>
    </div>
  );
}
