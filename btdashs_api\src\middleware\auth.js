// src/middleware/auth.js
const { expressjwt: jwt } = require("express-jwt");
const jwksRsa = require("jwks-rsa");

module.exports = jwt({
	secret: jwksRsa.expressJwtSecret({
		cache: true,
		rateLimit: true,
		jwksRequestsPerMinute: 5,
		jwksUri: "https://dev-ji1jed8mnt28gmsw.us.auth0.com/.well-known/jwks.json",
		timeout: 30000,
		strictSsl: true,
		handleSigningKeyError: (err, cb) => {
			console.error("JWKS Error:", err);
			cb(err);
		},
	}),
	issuer: "https://dev-ji1jed8mnt28gmsw.us.auth0.com/",
	algorithms: ["RS256"],
	credentialsRequired: false,
});
