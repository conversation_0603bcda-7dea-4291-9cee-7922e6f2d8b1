// app/dashboard/page.tsx
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { auth0 } from "@/lib/auth0";
import { fetchWithFallback } from "@/lib/utils";
import { ArrowRight, BarChart3, Clock, ImageIcon, Plus } from "lucide-react";
import { cookies } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function DashboardPage() {
	const session = await auth0.getSession();

	if (!session?.user) {
		redirect("/");
	}

	const cookieHeader = (await cookies()).toString();

	// Fetch campaigns data
	const campaignsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/campaigns`, {
		headers: { Cookie: cookieHeader },
		next: { tags: ["campaigns"] },
	});

	// Fetch user analytics
	const analyticsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/analytics`, {
		headers: { Cookie: cookieHeader },
		next: { tags: ["analytics"] },
	});

	const campaigns = campaignsRes.success ? campaignsRes.data || [] : [];
	const analytics = analyticsRes.success ? analyticsRes.data : null;

	// Calculate stats
	const activeCampaigns = campaigns.filter((c: any) => c.status === "active");
	const pendingCampaigns = campaigns.filter((c: any) => c.status === "pending");
	const recentCampaigns = campaigns.slice(0, 3); // Show 3 most recent

	return (
		<div className="space-y-6">
			{/* Welcome Header */}
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold tracking-tight">Welcome back, {session.user.name}</h1>
				<Link href="/dashboard/campaigns/create">
					<Button>
						<Plus className="mr-2 h-4 w-4" />
						Create Campaign
					</Button>
				</Link>
			</div>

			{/* Main Content */}
			<Tabs defaultValue="overview" className="space-y-4">
				<TabsList>
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="campaigns">Campaigns</TabsTrigger>
					<TabsTrigger value="analytics">Analytics</TabsTrigger>
				</TabsList>

				{/* Overview Tab */}
				<TabsContent value="overview" className="space-y-4">
					{/* Stats Cards */}
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
								<ImageIcon className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">{activeCampaigns.length}</div>
								<p className="text-xs text-muted-foreground">Currently running</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
								<Clock className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">{pendingCampaigns.length}</div>
								<p className="text-xs text-muted-foreground">Awaiting review</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
								<BarChart3 className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{analytics?.total_impressions?.toLocaleString() || "0"}
								</div>
								<p className="text-xs text-muted-foreground">All time</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Click Rate</CardTitle>
								<ArrowRight className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{analytics?.ctr ? (analytics.ctr * 100).toFixed(2) + "%" : "0%"}
								</div>
								<p className="text-xs text-muted-foreground">Average CTR</p>
							</CardContent>
						</Card>
						{/* Add more stat cards as needed */}
					</div>

					{/* Recent Campaigns and Performance */}
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
						{/* Recent Campaigns */}
						<Card className="col-span-4">
							<CardHeader>
								<CardTitle>Recent Campaigns</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								{recentCampaigns.length > 0 ? (
									recentCampaigns.map((campaign: any) => (
										<div key={campaign.id} className="flex items-center gap-4">
											<div className="w-16 h-16 rounded-md bg-muted flex items-center justify-center">
												{campaign.ad?.image_url ? (
													<img
														src={campaign.ad.image_url}
														alt={campaign.name}
														className="w-full h-full object-cover rounded-md"
													/>
												) : (
													<ImageIcon className="h-8 w-8 text-foreground" />
												)}
											</div>
											<div className="flex-1 space-y-1">
												<p className="text-sm font-medium leading-none">{campaign.name}</p>
												<p className="text-sm text-muted-foreground">
													{new Date(campaign.start_date).toLocaleDateString()} -{" "}
													{new Date(campaign.end_date).toLocaleDateString()}
												</p>
												<div className="flex items-center pt-2">
													<span
														className={`text-xs px-2 py-0.5 rounded ${
															campaign.status === "active"
																? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
																: campaign.status === "pending"
																? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
																: "bg-muted text-muted-foreground"
														}`}
													>
														{campaign.status}
													</span>
													{campaign.impressions && (
														<span className="text-xs text-muted-foreground ml-2">
															{campaign.impressions} impressions
														</span>
													)}
												</div>
											</div>
											<Link href={`/dashboard/campaigns/${campaign.id}`}>
												<Button variant="ghost" size="icon">
													<ArrowRight className="h-4 w-4" />
												</Button>
											</Link>
										</div>
									))
								) : (
									<div className="text-center py-8">
										<p className="text-muted-foreground">No campaigns yet</p>
										<Link href="/dashboard/campaigns/create">
											<Button className="mt-2">
												<Plus className="mr-2 h-4 w-4" />
												Create Campaign
											</Button>
										</Link>
									</div>
								)}
							</CardContent>
						</Card>

						{/* Performance Overview */}
						<Card className="col-span-3">
							<CardHeader>
								<CardTitle>Performance Overview</CardTitle>
								<CardDescription>Click-through rate by campaign</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="h-[200px] flex flex-col justify-center items-center text-center p-4 border-2 border-dashed rounded-lg">
									<BarChart3 className="h-10 w-10 text-muted-foreground" />
									<p className="mt-2 text-sm text-muted-foreground">
										Analytics data will appear here once your campaigns are live
									</p>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>
				<TabsContent value="campaigns" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle>All Campaigns</CardTitle>
							<CardDescription>Manage your advertising campaigns</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{campaigns.length > 0 ? (
									campaigns.map((campaign: any) => (
										<div key={campaign.id} className="flex items-center gap-4">
											<div className="w-16 h-16 rounded-md bg-muted flex items-center justify-center">
												{campaign.ad?.image_url ? (
													<img
														src={campaign.ad.image_url}
														alt={campaign.name}
														className="w-full h-full object-cover rounded-md"
													/>
												) : (
													<ImageIcon className="h-8 w-8 text-foreground" />
												)}
											</div>
											<div className="flex-1 space-y-1">
												<p className="text-sm font-medium leading-none">{campaign.name}</p>
												<p className="text-sm text-muted-foreground">
													{new Date(campaign.start_date).toLocaleDateString()} -{" "}
													{new Date(campaign.end_date).toLocaleDateString()}
												</p>
												<div className="flex items-center pt-2">
													<span
														className={`text-xs px-2 py-0.5 rounded ${
															campaign.status === "active"
																? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
																: campaign.status === "pending"
																? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
																: "bg-muted text-muted-foreground"
														}`}
													>
														{campaign.status}
													</span>
													{campaign.impressions && (
														<span className="text-xs text-muted-foreground ml-2">
															{campaign.impressions} impressions
														</span>
													)}
												</div>
											</div>
											<div className="flex gap-1">
												<Link href={`/dashboard/campaigns/${campaign.id}`}>
													<Button variant="ghost" size="icon">
														<ArrowRight className="h-4 w-4" />
													</Button>
												</Link>
												{campaign.status === "active" && (
													<Link href={`/dashboard/campaigns/${campaign.id}/analytics`}>
														<Button variant="ghost" size="icon">
															<BarChart3 className="h-4 w-4" />
														</Button>
													</Link>
												)}
											</div>
										</div>
									))
								) : (
									<div className="text-center py-8">
										<p className="text-muted-foreground">No campaigns yet</p>
										<Link href="/dashboard/campaigns/create">
											<Button className="mt-2">
												<Plus className="mr-2 h-4 w-4" />
												Create Campaign
											</Button>
										</Link>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				{/* Analytics Tab */}
				<TabsContent value="analytics" className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Total Impressions</CardTitle>
								<BarChart3 className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{analytics?.total_impressions?.toLocaleString() || "0"}
								</div>
								<p className="text-xs text-muted-foreground">All time</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
								<ArrowRight className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{analytics?.total_clicks?.toLocaleString() || "0"}
								</div>
								<p className="text-xs text-muted-foreground">All time</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Click Rate</CardTitle>
								<BarChart3 className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									{analytics?.ctr ? (analytics.ctr * 100).toFixed(2) + "%" : "0%"}
								</div>
								<p className="text-xs text-muted-foreground">Average CTR</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<CardTitle className="text-sm font-medium">Total Spend</CardTitle>
								<ArrowRight className="h-4 w-4 text-muted-foreground" />
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold">
									${analytics?.total_spend?.toFixed(2) || "0.00"}
								</div>
								<p className="text-xs text-muted-foreground">All time</p>
							</CardContent>
						</Card>
					</div>

					{/* Campaign Performance */}
					<Card>
						<CardHeader>
							<CardTitle>Campaign Performance</CardTitle>
							<CardDescription>Performance metrics for your campaigns</CardDescription>
						</CardHeader>
						<CardContent>
							{analytics?.campaigns && analytics.campaigns.length > 0 ? (
								<div className="space-y-4">
									<div className="grid grid-cols-5 gap-4 text-sm font-medium text-muted-foreground">
										<div>Campaign</div>
										<div className="text-right">Impressions</div>
										<div className="text-right">Clicks</div>
										<div className="text-right">CTR</div>
										<div className="text-right">Spend</div>
									</div>
									{analytics.campaigns.map((campaign: any) => (
										<div
											key={campaign.campaign_id}
											className="grid grid-cols-5 gap-4 text-sm py-2 border-b"
										>
											<div className="font-medium">{campaign.name}</div>
											<div className="text-right">
												{campaign.impressions?.toLocaleString() || "0"}
											</div>
											<div className="text-right">{campaign.clicks?.toLocaleString() || "0"}</div>
											<div className="text-right">
												{campaign.ctr ? (campaign.ctr * 100).toFixed(2) + "%" : "0%"}
											</div>
											<div className="text-right">${campaign.spend?.toFixed(2) || "0.00"}</div>
										</div>
									))}
								</div>
							) : (
								<div className="text-center py-8">
									<BarChart3 className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
									<p className="text-muted-foreground">No analytics data available yet</p>
									<p className="text-sm text-muted-foreground mt-1">
										Analytics will appear once your campaigns start receiving traffic
									</p>
								</div>
							)}
						</CardContent>
					</Card>

					{/* Quick Actions */}
					<div className="flex gap-4">
						<Link href="/dashboard/analytics">
							<Button variant="outline">
								<BarChart3 className="mr-2 h-4 w-4" />
								View Detailed Analytics
							</Button>
						</Link>
						<Link href="/dashboard/campaigns">
							<Button variant="outline">
								<ImageIcon className="mr-2 h-4 w-4" />
								Manage Campaigns
							</Button>
						</Link>
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
