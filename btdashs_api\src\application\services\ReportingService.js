const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class ReportingService {
	/**
	 * Generate daily aggregated reports for all active ads
	 * @param {string} date - Date in YYYY-MM-DD format (optional, defaults to yesterday)
	 * @returns {Promise<Object>} Aggregation results
	 */
	async generateDailyReports(date = null) {
		try {
			// Default to yesterday if no date provided
			if (!date) {
				const yesterday = new Date();
				yesterday.setDate(yesterday.getDate() - 1);
				date = yesterday.toISOString().split("T")[0];
			}

			const startTime = new Date(`${date} 00:00:00`);
			const endTime = new Date(`${date} 23:59:59`);

			logger.info("Starting daily report generation", { date });

			// Get all ads that had activity on this date
			const activeAds = await db("dtm_ads.ads")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.leftJoin("dtm_ads.ad_impressions", function () {
					this.on("ads.id", "ad_impressions.ad_id").andOnBetween("ad_impressions.created_at", [
						startTime,
						endTime,
					]);
				})
				.leftJoin("dtm_ads.ad_clicks", function () {
					this.on("ads.id", "ad_clicks.ad_id").andOnBetween("ad_clicks.created_at", [startTime, endTime]);
				})
				.select("ads.id", "ads.title", "ad_campaigns.id as campaign_id")
				.groupBy("ads.id", "ads.title", "ad_campaigns.id")
				.havingRaw("COUNT(ad_impressions.id) > 0 OR COUNT(ad_clicks.id) > 0");

			const results = {
				date,
				processedAds: 0,
				totalImpressions: 0,
				totalClicks: 0,
				totalSpend: 0,
				errors: [],
			};

			for (const ad of activeAds) {
				try {
					const adReport = await this.generateAdDailyReport(ad.id, date);
					results.processedAds++;
					results.totalImpressions += adReport.impressions;
					results.totalClicks += adReport.clicks;
					results.totalSpend += adReport.spend;
				} catch (error) {
					logger.error("Error generating daily report for ad", { error, adId: ad.id, date });
					results.errors.push({ adId: ad.id, error: error.message });
				}
			}

			// Generate campaign-level aggregates
			await this.generateCampaignDailyReports(date);

			logger.info("Daily report generation completed", results);
			return results;
		} catch (error) {
			logger.error("Error in daily report generation", { error, date });
			throw error;
		}
	}

	/**
	 * Generate daily report for a specific ad
	 * @param {number} adId - Ad ID
	 * @param {string} date - Date in YYYY-MM-DD format
	 * @returns {Promise<Object>} Ad daily report
	 */
	async generateAdDailyReport(adId, date) {
		try {
			const startTime = new Date(`${date} 00:00:00`);
			const endTime = new Date(`${date} 23:59:59`);

			// Get impressions count
			const impressionsResult = await db("dtm_ads.ad_impressions")
				.where("ad_id", adId)
				.whereBetween("created_at", [startTime, endTime])
				.count("* as count")
				.first();

			// Get clicks count
			const clicksResult = await db("dtm_ads.ad_clicks")
				.where("ad_id", adId)
				.whereBetween("created_at", [startTime, endTime])
				.count("* as count")
				.first();

			// Get unique users count
			const uniqueUsersResult = await db("dtm_ads.ad_impressions")
				.where("ad_id", adId)
				.whereBetween("created_at", [startTime, endTime])
				.whereNotNull("user_id")
				.countDistinct("user_id as count")
				.first();

			// Calculate spend from billing transactions
			const spendResult = await db("dtm_ads.billing_transactions")
				.leftJoin("dtm_ads.ads", "billing_transactions.campaign_id", "ads.campaign_id")
				.where("ads.id", adId)
				.where("billing_transactions.amount", "<", 0)
				.whereBetween("billing_transactions.created_at", [startTime, endTime])
				.sum("billing_transactions.amount as total")
				.first();

			// Calculate average view time
			const avgViewTimeResult = await db("dtm_ads.ad_impressions")
				.where("ad_id", adId)
				.whereBetween("created_at", [startTime, endTime])
				.whereNotNull("viewed_time")
				.avg("viewed_time as avg_time")
				.first();

			const impressions = parseInt(impressionsResult.count) || 0;
			const clicks = parseInt(clicksResult.count) || 0;
			const uniqueUsers = parseInt(uniqueUsersResult.count) || 0;
			const spend = Math.abs(parseFloat(spendResult.total)) || 0;
			const avgViewTime = parseInt(avgViewTimeResult.avg_time) || null;

			const reportData = {
				ad_id: adId,
				date,
				impressions,
				clicks,
				spend,
				avg_view_time: avgViewTime,
				unique_users: uniqueUsers,
				created_at: new Date(),
				updated_at: new Date(),
			};

			// Insert or update daily report
			await db("dtm_ads.daily_ad_reports").insert(reportData).onConflict(["ad_id", "date"]).merge(reportData);

			logger.debug("Ad daily report generated", { adId, date, reportData });
			return reportData;
		} catch (error) {
			logger.error("Error generating ad daily report", { error, adId, date });
			throw error;
		}
	}

	/**
	 * Generate campaign-level daily reports
	 * @param {string} date - Date in YYYY-MM-DD format
	 * @returns {Promise<void>}
	 */
	async generateCampaignDailyReports(date) {
		try {
			// Get all campaigns that had activity on this date
			const campaignReports = await db("dtm_ads.daily_ad_reports")
				.leftJoin("dtm_ads.ads", "daily_ad_reports.ad_id", "ads.id")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.where("daily_ad_reports.date", date)
				.select(
					"ad_campaigns.id as campaign_id",
					db.raw("SUM(daily_ad_reports.impressions) as total_impressions"),
					db.raw("SUM(daily_ad_reports.clicks) as total_clicks"),
					db.raw("SUM(daily_ad_reports.spend) as total_spend"),
					db.raw("AVG(daily_ad_reports.avg_view_time) as avg_view_time"),
					db.raw("SUM(daily_ad_reports.unique_users) as total_unique_users")
				)
				.groupBy("ad_campaigns.id")
				.havingRaw("SUM(daily_ad_reports.impressions) > 0 OR SUM(daily_ad_reports.clicks) > 0");

			for (const report of campaignReports) {
				const impressions = parseInt(report.total_impressions) || 0;
				const clicks = parseInt(report.total_clicks) || 0;
				const spend = parseFloat(report.total_spend) || 0;
				const ctr = impressions > 0 ? clicks / impressions : 0;
				const avgCpc = clicks > 0 ? spend / clicks : 0;
				const avgCpm = impressions > 0 ? (spend / impressions) * 1000 : 0;

				const campaignReportData = {
					campaign_id: report.campaign_id,
					date,
					impressions,
					clicks,
					spend,
					ctr: parseFloat(ctr.toFixed(4)),
					avg_cpc: parseFloat(avgCpc.toFixed(4)),
					avg_cpm: parseFloat(avgCpm.toFixed(2)),
					created_at: new Date(),
					updated_at: new Date(),
				};

				await db("dtm_ads.campaign_daily_reports")
					.insert(campaignReportData)
					.onConflict(["campaign_id", "date"])
					.merge(campaignReportData);
			}

			logger.info("Campaign daily reports generated", { date, campaignCount: campaignReports.length });
		} catch (error) {
			logger.error("Error generating campaign daily reports", { error, date });
			throw error;
		}
	}

	/**
	 * Generate weekly aggregated reports
	 * @param {string} weekStart - Week start date in YYYY-MM-DD format
	 * @returns {Promise<Object>} Weekly report data
	 */
	async generateWeeklyReports(weekStart = null) {
		try {
			// Default to last week if no date provided
			if (!weekStart) {
				const lastWeek = new Date();
				lastWeek.setDate(lastWeek.getDate() - 7);
				const dayOfWeek = lastWeek.getDay();
				const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Get Monday
				lastWeek.setDate(lastWeek.getDate() + mondayOffset);
				weekStart = lastWeek.toISOString().split("T")[0];
			}

			const weekEnd = new Date(weekStart);
			weekEnd.setDate(weekEnd.getDate() + 6);
			const weekEndStr = weekEnd.toISOString().split("T")[0];

			logger.info("Generating weekly reports", { weekStart, weekEnd: weekEndStr });

			// Aggregate campaign data for the week
			const weeklyData = await db("dtm_ads.campaign_daily_reports")
				.whereBetween("date", [weekStart, weekEndStr])
				.select(
					"campaign_id",
					db.raw("SUM(impressions) as total_impressions"),
					db.raw("SUM(clicks) as total_clicks"),
					db.raw("SUM(spend) as total_spend"),
					db.raw("AVG(ctr) as avg_ctr"),
					db.raw("AVG(avg_cpc) as avg_cpc"),
					db.raw("AVG(avg_cpm) as avg_cpm")
				)
				.groupBy("campaign_id");

			const results = {
				week_start: weekStart,
				week_end: weekEndStr,
				campaigns: weeklyData.length,
				total_impressions: 0,
				total_clicks: 0,
				total_spend: 0,
				avg_ctr: 0,
				campaign_reports: weeklyData,
			};

			// Calculate totals
			weeklyData.forEach((campaign) => {
				results.total_impressions += parseInt(campaign.total_impressions) || 0;
				results.total_clicks += parseInt(campaign.total_clicks) || 0;
				results.total_spend += parseFloat(campaign.total_spend) || 0;
			});

			results.avg_ctr = results.total_impressions > 0 ? results.total_clicks / results.total_impressions : 0;

			logger.info("Weekly reports generated", results);
			return results;
		} catch (error) {
			logger.error("Error generating weekly reports", { error, weekStart });
			throw error;
		}
	}

	/**
	 * Generate monthly aggregated reports
	 * @param {string} month - Month in YYYY-MM format
	 * @returns {Promise<Object>} Monthly report data
	 */
	async generateMonthlyReports(month = null) {
		try {
			// Default to last month if no month provided
			if (!month) {
				const lastMonth = new Date();
				lastMonth.setMonth(lastMonth.getMonth() - 1);
				month = lastMonth.toISOString().substring(0, 7); // YYYY-MM
			}

			const monthStart = `${month}-01`;
			const monthEnd = new Date(month + "-01");
			monthEnd.setMonth(monthEnd.getMonth() + 1);
			monthEnd.setDate(monthEnd.getDate() - 1);
			const monthEndStr = monthEnd.toISOString().split("T")[0];

			logger.info("Generating monthly reports", { month, monthStart, monthEnd: monthEndStr });

			// Aggregate campaign data for the month
			const monthlyData = await db("dtm_ads.campaign_daily_reports")
				.whereBetween("date", [monthStart, monthEndStr])
				.select(
					"campaign_id",
					db.raw("SUM(impressions) as total_impressions"),
					db.raw("SUM(clicks) as total_clicks"),
					db.raw("SUM(spend) as total_spend"),
					db.raw("AVG(ctr) as avg_ctr"),
					db.raw("AVG(avg_cpc) as avg_cpc"),
					db.raw("AVG(avg_cpm) as avg_cpm"),
					db.raw("COUNT(DISTINCT date) as active_days")
				)
				.groupBy("campaign_id");

			// Get top performing campaigns
			const topCampaigns = await db("dtm_ads.campaign_daily_reports")
				.leftJoin("dtm_ads.ad_campaigns", "campaign_daily_reports.campaign_id", "ad_campaigns.id")
				.whereBetween("campaign_daily_reports.date", [monthStart, monthEndStr])
				.select(
					"ad_campaigns.id",
					"ad_campaigns.name",
					db.raw("SUM(campaign_daily_reports.impressions) as total_impressions"),
					db.raw("SUM(campaign_daily_reports.clicks) as total_clicks"),
					db.raw("SUM(campaign_daily_reports.spend) as total_spend")
				)
				.groupBy("ad_campaigns.id", "ad_campaigns.name")
				.orderBy("total_spend", "desc")
				.limit(10);

			const results = {
				month,
				month_start: monthStart,
				month_end: monthEndStr,
				campaigns: monthlyData.length,
				total_impressions: 0,
				total_clicks: 0,
				total_spend: 0,
				avg_ctr: 0,
				top_campaigns: topCampaigns,
				campaign_reports: monthlyData,
			};

			// Calculate totals
			monthlyData.forEach((campaign) => {
				results.total_impressions += parseInt(campaign.total_impressions) || 0;
				results.total_clicks += parseInt(campaign.total_clicks) || 0;
				results.total_spend += parseFloat(campaign.total_spend) || 0;
			});

			results.avg_ctr = results.total_impressions > 0 ? results.total_clicks / results.total_impressions : 0;

			logger.info("Monthly reports generated", results);
			return results;
		} catch (error) {
			logger.error("Error generating monthly reports", { error, month });
			throw error;
		}
	}

	/**
	 * Get performance trends for a campaign
	 * @param {number} campaignId - Campaign ID
	 * @param {number} days - Number of days to analyze (default: 30)
	 * @returns {Promise<Object>} Trend analysis
	 */
	async getCampaignTrends(campaignId, days = 30) {
		try {
			const endDate = new Date();
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const dailyData = await db("dtm_ads.campaign_daily_reports")
				.where("campaign_id", campaignId)
				.whereBetween("date", [startDate.toISOString().split("T")[0], endDate.toISOString().split("T")[0]])
				.orderBy("date", "asc");

			if (dailyData.length === 0) {
				return {
					campaign_id: campaignId,
					period_days: days,
					trend_data: [],
					summary: {
						total_impressions: 0,
						total_clicks: 0,
						total_spend: 0,
						avg_ctr: 0,
						trend_direction: "stable",
					},
				};
			}

			// Calculate trends
			const firstHalf = dailyData.slice(0, Math.floor(dailyData.length / 2));
			const secondHalf = dailyData.slice(Math.floor(dailyData.length / 2));

			const firstHalfAvg = firstHalf.reduce((sum, day) => sum + day.ctr, 0) / firstHalf.length;
			const secondHalfAvg = secondHalf.reduce((sum, day) => sum + day.ctr, 0) / secondHalf.length;

			let trendDirection = "stable";
			const trendChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;

			if (trendChange > 5) trendDirection = "improving";
			else if (trendChange < -5) trendDirection = "declining";

			const summary = {
				total_impressions: dailyData.reduce((sum, day) => sum + day.impressions, 0),
				total_clicks: dailyData.reduce((sum, day) => sum + day.clicks, 0),
				total_spend: dailyData.reduce((sum, day) => sum + day.spend, 0),
				avg_ctr: dailyData.reduce((sum, day) => sum + day.ctr, 0) / dailyData.length,
				trend_direction: trendDirection,
				trend_change_percent: parseFloat(trendChange.toFixed(2)),
			};

			return {
				campaign_id: campaignId,
				period_days: days,
				trend_data: dailyData,
				summary,
			};
		} catch (error) {
			logger.error("Error getting campaign trends", { error, campaignId, days });
			throw error;
		}
	}

	/**
	 * Clean up old report data
	 * @param {number} retentionDays - Number of days to retain (default: 365)
	 * @returns {Promise<Object>} Cleanup results
	 */
	async cleanupOldReports(retentionDays = 365) {
		try {
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
			const cutoffDateStr = cutoffDate.toISOString().split("T")[0];

			const [adReportsDeleted, campaignReportsDeleted] = await Promise.all([
				db("dtm_ads.daily_ad_reports").where("date", "<", cutoffDateStr).del(),
				db("dtm_ads.campaign_daily_reports").where("date", "<", cutoffDateStr).del(),
			]);

			const results = {
				cutoff_date: cutoffDateStr,
				retention_days: retentionDays,
				ad_reports_deleted: adReportsDeleted,
				campaign_reports_deleted: campaignReportsDeleted,
			};

			logger.info("Old reports cleaned up", results);
			return results;
		} catch (error) {
			logger.error("Error cleaning up old reports", { error, retentionDays });
			throw error;
		}
	}
}

module.exports = ReportingService;
