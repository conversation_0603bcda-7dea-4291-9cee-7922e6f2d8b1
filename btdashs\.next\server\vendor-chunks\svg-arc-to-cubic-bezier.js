"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/svg-arc-to-cubic-bezier";
exports.ids = ["vendor-chunks/svg-arc-to-cubic-bezier"];
exports.modules = {

/***/ "(ssr)/./node_modules/svg-arc-to-cubic-bezier/modules/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/svg-arc-to-cubic-bezier/modules/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar TAU = Math.PI * 2;\n\nvar mapToEllipse = function mapToEllipse(_ref, rx, ry, cosphi, sinphi, centerx, centery) {\n  var x = _ref.x,\n      y = _ref.y;\n\n  x *= rx;\n  y *= ry;\n\n  var xp = cosphi * x - sinphi * y;\n  var yp = sinphi * x + cosphi * y;\n\n  return {\n    x: xp + centerx,\n    y: yp + centery\n  };\n};\n\nvar approxUnitArc = function approxUnitArc(ang1, ang2) {\n  // If 90 degree circular arc, use a constant\n  // as derived from http://spencermortensen.com/articles/bezier-circle\n  var a = ang2 === 1.5707963267948966 ? 0.551915024494 : ang2 === -1.5707963267948966 ? -0.551915024494 : 4 / 3 * Math.tan(ang2 / 4);\n\n  var x1 = Math.cos(ang1);\n  var y1 = Math.sin(ang1);\n  var x2 = Math.cos(ang1 + ang2);\n  var y2 = Math.sin(ang1 + ang2);\n\n  return [{\n    x: x1 - y1 * a,\n    y: y1 + x1 * a\n  }, {\n    x: x2 + y2 * a,\n    y: y2 - x2 * a\n  }, {\n    x: x2,\n    y: y2\n  }];\n};\n\nvar vectorAngle = function vectorAngle(ux, uy, vx, vy) {\n  var sign = ux * vy - uy * vx < 0 ? -1 : 1;\n\n  var dot = ux * vx + uy * vy;\n\n  if (dot > 1) {\n    dot = 1;\n  }\n\n  if (dot < -1) {\n    dot = -1;\n  }\n\n  return sign * Math.acos(dot);\n};\n\nvar getArcCenter = function getArcCenter(px, py, cx, cy, rx, ry, largeArcFlag, sweepFlag, sinphi, cosphi, pxp, pyp) {\n  var rxsq = Math.pow(rx, 2);\n  var rysq = Math.pow(ry, 2);\n  var pxpsq = Math.pow(pxp, 2);\n  var pypsq = Math.pow(pyp, 2);\n\n  var radicant = rxsq * rysq - rxsq * pypsq - rysq * pxpsq;\n\n  if (radicant < 0) {\n    radicant = 0;\n  }\n\n  radicant /= rxsq * pypsq + rysq * pxpsq;\n  radicant = Math.sqrt(radicant) * (largeArcFlag === sweepFlag ? -1 : 1);\n\n  var centerxp = radicant * rx / ry * pyp;\n  var centeryp = radicant * -ry / rx * pxp;\n\n  var centerx = cosphi * centerxp - sinphi * centeryp + (px + cx) / 2;\n  var centery = sinphi * centerxp + cosphi * centeryp + (py + cy) / 2;\n\n  var vx1 = (pxp - centerxp) / rx;\n  var vy1 = (pyp - centeryp) / ry;\n  var vx2 = (-pxp - centerxp) / rx;\n  var vy2 = (-pyp - centeryp) / ry;\n\n  var ang1 = vectorAngle(1, 0, vx1, vy1);\n  var ang2 = vectorAngle(vx1, vy1, vx2, vy2);\n\n  if (sweepFlag === 0 && ang2 > 0) {\n    ang2 -= TAU;\n  }\n\n  if (sweepFlag === 1 && ang2 < 0) {\n    ang2 += TAU;\n  }\n\n  return [centerx, centery, ang1, ang2];\n};\n\nvar arcToBezier = function arcToBezier(_ref2) {\n  var px = _ref2.px,\n      py = _ref2.py,\n      cx = _ref2.cx,\n      cy = _ref2.cy,\n      rx = _ref2.rx,\n      ry = _ref2.ry,\n      _ref2$xAxisRotation = _ref2.xAxisRotation,\n      xAxisRotation = _ref2$xAxisRotation === undefined ? 0 : _ref2$xAxisRotation,\n      _ref2$largeArcFlag = _ref2.largeArcFlag,\n      largeArcFlag = _ref2$largeArcFlag === undefined ? 0 : _ref2$largeArcFlag,\n      _ref2$sweepFlag = _ref2.sweepFlag,\n      sweepFlag = _ref2$sweepFlag === undefined ? 0 : _ref2$sweepFlag;\n\n  var curves = [];\n\n  if (rx === 0 || ry === 0) {\n    return [];\n  }\n\n  var sinphi = Math.sin(xAxisRotation * TAU / 360);\n  var cosphi = Math.cos(xAxisRotation * TAU / 360);\n\n  var pxp = cosphi * (px - cx) / 2 + sinphi * (py - cy) / 2;\n  var pyp = -sinphi * (px - cx) / 2 + cosphi * (py - cy) / 2;\n\n  if (pxp === 0 && pyp === 0) {\n    return [];\n  }\n\n  rx = Math.abs(rx);\n  ry = Math.abs(ry);\n\n  var lambda = Math.pow(pxp, 2) / Math.pow(rx, 2) + Math.pow(pyp, 2) / Math.pow(ry, 2);\n\n  if (lambda > 1) {\n    rx *= Math.sqrt(lambda);\n    ry *= Math.sqrt(lambda);\n  }\n\n  var _getArcCenter = getArcCenter(px, py, cx, cy, rx, ry, largeArcFlag, sweepFlag, sinphi, cosphi, pxp, pyp),\n      _getArcCenter2 = _slicedToArray(_getArcCenter, 4),\n      centerx = _getArcCenter2[0],\n      centery = _getArcCenter2[1],\n      ang1 = _getArcCenter2[2],\n      ang2 = _getArcCenter2[3];\n\n  // If 'ang2' == 90.0000000001, then `ratio` will evaluate to\n  // 1.0000000001. This causes `segments` to be greater than one, which is an\n  // unecessary split, and adds extra points to the bezier curve. To alleviate\n  // this issue, we round to 1.0 when the ratio is close to 1.0.\n\n\n  var ratio = Math.abs(ang2) / (TAU / 4);\n  if (Math.abs(1.0 - ratio) < 0.0000001) {\n    ratio = 1.0;\n  }\n\n  var segments = Math.max(Math.ceil(ratio), 1);\n\n  ang2 /= segments;\n\n  for (var i = 0; i < segments; i++) {\n    curves.push(approxUnitArc(ang1, ang2));\n    ang1 += ang2;\n  }\n\n  return curves.map(function (curve) {\n    var _mapToEllipse = mapToEllipse(curve[0], rx, ry, cosphi, sinphi, centerx, centery),\n        x1 = _mapToEllipse.x,\n        y1 = _mapToEllipse.y;\n\n    var _mapToEllipse2 = mapToEllipse(curve[1], rx, ry, cosphi, sinphi, centerx, centery),\n        x2 = _mapToEllipse2.x,\n        y2 = _mapToEllipse2.y;\n\n    var _mapToEllipse3 = mapToEllipse(curve[2], rx, ry, cosphi, sinphi, centerx, centery),\n        x = _mapToEllipse3.x,\n        y = _mapToEllipse3.y;\n\n    return { x1: x1, y1: y1, x2: x2, y2: y2, x: x, y: y };\n  });\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (arcToBezier);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/svg-arc-to-cubic-bezier/modules/index.js\n");

/***/ })

};
;