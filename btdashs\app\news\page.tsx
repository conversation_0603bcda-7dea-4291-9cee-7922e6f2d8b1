// app/news/page.tsx

import { fetchWithFallback } from "@/lib/data/utils";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import NewsClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

// Static SEO metadata for the news page
export const metadata: Metadata = generateSEOMetadata({
	title: "News | DynamicTaoMarketCap",
	description: "Read the latest news and updates from the TAO ecosystem.",
	url: "https://dynamictaomarketcap.com/news",
	image: "/default-news-og.jpg",
});

export default async function NewsPage() {
	const [newsRes] = await Promise.all([fetchWithFallback(`${process.env.APP_BASE_URL}/api/news`)]);

	if (newsRes.error) console.error("News fetch error:", newsRes.error);

	const [catRes] = await Promise.all([fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`)]);

	if (catRes.error) console.error("Categories fetch error:", catRes.error);

	return <NewsClientWrapper news={newsRes.data || []} categories={catRes.data || []} />;
}
