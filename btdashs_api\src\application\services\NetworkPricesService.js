// src/application/services/NetworkPricesService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Network Prices Service - Handles network pricing data operations
 *
 * This service manages network pricing information, providing data
 * aggregation and analysis for price tracking and historical data.
 *
 * Key responsibilities:
 * - Network price data CRUD operations
 * - Price history management
 * - Price analytics and calculations
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class NetworkPricesService extends BaseService {
	constructor() {
		super("dtm_base.network_prices", "NetworkPrice");
	}

	/**
	 * Get all network prices with default sorting by timestamp
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of network prices
	 */
	async getAllNetworkPrices(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "recorded_at", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all network prices", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get network price by ID
	 * @param {number} id - Network price ID
	 * @returns {Promise<Object|null>} Network price object or null if not found
	 */
	async getNetworkPriceById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting network price by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new network price entry
	 * @param {Object} priceData - Network price data
	 * @returns {Promise<Object>} Created network price object
	 */
	async createNetworkPrice(priceData) {
		try {
			const newPrice = await this.create({
				...priceData,
				recorded_at: priceData.recorded_at || new Date(),
			});
			logger.info("Network price created", { price_id: newPrice.id });
			return newPrice;
		} catch (error) {
			logger.error("Error creating network price", { error, priceData });
			throw error;
		}
	}

	/**
	 * Update a network price
	 * @param {number} id - Network price ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated network price object
	 */
	async updateNetworkPrice(id, updateData) {
		try {
			const updatedPrice = await this.updateById(id, updateData);
			logger.info("Network price updated", { price_id: id });
			return updatedPrice;
		} catch (error) {
			logger.error("Error updating network price", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a network price
	 * @param {number} id - Network price ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteNetworkPrice(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Network price deleted", { price_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting network price", { error, id });
			throw error;
		}
	}

	/**
	 * Get latest network prices
	 * @param {number} limit - Number of latest prices to return
	 * @returns {Promise<Array>} Array of latest network prices
	 */
	async getLatestNetworkPrices(limit = 10) {
		try {
			const prices = await this.getAll(
				{},
				{
					orderBy: { column: "recorded_at", direction: "desc" },
					limit,
				}
			);

			logger.info("Latest network prices retrieved", {
				count: prices.length,
				limit,
			});

			return prices;
		} catch (error) {
			logger.error("Error getting latest network prices", { error, limit });
			throw new Error(`Failed to get latest network prices: ${error.message}`);
		}
	}

	/**
	 * Get current network price (most recent)
	 * @returns {Promise<Object|null>} Current network price or null if not found
	 */
	async getCurrentNetworkPrice() {
		try {
			const prices = await this.getLatestNetworkPrices(1);
			return prices.length > 0 ? prices[0] : null;
		} catch (error) {
			logger.error("Error getting current network price", { error });
			throw new Error(`Failed to get current network price: ${error.message}`);
		}
	}

	/**
	 * Get network prices by date range
	 * @param {Date} startDate - Start date
	 * @param {Date} endDate - End date
	 * @returns {Promise<Array>} Array of network prices in date range
	 */
	async getNetworkPricesByDateRange(startDate, endDate) {
		try {
			const db = require("../../infrastructure/database/knex");

			const prices = await db(this.tableName)
				.where("recorded_at", ">=", startDate)
				.where("recorded_at", "<=", endDate)
				.orderBy("recorded_at", "asc");

			logger.info("Network prices retrieved by date range", {
				startDate,
				endDate,
				count: prices.length,
			});

			return prices;
		} catch (error) {
			logger.error("Error getting network prices by date range", { error, startDate, endDate });
			throw new Error(`Failed to get network prices by date range: ${error.message}`);
		}
	}

	/**
	 * Get price analytics for a time period
	 * @param {number} days - Number of days to analyze
	 * @returns {Promise<Object>} Price analytics
	 */
	async getPriceAnalytics(days = 7) {
		try {
			const db = require("../../infrastructure/database/knex");
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const analytics = await db(this.tableName)
				.where("timestamp", ">=", startDate)
				.select(
					db.raw("AVG(price_usd) as avg_price"),
					db.raw("MAX(price_usd) as max_price"),
					db.raw("MIN(price_usd) as min_price"),
					db.raw("COUNT(*) as data_points"),
					db.raw("STDDEV(price_usd) as price_volatility")
				)
				.first();

			// Calculate price change
			const firstPrice = await db(this.tableName)
				.where("timestamp", ">=", startDate)
				.orderBy("timestamp", "asc")
				.first();

			const lastPrice = await db(this.tableName)
				.where("timestamp", ">=", startDate)
				.orderBy("timestamp", "desc")
				.first();

			if (firstPrice && lastPrice) {
				analytics.price_change = lastPrice.price_usd - firstPrice.price_usd;
				analytics.price_change_percent =
					((lastPrice.price_usd - firstPrice.price_usd) / firstPrice.price_usd) * 100;
			}

			logger.info("Price analytics retrieved", {
				days,
				analytics,
			});

			return analytics;
		} catch (error) {
			logger.error("Error getting price analytics", { error, days });
			throw new Error(`Failed to get price analytics: ${error.message}`);
		}
	}

	/**
	 * Get price history for charting
	 * @param {string} interval - Time interval (1h, 1d, 1w, 1m)
	 * @param {number} limit - Number of data points to return
	 * @returns {Promise<Array>} Array of price history data
	 */
	async getPriceHistory(interval = "1d", limit = 30) {
		try {
			const db = require("../../infrastructure/database/knex");

			let groupBy;
			switch (interval) {
				case "1h":
					groupBy = db.raw("DATE_TRUNC('hour', timestamp)");
					break;
				case "1d":
					groupBy = db.raw("DATE_TRUNC('day', timestamp)");
					break;
				case "1w":
					groupBy = db.raw("DATE_TRUNC('week', timestamp)");
					break;
				case "1m":
					groupBy = db.raw("DATE_TRUNC('month', timestamp)");
					break;
				default:
					groupBy = db.raw("DATE_TRUNC('day', timestamp)");
			}

			const history = await db(this.tableName)
				.select(
					groupBy.as("period"),
					db.raw("AVG(price_usd) as avg_price"),
					db.raw("MAX(price_usd) as high_price"),
					db.raw("MIN(price_usd) as low_price"),
					db.raw(
						"FIRST_VALUE(price_usd) OVER (PARTITION BY " + groupBy + " ORDER BY timestamp) as open_price"
					),
					db.raw(
						"LAST_VALUE(price_usd) OVER (PARTITION BY " + groupBy + " ORDER BY timestamp) as close_price"
					)
				)
				.groupBy(groupBy)
				.orderBy("period", "desc")
				.limit(limit);

			logger.info("Price history retrieved", {
				interval,
				limit,
				count: history.length,
			});

			return history;
		} catch (error) {
			logger.error("Error getting price history", { error, interval, limit });
			throw new Error(`Failed to get price history: ${error.message}`);
		}
	}
}

module.exports = new NetworkPricesService();
