"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/standard-as-callback";
exports.ids = ["vendor-chunks/standard-as-callback"];
exports.modules = {

/***/ "(rsc)/./node_modules/standard-as-callback/built/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/standard-as-callback/built/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/standard-as-callback/built/utils.js\");\nfunction throwLater(e) {\n    setTimeout(function () {\n        throw e;\n    }, 0);\n}\nfunction asCallback(promise, nodeback, options) {\n    if (typeof nodeback === \"function\") {\n        promise.then((val) => {\n            let ret;\n            if (options !== undefined &&\n                Object(options).spread &&\n                Array.isArray(val)) {\n                ret = utils_1.tryCatch(nodeback).apply(undefined, [null].concat(val));\n            }\n            else {\n                ret =\n                    val === undefined\n                        ? utils_1.tryCatch(nodeback)(null)\n                        : utils_1.tryCatch(nodeback)(null, val);\n            }\n            if (ret === utils_1.errorObj) {\n                throwLater(ret.e);\n            }\n        }, (cause) => {\n            if (!cause) {\n                const newReason = new Error(cause + \"\");\n                Object.assign(newReason, { cause });\n                cause = newReason;\n            }\n            const ret = utils_1.tryCatch(nodeback)(cause);\n            if (ret === utils_1.errorObj) {\n                throwLater(ret.e);\n            }\n        });\n    }\n    return promise;\n}\nexports[\"default\"] = asCallback;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc3RhbmRhcmQtYXMtY2FsbGJhY2svYnVpbHQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCLG1CQUFPLENBQUMseUVBQVM7QUFDakM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSwyQ0FBMkMsT0FBTztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0Esa0JBQWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxzdGFuZGFyZC1hcy1jYWxsYmFja1xcYnVpbHRcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xuZnVuY3Rpb24gdGhyb3dMYXRlcihlKSB7XG4gICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICAgIHRocm93IGU7XG4gICAgfSwgMCk7XG59XG5mdW5jdGlvbiBhc0NhbGxiYWNrKHByb21pc2UsIG5vZGViYWNrLCBvcHRpb25zKSB7XG4gICAgaWYgKHR5cGVvZiBub2RlYmFjayA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIHByb21pc2UudGhlbigodmFsKSA9PiB7XG4gICAgICAgICAgICBsZXQgcmV0O1xuICAgICAgICAgICAgaWYgKG9wdGlvbnMgIT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAgICAgICAgIE9iamVjdChvcHRpb25zKS5zcHJlYWQgJiZcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KHZhbCkpIHtcbiAgICAgICAgICAgICAgICByZXQgPSB1dGlsc18xLnRyeUNhdGNoKG5vZGViYWNrKS5hcHBseSh1bmRlZmluZWQsIFtudWxsXS5jb25jYXQodmFsKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXQgPVxuICAgICAgICAgICAgICAgICAgICB2YWwgPT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgPyB1dGlsc18xLnRyeUNhdGNoKG5vZGViYWNrKShudWxsKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB1dGlsc18xLnRyeUNhdGNoKG5vZGViYWNrKShudWxsLCB2YWwpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHJldCA9PT0gdXRpbHNfMS5lcnJvck9iaikge1xuICAgICAgICAgICAgICAgIHRocm93TGF0ZXIocmV0LmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCAoY2F1c2UpID0+IHtcbiAgICAgICAgICAgIGlmICghY2F1c2UpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBuZXdSZWFzb24gPSBuZXcgRXJyb3IoY2F1c2UgKyBcIlwiKTtcbiAgICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKG5ld1JlYXNvbiwgeyBjYXVzZSB9KTtcbiAgICAgICAgICAgICAgICBjYXVzZSA9IG5ld1JlYXNvbjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHJldCA9IHV0aWxzXzEudHJ5Q2F0Y2gobm9kZWJhY2spKGNhdXNlKTtcbiAgICAgICAgICAgIGlmIChyZXQgPT09IHV0aWxzXzEuZXJyb3JPYmopIHtcbiAgICAgICAgICAgICAgICB0aHJvd0xhdGVyKHJldC5lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiBwcm9taXNlO1xufVxuZXhwb3J0cy5kZWZhdWx0ID0gYXNDYWxsYmFjaztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/standard-as-callback/built/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/standard-as-callback/built/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/standard-as-callback/built/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.tryCatch = exports.errorObj = void 0;\n//Try catch is not supported in optimizing\n//compiler, so it is isolated\nexports.errorObj = { e: {} };\nlet tryCatchTarget;\nfunction tryCatcher(err, val) {\n    try {\n        const target = tryCatchTarget;\n        tryCatchTarget = null;\n        return target.apply(this, arguments);\n    }\n    catch (e) {\n        exports.errorObj.e = e;\n        return exports.errorObj;\n    }\n}\nfunction tryCatch(fn) {\n    tryCatchTarget = fn;\n    return tryCatcher;\n}\nexports.tryCatch = tryCatch;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc3RhbmRhcmQtYXMtY2FsbGJhY2svYnVpbHQvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCLEdBQUcsZ0JBQWdCO0FBQ25DO0FBQ0E7QUFDQSxnQkFBZ0IsS0FBSztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxrQkFBa0I7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxzdGFuZGFyZC1hcy1jYWxsYmFja1xcYnVpbHRcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy50cnlDYXRjaCA9IGV4cG9ydHMuZXJyb3JPYmogPSB2b2lkIDA7XG4vL1RyeSBjYXRjaCBpcyBub3Qgc3VwcG9ydGVkIGluIG9wdGltaXppbmdcbi8vY29tcGlsZXIsIHNvIGl0IGlzIGlzb2xhdGVkXG5leHBvcnRzLmVycm9yT2JqID0geyBlOiB7fSB9O1xubGV0IHRyeUNhdGNoVGFyZ2V0O1xuZnVuY3Rpb24gdHJ5Q2F0Y2hlcihlcnIsIHZhbCkge1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRhcmdldCA9IHRyeUNhdGNoVGFyZ2V0O1xuICAgICAgICB0cnlDYXRjaFRhcmdldCA9IG51bGw7XG4gICAgICAgIHJldHVybiB0YXJnZXQuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgZXhwb3J0cy5lcnJvck9iai5lID0gZTtcbiAgICAgICAgcmV0dXJuIGV4cG9ydHMuZXJyb3JPYmo7XG4gICAgfVxufVxuZnVuY3Rpb24gdHJ5Q2F0Y2goZm4pIHtcbiAgICB0cnlDYXRjaFRhcmdldCA9IGZuO1xuICAgIHJldHVybiB0cnlDYXRjaGVyO1xufVxuZXhwb3J0cy50cnlDYXRjaCA9IHRyeUNhdGNoO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/standard-as-callback/built/utils.js\n");

/***/ })

};
;