// app/jobs/page.tsx

import EventsClientWrapper from "@/app/events/client-wrapper";
import { auth0 } from "@/lib/auth0";
import { fetchWithFallback } from "@/lib/data/utils";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import { cookies } from "next/headers";

export const revalidate = 600; // 10 minutes in seconds

// Static SEO metadata for the Events listing page
export const metadata: Metadata = generateSEOMetadata({
	title: "Events | DynamicTaoMarketCap",
	description: "Discover upcoming and past events in the TAO ecosystem.",
	url: "https://dynamictaomarketcap.com/events",
	image: "/default-event-og.jpg",
});

export default async function EventsPage() {
	// Fetch all necessary data in parallel
	const [eventsRes, companyRes, categoriesRes, subnetsRes, productsRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
	]);

	let authorized_events_admin = false;

	// Check if user is logged in
	const session = await auth0.getSession();
	if (session) {
		const cookieHeader = (await cookies()).toString();
		const userData = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/me`, {
			headers: { Cookie: cookieHeader },
		});

		if (userData.error) {
			console.error("User fetch error", userData.error);
		} else if (userData.data) {
			authorized_events_admin = userData.data.authorized_events_admin;
		}
	}

	// Log errors if present
	if (eventsRes.error) console.error("Events fetch error", eventsRes.error);
	if (companyRes.error) console.error("Company fetch error", companyRes.error);
	if (categoriesRes.error) console.error("Categories fetch error", categoriesRes.error);
	if (subnetsRes.error) console.error("Subnets fetch error", subnetsRes.error);
	if (productsRes.error) console.error("Products fetch error", productsRes.error);

	// Map event associations
	const enrichedEvents = eventsRes.data.map((event) => ({
		...event,
		companies: companyRes.data.filter((c) => event.company_ids?.includes(c.id)),
		categories: categoriesRes.data.filter((cat) => event.category_ids?.includes(cat.id)),
		subnets: subnetsRes.data.filter((s) => event.subnet_ids?.includes(s.netuid)),
		products: productsRes.data.filter((p) => event.product_ids?.includes(p.id)),
	}));

	return <EventsClientWrapper events={enrichedEvents} authorized_events_admin={authorized_events_admin} />;
}
