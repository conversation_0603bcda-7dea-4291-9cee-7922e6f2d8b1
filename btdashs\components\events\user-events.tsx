"use client";

import { EventsGrid } from "@/components/events/events-grid";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MarkdownEditor from "@/components/ui/markdown-editor";
import { MultiSelect } from "@/components/ui/multi-select";
import type { Category, Company, Event, Product, Subnet } from "@/lib/db/models";
import { Plus } from "lucide-react";
import { useState } from "react";

interface UserEventsClientProps {
	initialEvents: (Event & {
		companies?: Company[];
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
		relatedEvents?: Event[];
	})[];
	companies?: Company[];
	categories?: Category[];
	subnets?: Subnet[];
	products?: Product[];
	allEvents?: Event[];
}

const eventTypes = ["Conference", "Meetup", "Workshop", "Hackathon", "Webinar", "Networking", "General"];

export default function UserEventsClient({
	initialEvents,
	companies = [],
	categories = [],
	subnets = [],
	products = [],
	allEvents = [],
}: UserEventsClientProps) {
	const [events, setEvents] = useState(initialEvents);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [currentEvent, setCurrentEvent] = useState<
		| (Partial<Event> & {
				companies?: Company[];
				categories?: Category[];
				subnets?: Subnet[];
				products?: Product[];
				relatedEvents?: Event[];
		  })
		| null
	>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const formatDateForInput = (date: Date | string | null) => {
		if (!date) return "";
		const d = new Date(date);
		if (isNaN(d.getTime())) return "";
		const pad = (num: number) => num.toString().padStart(2, "0");
		return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}T${pad(d.getHours())}:${pad(
			d.getMinutes()
		)}`;
	};

	const parseInputDate = (dateString: string) => {
		if (!dateString) return null;
		const [datePart, timePart] = dateString.split("T");
		const [year, month, day] = datePart.split("-").map(Number);
		const [hours, minutes] = timePart.split(":").map(Number);
		const date = new Date(year, month - 1, day, hours, minutes);
		return date.toISOString();
	};

	const handleSave = async () => {
		if (!currentEvent) return;

		if (!currentEvent.name || !currentEvent.start_date) {
			setError("Name and start date are required");
			return;
		}

		if (currentEvent.start_date && currentEvent.end_date) {
			const startDate = new Date(currentEvent.start_date);
			const endDate = new Date(currentEvent.end_date);

			if (endDate < startDate) {
				setError("End date cannot be before start date");
				return;
			}
		}

		setIsLoading(true);
		setError(null);

		try {
			const method = currentEvent.id ? "PUT" : "POST";
			const url = currentEvent.id ? `/api/user/events/${currentEvent.id}` : "/api/user/events";

			const payload = {
				name: currentEvent.name,
				description: currentEvent.description || null,
				start_date: currentEvent.start_date ? new Date(currentEvent.start_date).toISOString() : null,
				end_date: currentEvent.end_date ? new Date(currentEvent.end_date).toISOString() : null,
				location: currentEvent.location || null,
				is_virtual: currentEvent.is_virtual || false,
				event_type: currentEvent.event_type || "general",
				website_url: currentEvent.website_url || null,
				registration_url: currentEvent.registration_url || null,
				image_url: currentEvent.image_url || null,
				image_url_banner: currentEvent.image_url_banner || null,
				subnet_ids: currentEvent.subnet_ids || [],
				product_ids: currentEvent.product_ids || [],
				company_ids: currentEvent.company_ids || [],
				category_ids: currentEvent.category_ids || [],
				event_ids: currentEvent.event_ids || [],
				organizer_ids: currentEvent.organizer_ids || [],
				desc_about_this_event: currentEvent.desc_about_this_event || null,
				desc_what_u_will_learn: currentEvent.desc_what_u_will_learn || null,
				desc_who_should_attend: currentEvent.desc_who_should_attend || null,
				speakers: currentEvent.speakers || null,
			};

			const response = await fetch(url, {
				method,
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(payload),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || "Failed to save event");
			}

			const { data } = await response.json();
			const savedEvent = (method === "PUT" ? data : data.data) as Event;

			// Update events state with full relational data
			setEvents((prev) => {
				if (currentEvent.id) {
					return prev.map((e) =>
						e.id === savedEvent.id
							? {
									...savedEvent,
									companies: companies.filter((c) => payload.company_ids.includes(c.id)),
									categories: categories.filter((c) => payload.category_ids.includes(c.id)),
									subnets: subnets.filter((s) => payload.subnet_ids.includes(s.netuid)),
									products: products.filter((p) => payload.product_ids.includes(p.id)),
									relatedEvents: allEvents.filter((e) => payload.event_ids.includes(e.id)),
							  }
							: e
					);
				} else {
					return [
						{
							...savedEvent,
							companies: companies.filter((c) => payload.company_ids.includes(c.id)),
							categories: categories.filter((c) => payload.category_ids.includes(c.id)),
							subnets: subnets.filter((s) => payload.subnet_ids.includes(s.netuid)),
							products: products.filter((p) => payload.product_ids.includes(p.id)),
							relatedEvents: allEvents.filter((e) => payload.event_ids.includes(e.id)),
						},
						...prev,
					];
				}
			});

			handleCloseDialog();
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleOpenDialog = (
		event?: Event & {
			companies?: Company[];
			categories?: Category[];
			subnets?: Subnet[];
			products?: Product[];
			relatedEvents?: Event[];
		}
	) => {
		setCurrentEvent(
			event
				? {
						...event,
						subnet_ids: event.subnet_ids || [],
						product_ids: event.product_ids || [],
						company_ids: event.company_ids || [],
						category_ids: event.category_ids || [],
						event_ids: event.event_ids || [],
						organizer_ids: event.organizer_ids || [],
				  }
				: {
						name: "",
						description: "",
						start_date: new Date(),
						end_date: null,
						location: "",
						is_virtual: false,
						event_type: "general",
						website_url: "",
						registration_url: "",
						image_url: "",
						image_url_banner: "",
						subnet_ids: [],
						product_ids: [],
						company_ids: [],
						category_ids: [],
						event_ids: [],
						organizer_ids: [],
						desc_about_this_event: "",
						desc_what_u_will_learn: "",
						desc_who_should_attend: "",
						speakers: [],
				  }
		);
		setError(null);
		setIsDialogOpen(true);
	};

	const handleCloseDialog = () => {
		setIsDialogOpen(false);
		setCurrentEvent(null);
		setError(null);
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
		const { name, value, type } = e.target;
		const checked = type === "checkbox" ? (e.target as HTMLInputElement).checked : undefined;

		setCurrentEvent((prev) => ({
			...prev!,
			[name]: checked !== undefined ? checked : value,
		}));
	};

	const handleDateChange = (name: string, value: string) => {
		const parsedDate = parseInputDate(value);
		setCurrentEvent((prev) => ({
			...prev!,
			[name]: parsedDate,
		}));
	};

	const handleMultiSelectChange = (name: string, values: number[]) => {
		setCurrentEvent((prev) => ({
			...prev!,
			[name]: values,
		}));
	};

	const handleDelete = async (id: number) => {
		if (!confirm("Are you sure you want to delete this event?")) return;

		setIsLoading(true);
		try {
			const response = await fetch(`/api/user/events/${id}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to delete event");
			}

			setEvents((prev) => prev.filter((e) => e.id !== id));
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Manage Events</h2>
				<Button onClick={() => handleOpenDialog()}>
					<Plus className="mr-2 h-4 w-4" />
					Create New Event
				</Button>
			</div>

			{error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>}

			<EventsGrid
				events={events}
				editMode={true}
				onEdit={(event) => handleOpenDialog(event)}
				onDelete={handleDelete}
				loading={isLoading}
			/>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>{currentEvent?.id ? "Edit Event" : "Create New Event"}</DialogTitle>
					</DialogHeader>

					<div className="grid gap-4 py-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-4">
								<div className="space-y-2">
									<Label htmlFor="name">Event Name*</Label>
									<Input
										id="name"
										name="name"
										value={currentEvent?.name || ""}
										onChange={handleChange}
										required
									/>
								</div>

								<div className="space-y-2">
									<Label htmlFor="event_type">Event Type</Label>
									<select
										id="event_type"
										name="event_type"
										value={currentEvent?.event_type || "general"}
										onChange={handleChange}
										className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
									>
										{eventTypes.map((type) => (
											<option key={type} value={type.toLowerCase()}>
												{type}
											</option>
										))}
									</select>
								</div>

								<div className="grid grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="start_date">Start Date*</Label>
										<Input
											id="start_date"
											name="start_date"
											type="datetime-local"
											value={formatDateForInput(currentEvent?.start_date ?? null)}
											onChange={(e) => handleDateChange("start_date", e.target.value)}
											required
										/>
									</div>

									<div className="space-y-2">
										<Label htmlFor="end_date">End Date</Label>
										<Input
											id="end_date"
											name="end_date"
											type="datetime-local"
											value={formatDateForInput(currentEvent?.end_date ?? null)}
											onChange={(e) => handleDateChange("end_date", e.target.value)}
										/>
									</div>
								</div>

								<div className="space-y-2">
									<Label htmlFor="location">Location</Label>
									<Input
										id="location"
										name="location"
										value={currentEvent?.location || ""}
										onChange={handleChange}
									/>
								</div>

								<div className="flex items-center space-x-2">
									<input
										type="checkbox"
										id="is_virtual"
										name="is_virtual"
										checked={currentEvent?.is_virtual || false}
										onChange={handleChange}
										className="h-4 w-4"
									/>
									<Label htmlFor="is_virtual">Virtual Event</Label>
								</div>
							</div>

							<div className="space-y-4">
								<div className="space-y-2">
									<Label htmlFor="website_url">Website URL</Label>
									<Input
										id="website_url"
										name="website_url"
										value={currentEvent?.website_url || ""}
										onChange={handleChange}
									/>
								</div>

								<div className="space-y-2">
									<Label htmlFor="registration_url">Registration URL</Label>
									<Input
										id="registration_url"
										name="registration_url"
										value={currentEvent?.registration_url || ""}
										onChange={handleChange}
									/>
								</div>

								<div className="space-y-2">
									<Label htmlFor="image_url">Image URL</Label>
									<Input
										id="image_url"
										name="image_url"
										value={currentEvent?.image_url || ""}
										onChange={handleChange}
									/>
								</div>

								<div className="space-y-2">
									<Label htmlFor="image_url_banner">Banner Image URL</Label>
									<Input
										id="image_url_banner"
										name="image_url_banner"
										value={currentEvent?.image_url_banner || ""}
										onChange={handleChange}
									/>
								</div>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="description">Event Description</Label>
							<MarkdownEditor
								initialValue={currentEvent?.description || ""}
								placeholder="Enter detailed event description..."
								onChange={(markdown) =>
									setCurrentEvent((prev) => ({ ...prev!, description: markdown }))
								}
								height="200px"
							/>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-4">
								<div className="space-y-2">
									<Label>Companies</Label>
									<MultiSelect
										options={companies.map((c) => ({ value: c.id, label: c.name }))}
										selected={currentEvent?.company_ids || []}
										onChange={(values) => handleMultiSelectChange("company_ids", values)}
										placeholder="Select companies..."
									/>
								</div>

								<div className="space-y-2">
									<Label>Subnets</Label>
									<MultiSelect
										options={subnets.map((s) => ({ value: s.netuid, label: s.name }))}
										selected={currentEvent?.subnet_ids || []}
										onChange={(values) => handleMultiSelectChange("subnet_ids", values)}
										placeholder="Select subnets..."
									/>
								</div>
							</div>

							<div className="space-y-4">
								<div className="space-y-2">
									<Label>Categories</Label>
									<MultiSelect
										options={categories.map((c) => ({ value: c.id, label: c.name }))}
										selected={currentEvent?.category_ids || []}
										onChange={(values) => handleMultiSelectChange("category_ids", values)}
										placeholder="Select categories..."
									/>
								</div>

								<div className="space-y-2">
									<Label>Products</Label>
									<MultiSelect
										options={products.map((p) => ({ value: p.id, label: p.name }))}
										selected={currentEvent?.product_ids || []}
										onChange={(values) => handleMultiSelectChange("product_ids", values)}
										placeholder="Select products..."
									/>
								</div>
							</div>
						</div>

						<div className="space-y-4">
							<div className="space-y-2">
								<Label>Related Events</Label>
								<MultiSelect
									options={allEvents
										.filter((e) => !currentEvent?.id || e.id !== currentEvent.id)
										.map((e) => ({
											value: e.id,
											label: `${e.name} (${new Date(e.start_date).toLocaleDateString()})`,
										}))}
									selected={currentEvent?.event_ids || []}
									onChange={(values) => handleMultiSelectChange("event_ids", values)}
									placeholder="Select related events..."
								/>
							</div>

							<div className="space-y-2">
								<Label>About This Event</Label>
								<MarkdownEditor
									initialValue={currentEvent?.desc_about_this_event || ""}
									placeholder="Describe what this event is about..."
									onChange={(markdown) =>
										setCurrentEvent((prev) => ({ ...prev!, desc_about_this_event: markdown }))
									}
									height="150px"
								/>
							</div>

							<div className="space-y-2">
								<Label>What You'll Learn</Label>
								<MarkdownEditor
									initialValue={currentEvent?.desc_what_u_will_learn || ""}
									placeholder="Describe what attendees will learn..."
									onChange={(markdown) =>
										setCurrentEvent((prev) => ({ ...prev!, desc_what_u_will_learn: markdown }))
									}
									height="150px"
								/>
							</div>

							<div className="space-y-2">
								<Label>Who Should Attend</Label>
								<MarkdownEditor
									initialValue={currentEvent?.desc_who_should_attend || ""}
									placeholder="Describe the target audience..."
									onChange={(markdown) =>
										setCurrentEvent((prev) => ({ ...prev!, desc_who_should_attend: markdown }))
									}
									height="150px"
								/>
							</div>
						</div>
					</div>

					{error && <div className="text-red-500 text-sm">{error}</div>}

					<div className="flex justify-end space-x-2">
						<Button variant="outline" onClick={handleCloseDialog} disabled={isLoading}>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={isLoading}>
							{isLoading ? "Saving..." : "Save Event"}
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
