"use client";

import { CategoryTag } from "@/components/category-tag";
import { Card, CardContent } from "@/components/ui/card";
import type { Category, Company, Product } from "@/lib/db/models";
import { ArrowUpRight, Download, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface ProductCardProps {
	product: Product;
	categories: Category[];
	companies: Company[];
}

export function ProductCard({ product, categories, companies }: ProductCardProps) {
	return (
		<>
			<Link href={`/products/${product.id}`}>
				<Card className="h-full overflow-hidden hover:shadow-md transition-shadow group">
					<div className="h-2 bg-green-500" />
					<CardContent className="p-4 flex flex-col h-full">
						<div className="flex items-start gap-3">
							<div className="relative w-12 h-12 rounded-lg overflow-hidden bg-muted flex items-center justify-center">
								<Image
									src={product.logo_url || "/placeholder.svg"}
									alt={product.name}
									width={48}
									height={48}
									className="object-cover"
								/>
							</div>
							<div className="flex-1 min-w-0">
								<div className="mt-auto pb-2 flex items-center justify-between">
									<h3 className="font-medium group-hover:text-primary transition-colors">
										{product.name}
									</h3>
									<ArrowUpRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
								</div>
								<div className="flex items-center gap-1 text-sm text-muted-foreground">
									{product.company_id &&
										(() => {
											const company = companies.find(
												(company) => company.id === product.company_id
											);
											return company ? <div key={company.id}>{company.name}</div> : null;
										})()}
									{product.verified && (
										<svg
											xmlns="http://www.w3.org/2000/svg"
											viewBox="0 0 24 24"
											fill="currentColor"
											className="w-4 h-4 text-blue-500"
										>
											<path
												fillRule="evenodd"
												d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
												clipRule="evenodd"
											/>
										</svg>
									)}
								</div>
							</div>
						</div>
						<p className="mt-3 text-sm text-muted-foreground line-clamp-2">{product.description}</p>
						<div className="mt-4 flex flex-wrap gap-2">
							{product.category_ids?.map((catId) => {
								const category = categories.find((category) => category.id === catId);
								return (
									category && (
										<span key={category.id} className="m-1 p-1 mt-1">
											<CategoryTag category={category.name} />
										</span>
									)
								);
							})}
						</div>
						<div className="mt-auto pt-3 flex items-center justify-between text-sm">
							<div className="flex items-center gap-1">
								<Star className="h-4 w-4 text-amber-500" />
								<span>0</span>
							</div>
							<div className="flex items-center gap-1 text-muted-foreground">
								<Users className="h-4 w-4" />
								<span>0</span>
							</div>
							<div className="flex items-center gap-1 text-muted-foreground">
								<Download className="h-4 w-4" />
								<span>0</span>
							</div>
						</div>
					</CardContent>
				</Card>
			</Link>
		</>
	);
}
