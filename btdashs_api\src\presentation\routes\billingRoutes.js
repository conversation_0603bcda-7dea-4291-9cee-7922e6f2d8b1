const express = require("express");
const router = express.Router();
const checkJwt = require("../../middleware/auth");
const rateLimits = require("../../middleware/rateLimitMiddleware");
const checkInternalKey = require("../../middleware/internal");
const billingController = require("../controllers/billingController");

// Payment intent management
router.post("/payment-intent", checkJwt, rateLimits.userGeneral, billingController.storePaymentIntent);

// Payment processing webhooks (internal API calls)
router.post("/payment-success", checkInternalKey, rateLimits.ipStrict, billingController.handlePaymentSuccess);
router.post("/payment-failed", checkInternalKey, rateLimits.ipStrict, billingController.handlePaymentFailed);

// Campaign activation
router.post("/campaigns/:id/activate", checkInternalKey, rateLimits.ipStrict, billingController.activateCampaign);

// Payment retry
router.post("/campaigns/:id/retry-payment", checkJwt, rateLimits.userCampaigns, billingController.retryPayment);

module.exports = router;
