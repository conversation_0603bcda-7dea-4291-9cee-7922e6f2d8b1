// app/api/user/campaigns/[id]/targeting/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

// GET /api/user/campaigns/[id]/targeting - Get campaign targeting
export async function GET(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const campaignId = paramsData.id;

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${campaignId}/targeting`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error: any) {
		console.error("Error fetching campaign targeting:", error);
		return NextResponse.json(
			{
				success: false,
				message: error.message || "Failed to fetch campaign targeting",
			},
			{ status: 500 }
		);
	}
}

// POST /api/user/campaigns/[id]/targeting - Set campaign targeting
export async function POST(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const campaignId = paramsData.id;
		const body = await request.json();

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${campaignId}/targeting`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${token}`,
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error: any) {
		console.error("Error setting campaign targeting:", error);
		return NextResponse.json(
			{
				success: false,
				message: error.message || "Failed to set campaign targeting",
			},
			{ status: 500 }
		);
	}
}
