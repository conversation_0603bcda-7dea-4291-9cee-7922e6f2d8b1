// app/news/[id]/page.tsx
import { fetchWithFallback } from "@/lib/data/utils";
import { Category, Company, Event, News, Product, Subnet } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import ProductClient from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

interface Params {
	params: { id: string };
}

export async function generateMetadata({ params }: Params): Promise<Metadata> {
	const paramsData = await params;
	const id = Number(paramsData.id);
	const productRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/products/${id}`);
	const product = productRes.data;

	return generateSEOMetadata({
		title: `${product.name} | DynamicTaoMarketCap`,
		description: product.description || `Explore ${product.name} metrics, and market data.`,
		url: `${process.env.APP_BASE_URL}/products/${id}`,
		image: product.logo_url || `default-product-og.png`,
	});
}

export default async function ProductPage({ params }: Params) {
	const paramsData = await params;
	const id = Number(paramsData.id);

	const [productsRes, catRes, subRes, compRes, newsRes, eventsRes, jobs] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/news`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/jobs`),
	]);

	if (productsRes.error) console.error("News fetch error", productsRes.error);
	if (catRes.error) console.error("Categories fetch error", catRes.error);
	if (subRes.error) console.error("Subnet fetch error", subRes.error);
	if (compRes.error) console.error("Subnet metrics fetch error", compRes.error);
	if (newsRes.error) console.error("Subnet fetch error", newsRes.error);
	if (eventsRes.error) console.error("Events fetch error", eventsRes.error);
	if (jobs.error) console.error("Jobs fetch error", jobs.error);

	const product = productsRes.data?.find((product: Product) => product.id === id);

	// Filter subnets to only include those connected to the product
	const filteredSubnets = subRes.data?.filter((sub: Subnet) => product.subnet_ids?.includes(sub.netuid));

	// Filter categories to only include those connected to the product
	const filteredCategories = catRes.data?.filter((cat: Category) => product.category_ids?.includes(cat.id));

	// Filter companies to only include those connected to the product
	const filteredCompanies = compRes.data?.filter((comp: Company) => product.company_ids?.includes(comp.id));

	// Filter news to only include those connected to the product
	const filteredNews = newsRes.data?.filter((news: News) => product.news_ids?.includes(news.id));

	// Filter events to only include those connected to the product
	const filteredEvents = eventsRes.data?.filter((event: Event) => product.event_ids?.includes(event.id));

	// Filter related products to only include those connected to the product
	const filteredRelatedProducts = productsRes.data?.filter((prod: any) => product.product_ids?.includes(prod.id));

	// Filter jobs to only include those connected to the product
	const filteredJobs = jobs.data?.filter((job: any) => product.jobs_ids?.includes(job.id));

	return (
		<ProductClient
			product={product}
			subnets={filteredSubnets}
			companies={filteredCompanies}
			categories={filteredCategories}
			news={filteredNews}
			events={filteredEvents}
			relatedProducts={filteredRelatedProducts || null}
			jobs={filteredJobs}
		/>
	);
}
