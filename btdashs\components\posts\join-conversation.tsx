import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>ead<PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function JoinConversation() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Join the conversation</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Join the community of Bittensor developers, validators, and AI enthusiasts.
        </p>
        <Link href="/auth/sign-up">
          <Button className="w-full">Sign Up</Button>
        </Link>
      </CardContent>
    </Card>
  )
}

