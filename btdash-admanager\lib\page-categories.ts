// Define page types and their categories
export type PageType = "subnet" | "companies" | "products" | "news"

export interface PageCategory {
  id: string
  name: string
  description: string
}

export interface PageTypeWithCategories {
  type: PageType
  label: string
  description: string
  categories: PageCategory[]
}

// Mock data for page types and categories
export const pageTypesWithCategories: PageTypeWithCategories[] = [
  {
    type: "subnet",
    label: "Subnet Pages",
    description: "Pages related to network subnets and infrastructure",
    categories: [
      {
        id: "subnet-networking",
        name: "Networking",
        description: "Network infrastructure and management",
      },
      {
        id: "subnet-security",
        name: "Security",
        description: "Network security and protection",
      },
      {
        id: "subnet-cloud",
        name: "Cloud",
        description: "Cloud networking and infrastructure",
      },
      {
        id: "subnet-vpn",
        name: "VPN",
        description: "Virtual private networks",
      },
    ],
  },
  {
    type: "companies",
    label: "Company Pages",
    description: "Pages related to companies and organizations",
    categories: [
      {
        id: "companies-tech",
        name: "Technology",
        description: "Technology companies",
      },
      {
        id: "companies-finance",
        name: "Finance",
        description: "Financial institutions and services",
      },
      {
        id: "companies-healthcare",
        name: "Healthcare",
        description: "Healthcare providers and services",
      },
      {
        id: "companies-retail",
        name: "Retail",
        description: "Retail and e-commerce businesses",
      },
      {
        id: "companies-manufacturing",
        name: "Manufacturing",
        description: "Manufacturing and industrial companies",
      },
    ],
  },
  {
    type: "products",
    label: "Product Pages",
    description: "Pages related to products and services",
    categories: [
      {
        id: "products-hardware",
        name: "Hardware",
        description: "Computer hardware and components",
      },
      {
        id: "products-software",
        name: "Software",
        description: "Software applications and services",
      },
      {
        id: "products-services",
        name: "Services",
        description: "Professional services and consulting",
      },
      {
        id: "products-gadgets",
        name: "Gadgets",
        description: "Consumer electronics and gadgets",
      },
    ],
  },
  {
    type: "news",
    label: "News Pages",
    description: "Pages related to news and articles",
    categories: [
      {
        id: "news-tech",
        name: "Technology",
        description: "Technology news and updates",
      },
      {
        id: "news-business",
        name: "Business",
        description: "Business news and market updates",
      },
      {
        id: "news-security",
        name: "Security",
        description: "Security news and alerts",
      },
      {
        id: "news-industry",
        name: "Industry",
        description: "Industry trends and analysis",
      },
      {
        id: "news-events",
        name: "Events",
        description: "Industry events and conferences",
      },
    ],
  },
]

// Helper function to get all categories across all page types
export function getAllCategories(): PageCategory[] {
  return pageTypesWithCategories.flatMap((pageType) => pageType.categories)
}

// Helper function to get a category by ID
export function getCategoryById(categoryId: string): PageCategory | undefined {
  return getAllCategories().find((category) => category.id === categoryId)
}

// Helper function to get a page type by type
export function getPageTypeByType(type: PageType): PageTypeWithCategories | undefined {
  return pageTypesWithCategories.find((pageType) => pageType.type === type)
}

// Helper function to get categories by page type
export function getCategoriesByPageType(type: PageType): PageCategory[] {
  const pageType = getPageTypeByType(type)
  return pageType ? pageType.categories : []
}
