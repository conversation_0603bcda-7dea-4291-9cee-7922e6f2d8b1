import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		// Validate request body
		let body;
		try {
			body = await request.json();
		} catch (parseError) {
			return NextResponse.json({ success: false, message: "Invalid JSON in request body" }, { status: 400 });
		}

		const { ad_id, session_id, user_id, country_code, device_type } = body;

		// Validate required fields
		if (!ad_id || !session_id) {
			return NextResponse.json(
				{ success: false, message: "Missing required fields: ad_id and session_id" },
				{ status: 400 }
			);
		}

		// Validate field types
		if (typeof ad_id !== "number" || typeof session_id !== "string") {
			return NextResponse.json(
				{ success: false, message: "Invalid field types: ad_id must be number, session_id must be string" },
				{ status: 400 }
			);
		}

		const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;

		if (!API_BASE) {
			console.error("API_BASE_URL not configured");
			return NextResponse.json({ success: false, message: "Server configuration error" }, { status: 500 });
		}

		if (!process.env.INTERNAL_API_KEY) {
			console.error("INTERNAL_API_KEY not configured");
			return NextResponse.json({ success: false, message: "Server configuration error" }, { status: 500 });
		}

		// Forward to backend API with timeout
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), 5000);

		try {
			// Backend expects GET request with ad ID as URL parameter and other data as query params
			const params = new URLSearchParams({
				session_id,
				...(user_id && { user_id: user_id.toString() }),
				...(country_code && { country_code }),
				...(device_type && { device_type }),
			});

			const response = await fetch(`${API_BASE}/track/click/${ad_id}?${params}`, {
				method: "GET",
				headers: {
					"x-internal-api-key": process.env.INTERNAL_API_KEY!,
				},
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			if (response.ok) {
				const result = await response.json();
				return NextResponse.json(result);
			} else {
				const errorText = await response.text().catch(() => "Unknown error");
				console.error(`Backend API error ${response.status}: ${errorText}`);

				// Don't expose backend errors to client
				return NextResponse.json({ success: false, message: "Failed to track click" }, { status: 500 });
			}
		} finally {
			clearTimeout(timeoutId);
		}
	} catch (error) {
		console.error("Error tracking click:", error);

		if (error instanceof Error) {
			if (error.name === "AbortError") {
				return NextResponse.json({ success: false, message: "Request timeout" }, { status: 504 });
			}
		}

		return NextResponse.json({ success: false, message: "Failed to track click" }, { status: 500 });
	}
}
