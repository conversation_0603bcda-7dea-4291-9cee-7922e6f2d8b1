"use client";

import { CategoryTag } from "@/components/category-tag";
import { ProductsGrid } from "@/components/products/products-grid";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { Category, Company, Product } from "@/lib/db/models";
import { Download, Filter, Search, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export const revalidate = 600; // 10 minutes in seconds

interface ProductsClientWrapperProps {
	products: Product[];
	categories: Category[];
	companies: Company[];
}

export default function ProductsClientWrapper({ products, categories, companies }: ProductsClientWrapperProps) {
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<number | "All">("All");
	const [activeTab, setActiveTab] = useState("all");
	const [filteredApps, setFilteredApps] = useState<Product[]>(products);

	const featuredApps = products.filter((app) => app.featured);

	const relatedCategories: Category[] =
		products
			.flatMap((product) => product.category_ids || [])
			.map((catId) => categories.find((c) => c.id === catId))
			.filter((c): c is Category => Boolean(c)) || [];

	useEffect(() => {
		let filtered = products;

		if (searchQuery) {
			const q = searchQuery.toLowerCase();
			filtered = filtered.filter(
				(app) =>
					app.name.toLowerCase().includes(q) ||
					(app.description?.toLowerCase().includes(q) ?? false) ||
					categories.some((c) => c.name.toLowerCase().includes(q)) ||
					companies.some((c) => c.name.toLowerCase().includes(q))
			);
		}

		if (selectedCategory !== "All") {
			filtered = filtered.filter((app) => app.category_ids?.includes(selectedCategory));
		}

		if (activeTab === "featured") {
			filtered = filtered.filter((app) => app.featured);
		}

		setFilteredApps(filtered);
	}, [searchQuery, selectedCategory, activeTab, products, categories, companies]);

	return (
		<div className="container py-8">
			<div className="flex flex-col space-y-6">
				{/* Header */}
				<div className="flex flex-col space-y-2">
					<h1 className="text-3xl font-bold">Products</h1>
					<p className="text-muted-foreground">
						Discover products & applications built on the Bittensor network across various subnets
					</p>
				</div>

				{/* Search and filters */}
				<div className="flex flex-col sm:flex-row gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
						<Input
							placeholder="Search products..."
							className="pl-10"
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
						/>
					</div>
					<div className="flex gap-2">
						<Button variant="outline" className="gap-2">
							<Filter className="h-4 w-4" />
							<span className="hidden sm:inline">Filters</span>
						</Button>
					</div>
				</div>

				{/* Featured Product Banner */}
				{featuredApps.length > 0 && (
					<div className="mb-8">
						<h2 className="text-xl font-semibold mb-4">Featured Products</h2>
						<div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl overflow-hidden text-white">
							<div className="flex flex-col md:flex-row">
								<div className="p-6 md:p-8 flex-1">
									<div className="flex items-top gap-3 mb-4">
										<div className="relative w-16 h-16 rounded-lg overflow-hidden bg-white/20 p-2 flex items-center justify-center">
											<Image
												src={featuredApps[0].logo_url || "/placeholder.svg"}
												alt={featuredApps[0].name}
												width={56}
												height={56}
												className="object-contain"
											/>
										</div>
										<div>
											<h3 className="text-2xl font-bold">{featuredApps[0].name}</h3>
											<div className="flex items-center gap-1 text-sm text-muted-foreground">
												{featuredApps[0].company_id &&
													(() => {
														const company = companies.find(
															(c) => c.id === featuredApps[0].company_id
														);
														return company ? (
															<div
																className="text-white text-xl font-semibold"
																key={company.id}
															>
																{company.name}
															</div>
														) : null;
													})()}
												{featuredApps[0].verified && (
													<svg
														xmlns="http://www.w3.org/2000/svg"
														viewBox="0 0 24 24"
														fill="currentColor"
														className="w-4 h-4 text-green-500"
													>
														<path
															fillRule="evenodd"
															d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
															clipRule="evenodd"
														/>
													</svg>
												)}
											</div>
											<div className="mt-2 flex flex-wrap gap-2">
												{featuredApps[0].category_ids?.map((catId) => {
													const category = categories.find((c) => c.id === catId);
													return category ? (
														<span key={category.id}>
															<CategoryTag category={category.name} />
														</span>
													) : null;
												})}
											</div>
										</div>
									</div>
									<p className="mb-6 text-lg">{featuredApps[0].description}</p>
									<div className="flex flex-wrap gap-4 mb-6">
										<div className="flex items-center gap-1">
											<Star className="h-5 w-5 text-amber-300" />
											<span className="font-medium">0</span>
										</div>
										<div className="flex items-center gap-1">
											<Users className="h-5 w-5" />
											<span>0 users</span>
										</div>
										<div className="flex items-center gap-1">
											<Download className="h-5 w-5" />
											<span> downloads</span>
										</div>
									</div>
									<div className="flex gap-3">
										<Link href={`/products/${featuredApps[0].id}`}>
											<Button className="bg-white text-blue-600 hover:bg-white/90">
												View Details
											</Button>
										</Link>
									</div>
								</div>
								<div className="relative md:w-2/5 h-60 md:h-auto">
									<div className="absolute inset-0 bg-black/20" />
									<Image
										src={featuredApps[0].hero || "/placeholder.svg"}
										alt={`${featuredApps[0].name} Featured application`}
										className="object-cover"
										fill
									/>
								</div>
							</div>
						</div>
					</div>
				)}

				<div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
					{/* Categories sidebar */}
					<div className="space-y-6">
						<Card>
							<CardContent className="p-4 flex flex-col h-full">
								<h3 className="font-medium mb-3">Categories</h3>
								<div className="space-y-2">
									<Button
										variant={selectedCategory === "All" ? "default" : "ghost"}
										className="w-full justify-start"
										onClick={() => setSelectedCategory("All")}
									>
										All
									</Button>
									{relatedCategories.map((category) => (
										<Button
											key={category.id}
											variant={selectedCategory === category.id ? "default" : "ghost"}
											className="w-full justify-start"
											onClick={() => setSelectedCategory(category.id)}
										>
											{category.name}
										</Button>
									))}
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Products grid */}
					<div className="lg:col-span-3 space-y-6">
						<Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
							<TabsList className="mb-4">
								<TabsTrigger value="all">All Products</TabsTrigger>
								<TabsTrigger value="featured">Featured</TabsTrigger>
								<TabsTrigger value="new">New</TabsTrigger>
								<TabsTrigger value="popular">Popular</TabsTrigger>
							</TabsList>

							<TabsContent value="all" className="mt-0">
								<ProductsGrid
									products={filteredApps}
									categories={categories}
									companies={companies}
									emptyMessage="No products found matching your criteria."
								/>
							</TabsContent>

							<TabsContent value="featured" className="mt-0">
								<ProductsGrid
									products={filteredApps.filter((p) => p.featured)}
									categories={categories}
									companies={companies}
									emptyMessage="No featured products available."
								/>
							</TabsContent>

							<TabsContent value="new" className="mt-0">
								<div className="text-center py-12">
									<p className="text-muted-foreground">No new products available at the moment.</p>
								</div>
							</TabsContent>

							<TabsContent value="popular" className="mt-0">
								<div className="text-center py-12">
									<p className="text-muted-foreground">Popular products will be available soon.</p>
								</div>
							</TabsContent>
						</Tabs>
					</div>
				</div>
			</div>
		</div>
	);
}
