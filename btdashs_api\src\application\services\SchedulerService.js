const cron = require("node-cron");
const ReportingService = require("./ReportingService");
const NotificationService = require("./NotificationService");
const NetworkUpdateService = require("./NetworkUpdateService");
const logger = require("../../../logger");
const db = require("../../infrastructure/database/knex");

class SchedulerService {
	constructor() {
		this.jobs = new Map();
		this.reportingService = new ReportingService();
		this.notificationService = new NotificationService();
		this.networkUpdateService = new NetworkUpdateService();
	}

	/**
	 * Initialize all scheduled jobs
	 */
	initializeJobs() {
		try {
			// Daily report generation - runs at 2 AM every day
			this.scheduleJob("daily-reports", "0 2 * * *", async () => {
				await this.runDailyReports();
			});

			// Weekly report generation - runs at 3 AM every Monday
			this.scheduleJob("weekly-reports", "0 3 * * 1", async () => {
				await this.runWeeklyReports();
			});

			// Monthly report generation - runs at 4 AM on the 1st of each month
			this.scheduleJob("monthly-reports", "0 4 1 * *", async () => {
				await this.runMonthlyReports();
			});

			// Cleanup old reports - runs at 1 AM every Sunday
			this.scheduleJob("cleanup-reports", "0 1 * * 0", async () => {
				await this.runReportCleanup();
			});

			// Cleanup old notifications - runs at 1:30 AM every Sunday
			this.scheduleJob("cleanup-notifications", "30 1 * * 0", async () => {
				await this.runNotificationCleanup();
			});

			// Check low balances - runs every hour
			this.scheduleJob("check-low-balances", "0 * * * *", async () => {
				await this.checkLowBalances();
			});

			// Health check - runs every 5 minutes
			this.scheduleJob("health-check", "*/5 * * * *", async () => {
				await this.runHealthCheck();
			});

			// Network update jobs (only if enabled)
			if (process.env.NETWORK_UPDATES_ENABLED === "true") {
				// TAO data update - runs every hour
				this.scheduleJob("tao-update", "0 * * * *", async () => {
					await this.runTaoUpdate();
				});

				// GitHub data update - runs daily at midnight
				this.scheduleJob("github-update", "0 0 * * *", async () => {
					await this.runGithubUpdate();
				});

				// Comprehensive network update - DISABLED for now
				// Reason: TAO update already includes network stats and validator updates
				// GitHub updates are intentionally daily to save costs
				// this.scheduleJob("network-comprehensive-update", "0 */6 * * *", async () => {
				// 	await this.runComprehensiveNetworkUpdate();
				// });

				logger.info("Network update jobs enabled and scheduled");
			} else {
				logger.info("Network update jobs disabled (NETWORK_UPDATES_ENABLED != true)");
			}

			logger.info("Scheduler service initialized with all jobs");
		} catch (error) {
			logger.error("Error initializing scheduler jobs", { error });
		}
	}

	/**
	 * Schedule a new job
	 * @param {string} name - Job name
	 * @param {string} schedule - Cron schedule
	 * @param {Function} task - Task function to execute
	 * @param {Object} options - Additional options
	 */
	scheduleJob(name, schedule, task, options = {}) {
		try {
			const { timezone = "UTC", runOnInit = false } = options;

			const job = cron.schedule(
				schedule,
				async () => {
					const startTime = Date.now();
					logger.info("Starting scheduled job", { jobName: name });

					try {
						await task();
						const duration = Date.now() - startTime;
						logger.info("Scheduled job completed successfully", {
							jobName: name,
							duration: `${duration}ms`,
						});
					} catch (error) {
						const duration = Date.now() - startTime;
						logger.error("Scheduled job failed", {
							jobName: name,
							error,
							duration: `${duration}ms`,
						});
					}
				},
				{
					scheduled: true,
					timezone,
				}
			);

			this.jobs.set(name, job);

			if (runOnInit) {
				// Run the task immediately
				setTimeout(() => task(), 1000);
			}

			logger.info("Job scheduled successfully", { name, schedule, timezone });
		} catch (error) {
			logger.error("Error scheduling job", { error, name, schedule });
		}
	}

	/**
	 * Run daily reports generation
	 */
	async runDailyReports() {
		try {
			logger.info("Starting daily reports generation");

			const results = await this.reportingService.generateDailyReports();

			// Send notification to admins if there were errors
			if (results.errors.length > 0) {
				await this.notifyAdmins(
					"Daily Reports - Errors Detected",
					`Daily report generation completed with ${results.errors.length} errors. Please check the logs.`,
					{ results }
				);
			}

			logger.info("Daily reports generation completed", results);
		} catch (error) {
			logger.error("Error in daily reports generation", { error });
			await this.notifyAdmins(
				"Daily Reports - Critical Error",
				"Daily report generation failed completely. Please investigate immediately.",
				{ error: error.message }
			);
		}
	}

	/**
	 * Run weekly reports generation
	 */
	async runWeeklyReports() {
		try {
			logger.info("Starting weekly reports generation");

			const results = await this.reportingService.generateWeeklyReports();

			logger.info("Weekly reports generation completed", results);
		} catch (error) {
			logger.error("Error in weekly reports generation", { error });
			await this.notifyAdmins(
				"Weekly Reports - Error",
				"Weekly report generation failed. Please check the logs.",
				{ error: error.message }
			);
		}
	}

	/**
	 * Run monthly reports generation
	 */
	async runMonthlyReports() {
		try {
			logger.info("Starting monthly reports generation");

			const results = await this.reportingService.generateMonthlyReports();

			// Send monthly summary to all admins
			await this.notifyAdmins(
				"Monthly Reports Generated",
				`Monthly reports have been generated. Total campaigns: ${
					results.campaigns
				}, Total spend: $${results.total_spend.toFixed(2)}`,
				{ results }
			);

			logger.info("Monthly reports generation completed", results);
		} catch (error) {
			logger.error("Error in monthly reports generation", { error });
			await this.notifyAdmins(
				"Monthly Reports - Error",
				"Monthly report generation failed. Please check the logs.",
				{ error: error.message }
			);
		}
	}

	/**
	 * Run report cleanup
	 */
	async runReportCleanup() {
		try {
			logger.info("Starting report cleanup");

			const results = await this.reportingService.cleanupOldReports();

			logger.info("Report cleanup completed", results);
		} catch (error) {
			logger.error("Error in report cleanup", { error });
		}
	}

	/**
	 * Run notification cleanup
	 */
	async runNotificationCleanup() {
		try {
			logger.info("Starting notification cleanup");

			const results = await this.notificationService.cleanupOldNotifications();

			logger.info("Notification cleanup completed", { deletedCount: results });
		} catch (error) {
			logger.error("Error in notification cleanup", { error });
		}
	}

	/**
	 * Check for low balances and send alerts
	 */
	async checkLowBalances() {
		try {
			const lowBalanceThreshold = 10; // $10

			const lowBalanceUsers = await db("dtm_ads.advertiser_balances")
				.leftJoin("dtm_base.users", "advertiser_balances.user_id", "users.id")
				.where("advertiser_balances.balance", "<", lowBalanceThreshold)
				.select("advertiser_balances.user_id", "advertiser_balances.balance", "users.name", "users.email");

			for (const user of lowBalanceUsers) {
				// Check if we've already sent a low balance notification recently (within 24 hours)
				const recentNotification = await db("dtm_ads.ad_notifications")
					.where({
						user_id: user.user_id,
						type: "low_balance",
					})
					.where("created_at", ">", new Date(Date.now() - 24 * 60 * 60 * 1000))
					.first();

				if (!recentNotification) {
					await this.notificationService.sendNotification({
						user_id: user.user_id,
						type: "low_balance",
						title: "Low Balance Alert",
						message: `Your account balance is low ($${user.balance.toFixed(
							2
						)}). Please add funds to continue running campaigns.`,
						priority: "high",
						email: {
							enabled: true,
							subject: "Low Balance Alert - BTDash",
							template: "low_balance",
							templateData: {
								userName: user.name,
								balance: user.balance.toFixed(2),
							},
						},
					});

					logger.info("Low balance notification sent", {
						userId: user.user_id,
						balance: user.balance,
					});
				}
			}

			if (lowBalanceUsers.length > 0) {
				logger.info("Low balance check completed", {
					usersWithLowBalance: lowBalanceUsers.length,
				});
			}
		} catch (error) {
			logger.error("Error checking low balances", { error });
		}
	}

	/**
	 * Run health check
	 */
	async runHealthCheck() {
		try {
			// Check database connectivity
			await db.raw("SELECT 1");

			// Check Redis connectivity (only if enabled)
			if (process.env.REDIS_ENABLED !== "false") {
				const redisClient = require("../../infrastructure/redis/redisClient");
				if (redisClient && redisClient.isConnected) {
					await redisClient.ping();
				}
			}

			// Log health status every hour (12 * 5 minutes)
			const now = new Date();
			if (now.getMinutes() === 0) {
				logger.info("System health check passed", { timestamp: now.toISOString() });
			}
		} catch (error) {
			logger.error("Health check failed", { error });

			// Send critical alert to admins
			await this.notifyAdmins(
				"System Health Alert",
				"System health check failed. Please investigate immediately.",
				{ error: error.message, timestamp: new Date().toISOString() }
			);
		}
	}

	/**
	 * Run TAO data update
	 */
	async runTaoUpdate() {
		try {
			logger.info("Starting scheduled TAO data update");

			const result = await this.networkUpdateService.updateAllTao();

			if (!result.success) {
				await this.notifyAdmins(
					"TAO Update Failed",
					"Scheduled TAO data update failed. Please check the logs.",
					{ result }
				);
			}

			logger.info("Scheduled TAO data update completed", result);
		} catch (error) {
			logger.error("Error in scheduled TAO data update", { error });
			await this.notifyAdmins(
				"TAO Update Error",
				"Scheduled TAO data update encountered an error. Please investigate.",
				{ error: error.message }
			);
		}
	}

	/**
	 * Run GitHub data update
	 */
	async runGithubUpdate() {
		try {
			logger.info("Starting scheduled GitHub data update");

			const result = await this.networkUpdateService.updateGithubData();

			if (!result.success) {
				await this.notifyAdmins(
					"GitHub Update Failed",
					"Scheduled GitHub data update failed. Please check the logs.",
					{ result }
				);
			}

			logger.info("Scheduled GitHub data update completed", result);
		} catch (error) {
			logger.error("Error in scheduled GitHub data update", { error });
			await this.notifyAdmins(
				"GitHub Update Error",
				"Scheduled GitHub data update encountered an error. Please investigate.",
				{ error: error.message }
			);
		}
	}

	/**
	 * Run comprehensive network data update
	 */
	async runComprehensiveNetworkUpdate() {
		try {
			logger.info("Starting scheduled comprehensive network data update");

			const result = await this.networkUpdateService.updateAllNetworkData();

			if (!result.success || result.successRate < 0.5) {
				await this.notifyAdmins(
					"Network Update Issues",
					`Comprehensive network update completed with ${(result.successRate * 100).toFixed(
						1
					)}% success rate. Please review.`,
					{ result }
				);
			}

			logger.info("Scheduled comprehensive network data update completed", {
				success: result.success,
				successRate: result.successRate,
			});
		} catch (error) {
			logger.error("Error in scheduled comprehensive network data update", { error });
			await this.notifyAdmins(
				"Network Update Error",
				"Scheduled comprehensive network data update encountered an error. Please investigate.",
				{ error: error.message }
			);
		}
	}

	/**
	 * Send notification to all admin users
	 * @param {string} title - Notification title
	 * @param {string} message - Notification message
	 * @param {Object} metadata - Additional metadata
	 */
	async notifyAdmins(title, message, metadata = {}) {
		try {
			const UserService = require("./UserService");
			// Get admin users from admin_users table
			const admins = await UserService.getAdminUsers();

			for (const admin of admins) {
				await this.notificationService.sendNotification({
					user_id: admin.id,
					type: "system_alert",
					title,
					message,
					metadata,
					priority: "high",
					email: {
						enabled: true,
						subject: `[BTDash Admin] ${title}`,
						template: "default",
						templateData: {
							userName: admin.name,
							title,
							message,
						},
					},
				});
			}

			logger.info("Admin notifications sent", {
				title,
				adminCount: admins.length,
			});
		} catch (error) {
			logger.error("Error sending admin notifications", { error, title });
		}
	}

	/**
	 * Stop a specific job
	 * @param {string} name - Job name
	 */
	stopJob(name) {
		try {
			const job = this.jobs.get(name);
			if (job) {
				job.stop();
				this.jobs.delete(name);
				logger.info("Job stopped", { name });
				return true;
			} else {
				logger.warn("Job not found", { name });
				return false;
			}
		} catch (error) {
			logger.error("Error stopping job", { error, name });
			return false;
		}
	}

	/**
	 * Stop all jobs
	 */
	stopAllJobs() {
		try {
			for (const [name, job] of this.jobs) {
				job.stop();
			}
			this.jobs.clear();
			logger.info("All scheduled jobs stopped");
		} catch (error) {
			logger.error("Error stopping all jobs", { error });
		}
	}

	/**
	 * Get status of all jobs
	 * @returns {Array} Job statuses
	 */
	getJobStatuses() {
		const statuses = [];
		for (const [name, job] of this.jobs) {
			statuses.push({
				name,
				running: job.running || false,
				scheduled: true,
			});
		}
		return statuses;
	}
}

module.exports = SchedulerService;
