"use client";

import { Book, Github, Globe } from "lucide-react";
import Image from "next/image";
/* import { useRef, useState } from "react"; */
import { Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

import { CategoryTag } from "@/components/category-tag";
import { SubnetDocumentation } from "@/components/subnet-documentation";
import { SubnetGithubContributionGraph } from "@/components/subnet-github-contribution-graph";
import { SubnetNews } from "@/components/subnet-news";
import { SubnetTeam } from "@/components/subnet-team";
import { SubnetValidators } from "@/components/subnet-validators";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import tao from "@/public/tao-logo.svg";

import type { Category, Company, Event, Job, News, Product, Subnet, SubnetMetric } from "@/lib/db/models";
// Import the new chart component
import { SubnetRelationshipChart } from "@/components/subnets/subnet-relationship-chart";
import ReactMarkdown from "react-markdown";
import { SubnetApplications } from "../subnet-applications";

interface KeyFeature {
	title: string;
	description: string;
}

interface SubnetProfileProps {
	subnet: Subnet;
	metrics: SubnetMetric;
	categories: Category[];
	news: News[];
	products: Product[];
	jobs: Job[];
	events: Event[];
	companies: Company[];
}

export default function SubnetProfile({
	subnet,
	metrics,
	categories,
	news,
	products,
	jobs,
	events,
	companies,
}: SubnetProfileProps) {
	/* const [imageError, setImageError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false); */

	const netuid = subnet.netuid;
	/* const images = subnet.images?.length
    ? subnet.images
    : [
        "https://via.placeholder.com/800x400?text=Image+1",
        "https://via.placeholder.com/800x400?text=Image+2",
        "https://via.placeholder.com/800x400?text=Image+3",
      ]; */

	const data = {
		companyCount: (companies?.length ?? 0) as number,
		productCount: (products?.length ?? 0) as number,
		eventCount: (events?.length ?? 0) as number,
		jobCount: (jobs?.length ?? 0) as number,
		categoryCount: (categories?.length ?? 0) as number,
		validatorCount: (metrics?.validators_count ?? 0) as number,
		newsCount: (news.length ?? 0) as number,
		subnetCount: (subnet?.subnet_ids?.length ?? 0) as number,
	};
	const metricsCards = (
		<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
			<Card>
				<CardHeader>
					<CardTitle>Price</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-3xl font-bold">
						<Image src={tao} alt="TAO" width={24} height={24} className="inline-block pr-2" />
						{metrics?.alpha_price_tao != null && !isNaN(metrics.alpha_price_tao)
							? Number(metrics.alpha_price_tao) < 0.01
								? Number(metrics.alpha_price_tao).toFixed(3)
								: Number(metrics.alpha_price_tao).toFixed(2)
							: "0.00"}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Validators</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-3xl font-bold">{metrics?.validators_count ?? 0}</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Emission</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-3xl font-bold">{((metrics?.emission ?? 0) / 1e7).toFixed(2)}%</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Miners</CardTitle>
				</CardHeader>
				<CardContent>
					<div
						className={`text-3xl font-bold ${
							subnet.active_miners <= 5
								? "text-red-500"
								: subnet.active_miners <= 15
								? "text-orange-500"
								: "text-green-500"
						}`}
					>
						{subnet.active_miners || 0}
					</div>
				</CardContent>
			</Card>
		</div>
	);

	return (
		<>
			<div className="py-8 px-6 sm:px-8 lg:px-12">
				{/* Top Horizontal Ad */}
				<div className="w-full mb-8" style={{ minHeight: "90px" }}>
					<SmartAdBanner slotId={7} className="mx-auto" />
				</div>

				{/* Hero Section */}
				<div className="mb-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
					<div className="lg:col-span-2">
						<div className="flex items-center gap-4 mb-4">
							<div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold">
								{subnet.subnet_symbol || subnet.name.charAt(0)}
							</div>

							<div>
								<h1 className="text-3xl font-bold">{subnet.name}</h1>
								<p className="text-muted-foreground">Subnet ID: {netuid}</p>
								<p className="text-xs text-muted-foreground">
									Coldkey:
									<a
										href={`https://taostats.io/account/${subnet.sub_address_pkey}/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile`}
										target="_blank"
										rel="noopener noreferrer"
										className="text-xs text-muted-foreground underline hover:text-primary"
									>
										{subnet.sub_address_pkey}
									</a>
								</p>
								<div className="mt-3 flex flex-wrap gap-2 text-muted-foreground">
									{categories && categories.length > 0
										? categories.map((category, id) => (
												<CategoryTag key={id} category={category.name} />
										  ))
										: null}
								</div>
							</div>
						</div>

						<div className="my-4">
							<ReactMarkdown>{subnet.description_short}</ReactMarkdown>
						</div>

						<div className="flex gap-4 mb-8">
							{subnet.white_paper ? (
								<Button asChild size="sm" className="gap-2" variant="default">
									<a href={subnet.white_paper} target="_blank" rel="noopener noreferrer">
										<Book className="h-5 w-5" />
										Read White Paper
									</a>
								</Button>
							) : (
								<Button size="sm" className="gap-2" disabled>
									<Book className="h-5 w-5" />
									White Paper Unavailable
								</Button>
							)}
						</div>
						{subnet.images || subnet.main_video_url ? (
							<div className="flex flex-wrap gap-2 overflow-hidden">{metricsCards}</div>
						) : null}
					</div>

					{/* Media: video or image carousel */}
					{subnet.main_video_url ? (
						<div>
							<div className="relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700">
								<iframe
									className="absolute left-0 w-full h-full"
									src={subnet.main_video_url}
									title="Subnet video"
									frameBorder="0"
									allow="autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
									allowFullScreen
								/>
							</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
								<a
									href={subnet.website_perm || "https://subnet-website.example.com"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
										<Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
											Official Website
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.website_perm
												? `${subnet.website_perm.replace("https://www.", "").slice(0, 30)}${
														subnet.website_perm.replace("https://www.", "").length > 40
															? "..."
															: ""
												  }`
												: "subnet-website.example.com"}
										</p>
									</div>
								</a>

								<a
									href={subnet.github_repo || "https://github.com/example/subnet-repo"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
										<Github className="h-6 w-6 text-purple-600 dark:text-purple-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
											GitHub Repository
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.github_repo
												? `${subnet.github_repo
														.replace("https://github.com/", "")
														.slice(0, 30)}${
														subnet.github_repo.replace("https://github.com/", "").length >
														40
															? "..."
															: ""
												  }`
												: "github.com/example/subnet-repo"}
										</p>
									</div>
								</a>
							</div>
						</div>
					) : !subnet.main_video_url ? (
						<div>
							{subnet.images ? null : <div className="gap-2 overflow-hidden">{metricsCards}</div>}
							<div className="rounded-lg overflow-hidden shadow-lg">
								<Swiper
									modules={[Navigation, Pagination]}
									navigation
									pagination={{ clickable: true }}
									spaceBetween={10}
									slidesPerView={1}
									className="w-full h-full"
								>
									{subnet.images?.map((image: string, index: number) => (
										<SwiperSlide key={index}>
											<Image
												src={image}
												alt={`Subnet Image ${index + 1}`}
												width={800}
												height={400}
												className="w-full h-auto object-cover"
											/>
										</SwiperSlide>
									))}
								</Swiper>
							</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
								<a
									href={subnet.website_perm || "https://subnet-website.example.com"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
										<Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
											Official Website
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.website_perm
												? `${subnet.website_perm.replace("https://www.", "").slice(0, 30)}${
														subnet.website_perm.replace("https://www.", "").length > 40
															? "..."
															: ""
												  }`
												: "subnet-website.example.com"}
										</p>
									</div>
								</a>

								<a
									href={subnet.github_repo || "https://github.com/example/subnet-repo"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
										<Github className="h-6 w-6 text-purple-600 dark:text-purple-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
											GitHub Repository
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.github_repo
												? `${subnet.github_repo
														.replace("https://github.com/", "")
														.slice(0, 30)}${
														subnet.github_repo.replace("https://github.com/", "").length >
														40
															? "..."
															: ""
												  }`
												: "github.com/example/subnet-repo"}
										</p>
									</div>
								</a>
							</div>
						</div>
					) : (
						<div>
							<div className=" gap-4 overflow-hidden max-[60px]">{metricsCards}</div>
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
								<a
									href={subnet.website_perm || "https://subnet-website.example.com"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
										<Globe className="h-6 w-6 text-blue-600 dark:text-blue-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
											Official Website
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.website_perm
												? `${subnet.website_perm.replace("https://www.", "").slice(0, 30)}${
														subnet.website_perm.replace("https://www.", "").length > 40
															? "..."
															: ""
												  }`
												: "subnet-website.example.com"}
										</p>
									</div>
								</a>

								<a
									href={subnet.github_repo || "https://github.com/example/subnet-repo"}
									target="_blank"
									rel="noopener noreferrer"
									className="group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200"
								>
									<div className="flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
										<Github className="h-6 w-6 text-purple-600 dark:text-purple-400" />
									</div>
									<div className="flex-grow">
										<h3 className="font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
											GitHub Repository
										</h3>
										<p className="text-sm text-slate-500 dark:text-slate-400 truncate">
											{subnet.github_repo
												? `${subnet.github_repo
														.replace("https://github.com/", "")
														.slice(0, 30)}${
														subnet.github_repo.replace("https://github.com/", "").length >
														40
															? "..."
															: ""
												  }`
												: "github.com/example/subnet-repo"}
										</p>
									</div>
								</a>
							</div>
						</div>
					)}

					{/* Sidebar Ad */}
					<div className="flex justify-center">
						<SmartAdBanner slotId={9} />
					</div>
				</div>

				{/* Ecosystem Overview Section */}
				<div className="grid grid-cols-7 gap-4 mb-4">
					{/* GitHub Contributions – 3/4 width */}
					<div className="col-span-5">
						<SubnetGithubContributionGraph
							className="h-[360px]"
							contributions={metrics?.github_contributions?.data || []}
						/>
					</div>

					{/* Relationship Chart – 1/4 width */}
					<div className="h-[360px] col-span-2 overflow-visible">
						<SubnetRelationshipChart subnetId={subnet.name} data={data} className="h-full" />
					</div>
				</div>

				{/* Applications Section */}
				<div className="mt-0 mb-8">
					<SubnetApplications products={products} />
				</div>

				{/* Banner Ad */}
				<div className="w-full mb-8 flex justify-center">
					<SmartAdBanner slotId={10} className="mx-auto" />
				</div>

				{/* Tabs */}
				<Tabs defaultValue="overview" className="space-y-4">
					<TabsList className="flex flex-wrap">
						<TabsTrigger value="overview">Overview</TabsTrigger>
						<TabsTrigger value="team">Team</TabsTrigger>
						<TabsTrigger value="documentation">Documentation</TabsTrigger>
						<TabsTrigger value="validators">Validators</TabsTrigger>
						<TabsTrigger value="news">News</TabsTrigger>
					</TabsList>

					<div className="grid grid-cols-1 lg:grid-cols-1 gap-8">
						<div className="lg:col-span-3">
							<TabsContent value="overview" className="space-y-8">
								<Card>
									<CardHeader>
										<CardTitle className="text-lg">Key Features</CardTitle>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="grid md:grid-cols-2 gap-4">
											{subnet.key_features && subnet.key_features.length > 0 ? (
												subnet.key_features[0].map(
													(feature: KeyFeature, id: number) =>
														feature && (
															<div className="space-y-2 pb-4" key={id}>
																<h4 className="font-medium">{feature.title}</h4>
																<p className="text-muted-foreground">
																	{feature.description}
																</p>
															</div>
														)
												)
											) : (
												<p className="text-muted-foreground">No key features available.</p>
											)}
										</div>
									</CardContent>
								</Card>
							</TabsContent>

							<TabsContent value="team">
								<SubnetTeam subnet={subnet} />
							</TabsContent>
							<TabsContent value="documentation">
								<SubnetDocumentation />
							</TabsContent>
							<TabsContent value="validators">
								<SubnetValidators />
							</TabsContent>
							<TabsContent value="news">
								<SubnetNews news={news} />
							</TabsContent>
						</div>
					</div>
				</Tabs>

				{/* Bottom Horizontal Ad */}
				<div className="flex-1 mt-8 mb-8 min-h-[90px] w-full">
					<SmartAdBanner slotId={8} className="w-full h-full" />
				</div>
			</div>
		</>
	);
}
