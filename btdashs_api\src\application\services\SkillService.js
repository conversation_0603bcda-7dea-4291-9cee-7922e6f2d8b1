// src/application/services/SkillService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * Skill Service - Handles user skills business logic
 */
class SkillService extends BaseService {
	constructor() {
		super("dtm_base.user_skills", "Skill");
	}

	/**
	 * Create a skill for a user
	 * @param {number} userId - User ID
	 * @param {Object} skillData - Skill data
	 * @returns {Promise<Object>} Created skill object
	 */
	async createUserSkill(userId, skillData) {
		const { skill_id, endorsements, level } = skillData;

		try {
			return await this.create({
				user_id: userId,
				skill_id,
				endorsements,
				level,
			});
		} catch (error) {
			logger.error("Error creating user skill", { error, userId });
			throw error;
		}
	}

	/**
	 * Get all skills for a user
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of skills
	 */
	async getUserSkills(userId) {
		try {
			return await this.getByUserId(userId);
		} catch (error) {
			logger.error("Error getting user skills", { error, userId });
			throw error;
		}
	}

	/**
	 * Update a skill
	 * @param {number} skillId - Skill ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated skill object
	 */
	async updateUserSkill(skillId, updateData) {
		const { endorsements, level } = updateData;

		try {
			return await this.updateById(skillId, {
				endorsements,
				level,
			});
		} catch (error) {
			logger.error("Error updating user skill", { error, skillId });
			throw error;
		}
	}

	/**
	 * Delete a skill
	 * @param {number} skillId - Skill ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteUserSkill(skillId) {
		try {
			return await this.deleteById(skillId);
		} catch (error) {
			logger.error("Error deleting user skill", { error, skillId });
			throw error;
		}
	}
}

module.exports = new SkillService();
