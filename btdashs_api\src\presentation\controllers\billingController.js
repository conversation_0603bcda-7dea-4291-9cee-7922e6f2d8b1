const { asyncHandler } = require("../../middleware/errorHandler");
const BudgetService = require("../../application/services/BudgetService");
const AdminService = require("../../application/services/AdminService");
const { sendSuccess, sendError, sendUnauthorized } = require("../../utils/responseWrapper");
const logger = require("../../../logger");
const db = require("../../infrastructure/database/knex");

/**
 * Store payment intent in database for tracking
 */
const storePaymentIntent = asyncHandler(async (req, res) => {
	const { payment_intent_id, campaign_id, amount, status, currency } = req.body;

	if (!payment_intent_id || !campaign_id || !amount) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Get campaign details to find advertiser
		const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaign_id }).first();

		if (!campaign) {
			return sendError(res, "Campaign not found", null, 404);
		}

		// Create billing transaction record
		const budgetService = new BudgetService();
		const transaction = await db("dtm_ads.billing_transactions")
			.insert({
				advertiser_id: campaign.advertiser_id,
				amount: amount,
				currency: currency || "USD",
				description: `Campaign payment for "${campaign.name}"`,
				status: status || "pending",
				payment_method: "stripe",
				campaign_id: campaign_id,
				invoice_id: payment_intent_id, // Store payment intent ID as invoice ID
				created_at: new Date(),
				updated_at: new Date(),
			})
			.returning("*");

		logger.info("Payment intent stored", {
			payment_intent_id,
			campaign_id,
			amount,
			transaction_id: transaction[0]?.id,
		});

		return sendSuccess(res, transaction[0], "Payment intent stored successfully");
	} catch (error) {
		logger.error("Error storing payment intent", { error, payment_intent_id, campaign_id });
		return sendError(res, "Failed to store payment intent", error.message, 500);
	}
});

/**
 * Handle successful payment and activate campaign
 */
const handlePaymentSuccess = asyncHandler(async (req, res) => {
	const { payment_intent_id, campaign_id, amount, status, payment_method } = req.body;

	if (!payment_intent_id || !campaign_id) {
		return sendError(res, "Missing required fields", null, 400);
	}

	const trx = await db.transaction();

	try {
		// Update billing transaction status
		await trx("dtm_ads.billing_transactions")
			.where({
				invoice_id: payment_intent_id,
				campaign_id: campaign_id,
			})
			.update({
				status: "completed",
				payment_method: payment_method || "stripe",
				updated_at: new Date(),
			});

		// Get campaign details
		const campaign = await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).first();

		if (!campaign) {
			throw new Error("Campaign not found");
		}

		// Add funds to advertiser balance
		const budgetService = new BudgetService();
		await budgetService.addFunds(
			campaign.advertiser_id,
			amount,
			`Campaign payment for "${campaign.name}"`,
			"stripe"
		);

		// Update campaign status to active (payment successful)
		await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).update({
			status: "active",
			updated_at: new Date(),
		});

		// Also activate all ads in this campaign
		await trx("dtm_ads.ads").where({ campaign_id: campaign_id, status: "pending" }).update({
			status: "active",
			updated_at: new Date(),
		});

		await trx.commit();

		logger.info("Payment processed successfully", {
			payment_intent_id,
			campaign_id,
			amount,
			advertiser_id: campaign.advertiser_id,
		});

		return sendSuccess(res, { campaign_id, status: "active" }, "Payment processed and campaign activated");
	} catch (error) {
		await trx.rollback();
		logger.error("Error processing payment success", { error, payment_intent_id, campaign_id });
		return sendError(res, "Failed to process payment", error.message, 500);
	}
});

/**
 * Handle failed payment
 */
const handlePaymentFailed = asyncHandler(async (req, res) => {
	const { payment_intent_id, campaign_id, status, failure_reason } = req.body;

	if (!payment_intent_id || !campaign_id) {
		return sendError(res, "Missing required fields", null, 400);
	}

	const trx = await db.transaction();

	try {
		// Get campaign details for user notification
		const campaign = await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).first();

		if (!campaign) {
			await trx.rollback();
			return sendError(res, "Campaign not found", null, 404);
		}

		// Update billing transaction status
		await trx("dtm_ads.billing_transactions")
			.where({
				invoice_id: payment_intent_id,
				campaign_id: campaign_id,
			})
			.update({
				status: status === "processing_error" ? "processing_error" : "failed",
				description: db.raw("description || ' - ' || ?", [failure_reason || "Payment failed"]),
				updated_at: new Date(),
			});

		// Update campaign status based on failure type
		const newStatus = status === "processing_error" ? "payment_error" : "payment_failed";
		await trx("dtm_ads.ad_campaigns").where({ id: campaign_id }).update({
			status: newStatus,
			updated_at: new Date(),
		});

		// Send user notification about payment failure
		const NotificationService = require("../../application/services/NotificationService");
		const notificationService = new NotificationService();

		const isProcessingError = status === "processing_error";
		await notificationService.sendNotification({
			user_id: campaign.advertiser_id,
			type: "payment_failure",
			title: isProcessingError ? "Payment Processing Error" : "Payment Failed",
			message: isProcessingError
				? `There was an error processing payment for your campaign "${campaign.name}". Our team has been notified and will resolve this issue. You may retry payment or contact support.`
				: `Payment failed for your campaign "${campaign.name}". Reason: ${
						failure_reason || "Payment declined"
				  }. Please update your payment method and try again.`,
			related_id: campaign_id,
			metadata: {
				payment_intent_id,
				failure_reason,
				retry_available: true,
				support_contact: true,
			},
			email: {
				enabled: true,
				subject: isProcessingError ? "Payment Processing Error - BTDash" : "Payment Failed - BTDash",
				template: isProcessingError ? "payment_processing_error" : "payment_failed",
				templateData: {
					campaignName: campaign.name,
					failureReason: failure_reason,
					supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
					retryUrl: `${process.env.FRONTEND_URL}/dashboard/campaigns/${campaign_id}/payment`,
				},
			},
		});

		await trx.commit();

		logger.info("Payment failure processed with user notification", {
			payment_intent_id,
			campaign_id,
			failure_reason,
			status: newStatus,
		});

		return sendSuccess(
			res,
			{
				campaign_id,
				status: newStatus,
				retry_available: true,
				user_notified: true,
			},
			"Payment failure processed and user notified"
		);
	} catch (error) {
		await trx.rollback();
		logger.error("Error processing payment failure", { error, payment_intent_id, campaign_id });
		return sendError(res, "Failed to process payment failure", error.message, 500);
	}
});

/**
 * Activate campaign after successful payment
 */
const activateCampaign = asyncHandler(async (req, res) => {
	const { id: campaignId } = req.params;
	const { payment_intent_id } = req.body;

	if (!campaignId || !payment_intent_id) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Verify payment was successful
		const transaction = await db("dtm_ads.billing_transactions")
			.where({
				invoice_id: payment_intent_id,
				campaign_id: campaignId,
				status: "completed",
			})
			.first();

		if (!transaction) {
			return sendError(res, "Payment not found or not completed", null, 400);
		}

		// Update campaign status to active
		const [updatedCampaign] = await db("dtm_ads.ad_campaigns")
			.where({ id: campaignId })
			.update({
				status: "active",
				updated_at: new Date(),
			})
			.returning("*");

		if (!updatedCampaign) {
			return sendError(res, "Campaign not found", null, 404);
		}

		// Also activate all ads in this campaign
		await db("dtm_ads.ads").where({ campaign_id: campaignId, status: "pending" }).update({
			status: "active",
			updated_at: new Date(),
		});

		logger.info("Campaign activated after payment", {
			campaignId,
			payment_intent_id,
			transaction_id: transaction.id,
		});

		return sendSuccess(res, updatedCampaign, "Campaign activated successfully");
	} catch (error) {
		logger.error("Error activating campaign", { error, campaignId, payment_intent_id });
		return sendError(res, "Failed to activate campaign", error.message, 500);
	}
});

/**
 * Retry payment for a failed campaign
 */
const retryPayment = asyncHandler(async (req, res) => {
	const { id: campaignId } = req.params;
	const userId = req.auth?.sub;

	if (!campaignId || !userId) {
		return sendError(res, "Missing required fields", null, 400);
	}

	try {
		// Get campaign details and verify ownership
		const campaign = await db("dtm_ads.ad_campaigns")
			.leftJoin("dtm_base.users", "ad_campaigns.advertiser_id", "users.id")
			.where({
				"ad_campaigns.id": campaignId,
				"users.auth0_id": userId,
			})
			.select("ad_campaigns.*")
			.first();

		if (!campaign) {
			return sendError(res, "Campaign not found or access denied", null, 404);
		}

		// Check if campaign is in a retryable state
		if (!["payment_failed", "payment_error"].includes(campaign.status)) {
			return sendError(res, "Campaign is not in a retryable state", null, 400);
		}

		// Reset campaign status to approved for retry
		await db("dtm_ads.ad_campaigns").where({ id: campaignId }).update({
			status: "approved",
			updated_at: new Date(),
		});

		// Create new billing transaction for retry
		await db("dtm_ads.billing_transactions").insert({
			advertiser_id: campaign.advertiser_id,
			amount: campaign.total_budget,
			currency: "USD",
			description: `Campaign payment retry for "${campaign.name}"`,
			status: "pending",
			payment_method: "stripe",
			campaign_id: campaignId,
			created_at: new Date(),
			updated_at: new Date(),
		});

		logger.info("Payment retry initiated", {
			campaignId,
			userId,
			budget: campaign.total_budget,
		});

		return sendSuccess(
			res,
			{
				campaign_id: campaignId,
				status: "approved",
				retry_initiated: true,
				payment_required: true,
			},
			"Payment retry initiated successfully"
		);
	} catch (error) {
		logger.error("Error initiating payment retry", { error, campaignId, userId });
		return sendError(res, "Failed to initiate payment retry", error.message, 500);
	}
});

module.exports = {
	storePaymentIntent,
	handlePaymentSuccess,
	handlePaymentFailed,
	activateCampaign,
	retryPayment,
};
