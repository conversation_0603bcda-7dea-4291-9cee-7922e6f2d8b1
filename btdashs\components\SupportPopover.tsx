"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, Copy, HandCoins } from "lucide-react";
import { useState } from "react";

const addresses = {
  TAO: process.env.NEXT_PUBLIC_DONATE_TAO || "Unavailable",
  DTAO: process.env.NEXT_PUBLIC_DONATE_DTAO || "Unavailable",
  BTC: process.env.NEXT_PUBLIC_DONATE_BTC || "Unavailable",
  USDT: process.env.NEXT_PUBLIC_DONATE_USDT || "Unavailable",
};

function CopyButton({ text }: { text: string }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={handleCopy}
      className="h-6 w-6 p-0"
      title="Copy to clipboard"
    >
      {copied ? (
        <Check className="w-4 h-4 text-green-500" />
      ) : (
        <Copy className="w-4 h-4 text-muted-foreground" />
      )}
    </Button>
  );
}

export function SupportPopover() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-amber-100 dark:hover:bg-amber-900/20 group"
        >
          <HandCoins className="w-10 h-10 text-amber-500 group-hover:scale-110 transition-transform" />
          <span className="sr-only">Support us</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-96 rounded-lg border bg-white p-4 shadow-xl dark:bg-neutral-900 dark:border-neutral-700 ring-1 ring-inset ring-rose-400/30"
        align="end"
      >
        <div className="space-y-2 text-sm">
          <div className="mb-2">
            <h3 className="font-semibold text-rose-600 dark:text-rose-400 mb-1">
              ❤️ Support our work
            </h3>
            <p className="text-muted-foreground">
              Help us keep the project alive by donating crypto.
            </p>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <img src="/tao-logo.png" alt="TAO" className="w-4 h-4" />
              <span className="font-mono text-xs break-all">
                {addresses.TAO}
              </span>
              <CopyButton text={addresses.TAO} />
            </div>
            <div className="flex items-center gap-2">
              <img src="/dtao-logo.png" alt="DTAO" className="w-4 h-4" />
              <span className="font-mono text-xs break-all">
                {addresses.DTAO}
              </span>
              <CopyButton text={addresses.DTAO} />
            </div>
            <div className="flex items-center gap-2">
              <img src="/btc-logo.svg" alt="BTC" className="w-4 h-4" />
              <span className="font-mono text-xs break-all">
                {addresses.BTC}
              </span>
              <CopyButton text={addresses.BTC} />
            </div>
            <div className="flex items-center gap-2">
              <img src="/usdt-logo.svg" alt="USDT" className="w-4 h-4" />
              <span className="font-mono text-xs break-all">
                {addresses.USDT}
              </span>
              <CopyButton text={addresses.USDT} />
            </div>
          </div>
          <p className="text-xs mt-3 text-muted-foreground">
            Every donation helps. Thank you for your support! 🙏
          </p>
        </div>
      </PopoverContent>
    </Popover>
  );
}
