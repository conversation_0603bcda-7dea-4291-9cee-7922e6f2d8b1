// src/application/services/SubnetMetricsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Subnet Metrics Service - Handles subnet performance metrics operations
 *
 * This service manages subnet performance metrics and analytics,
 * providing data aggregation and analysis for subnet monitoring.
 *
 * Key responsibilities:
 * - Subnet metrics CRUD operations
 * - Performance analytics calculation
 * - Historical metrics management
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class SubnetMetricsService extends BaseService {
	constructor() {
		super("dtm_base.subnet_metrics", "SubnetMetric");
	}

	/**
	 * Get all subnet metrics with default sorting by recorded_at
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of subnet metrics
	 */
	async getAllSubnetMetrics(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "recorded_at", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all subnet metrics", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get subnet metric by ID
	 * @param {number} id - Subnet metric ID
	 * @returns {Promise<Object|null>} Subnet metric object or null if not found
	 */
	async getSubnetMetricById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting subnet metric by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Get metrics by subnet ID (netuid)
	 * @param {number} netuid - Subnet network UID
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of metrics for the subnet
	 */
	async getMetricsBySubnetId(netuid, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "recorded_at", direction: "desc" },
				...options,
			};

			const metrics = await this.getAll({ netuid }, queryOptions);
			logger.info("Subnet metrics retrieved by subnet ID", {
				netuid,
				count: metrics.length,
			});
			return metrics;
		} catch (error) {
			logger.error("Error getting metrics by subnet ID", { error, netuid });
			throw new Error(`Failed to get metrics by subnet ID: ${error.message}`);
		}
	}

	/**
	 * Create a new subnet metric entry
	 * @param {Object} metricData - Subnet metric data
	 * @returns {Promise<Object>} Created subnet metric object
	 */
	async createSubnetMetric(metricData) {
		try {
			const newMetric = await this.create({
				...metricData,
				recorded_at: metricData.recorded_at || new Date(),
			});
			logger.info("Subnet metric created", { metric_id: newMetric.id });
			return newMetric;
		} catch (error) {
			logger.error("Error creating subnet metric", { error, metricData });
			throw error;
		}
	}

	/**
	 * Update a subnet metric
	 * @param {number} id - Subnet metric ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated subnet metric object
	 */
	async updateSubnetMetric(id, updateData) {
		try {
			const updatedMetric = await this.updateById(id, updateData);
			logger.info("Subnet metric updated", { metric_id: id });
			return updatedMetric;
		} catch (error) {
			logger.error("Error updating subnet metric", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a subnet metric
	 * @param {number} id - Subnet metric ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteSubnetMetric(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Subnet metric deleted", { metric_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting subnet metric", { error, id });
			throw error;
		}
	}

	/**
	 * Get latest metrics for a subnet
	 * @param {number} netuid - Subnet network UID
	 * @param {number} limit - Number of latest metrics to return
	 * @returns {Promise<Array>} Array of latest subnet metrics
	 */
	async getLatestSubnetMetrics(netuid, limit = 10) {
		try {
			const metrics = await this.getAll(
				{ netuid },
				{
					orderBy: { column: "recorded_at", direction: "desc" },
					limit,
				}
			);

			logger.info("Latest subnet metrics retrieved", {
				netuid,
				count: metrics.length,
				limit,
			});

			return metrics;
		} catch (error) {
			logger.error("Error getting latest subnet metrics", { error, netuid, limit });
			throw new Error(`Failed to get latest subnet metrics: ${error.message}`);
		}
	}

	/**
	 * Get subnet metrics by date range
	 * @param {number} netuid - Subnet network UID
	 * @param {Date} startDate - Start date
	 * @param {Date} endDate - End date
	 * @returns {Promise<Array>} Array of subnet metrics in date range
	 */
	async getSubnetMetricsByDateRange(netuid, startDate, endDate) {
		try {
			const db = require("../../infrastructure/database/knex");

			const metrics = await db(this.tableName)
				.where("netuid", netuid)
				.where("recorded_at", ">=", startDate)
				.where("recorded_at", "<=", endDate)
				.orderBy("recorded_at", "asc");

			logger.info("Subnet metrics retrieved by date range", {
				netuid,
				startDate,
				endDate,
				count: metrics.length,
			});

			return metrics;
		} catch (error) {
			logger.error("Error getting subnet metrics by date range", { error, netuid, startDate, endDate });
			throw new Error(`Failed to get subnet metrics by date range: ${error.message}`);
		}
	}

	/**
	 * Get subnet performance analytics
	 * @param {number} netuid - Subnet network UID
	 * @param {number} days - Number of days to analyze
	 * @returns {Promise<Object>} Performance analytics
	 */
	async getSubnetPerformanceAnalytics(netuid, days = 7) {
		try {
			const db = require("../../infrastructure/database/knex");
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const analytics = await db(this.tableName)
				.where("netuid", netuid)
				.where("recorded_at", ">=", startDate)
				.select(
					db.raw("AVG(total_stake) as avg_stake"),
					db.raw("MAX(total_stake) as max_stake"),
					db.raw("MIN(total_stake) as min_stake"),
					db.raw("AVG(validator_count) as avg_validators"),
					db.raw("AVG(miner_count) as avg_miners"),
					db.raw("COUNT(*) as data_points")
				)
				.first();

			logger.info("Subnet performance analytics retrieved", {
				netuid,
				days,
				analytics,
			});

			return analytics;
		} catch (error) {
			logger.error("Error getting subnet performance analytics", { error, netuid, days });
			throw new Error(`Failed to get subnet performance analytics: ${error.message}`);
		}
	}

	/**
	 * Get subnet comparison metrics
	 * @param {Array} netuids - Array of subnet network UIDs to compare
	 * @param {number} days - Number of days to analyze
	 * @returns {Promise<Array>} Array of comparison metrics
	 */
	async getSubnetComparisonMetrics(netuids, days = 7) {
		try {
			const db = require("../../infrastructure/database/knex");
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const comparison = await db(this.tableName)
				.whereIn("netuid", netuids)
				.where("recorded_at", ">=", startDate)
				.select(
					"netuid",
					db.raw("AVG(total_stake) as avg_stake"),
					db.raw("AVG(validator_count) as avg_validators"),
					db.raw("AVG(miner_count) as avg_miners"),
					db.raw("COUNT(*) as data_points")
				)
				.groupBy("netuid")
				.orderBy("avg_stake", "desc");

			logger.info("Subnet comparison metrics retrieved", {
				netuids,
				days,
				count: comparison.length,
			});

			return comparison;
		} catch (error) {
			logger.error("Error getting subnet comparison metrics", { error, netuids, days });
			throw new Error(`Failed to get subnet comparison metrics: ${error.message}`);
		}
	}

	/**
	 * Get top performing subnets
	 * @param {string} metric - Metric to sort by (total_stake, validator_count, etc.)
	 * @param {number} limit - Number of top subnets to return
	 * @returns {Promise<Array>} Array of top performing subnets
	 */
	async getTopPerformingSubnets(metric = "total_stake", limit = 10) {
		try {
			const db = require("../../infrastructure/database/knex");

			const topSubnets = await db(this.tableName)
				.select("netuid", db.raw(`AVG(${metric}) as avg_${metric}`), db.raw("COUNT(*) as data_points"))
				.groupBy("netuid")
				.orderBy(`avg_${metric}`, "desc")
				.limit(limit);

			logger.info("Top performing subnets retrieved", {
				metric,
				limit,
				count: topSubnets.length,
			});

			return topSubnets;
		} catch (error) {
			logger.error("Error getting top performing subnets", { error, metric, limit });
			throw new Error(`Failed to get top performing subnets: ${error.message}`);
		}
	}
}

module.exports = new SubnetMetricsService();
