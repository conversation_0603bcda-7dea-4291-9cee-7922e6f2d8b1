const redisClient = require("../../infrastructure/redis/redisClient");
const logger = require("../../../logger");

class RateLimitService {
	constructor() {
		// Default rate limit configurations
		this.defaultLimits = {
			// API endpoints
			"api:general": { requests: 100, window: 60 }, // 100 requests per minute
			"api:auth": { requests: 10, window: 60 }, // 10 auth requests per minute
			"api:upload": { requests: 5, window: 60 }, // 5 uploads per minute
			"api:analytics": { requests: 50, window: 60 }, // 50 analytics requests per minute

			// User-specific limits
			"user:general": { requests: 1000, window: 3600 }, // 1000 requests per hour per user
			"user:campaigns": { requests: 20, window: 60 }, // 20 campaign operations per minute
			"user:ads": { requests: 50, window: 60 }, // 50 ad operations per minute

			// Admin limits (higher)
			"admin:general": { requests: 5000, window: 3600 }, // 5000 requests per hour
			"admin:approval": { requests: 100, window: 60 }, // 100 approvals per minute

			// IP-based limits
			"ip:general": { requests: 200, window: 60 }, // 200 requests per minute per IP
			"ip:strict": { requests: 10, window: 60 }, // 10 requests per minute for sensitive endpoints
		};
	}

	/**
	 * Check rate limit using sliding window algorithm
	 * @param {string} identifier - Unique identifier (user_id, ip, etc.)
	 * @param {string} limitType - Type of limit to apply
	 * @param {Object} customLimit - Custom limit override
	 * @returns {Promise<Object>} Rate limit result
	 */
	async checkRateLimit(identifier, limitType, customLimit = null) {
		try {
			const limit = customLimit || this.defaultLimits[limitType];
			if (!limit) {
				logger.warn("Unknown rate limit type", { limitType });
				return { allowed: true, remaining: 999, resetTime: Date.now() + 60000 };
			}

			const key = `rate_limit:${limitType}:${identifier}`;
			const now = Date.now();
			const windowStart = now - limit.window * 1000;

			// Use Lua script for atomic operations
			const luaScript = `
				local key = KEYS[1]
				local window_start = tonumber(ARGV[1])
				local now = tonumber(ARGV[2])
				local limit = tonumber(ARGV[3])
				local window_size = tonumber(ARGV[4])
				
				-- Remove expired entries
				redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
				
				-- Count current requests in window
				local current_count = redis.call('ZCARD', key)
				
				-- Check if limit exceeded
				if current_count >= limit then
					local ttl = redis.call('TTL', key)
					if ttl == -1 then
						redis.call('EXPIRE', key, window_size)
					end
					return {0, current_count, limit, window_start + window_size * 1000}
				end
				
				-- Add current request
				redis.call('ZADD', key, now, now)
				redis.call('EXPIRE', key, window_size)
				
				return {1, current_count + 1, limit, window_start + window_size * 1000}
			`;

			const result = await redisClient.eval(luaScript, 1, key, windowStart, now, limit.requests, limit.window);

			if (!result) {
				// Redis error, allow request but log
				logger.error("Rate limit check failed, allowing request", { identifier, limitType });
				return { allowed: true, remaining: limit.requests - 1, resetTime: now + limit.window * 1000 };
			}

			const [allowed, currentCount, maxRequests, resetTime] = result;

			return {
				allowed: allowed === 1,
				remaining: Math.max(0, maxRequests - currentCount),
				resetTime: resetTime,
				limit: maxRequests,
				window: limit.window,
			};
		} catch (error) {
			logger.error("Rate limit service error", { error, identifier, limitType });
			// On error, allow the request to avoid blocking legitimate users
			return { allowed: true, remaining: 999, resetTime: Date.now() + 60000 };
		}
	}

	/**
	 * Check multiple rate limits at once
	 * @param {string} identifier - Unique identifier
	 * @param {Array} limitTypes - Array of limit types to check
	 * @returns {Promise<Object>} Combined rate limit result
	 */
	async checkMultipleRateLimits(identifier, limitTypes) {
		try {
			const results = await Promise.all(
				limitTypes.map((limitType) => this.checkRateLimit(identifier, limitType))
			);

			// If any limit is exceeded, deny the request
			const denied = results.find((result) => !result.allowed);
			if (denied) {
				return {
					allowed: false,
					remaining: denied.remaining,
					resetTime: denied.resetTime,
					limitType: limitTypes[results.indexOf(denied)],
				};
			}

			// Return the most restrictive remaining count
			const minRemaining = Math.min(...results.map((r) => r.remaining));
			const earliestReset = Math.min(...results.map((r) => r.resetTime));

			return {
				allowed: true,
				remaining: minRemaining,
				resetTime: earliestReset,
			};
		} catch (error) {
			logger.error("Multiple rate limit check error", { error, identifier, limitTypes });
			return { allowed: true, remaining: 999, resetTime: Date.now() + 60000 };
		}
	}

	/**
	 * Get current rate limit status without incrementing
	 * @param {string} identifier - Unique identifier
	 * @param {string} limitType - Type of limit to check
	 * @returns {Promise<Object>} Current status
	 */
	async getRateLimitStatus(identifier, limitType) {
		try {
			const limit = this.defaultLimits[limitType];
			if (!limit) {
				return { current: 0, limit: 999, remaining: 999, resetTime: Date.now() + 60000 };
			}

			const key = `rate_limit:${limitType}:${identifier}`;
			const now = Date.now();
			const windowStart = now - limit.window * 1000;

			// Clean expired entries and count current
			const luaScript = `
				local key = KEYS[1]
				local window_start = tonumber(ARGV[1])
				local window_size = tonumber(ARGV[2])
				
				redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
				local current_count = redis.call('ZCARD', key)
				local ttl = redis.call('TTL', key)
				
				return {current_count, ttl}
			`;

			const result = await redisClient.eval(luaScript, 1, key, windowStart, limit.window);

			if (!result) {
				return {
					current: 0,
					limit: limit.requests,
					remaining: limit.requests,
					resetTime: now + limit.window * 1000,
				};
			}

			const [currentCount, ttl] = result;
			const resetTime = ttl > 0 ? now + ttl * 1000 : now + limit.window * 1000;

			return {
				current: currentCount,
				limit: limit.requests,
				remaining: Math.max(0, limit.requests - currentCount),
				resetTime: resetTime,
			};
		} catch (error) {
			logger.error("Get rate limit status error", { error, identifier, limitType });
			return { current: 0, limit: 999, remaining: 999, resetTime: Date.now() + 60000 };
		}
	}

	/**
	 * Reset rate limit for a specific identifier and type
	 * @param {string} identifier - Unique identifier
	 * @param {string} limitType - Type of limit to reset
	 * @returns {Promise<boolean>} Success status
	 */
	async resetRateLimit(identifier, limitType) {
		try {
			const key = `rate_limit:${limitType}:${identifier}`;
			const result = await redisClient.del(key);

			logger.info("Rate limit reset", { identifier, limitType });
			return result > 0;
		} catch (error) {
			logger.error("Reset rate limit error", { error, identifier, limitType });
			return false;
		}
	}

	/**
	 * Set custom rate limit for specific identifier
	 * @param {string} identifier - Unique identifier
	 * @param {string} limitType - Type of limit
	 * @param {Object} customLimit - Custom limit configuration
	 * @returns {Promise<boolean>} Success status
	 */
	async setCustomRateLimit(identifier, limitType, customLimit) {
		try {
			const customKey = `custom_limit:${limitType}:${identifier}`;
			await redisClient.set(customKey, JSON.stringify(customLimit), 3600); // 1 hour TTL

			logger.info("Custom rate limit set", { identifier, limitType, customLimit });
			return true;
		} catch (error) {
			logger.error("Set custom rate limit error", { error, identifier, limitType });
			return false;
		}
	}

	/**
	 * Get custom rate limit if exists
	 * @param {string} identifier - Unique identifier
	 * @param {string} limitType - Type of limit
	 * @returns {Promise<Object|null>} Custom limit or null
	 */
	async getCustomRateLimit(identifier, limitType) {
		try {
			const customKey = `custom_limit:${limitType}:${identifier}`;
			const result = await redisClient.get(customKey);

			return result ? JSON.parse(result) : null;
		} catch (error) {
			logger.error("Get custom rate limit error", { error, identifier, limitType });
			return null;
		}
	}

	/**
	 * Get rate limit statistics
	 * @param {string} limitType - Type of limit
	 * @returns {Promise<Object>} Statistics
	 */
	async getRateLimitStats(limitType) {
		try {
			const pattern = `rate_limit:${limitType}:*`;
			const keys = await redisClient.client.keys(pattern);

			const stats = {
				totalIdentifiers: keys.length,
				activeIdentifiers: 0,
				totalRequests: 0,
			};

			for (const key of keys) {
				const count = await redisClient.client.zcard(key);
				if (count > 0) {
					stats.activeIdentifiers++;
					stats.totalRequests += count;
				}
			}

			return stats;
		} catch (error) {
			logger.error("Get rate limit stats error", { error, limitType });
			return { totalIdentifiers: 0, activeIdentifiers: 0, totalRequests: 0 };
		}
	}
}

module.exports = RateLimitService;
