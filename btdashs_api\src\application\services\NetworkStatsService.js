// src/application/services/NetworkStatsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Network Stats Service - Handles network statistics operations
 *
 * This service manages network statistics and metrics, providing data
 * aggregation and analysis for network performance monitoring.
 *
 * Key responsibilities:
 * - Network statistics CRUD operations
 * - Performance metrics calculation
 * - Historical data management
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class NetworkStatsService extends BaseService {
	constructor() {
		super("dtm_base.network_stats", "NetworkStat");
	}

	/**
	 * Get all network stats with default sorting by timestamp
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of network stats
	 */
	async getAllNetworkStats(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "recorded_at", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all network stats", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get network stat by ID
	 * @param {number} id - Network stat ID
	 * @returns {Promise<Object|null>} Network stat object or null if not found
	 */
	async getNetworkStatById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting network stat by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new network stat entry
	 * @param {Object} statData - Network stat data
	 * @returns {Promise<Object>} Created network stat object
	 */
	async createNetworkStat(statData) {
		try {
			const newStat = await this.create({
				...statData,
				recorded_at: statData.recorded_at || new Date(),
			});
			logger.info("Network stat created", { stat_id: newStat.id });
			return newStat;
		} catch (error) {
			logger.error("Error creating network stat", { error, statData });
			throw error;
		}
	}

	/**
	 * Update a network stat
	 * @param {number} id - Network stat ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated network stat object
	 */
	async updateNetworkStat(id, updateData) {
		try {
			const updatedStat = await this.updateById(id, updateData);
			logger.info("Network stat updated", { stat_id: id });
			return updatedStat;
		} catch (error) {
			logger.error("Error updating network stat", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a network stat
	 * @param {number} id - Network stat ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteNetworkStat(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Network stat deleted", { stat_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting network stat", { error, id });
			throw error;
		}
	}

	/**
	 * Get latest network stats
	 * @param {number} limit - Number of latest stats to return
	 * @returns {Promise<Array>} Array of latest network stats
	 */
	async getLatestNetworkStats(limit = 10) {
		try {
			const stats = await this.getAll(
				{},
				{
					orderBy: { column: "recorded_at", direction: "desc" },
					limit,
				}
			);

			logger.info("Latest network stats retrieved", {
				count: stats.length,
				limit,
			});

			return stats;
		} catch (error) {
			logger.error("Error getting latest network stats", { error, limit });
			throw new Error(`Failed to get latest network stats: ${error.message}`);
		}
	}

	/**
	 * Get network stats by date range
	 * @param {Date} startDate - Start date
	 * @param {Date} endDate - End date
	 * @returns {Promise<Array>} Array of network stats in date range
	 */
	async getNetworkStatsByDateRange(startDate, endDate) {
		try {
			const db = require("../../infrastructure/database/knex");

			const stats = await db(this.tableName)
				.where("timestamp", ">=", startDate)
				.where("timestamp", "<=", endDate)
				.orderBy("timestamp", "asc");

			logger.info("Network stats retrieved by date range", {
				startDate,
				endDate,
				count: stats.length,
			});

			return stats;
		} catch (error) {
			logger.error("Error getting network stats by date range", { error, startDate, endDate });
			throw new Error(`Failed to get network stats by date range: ${error.message}`);
		}
	}

	/**
	 * Get network stats summary
	 * @returns {Promise<Object>} Network stats summary
	 */
	async getNetworkStatsSummary() {
		try {
			const db = require("../../infrastructure/database/knex");

			const summary = await db(this.tableName)
				.select(
					db.raw("COUNT(*) as total_records"),
					db.raw("MAX(timestamp) as latest_timestamp"),
					db.raw("MIN(timestamp) as earliest_timestamp"),
					db.raw("AVG(total_stake) as avg_total_stake"),
					db.raw("MAX(total_stake) as max_total_stake"),
					db.raw("MIN(total_stake) as min_total_stake")
				)
				.first();

			logger.info("Network stats summary retrieved", { summary });

			return summary;
		} catch (error) {
			logger.error("Error getting network stats summary", { error });
			throw new Error(`Failed to get network stats summary: ${error.message}`);
		}
	}

	/**
	 * Get network performance metrics
	 * @param {number} days - Number of days to analyze
	 * @returns {Promise<Object>} Performance metrics
	 */
	async getNetworkPerformanceMetrics(days = 7) {
		try {
			const db = require("../../infrastructure/database/knex");
			const startDate = new Date();
			startDate.setDate(startDate.getDate() - days);

			const metrics = await db(this.tableName)
				.where("timestamp", ">=", startDate)
				.select(
					db.raw("AVG(total_stake) as avg_stake"),
					db.raw("AVG(total_validators) as avg_validators"),
					db.raw("AVG(total_subnets) as avg_subnets"),
					db.raw("COUNT(*) as data_points")
				)
				.first();

			logger.info("Network performance metrics retrieved", {
				days,
				metrics,
			});

			return metrics;
		} catch (error) {
			logger.error("Error getting network performance metrics", { error, days });
			throw new Error(`Failed to get network performance metrics: ${error.message}`);
		}
	}
}

module.exports = new NetworkStatsService();
