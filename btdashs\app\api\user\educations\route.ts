import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const GET = async function getUserEducations(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/educations`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const educations = await response.json();
		return NextResponse.json(educations);
	} catch (error) {
		console.error("Error fetching educations:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};

export const POST = async function addUserEducation(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const body = await request.json();

		// Validate required fields
		if (!body.institution_name || !body.start_date) {
			return NextResponse.json({ error: "Institution name and start date are required" }, { status: 400 });
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/educations`, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const education = await response.json();
		return NextResponse.json({ data: education }, { status: 201 });
	} catch (error) {
		console.error("Error adding education:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};
