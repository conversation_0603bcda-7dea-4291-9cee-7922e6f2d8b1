import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
	try {
		const { token } = await auth0.getAccessToken();
		const { searchParams } = new URL(req.url);
		
		const entityType = searchParams.get('entity_type') || 'campaign';

		const response = await fetch(`${process.env.API_BASE_URL}/admin/rejection-reasons?entity_type=${entityType}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		console.error("Rejection reasons fetch error:", error);

		return NextResponse.json(
			{
				success: false,
				message: error.message || "Internal server error",
				error: error.code || "unknown_error",
			},
			{ status: 500 }
		);
	}
}
