"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, Trash2, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
  created: number;
}

interface PaymentMethodListProps {
  customerId?: string;
  onPaymentMethodDeleted?: () => void;
  showAddButton?: boolean;
  onAddClick?: () => void;
}

export function PaymentMethodList({
  customerId,
  onPaymentMethodDeleted,
  showAddButton = true,
  onAddClick
}: PaymentMethodListProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchPaymentMethods = async () => {
    if (!customerId) {
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`/api/stripe/payment-methods?customerId=${customerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }
      
      const data = await response.json();
      setPaymentMethods(data.paymentMethods || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load payment methods';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentMethods();
  }, [customerId]);

  const handleDelete = async (paymentMethodId: string) => {
    setDeletingId(paymentMethodId);
    
    try {
      const response = await fetch('/api/stripe/detach-payment-method', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentMethodId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove payment method');
      }

      // Remove from local state
      setPaymentMethods(prev => prev.filter(pm => pm.id !== paymentMethodId));
      
      toast({
        title: "Payment method removed",
        description: "The payment method has been removed successfully.",
      });

      onPaymentMethodDeleted?.();
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove payment method';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  const getCardBrandIcon = (brand: string) => {
    // You can replace this with actual brand icons
    return <CreditCard className="h-5 w-5" />;
  };

  const formatCardBrand = (brand: string) => {
    return brand.charAt(0).toUpperCase() + brand.slice(1);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading payment methods...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Methods</CardTitle>
        <CardDescription>Manage your saved payment methods</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {paymentMethods.length === 0 ? (
          <div className="text-center py-8">
            <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No payment methods</h3>
            <p className="text-muted-foreground mb-4">
              Add a payment method to get started with campaign billing.
            </p>
            {showAddButton && (
              <Button onClick={onAddClick}>
                Add Payment Method
              </Button>
            )}
          </div>
        ) : (
          <>
            {paymentMethods.map((paymentMethod) => (
              <div
                key={paymentMethod.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {getCardBrandIcon(paymentMethod.card?.brand || 'card')}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {formatCardBrand(paymentMethod.card?.brand || 'Card')} ending in {paymentMethod.card?.last4}
                      </span>
                      <Badge variant="secondary">Default</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Expires {paymentMethod.card?.exp_month}/{paymentMethod.card?.exp_year}
                    </p>
                  </div>
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDelete(paymentMethod.id)}
                  disabled={deletingId === paymentMethod.id}
                >
                  {deletingId === paymentMethod.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              </div>
            ))}
            
            {showAddButton && (
              <div className="pt-4 border-t">
                <Button variant="outline" onClick={onAddClick} className="w-full">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Add Another Payment Method
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
