import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Lock, Network, Zap } from "lucide-react";
import Link from "next/link";
import type React from "react";

export default function PlatformPage() {
	return (
		<div className="min-h-screen">
			{/* Hero Section */}
			<section className="relative py-20 overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-background" />
				<div className="relative max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center space-y-8 max-w-3xl mx-auto">
						<div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm text-primary mb-4">
							Platform
						</div>
						<h1 className="text-4xl md:text-6xl font-bold tracking-tight">
							Bittensor Market Empowers Everyone to Participate in the Decentralized AI Economy
						</h1>
						<p className="text-xl text-muted-foreground max-w-2xl mx-auto">
							For the first time, anyone can participate in the Bittensor network. Within a few clicks,
							you can explore subnets, become a validator, or start building on the network.
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/subnets">
								<Button size="lg" className="w-full sm:w-auto">
									Explore Subnets
								</Button>
							</Link>
							<Link href="/posts">
								<Button size="lg" variant="outline" className="w-full sm:w-auto">
									Latest Updates
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</section>

			{/* Features Grid */}
			<section className="py-20 bg-muted/50">
				<div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-12">
						<h2 className="text-3xl font-bold mb-4">Everything You Need to Succeed</h2>
						<p className="text-xl text-muted-foreground">
							Comprehensive tools and features to help you navigate the Bittensor ecosystem
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<FeatureCard
							icon={Brain}
							title="Subnet Discovery"
							description="Explore and analyze different subnets, their performance metrics, and opportunities"
						/>
						<FeatureCard
							icon={Network}
							title="Network Analytics"
							description="Real-time insights into network activity, validator performance, and token metrics"
						/>
						<FeatureCard
							icon={Lock}
							title="Secure Transactions"
							description="Safe and efficient token swaps with built-in security measures"
						/>
						<FeatureCard
							icon={Cpu}
							title="Validator Tools"
							description="Everything you need to set up and manage your validator nodes"
						/>
						<FeatureCard
							icon={BarChart}
							title="Market Data"
							description="Comprehensive market data and analysis tools for informed decision-making"
						/>
						<FeatureCard
							icon={Zap}
							title="Real-time Updates"
							description="Stay informed with live updates on network activities and opportunities"
						/>
					</div>
				</div>
			</section>

			{/* How It Works */}
			<section className="py-20">
				<div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center mb-12">
						<h2 className="text-3xl font-bold mb-4">How It Works</h2>
						<p className="text-xl text-muted-foreground">
							Get started with Bittensor Market in three simple steps
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
						<div className="text-center space-y-4">
							<div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
								<span className="text-xl font-bold text-primary">1</span>
							</div>
							<h3 className="text-xl font-semibold">Explore Subnets</h3>
							<p className="text-muted-foreground">
								Browse through various subnets and find the ones that match your interests and expertise
							</p>
						</div>
						<div className="text-center space-y-4">
							<div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
								<span className="text-xl font-bold text-primary">2</span>
							</div>
							<h3 className="text-xl font-semibold">Set Up Your Account</h3>
							<p className="text-muted-foreground">
								Create your account and configure your wallet to start participating in the network
							</p>
						</div>
						<div className="text-center space-y-4">
							<div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
								<span className="text-xl font-bold text-primary">3</span>
							</div>
							<h3 className="text-xl font-semibold">Start Contributing</h3>
							<p className="text-muted-foreground">
								Begin validating, developing, or participating in the network's governance
							</p>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-primary text-primary-foreground">
				<div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center space-y-8">
						<h2 className="text-3xl md:text-4xl font-bold">Ready to Get Started?</h2>
						<p className="text-xl opacity-90 max-w-2xl mx-auto">
							Join thousands of others who are already participating in the future of decentralized AI
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/auth/login?screen_hint=signup">
								<Button size="lg" variant="secondary" className="w-full sm:w-auto">
									Create Account
								</Button>
							</Link>
							<Link href="/docs">
								<Button size="lg" variant="outline" className="w-full sm:w-auto border-current">
									Read Documentation
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}

function FeatureCard({
	icon: Icon,
	title,
	description,
}: {
	icon: React.ElementType;
	title: string;
	description: string;
}) {
	return (
		<Card className="relative overflow-hidden">
			<CardContent className="pt-6">
				<div className="absolute top-0 right-0 w-32 h-32 bg-primary/5 rounded-full -mr-16 -mt-16" />
				<div className="relative space-y-4">
					<Icon className="w-8 h-8 text-primary" />
					<h3 className="text-xl font-semibold">{title}</h3>
					<p className="text-muted-foreground">{description}</p>
				</div>
			</CardContent>
		</Card>
	);
}
