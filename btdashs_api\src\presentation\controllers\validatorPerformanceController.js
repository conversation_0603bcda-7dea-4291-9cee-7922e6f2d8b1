const ValidatorPerformanceService = require("../../application/services/ValidatorPerformanceService");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const ProgressTracker = require("../../../utils/progressTracker");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllValidatorPerformance = asyncHandler(async (req, res) => {
	const performance = await ValidatorPerformanceService.getAllValidatorPerformance();
	return sendSuccess(res, performance, "Validator performance retrieved successfully");
});

const getValidatorPerformanceById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const performance = await ValidatorPerformanceService.getValidatorPerformanceById(id);
	if (!performance) {
		return sendNotFound(res, "Validator performance not found");
	}
	return sendSuccess(res, performance, "Validator performance retrieved successfully");
});

const createValidatorPerformance = async (req, res) => {
	try {
		const { validator_id, netuid, vtrust, emissions } = req.body;
		const [newPerformance] = await db("dtm_base.validator_subnet_performance")
			.insert({
				validator_id,
				netuid,
				vtrust,
				emissions,
			})
			.returning("*");
		res.status(201).json({ data: newPerformance });
	} catch (error) {
		console.error("Error creating validator performance:", error);
		res.status(500).json({
			message: "Error creating validator performance",
		});
	}
};

const updateValidatorPerformance = async (req, res) => {
	try {
		const { id } = req.params;
		const { validator_id, netuid, vtrust, emissions } = req.body;
		const [updatedPerformance] = await db("dtm_base.validator_subnet_performance")
			.where({ id })
			.update({
				validator_id,
				netuid,
				vtrust,
				emissions,
			})
			.returning("*");
		if (!updatedPerformance) {
			return res.status(404).json({ message: "Validator performance not found" });
		}
		res.status(200).json({ data: updatedPerformance });
	} catch (error) {
		console.error("Error updating validator performance:", error);
		res.status(500).json({
			message: "Error updating validator performance",
		});
	}
};

const deleteValidatorPerformance = async (req, res) => {
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.validator_subnet_performance").where({ id }).del();
		if (!deleted) {
			return res.status(404).json({ message: "Validator performance not found" });
		}
		res.status(204).send();
	} catch (error) {
		console.error("Error deleting validator performance:", error);
		res.status(500).json({
			message: "Error deleting validator performance",
		});
	}
};

/* --- TAOSTATS API INTERACTIONS --- */

// Update Validators with data fetched from TaoStats API

const updateValidatorsPerformanceWithTaoStats = async (req, res) => {
	try {
		logger.info("Starting update of validator performance with TaoStats data...");

		// Fetch all subnets and validators upfront
		const subnets = await db("dtm_base.subnets");
		const validators = await db("dtm_base.validators");

		// Create a map for quick validator lookup by hotkey
		const validatorMap = validators.reduce((acc, validator) => {
			acc[validator.hotkey] = validator;
			return acc;
		}, {});

		// Initialize progress tracker (only change to original function)
		const progress = new ProgressTracker(subnets.length, "subnets");
		progress.addMetric("validators", 0);

		for (const subnet of subnets) {
			const { netuid } = subnet;

			// Fetch all validator metrics for this subnet
			const metricsData = await fetchAllPages(`/validator/metrics/latest/v1`, { netuid });

			if (!metricsData || metricsData.length === 0) {
				console.warn(`\nNo metrics found for netuid: ${netuid}`);
				progress.increment();
				continue;
			}

			// Process each validator in the metrics data
			let subnetValidatorsProcessed = 0;
			for (const validatorData of metricsData) {
				const hotkey = validatorData.hotkey.hex;
				const validator = validatorMap[hotkey];

				if (!validator) {
					continue;
				}

				// Map and parse data to match table schema
				const performanceData = {
					validator_id: validator.validator_id,
					netuid: netuid,
					vtrust: parseFloat(validatorData.trust),
					emissions: parseInt(validatorData.emission, 10),
				};

				// Check if performance record exists in the DB
				const existingPerformance = await db("dtm_base.validator_subnet_performance")
					.where({
						validator_id: performanceData.validator_id,
						netuid: performanceData.netuid,
					})
					.first();

				if (existingPerformance) {
					// Update the existing record
					await db("dtm_base.validator_subnet_performance")
						.where({
							validator_id: performanceData.validator_id,
							netuid: performanceData.netuid,
						})
						.update({
							vtrust: performanceData.vtrust,
							emissions: performanceData.emissions,
						});
				} else {
					// Insert a new record
					await db("dtm_base.validator_subnet_performance").insert(performanceData);
				}

				subnetValidatorsProcessed++;
			}

			progress.addMetric("validators", (progress.additionalMetrics.validators || 0) + subnetValidatorsProcessed);
			progress.increment();
		}

		// Complete progress tracking
		progress.complete();

		updateEndpointStatus("/update/validator-performance", true, "Validator performance updated successfully");
		res?.status(200).json({ success: true });
	} catch (error) {
		console.error("\nError updating validator performance:", error);

		updateEndpointStatus("/update/validator-performance", false, error.message);
		res?.status(500).json({ error: error.message });
	}
};

module.exports = {
	getAllValidatorPerformance,
	getValidatorPerformanceById,
	createValidatorPerformance,
	updateValidatorPerformance,
	deleteValidatorPerformance,
	updateValidatorsPerformanceWithTaoStats,
};
