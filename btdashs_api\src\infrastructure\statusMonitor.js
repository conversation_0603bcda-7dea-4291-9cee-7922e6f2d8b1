// infrastructure/statusMonitor.js

let statuses = {};

const updateEndpointStatus = (endpoint, success, message = "") => {
	statuses[endpoint] = {
		timestamp: new Date().toISOString(),
		success,
		message: message || (success ? "Last update succeeded" : "Last update failed"),
	};
};

const getEndpointStatuses = () => ({ ...statuses });

module.exports = { updateEndpointStatus, getEndpointStatuses };
