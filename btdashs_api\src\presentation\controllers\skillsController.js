const SkillsService = require("../../application/services/SkillsService");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

// Get all skills
const getAllSkills = asyncHandler(async (req, res) => {
	const skills = await SkillsService.getAllSkills();
	return sendSuccess(res, skills, "Skills retrieved successfully");
});

// Get a skill by ID
const getSkillById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const skill = await SkillsService.getSkillById(id);

	if (!skill) {
		return sendNotFound(res, "Skill not found");
	}

	return sendSuccess(res, skill, "Skill retrieved successfully");
});

// Create a new skill
const createSkill = asyncHandler(async (req, res) => {
	const { name } = req.body;
	const newSkill = await SkillsService.createSkill({ name });
	return sendSuccess(res, newSkill, "Skill created successfully", 201);
});

// Update a skill
const updateSkill = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const { name } = req.body;
	const updatedSkill = await SkillsService.updateSkill(id, { name });

	if (!updatedSkill) {
		return sendNotFound(res, "Skill not found");
	}

	return sendSuccess(res, updatedSkill, "Skill updated successfully");
});

// Delete a skill
const deleteSkill = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const deleted = await SkillsService.deleteSkill(id);

	if (!deleted) {
		return sendNotFound(res, "Skill not found");
	}

	return sendSuccess(res, null, "Skill deleted successfully");
});

module.exports = {
	getAllSkills,
	getSkillById,
	createSkill,
	updateSkill,
	deleteSkill,
};
