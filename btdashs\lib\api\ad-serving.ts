import { ServedAd } from "@/lib/db/models";

// Database ad slot IDs and their configurations
export const AD_SLOTS = {
	// Home Page Slots (1-4)
	HOME_LEADERBOARD: 1, // 728x90
	HOME_BILLBOARD: 2, // 970x250
	HOME_MEDIUM_RECTANGLE: 3, // 300x250
	HOME_SKYSCRAPER: 4, // 160x600

	// Subnet Page Slots (7-10)
	SUBNET_MEDIUM_RECTANGLE: 7, // 300x250
	SUBNET_HALF_PAGE: 8, // 300x600
	SUBNET_BANNER: 9, // 468x60
	SUBNET_WIDE_SKYSCRAPER: 10, // 160x600

	// Company Page Slots (11-14)
	COMPANY_MEDIUM_RECTANGLE: 11, // 300x250
	COMPANY_HALF_PAGE: 12, // 300x600
	COMPANY_LEADERBOARD: 13, // 728x90
	COMPANY_SQUARE_BUTTON: 14, // 125x125

	// Global Slots (15-16)
	GLOBAL_POPUP: 15, // 400x400
	GLOBAL_STICKY_FOOTER: 16, // 320x50
} as const;

// Slot dimensions mapping
export const SLOT_DIMENSIONS = {
	[AD_SLOTS.HOME_LEADERBOARD]: { width: 728, height: 90 },
	[AD_SLOTS.HOME_BILLBOARD]: { width: 970, height: 250 },
	[AD_SLOTS.HOME_MEDIUM_RECTANGLE]: { width: 300, height: 250 },
	[AD_SLOTS.HOME_SKYSCRAPER]: { width: 160, height: 600 },
	[AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: { width: 300, height: 250 },
	[AD_SLOTS.SUBNET_HALF_PAGE]: { width: 300, height: 600 },
	[AD_SLOTS.SUBNET_BANNER]: { width: 468, height: 60 },
	[AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: { width: 160, height: 600 },
	[AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: { width: 300, height: 250 },
	[AD_SLOTS.COMPANY_HALF_PAGE]: { width: 300, height: 600 },
	[AD_SLOTS.COMPANY_LEADERBOARD]: { width: 728, height: 90 },
	[AD_SLOTS.COMPANY_SQUARE_BUTTON]: { width: 125, height: 125 },
	[AD_SLOTS.GLOBAL_POPUP]: { width: 400, height: 400 },
	[AD_SLOTS.GLOBAL_STICKY_FOOTER]: { width: 320, height: 50 },
} as const;

// Google AdSense fallback slots mapped by slot ID
export const GOOGLE_AD_SLOTS = {
	[AD_SLOTS.HOME_LEADERBOARD]: "8814794983", // 728x90
	[AD_SLOTS.HOME_BILLBOARD]: "7230250579", // 970x250
	[AD_SLOTS.HOME_MEDIUM_RECTANGLE]: "7844510005", // 300x250
	[AD_SLOTS.HOME_SKYSCRAPER]: "7964747747", // 160x600
	[AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: "7844510005", // 300x250
	[AD_SLOTS.SUBNET_HALF_PAGE]: "7964747747", // 300x600
	[AD_SLOTS.SUBNET_BANNER]: "8814794983", // 468x60 (use horizontal)
	[AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: "7964747747", // 160x600
	[AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: "7844510005", // 300x250
	[AD_SLOTS.COMPANY_HALF_PAGE]: "7964747747", // 300x600
	[AD_SLOTS.COMPANY_LEADERBOARD]: "8814794983", // 728x90
	[AD_SLOTS.COMPANY_SQUARE_BUTTON]: "7844510005", // 125x125 (use square)
	[AD_SLOTS.GLOBAL_POPUP]: "7844510005", // 400x400 (use square)
	[AD_SLOTS.GLOBAL_STICKY_FOOTER]: "8814794983", // 320x50 (use horizontal)
} as const;

export interface AdServeRequest {
	slot: number;
	country_code?: string;
	device_type?: string;
	language?: string;
	user_agent?: string;
}

export interface AdServeResponse {
	success: boolean;
	data?: ServedAd;
	message: string;
}

export interface AdImpressionRequest {
	ad_id: number;
	session_id: string;
	user_id?: number;
	country_code?: string;
	device_type?: string;
	viewed_time?: number;
}

export interface AdClickRequest {
	ad_id: number;
	session_id: string;
	user_id?: number;
	country_code?: string;
	device_type?: string;
}

/**
 * SERVER-SIDE ONLY
 * Fetch a paid ad from the backend API
 */
export async function fetchPaidAd(slotId: number, userContext?: Partial<AdServeRequest>): Promise<ServedAd | null> {
	try {
		const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;

		if (!API_BASE) {
			console.error("API_BASE_URL not configured");
			return null;
		}

		const params = new URLSearchParams({
			slot: slotId.toString(),
			...(userContext?.country_code && { country_code: userContext.country_code }),
			...(userContext?.device_type && { device_type: userContext.device_type }),
			...(userContext?.language && { language: userContext.language }),
			...(userContext?.user_agent && { user_agent: userContext.user_agent }),
		});

		const headers = new Headers();
		headers.set("x-internal-api-key", process.env.INTERNAL_API_KEY!);

		const response = await fetch(`${API_BASE}/serve-ad?${params}`, {
			headers,
			cache: "no-store",
		});

		if (!response.ok) {
			if (response.status === 404) {
				// No ads available for this slot
				return null;
			}
			throw new Error(`Ad serving failed: ${response.status}`);
		}

		const result: AdServeResponse = await response.json();

		if (result.success && result.data) {
			return result.data;
		}

		return null;
	} catch (error) {
		console.error("Error fetching paid ad:", error);
		return null;
	}
}

/**
 * CLIENT-SIDE
 * Track ad impression
 */
export async function trackAdImpression(impressionData: AdImpressionRequest): Promise<boolean> {
	try {
		const response = await fetch("/api/ads/track/impression", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(impressionData),
		});

		return response.ok;
	} catch (error) {
		console.error("Error tracking impression:", error);
		return false;
	}
}

/**
 * CLIENT-SIDE
 * Track ad click and redirect
 */
export async function trackAdClick(clickData: AdClickRequest): Promise<boolean> {
	try {
		const response = await fetch("/api/ads/track/click", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(clickData),
		});

		return response.ok;
	} catch (error) {
		console.error("Error tracking click:", error);
		return false;
	}
}

/**
 * Generate a session ID for ad tracking
 */
export function generateSessionId(): string {
	return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get device type from user agent
 */
export function getDeviceType(userAgent?: string): string {
	if (!userAgent) return "unknown";

	const ua = userAgent.toLowerCase();
	if (ua.includes("mobile") || ua.includes("android") || ua.includes("iphone")) {
		return "mobile";
	}
	if (ua.includes("tablet") || ua.includes("ipad")) {
		return "tablet";
	}
	return "desktop";
}

/**
 * Get user's country code (placeholder - would integrate with geolocation service)
 */
export function getCountryCode(): string {
	// In a real implementation, this would use a geolocation service
	// For now, return a default
	return "US";
}

/**
 * Get user's language preference
 */
export function getLanguage(): string {
	if (typeof window !== "undefined") {
		return navigator.language || "en-US";
	}
	return "en-US";
}
