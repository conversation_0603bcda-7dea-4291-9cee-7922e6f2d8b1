import { ServedAd } from "@/lib/db/models";

// Ad serving configuration
export const AD_SLOT_MAPPING = {
	horizontal: 1, // Leaderboard 728x90
	square: 2, // Sidebar Rectangle 300x250
	vertical: 3, // Skyscraper 300x600
	billboard: 4, // Homepage Billboard 970x250
} as const;

export type AdVariant = keyof typeof AD_SLOT_MAPPING;

// Google AdSense fallback slots
export const GOOGLE_AD_SLOTS = {
	horizontal: "8814794983",
	square: "7844510005", 
	vertical: "7964747747",
	billboard: "7230250579",
} as const;

export interface AdServeRequest {
	slot: number;
	country_code?: string;
	device_type?: string;
	language?: string;
	user_agent?: string;
}

export interface AdServeResponse {
	success: boolean;
	data?: ServedAd;
	message: string;
}

export interface AdImpressionRequest {
	ad_id: number;
	session_id: string;
	user_id?: number;
	country_code?: string;
	device_type?: string;
	viewed_time?: number;
}

export interface AdClickRequest {
	ad_id: number;
	session_id: string;
	user_id?: number;
	country_code?: string;
	device_type?: string;
}

/**
 * SERVER-SIDE ONLY
 * Fetch a paid ad from the backend API
 */
export async function fetchPaidAd(variant: AdVariant, userContext?: Partial<AdServeRequest>): Promise<ServedAd | null> {
	try {
		const slotId = AD_SLOT_MAPPING[variant];
		const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;
		
		if (!API_BASE) {
			console.error("API_BASE_URL not configured");
			return null;
		}

		const params = new URLSearchParams({
			slot: slotId.toString(),
			...(userContext?.country_code && { country_code: userContext.country_code }),
			...(userContext?.device_type && { device_type: userContext.device_type }),
			...(userContext?.language && { language: userContext.language }),
			...(userContext?.user_agent && { user_agent: userContext.user_agent }),
		});

		const headers = new Headers();
		headers.set("x-internal-api-key", process.env.INTERNAL_API_KEY!);

		const response = await fetch(`${API_BASE}/api/serve-ad?${params}`, {
			headers,
			cache: "no-store",
		});

		if (!response.ok) {
			if (response.status === 404) {
				// No ads available for this slot
				return null;
			}
			throw new Error(`Ad serving failed: ${response.status}`);
		}

		const result: AdServeResponse = await response.json();
		
		if (result.success && result.data) {
			return result.data;
		}

		return null;
	} catch (error) {
		console.error("Error fetching paid ad:", error);
		return null;
	}
}

/**
 * CLIENT-SIDE
 * Track ad impression
 */
export async function trackAdImpression(impressionData: AdImpressionRequest): Promise<boolean> {
	try {
		const response = await fetch("/api/ads/track/impression", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(impressionData),
		});

		return response.ok;
	} catch (error) {
		console.error("Error tracking impression:", error);
		return false;
	}
}

/**
 * CLIENT-SIDE
 * Track ad click and redirect
 */
export async function trackAdClick(clickData: AdClickRequest): Promise<boolean> {
	try {
		const response = await fetch("/api/ads/track/click", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(clickData),
		});

		return response.ok;
	} catch (error) {
		console.error("Error tracking click:", error);
		return false;
	}
}

/**
 * Generate a session ID for ad tracking
 */
export function generateSessionId(): string {
	return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get device type from user agent
 */
export function getDeviceType(userAgent?: string): string {
	if (!userAgent) return "unknown";
	
	const ua = userAgent.toLowerCase();
	if (ua.includes("mobile") || ua.includes("android") || ua.includes("iphone")) {
		return "mobile";
	}
	if (ua.includes("tablet") || ua.includes("ipad")) {
		return "tablet";
	}
	return "desktop";
}

/**
 * Get user's country code (placeholder - would integrate with geolocation service)
 */
export function getCountryCode(): string {
	// In a real implementation, this would use a geolocation service
	// For now, return a default
	return "US";
}

/**
 * Get user's language preference
 */
export function getLanguage(): string {
	if (typeof window !== "undefined") {
		return navigator.language || "en-US";
	}
	return "en-US";
}
