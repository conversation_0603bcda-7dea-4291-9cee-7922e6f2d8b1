# Smart Ad Serving Implementation

## Overview

The BTDash frontend now includes intelligent ad serving that integrates with the backend ad platform to serve paid campaign ads with fallback to Google AdSense.

## Architecture

### Components

1. **SmartAdBanner** (`components/ads-placements/smart-ad-banner.tsx`)

    - Main component that replaces the old AdBanner
    - Handles paid ad fetching, fallback logic, and tracking
    - Supports all standard IAB ad sizes

2. **Ad Serving API** (`lib/api/ad-serving.ts`)

    - Utilities for fetching ads from backend
    - Tracking functions for impressions and clicks
    - Device detection and user context helpers

3. **API Routes**
    - `/api/ads/serve` - Fetches paid ads from backend
    - `/api/ads/track/impression` - Tracks ad impressions
    - `/api/ads/track/click` - Tracks ad clicks

### Ad Slots

The system uses database-driven slot IDs instead of variants:

-   **Home Page Slots (1-4)**: Leaderboard, Billboard, Medium Rectangle, Skyscraper
-   **Subnet Page Slots (7-10)**: Medium Rectangle, Half Page, Banner, Wide Skyscraper
-   **Company Page Slots (11-14)**: Medium Rectangle, Half Page, Leaderboard, Square Button
-   **Global Slots (15-16)**: <PERSON>up, Sticky Footer

## Smart Ad Logic

1. **Primary**: Attempt to serve paid campaign ads from backend
2. **Fallback**: Use Google AdSense when no paid ads available
3. **Error Handling**: Graceful degradation on API failures
4. **Tracking**: Automatic impression and click tracking

## Usage

### Basic Usage

```tsx
import { SmartAdBanner } from "@/components/ads-placements/smart-ad-banner";

<SmartAdBanner slotId={1} />; // Home leaderboard slot
```

### With Custom Configuration

```tsx
<SmartAdBanner
	slotId={3} // Home medium rectangle slot
	className="my-custom-class"
	googleAdSlotOverride="custom-google-slot-id"
	enablePaidAds={false} // Disable paid ads for testing
/>
```

## Configuration

### Environment Variables Required

```env
# Backend API configuration
API_BASE_URL=http://localhost:3001
INTERNAL_API_KEY=your-internal-api-key

# Google AdSense
NEXT_PUBLIC_GOOGLE_ADSENSE_CLIENT_ID=ca-pub-5681407322305640
```

### Slot Mapping

The component uses direct database slot IDs:

-   **Home Page**: Slots 1-4 (Leaderboard, Billboard, Medium Rectangle, Skyscraper)
-   **Subnet Pages**: Slots 7-10 (Medium Rectangle, Half Page, Banner, Wide Skyscraper)
-   **Company Pages**: Slots 11-14 (Medium Rectangle, Half Page, Leaderboard, Square Button)
-   **Global**: Slots 15-16 (Popup, Sticky Footer)

## Error Handling

### Retry Logic

-   Automatic retry with exponential backoff (1s, 2s, 4s)
-   Maximum 3 retry attempts
-   Graceful fallback to Google AdSense on failure

### Timeout Handling

-   5-second timeout for ad serving requests
-   10-second timeout for backend API calls
-   Automatic fallback on timeout

### Loading States

-   Shows loading indicator while fetching paid ads
-   Displays retry count during retry attempts
-   Seamless transition to Google ads on failure

## Tracking

### Impression Tracking

-   Triggered when ad is 50% visible in viewport
-   Uses Intersection Observer API
-   Includes user context (country, device, language)

### Click Tracking

-   Triggered on ad click before redirect
-   Non-blocking (doesn't delay redirect)
-   Includes session and user context

## Testing

### Test Page

Visit `/test-ads` to test all ad variants and configurations.

### Manual Testing

1. Enable/disable paid ads to test fallback
2. Check browser console for errors
3. Verify tracking events in network tab
4. Test responsive behavior on different screen sizes

### Production Checklist

-   [ ] Environment variables configured
-   [ ] Backend API accessible
-   [ ] Google AdSense account configured
-   [ ] Test page removed or access restricted
-   [ ] Error monitoring configured

## Migration from Old AdBanner

### Before

```tsx
<AdBanner variant="horizontal" adSlot="**********" />
```

### After

```tsx
<SmartAdBanner slotId={1} /> // Use specific slot ID
```

The `adSlot` prop is no longer required as Google AdSense slots are automatically mapped by variant.

## Performance Considerations

-   Client-side caching reduces API calls
-   Lazy loading with Intersection Observer
-   Minimal bundle size impact
-   Graceful degradation ensures fast fallback

## Security

-   Internal API key authentication for backend calls
-   Input validation on all API routes
-   No sensitive data exposed to client
-   Secure tracking with session management

## Monitoring

Monitor these metrics in production:

-   Ad serving success rate
-   Fallback rate to Google AdSense
-   API response times
-   Error rates by type
-   Impression and click tracking accuracy
