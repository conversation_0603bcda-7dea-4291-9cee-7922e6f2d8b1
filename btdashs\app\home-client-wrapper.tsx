"use client";

import { SmartAdBanner } from "@/components/ads-placements/smart-ad-banner";
import { CategoryFilter } from "@/components/category-filter";
import { Dashboardspring } from "@/components/dashboard/dashboard-connect-widgets";
import { EventsRow } from "@/components/events-row";
import { FeaturedSubnetApplications } from "@/components/featured-subnet-applications";
import { NewsArticleRow } from "@/components/news-article-row";
import { NewsSection } from "@/components/news-section";
import { SubnetCategoryRow } from "@/components/subnet-category-row";
import { TrendingSubnets } from "@/components/trending-subnets";
import { TrendingWidgets } from "@/components/trending-widgets";
import { Category, Company, Event, Job, News, Product, Subnet } from "@/lib/db/models";
import { useState } from "react";

export default function HomeClientWrapper({
	categories = [],
	subnets = [],
	metrics = [],
	networkStats = {},
	companies = [],
	news = [],
	jobs = [],
	events = [],
	products = [],
}: {
	categories: Category[];
	subnets: Subnet[];
	metrics: any[];
	networkStats: any;
	companies: Company[];
	news: News[];
	jobs: Job[];
	events: Event[];
	products: Product[];
}) {
	const [selectedCategory_id, setSelectedCategory] = useState<number | null>(null);
	return (
		<>
			{/* Header Section */}
			<div className="mb-6">
				<h1 className="text-3xl font-bold mb-2">Bittensor Subnet Dashboard</h1>
				<p className="text-muted-foreground">Explore and analyze the Bittensor ecosystem</p>
			</div>
			{/* Stats Section */}
			{/* <SubnetsStats subnets={subnets} metrics={metrics} /> */}
			<Dashboardspring
				subnetsCount={subnets.length}
				appsCount={products.length}
				newsCount={news.length}
				jobsCount={jobs.length}
				eventsCount={events.length}
				companiesCount={companies.length}
			/>
			{/* Trending Subnets Section with Ad Space */}
			<div className="mb-8">
				<div className="flex justify-between items-center mb-2">
					<h2 className="text-xl font-bold">Trending Subnets</h2>
					<button
						className="text-sm font-medium text-primary hover:underline"
						onClick={() => (window.location.href = "/subnets")}
					>
						View All
					</button>
				</div>
				<div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
					<div className="lg:col-span-3 bg-card dark:bg-card rounded-lg shadow-sm border dark:border-border">
						<div className="p-2 border-b dark:border-border">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<div className="h-2 w-2 rounded-full bg-green-500"></div>
									<span className="text-xs font-medium">Live Updates</span>
								</div>
								<div className="text-xs text-muted-foreground">Last updated: Just now</div>
							</div>
						</div>
						<div className="p-3">
							<TrendingSubnets subnets={subnets} metrics={metrics} />
						</div>
					</div>
					<div className="bg-gradient-to-br from-card to-card/80 dark:from-card/90 dark:to-card/60 rounded-lg shadow-sm border dark:border-border overflow-hidden flex flex-col w-full">
						<div className="p-2 border-b dark:border-border flex justify-between items-center">
							<span className="font-medium text-xs">Featured</span>
							<span className="text-xs text-muted-foreground">Sponsored</span>
						</div>
						<div className="flex-1 min-h-[200px] w-full">
							<SmartAdBanner variant="square" className="w-full h-full" />
						</div>
					</div>
				</div>
			</div>
			{/* Events Row */}
			<div className="mb-8">
				<EventsRow events={events} company={companies} />
			</div>
			{/* Category Filter */}
			<div className="mb-8">
				<h2 className="text-2xl font-bold mb-3">Explore Subnets by Category</h2>
				<CategoryFilter
					selectedCategory_id={selectedCategory_id}
					onSelectCategory={setSelectedCategory}
					categories={categories}
				/>
			</div>
			{/* Conditional rendering based on selected category */}
			{selectedCategory_id ? (
				<div className="space-y-8">
					{categories
						.filter((category) => category.id === selectedCategory_id)
						.map((category) => (
							<SubnetCategoryRow
								key={category.id}
								categoryName={category.name}
								categoryId={category.id}
								subnets={subnets.filter((s) => category.netuids?.includes(s.netuid))}
								metrics={metrics}
								isScrollable={false}
							/>
						))}

					<div className="mb-8">
						<h2 className="text-2xl font-bold mb-3">Latest Subnet News</h2>
						<NewsArticleRow news={news} subnets={subnets} categories={categories} companies={companies} />
					</div>
				</div>
			) : (
				<div className="space-y-8">
					{categories.slice(0, 2).map((category) => (
						<SubnetCategoryRow
							key={category.id}
							categoryName={category.name}
							categoryId={category.id}
							subnets={subnets.filter((s) => category.netuids?.includes(s.netuid))}
							metrics={metrics}
							isScrollable={true}
						/>
					))}

					<div className="mb-8">
						<h2 className="text-2xl font-bold mb-3">Latest Subnet News</h2>
						<NewsArticleRow news={news} subnets={subnets} categories={categories} companies={companies} />
					</div>
					{categories.slice(2).map((category) => (
						<SubnetCategoryRow
							key={category.id}
							categoryName={category.name}
							categoryId={category.id}
							subnets={subnets.filter((s) => category.netuids?.includes(s.netuid))}
							metrics={metrics}
							isScrollable={true}
						/>
					))}
				</div>
			)}
			<div className="mt-8">
				<FeaturedSubnetApplications apps={products} categories={categories} />
			</div>
			{/* Additional Sections */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
				<div className="lg:col-span-2 space-y-6">
					<NewsSection />
				</div>

				<div className="space-y-6">
					<TrendingWidgets subnets={subnets} subnetMetrics={metrics} networkStats={networkStats} />
				</div>
			</div>
		</>
	);
}
