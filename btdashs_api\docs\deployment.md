# BTDash API Deployment Guide

## Overview

This guide covers the deployment process for the BTDash API, including environment setup, database configuration, and production deployment strategies.

## Prerequisites

### System Requirements
- **Node.js**: Version 18.x or higher
- **PostgreSQL**: Version 13.x or higher
- **Redis**: Version 6.x or higher (for caching and sessions)
- **Memory**: Minimum 2GB RAM for production
- **Storage**: Minimum 20GB available space

### Required Services
- **Auth0**: Authentication provider
- **Database**: PostgreSQL instance
- **Monitoring**: Application performance monitoring (optional)
- **Load Balancer**: For production deployments (optional)

## Environment Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Server Configuration
NODE_ENV=production
PORT=3001
API_BASE_URL=https://api.btdash.com

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=btdashs_production
DB_USER=btdash_user
DB_PASSWORD=secure_password
DB_SSL=true

# Auth0 Configuration
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_AUDIENCE=https://api.btdash.com
AUTH0_ISSUER=https://your-domain.auth0.com/
AUTH0_SYNC_SECRET=your-sync-secret

# API Keys
INTERNAL_API_KEY=your-internal-api-key
TAOSTATS_API_KEY=your-taostats-api-key

# Logging Configuration
LOG_LEVEL=info
PAPERTRAIL_HOST=logs.papertrailapp.com
PAPERTRAIL_PORT=12345

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_MAX_REQUESTS_UNAUTH=20

# CORS Configuration
CORS_ORIGINS=https://btdash.com,https://admanager.btdash.com

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
```

### Development Environment

```bash
# Clone the repository
git clone https://github.com/your-org/btdashs_api.git
cd btdashs_api

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Set up database
npm run db:migrate
npm run db:seed

# Start development server
npm run dev
```

### Production Environment

```bash
# Install dependencies (production only)
npm ci --only=production

# Run database migrations
npm run db:migrate

# Start production server
npm start
```

## Database Setup

### PostgreSQL Installation

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### CentOS/RHEL
```bash
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Docker
```bash
docker run --name btdash-postgres \
  -e POSTGRES_DB=btdashs_production \
  -e POSTGRES_USER=btdash_user \
  -e POSTGRES_PASSWORD=secure_password \
  -p 5432:5432 \
  -v btdash_data:/var/lib/postgresql/data \
  -d postgres:13
```

### Database Configuration

1. **Create Database and User**:
```sql
CREATE DATABASE btdashs_production;
CREATE USER btdash_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE btdashs_production TO btdash_user;
```

2. **Configure PostgreSQL** (`postgresql.conf`):
```
# Connection settings
listen_addresses = '*'
port = 5432
max_connections = 100

# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# Logging
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000

# Security
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
```

3. **Configure Access** (`pg_hba.conf`):
```
# Local connections
local   all             all                                     peer
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5

# Remote connections (adjust as needed)
host    btdashs_production  btdash_user     0.0.0.0/0           md5
```

### Database Migrations

```bash
# Run all pending migrations
npm run db:migrate

# Rollback last migration
npm run db:rollback

# Check migration status
npm run db:status

# Create new migration
npm run db:make migration_name
```

## Deployment Strategies

### 1. Traditional Server Deployment

#### Using PM2 (Recommended)

```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'btdash-api',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
```

#### Using systemd

```bash
# Create service file
sudo cat > /etc/systemd/system/btdash-api.service << EOF
[Unit]
Description=BTDash API
After=network.target

[Service]
Type=simple
User=btdash
WorkingDirectory=/opt/btdash-api
ExecStart=/usr/bin/node server.js
Restart=on-failure
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable btdash-api
sudo systemctl start btdash-api
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

# Create app directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S btdash -u 1001

# Change ownership
RUN chown -R btdash:nodejs /usr/src/app
USER btdash

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start application
CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=************************************************/btdashs_production
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=btdashs_production
      - POSTGRES_USER=btdash_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Cloud Deployment

#### AWS Elastic Beanstalk
```bash
# Install EB CLI
pip install awsebcli

# Initialize EB application
eb init btdash-api

# Create environment
eb create production

# Deploy
eb deploy
```

#### Heroku
```bash
# Install Heroku CLI
# Create Heroku app
heroku create btdash-api

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set AUTH0_DOMAIN=your-domain.auth0.com

# Deploy
git push heroku main

# Run migrations
heroku run npm run db:migrate
```

## Load Balancing and Scaling

### Nginx Configuration

```nginx
upstream btdash_api {
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}

server {
    listen 80;
    server_name api.btdash.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.btdash.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    location / {
        proxy_pass http://btdash_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /health {
        access_log off;
        proxy_pass http://btdash_api;
    }
}
```

## Monitoring and Logging

### Application Monitoring

1. **Health Checks**: Implement comprehensive health checks
2. **Metrics Collection**: Use tools like Prometheus or DataDog
3. **Error Tracking**: Integrate with Sentry or similar services
4. **Performance Monitoring**: Use APM tools like New Relic

### Log Management

```bash
# Centralized logging with rsyslog
echo "*.* @@logs.papertrailapp.com:12345" >> /etc/rsyslog.conf
systemctl restart rsyslog

# Log rotation
cat > /etc/logrotate.d/btdash-api << EOF
/opt/btdash-api/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 btdash btdash
    postrotate
        pm2 reload btdash-api
    endscript
}
EOF
```

## Security Considerations

### SSL/TLS Configuration
- Use Let's Encrypt for free SSL certificates
- Configure strong cipher suites
- Enable HSTS headers
- Implement certificate pinning

### Firewall Configuration
```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 3001  # Block direct API access
sudo ufw enable
```

### Security Headers
```javascript
// Add to Express middleware
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  next();
});
```

## Backup and Recovery

### Database Backups
```bash
# Daily backup script
#!/bin/bash
BACKUP_DIR="/backups/btdash"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U btdash_user btdashs_production > "$BACKUP_DIR/backup_$DATE.sql"

# Keep only last 30 days
find $BACKUP_DIR -name "backup_*.sql" -mtime +30 -delete
```

### Application Backups
```bash
# Backup application files
tar -czf /backups/btdash-api-$(date +%Y%m%d).tar.gz /opt/btdash-api

# Backup to S3
aws s3 cp /backups/btdash-api-$(date +%Y%m%d).tar.gz s3://btdash-backups/
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check database credentials
   - Verify network connectivity
   - Check PostgreSQL service status

2. **Auth0 Authentication Failures**
   - Verify Auth0 configuration
   - Check JWT token validity
   - Validate audience and issuer

3. **Performance Issues**
   - Monitor database query performance
   - Check memory usage
   - Review application logs

### Debugging Commands
```bash
# Check application status
pm2 status
pm2 logs btdash-api

# Database connectivity
psql -h localhost -U btdash_user -d btdashs_production -c "SELECT 1;"

# Check system resources
htop
df -h
free -m

# Network connectivity
netstat -tlnp | grep 3001
curl -I http://localhost:3001/health
```
