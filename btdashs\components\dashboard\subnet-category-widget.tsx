"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Brain, Database, HardDrive, Cpu, MessageSquare } from "lucide-react";

const categories = [
  { id: "ai", name: "AI & ML", icon: Brain },
  { id: "data", name: "Data Validation", icon: Database },
  { id: "storage", name: "Storage", icon: HardDrive },
  { id: "compute", name: "Edge Computing", icon: Cpu },
  { id: "nlp", name: "NLP", icon: MessageSquare },
];

interface SubnetCategoryWidgetProps {
  onCategorySelect: (category: string | null) => void;
}

export function SubnetCategoryWidget({
  onCategorySelect,
}: SubnetCategoryWidgetProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleCategoryClick = (categoryId: string) => {
    const newCategory = categoryId === selectedCategory ? null : categoryId;
    setSelectedCategory(newCategory);
    onCategorySelect(newCategory);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subnet Categories</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              className="h-auto py-4 px-3 flex flex-col items-center justify-center gap-2"
              onClick={() => handleCategoryClick(category.id)}
            >
              <category.icon className="h-6 w-6" />
              <span className="text-sm text-center">{category.name}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
