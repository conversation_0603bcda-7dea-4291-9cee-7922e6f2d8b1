// src/application/services/EducationService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * Education Service - Handles user education records business logic
 */
class EducationService extends BaseService {
	constructor() {
		super("dtm_base.user_educations", "Education");
	}

	/**
	 * Create an education record for a user
	 * @param {number} userId - User ID
	 * @param {Object} educationData - Education data
	 * @returns {Promise<Object>} Created education object
	 */
	async createUserEducation(userId, educationData) {
		const {
			institution_name,
			start_date,
			end_date,
			degree,
			description,
			field_of_study
		} = educationData;

		try {
			return await this.create({
				user_id: userId,
				institution_name,
				start_date,
				end_date,
				degree,
				description,
				field_of_study,
			});
		} catch (error) {
			logger.error("Error creating user education", { error, userId });
			throw error;
		}
	}

	/**
	 * Get all education records for a user
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of education records
	 */
	async getUserEducations(userId) {
		try {
			return await this.getByUserId(userId);
		} catch (error) {
			logger.error("Error getting user educations", { error, userId });
			throw error;
		}
	}

	/**
	 * Update an education record
	 * @param {number} educationId - Education ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated education object
	 */
	async updateUserEducation(educationId, updateData) {
		const {
			institution_name,
			start_date,
			end_date,
			degree,
			description,
			field_of_study
		} = updateData;

		try {
			return await this.updateById(educationId, {
				institution_name,
				start_date,
				end_date,
				degree,
				description,
				field_of_study,
			});
		} catch (error) {
			logger.error("Error updating user education", { error, educationId });
			throw error;
		}
	}

	/**
	 * Delete an education record
	 * @param {number} educationId - Education ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteUserEducation(educationId) {
		try {
			return await this.deleteById(educationId);
		} catch (error) {
			logger.error("Error deleting user education", { error, educationId });
			throw error;
		}
	}
}

module.exports = new EducationService();
