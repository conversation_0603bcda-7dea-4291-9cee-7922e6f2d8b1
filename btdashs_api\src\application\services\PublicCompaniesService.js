// src/application/services/PublicCompaniesService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Public Companies Service - Handles public company directory operations
 * 
 * This service manages the public company directory, separate from user-company
 * relationships handled by CompanyService. It provides CRUD operations for
 * the public company listings that appear in the company directory.
 * 
 * Key responsibilities:
 * - Public company directory management
 * - Company profile CRUD operations
 * - Company search and filtering
 * - Data validation and sanitization
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class PublicCompaniesService extends BaseService {
  constructor() {
    super("dtm_base.companies", "Company");
  }

  /**
   * Get all companies with optional filtering and sorting
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options (limit, offset, orderBy)
   * @returns {Promise<Array>} Array of companies
   */
  async getAllCompanies(filters = {}, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'name', direction: 'asc' },
        ...options
      };

      return await this.getAll(filters, queryOptions);
    } catch (error) {
      logger.error("Error getting all companies", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get company by ID
   * @param {number} id - Company ID
   * @returns {Promise<Object|null>} Company object or null if not found
   */
  async getCompanyById(id) {
    try {
      return await this.getById(id);
    } catch (error) {
      logger.error("Error getting company by ID", { error, id });
      throw error;
    }
  }

  /**
   * Create a new company
   * @param {Object} companyData - Company data
   * @returns {Promise<Object>} Created company object
   */
  async createCompany(companyData) {
    const { name, description, website, logo_url } = companyData;

    try {
      const newCompany = await this.create({
        name,
        description,
        website,
        logo_url,
      });

      logger.info("Company created", { company_id: newCompany.id });
      return newCompany;
    } catch (error) {
      logger.error("Error creating company", { error, companyData });
      throw error;
    }
  }

  /**
   * Update a company
   * @param {number} id - Company ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated company object
   */
  async updateCompany(id, updateData) {
    const { name, description, website, logo_url } = updateData;

    try {
      const updatedCompany = await this.updateById(id, {
        name,
        description,
        website,
        logo_url,
      });

      logger.info("Company updated", { company_id: id });
      return updatedCompany;
    } catch (error) {
      logger.error("Error updating company", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete a company
   * @param {number} id - Company ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteCompany(id) {
    try {
      const result = await this.deleteById(id);
      logger.info("Company deleted", { company_id: id });
      return result;
    } catch (error) {
      logger.error("Error deleting company", { error, id });
      throw error;
    }
  }
}

module.exports = new PublicCompaniesService();
