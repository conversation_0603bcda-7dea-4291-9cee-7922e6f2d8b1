"use client";

import { Calendar, Download, Filter, LayoutDashboard, Loader2, TrendingUp, Users } from "lucide-react";
import { useEffect, useState } from "react";
import {
	Bar,
	BarChart,
	CartesianGrid,
	Cell,
	Legend,
	Line,
	LineChart,
	Pie,
	PieChart,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function AdminAnalyticsPage() {
	const { toast } = useToast();
	const [dateRange, setDateRange] = useState("last30days");

	// Fetch admin analytics data
	const {
		data: analyticsData,
		error: analyticsError,
		isLoading: analyticsLoading,
	} = useSWR(`/api/admin/analytics?range=${dateRange}`, fetcher, {
		refreshInterval: 60000, // Refresh every minute
	});

	useEffect(() => {
		if (analyticsError) {
			toast({
				title: "Error",
				description: "Failed to load analytics data. Please try again.",
				variant: "destructive",
			});
		}
	}, [analyticsError, toast]);

	if (analyticsLoading) {
		return (
			<div className="flex h-64 items-center justify-center">
				<div className="text-center">
					<Loader2 className="mx-auto h-8 w-8 animate-spin" />
					<p className="mt-4 text-lg">Loading analytics data...</p>
				</div>
			</div>
		);
	}

	// Use real data or fallback to empty arrays
	const monthlyData = analyticsData?.monthlyData || [];
	const placementData = analyticsData?.placementData || [];
	const topCampaigns = analyticsData?.topCampaigns || [];
	const recentActivity = analyticsData?.recentActivity || [];

	const deviceData = analyticsData?.deviceData || [
		{ name: "Desktop", value: 45 },
		{ name: "Mobile", value: 40 },
		{ name: "Tablet", value: 15 },
	];

	const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];
	const [viewType, setViewType] = useState<string>("revenue");

	// Calculate total metrics from real data
	const totalImpressions = analyticsData?.total_impressions || 0;
	const totalClicks = analyticsData?.total_clicks || 0;
	const totalRevenue = analyticsData?.total_spend || 0;
	const averageCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

	return (
		<div className="space-y-6 p-6">
			<div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
					<p className="text-muted-foreground">
						Track performance metrics across all campaigns and placements
					</p>
				</div>
				<div className="flex flex-col gap-2 sm:flex-row sm:items-center">
					<Select value={dateRange} onValueChange={setDateRange}>
						<SelectTrigger className="w-[180px]">
							<Calendar className="mr-2 h-4 w-4" />
							<SelectValue placeholder="Select date range" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="today">Today</SelectItem>
							<SelectItem value="yesterday">Yesterday</SelectItem>
							<SelectItem value="last7days">Last 7 days</SelectItem>
							<SelectItem value="last30days">Last 30 days</SelectItem>
							<SelectItem value="thisMonth">This month</SelectItem>
							<SelectItem value="lastMonth">Last month</SelectItem>
							<SelectItem value="custom">Custom range</SelectItem>
						</SelectContent>
					</Select>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="outline" size="icon">
								<Download className="h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuItem>Export as CSV</DropdownMenuItem>
							<DropdownMenuItem>Export as PDF</DropdownMenuItem>
							<DropdownMenuItem>Schedule reports</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
					<Button variant="outline" size="icon">
						<Filter className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Summary Cards */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth="2"
							className="h-4 w-4 text-muted-foreground"
						>
							<path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
						</svg>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
						<p className="text-xs text-muted-foreground">
							<span className="text-green-500">+12.5%</span> from last period
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Impressions</CardTitle>
						<LayoutDashboard className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalImpressions.toLocaleString()}</div>
						<p className="text-xs text-muted-foreground">
							<span className="text-green-500">+8.2%</span> from last period
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Clicks</CardTitle>
						<TrendingUp className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalClicks.toLocaleString()}</div>
						<p className="text-xs text-muted-foreground">
							<span className="text-green-500">+5.7%</span> from last period
						</p>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Average CTR</CardTitle>
						<Users className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{averageCTR.toFixed(2)}%</div>
						<p className="text-xs text-muted-foreground">
							<span className="text-red-500">-0.3%</span> from last period
						</p>
					</CardContent>
				</Card>
			</div>

			<Tabs defaultValue="overview" className="w-full">
				<TabsList className="mb-4 grid w-full grid-cols-4 lg:w-auto">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="campaigns">Campaigns</TabsTrigger>
					<TabsTrigger value="placements">Placements</TabsTrigger>
					<TabsTrigger value="audience">Audience</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-6">
					{/* Revenue/Impressions/Clicks Toggle */}
					<div className="flex justify-end">
						<div className="inline-flex items-center rounded-md border p-1">
							<Button
								variant={viewType === "revenue" ? "default" : "ghost"}
								size="sm"
								onClick={() => setViewType("revenue")}
								className="text-xs"
							>
								Revenue
							</Button>
							<Button
								variant={viewType === "impressions" ? "default" : "ghost"}
								size="sm"
								onClick={() => setViewType("impressions")}
								className="text-xs"
							>
								Impressions
							</Button>
							<Button
								variant={viewType === "clicks" ? "default" : "ghost"}
								size="sm"
								onClick={() => setViewType("clicks")}
								className="text-xs"
							>
								Clicks
							</Button>
						</div>
					</div>

					{/* Main Chart */}
					<Card>
						<CardHeader>
							<CardTitle>
								{viewType === "revenue"
									? "Revenue"
									: viewType === "impressions"
									? "Impressions"
									: "Clicks"}{" "}
								Over Time
							</CardTitle>
							<CardDescription>Monthly performance metrics for all campaigns</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="h-[350px] w-full">
								<ResponsiveContainer width="100%" height="100%">
									<LineChart data={monthlyData}>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis dataKey="name" />
										<YAxis />
										<Tooltip
											formatter={(value) => [
												viewType === "revenue" ? `$${value}` : value.toLocaleString(),
												viewType === "revenue"
													? "Revenue"
													: viewType === "impressions"
													? "Impressions"
													: "Clicks",
											]}
										/>
										<Legend />
										<Line
											type="monotone"
											dataKey={viewType}
											stroke="#8884d8"
											strokeWidth={2}
											activeDot={{ r: 8 }}
										/>
									</LineChart>
								</ResponsiveContainer>
							</div>
						</CardContent>
						<CardFooter className="justify-between border-t px-6 py-4">
							<div className="flex items-center">
								<div className="w-2 h-2 rounded-full bg-[#8884d8] mr-2"></div>
								<span className="text-sm text-muted-foreground">
									{viewType === "revenue"
										? "Total Revenue"
										: viewType === "impressions"
										? "Total Impressions"
										: "Total Clicks"}
								</span>
							</div>
							<div className="text-sm font-medium">
								{viewType === "revenue"
									? `$${totalRevenue.toLocaleString()}`
									: viewType === "impressions"
									? totalImpressions.toLocaleString()
									: totalClicks.toLocaleString()}
							</div>
						</CardFooter>
					</Card>

					<div className="grid gap-6 md:grid-cols-2">
						{/* Placement Performance */}
						<Card>
							<CardHeader>
								<CardTitle>Top Placements</CardTitle>
								<CardDescription>Performance by placement type</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="h-[300px] w-full">
									<ResponsiveContainer width="100%" height="100%">
										<BarChart data={placementData}>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis dataKey="name" />
											<YAxis />
											<Tooltip />
											<Legend />
											<Bar dataKey="impressions" fill="#8884d8" name="Impressions" />
											<Bar dataKey="clicks" fill="#82ca9d" name="Clicks" />
										</BarChart>
									</ResponsiveContainer>
								</div>
							</CardContent>
						</Card>

						{/* Device Distribution */}
						<Card>
							<CardHeader>
								<CardTitle>Device Distribution</CardTitle>
								<CardDescription>Traffic by device type</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="h-[300px] w-full">
									<ResponsiveContainer width="100%" height="100%">
										<PieChart>
											<Pie
												data={deviceData}
												cx="50%"
												cy="50%"
												labelLine={false}
												label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
												outerRadius={100}
												fill="#8884d8"
												dataKey="value"
											>
												{deviceData.map((entry, index) => (
													<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
												))}
											</Pie>
											<Tooltip formatter={(value) => [`${value}%`, "Percentage"]} />
										</PieChart>
									</ResponsiveContainer>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="campaigns" className="space-y-6">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between">
							<div>
								<CardTitle>Campaign Performance</CardTitle>
								<CardDescription>Detailed metrics for all active campaigns</CardDescription>
							</div>
							<Select defaultValue="all">
								<SelectTrigger className="w-[180px]">
									<SelectValue placeholder="Filter by status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Campaigns</SelectItem>
									<SelectItem value="active">Active</SelectItem>
									<SelectItem value="completed">Completed</SelectItem>
									<SelectItem value="pending">Pending</SelectItem>
								</SelectContent>
							</Select>
						</CardHeader>
						<CardContent>
							<div className="space-y-8">
								<div className="rounded-md border">
									<div className="grid grid-cols-12 border-b bg-muted/50 p-4 text-sm font-medium">
										<div className="col-span-4">Campaign</div>
										<div className="col-span-2 text-right">Impressions</div>
										<div className="col-span-2 text-right">Clicks</div>
										<div className="col-span-2 text-right">CTR</div>
										<div className="col-span-2 text-right">Revenue</div>
									</div>
									<div className="divide-y">
										{approvedCampaigns.map((campaign) => {
											// Generate random metrics for each campaign
											const impressions =
												campaign.impressions || Math.floor(Math.random() * 10000) + 1000;
											const clicks =
												campaign.clicks ||
												Math.floor(impressions * (Math.random() * 0.08 + 0.02));
											const ctr = (clicks / impressions) * 100;
											const revenue = Math.floor(clicks * (Math.random() * 2 + 1));

											return (
												<div key={campaign.id} className="grid grid-cols-12 items-center p-4">
													<div className="col-span-4">
														<div className="font-medium">{campaign.name}</div>
														<div className="text-sm text-muted-foreground">
															{new Date(campaign.startDate).toLocaleDateString()} -{" "}
															{new Date(campaign.endDate).toLocaleDateString()}
														</div>
														<div className="mt-1">
															<Badge variant="outline" className="text-xs">
																{campaign.status}
															</Badge>
														</div>
													</div>
													<div className="col-span-2 text-right">
														{impressions.toLocaleString()}
													</div>
													<div className="col-span-2 text-right">
														{clicks.toLocaleString()}
													</div>
													<div className="col-span-2 text-right">{ctr.toFixed(2)}%</div>
													<div className="col-span-2 text-right">
														${revenue.toLocaleString()}
													</div>
												</div>
											);
										})}
									</div>
								</div>
							</div>
						</CardContent>
						<CardFooter className="flex justify-between border-t px-6 py-4">
							<div className="text-sm text-muted-foreground">
								Showing {approvedCampaigns.length} campaigns
							</div>
							<Button variant="outline" size="sm">
								View All Campaigns
							</Button>
						</CardFooter>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Campaign Comparison</CardTitle>
							<CardDescription>Compare performance across campaigns</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="h-[400px] w-full">
								<ResponsiveContainer width="100%" height="100%">
									<BarChart
										data={approvedCampaigns.slice(0, 5).map((campaign) => {
											const impressions =
												campaign.impressions || Math.floor(Math.random() * 10000) + 1000;
											const clicks =
												campaign.clicks ||
												Math.floor(impressions * (Math.random() * 0.08 + 0.02));
											const ctr = (clicks / impressions) * 100;

											return {
												name:
													campaign.name.length > 15
														? campaign.name.substring(0, 15) + "..."
														: campaign.name,
												impressions,
												clicks,
												ctr,
											};
										})}
										layout="vertical"
										margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
									>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis type="number" />
										<YAxis dataKey="name" type="category" width={150} />
										<Tooltip />
										<Legend />
										<Bar dataKey="impressions" fill="#8884d8" name="Impressions" />
										<Bar dataKey="clicks" fill="#82ca9d" name="Clicks" />
									</BarChart>
								</ResponsiveContainer>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="placements" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Placement Analytics</CardTitle>
							<CardDescription>Performance metrics for all ad placements</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-8">
								<div className="rounded-md border">
									<div className="grid grid-cols-12 border-b bg-muted/50 p-4 text-sm font-medium">
										<div className="col-span-4">Placement</div>
										<div className="col-span-2 text-right">Est. Views</div>
										<div className="col-span-2 text-right">Fill Rate</div>
										<div className="col-span-2 text-right">Avg. CTR</div>
										<div className="col-span-2 text-right">Revenue</div>
									</div>
									<div className="divide-y">
										{db.placements.getAll().map((placement) => {
											// Generate random metrics for each placement
											const fillRate = Math.floor(Math.random() * 30) + 70; // 70-100%
											const ctr = (Math.random() * 3 + 4).toFixed(2); // 4-7%
											const revenue = Math.floor(
												placement.estimatedViews * (Number.parseInt(ctr) / 100) * 0.5
											);

											return (
												<div key={placement.id} className="grid grid-cols-12 items-center p-4">
													<div className="col-span-4">
														<div className="font-medium">{placement.name}</div>
														<div className="text-sm text-muted-foreground">
															{placement.dimensions} • {placement.priceDisplay}
														</div>
													</div>
													<div className="col-span-2 text-right">
														{placement.estimatedViews.toLocaleString()}
													</div>
													<div className="col-span-2 text-right">{fillRate}%</div>
													<div className="col-span-2 text-right">{ctr}%</div>
													<div className="col-span-2 text-right">
														${revenue.toLocaleString()}
													</div>
												</div>
											);
										})}
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					<div className="grid gap-6 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle>Fill Rate by Placement</CardTitle>
								<CardDescription>Percentage of ad inventory filled</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="h-[300px] w-full">
									<ResponsiveContainer width="100%" height="100%">
										<BarChart
											data={db.placements.getAll().map((placement) => ({
												name:
													placement.name.length > 15
														? placement.name.substring(0, 15) + "..."
														: placement.name,
												fillRate: Math.floor(Math.random() * 30) + 70,
											}))}
										>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis dataKey="name" />
											<YAxis domain={[0, 100]} />
											<Tooltip formatter={(value) => [`${value}%`, "Fill Rate"]} />
											<Legend />
											<Bar dataKey="fillRate" fill="#8884d8" name="Fill Rate (%)" />
										</BarChart>
									</ResponsiveContainer>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Revenue by Placement</CardTitle>
								<CardDescription>Distribution of revenue across placements</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="h-[300px] w-full">
									<ResponsiveContainer width="100%" height="100%">
										<PieChart>
											<Pie
												data={db.placements.getAll().map((placement) => {
													const ctr = (Math.random() * 3 + 4) / 100;
													const revenue = Math.floor(placement.estimatedViews * ctr * 0.5);
													return {
														name:
															placement.name.length > 10
																? placement.name.substring(0, 10) + "..."
																: placement.name,
														value: revenue,
													};
												})}
												cx="50%"
												cy="50%"
												labelLine={false}
												label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
												outerRadius={100}
												fill="#8884d8"
												dataKey="value"
											>
												{db.placements.getAll().map((_, index) => (
													<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
												))}
											</Pie>
											<Tooltip formatter={(value) => [`$${value}`, "Revenue"]} />
										</PieChart>
									</ResponsiveContainer>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				<TabsContent value="audience" className="space-y-6">
					<div className="grid gap-6 md:grid-cols-2">
						<Card>
							<CardHeader>
								<CardTitle>Geographic Distribution</CardTitle>
								<CardDescription>User distribution by country</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									{[
										{ country: "United States", percentage: 42, color: "#0088FE" },
										{ country: "United Kingdom", percentage: 18, color: "#00C49F" },
										{ country: "Germany", percentage: 12, color: "#FFBB28" },
										{ country: "France", percentage: 8, color: "#FF8042" },
										{ country: "Canada", percentage: 6, color: "#8884d8" },
										{ country: "Other", percentage: 14, color: "#82ca9d" },
									].map((item) => (
										<div key={item.country} className="space-y-2">
											<div className="flex items-center justify-between">
												<div className="flex items-center gap-2">
													<div
														className="h-3 w-3 rounded-full"
														style={{ backgroundColor: item.color }}
													></div>
													<span className="text-sm font-medium">{item.country}</span>
												</div>
												<span className="text-sm font-medium">{item.percentage}%</span>
											</div>
											<div className="h-2 w-full rounded-full bg-muted">
												<div
													className="h-2 rounded-full"
													style={{
														width: `${item.percentage}%`,
														backgroundColor: item.color,
													}}
												></div>
											</div>
										</div>
									))}
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardHeader>
								<CardTitle>Age Distribution</CardTitle>
								<CardDescription>User distribution by age group</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="h-[300px] w-full">
									<ResponsiveContainer width="100%" height="100%">
										<BarChart
											data={[
												{ age: "18-24", users: 15 },
												{ age: "25-34", users: 30 },
												{ age: "35-44", users: 25 },
												{ age: "45-54", users: 18 },
												{ age: "55-64", users: 8 },
												{ age: "65+", users: 4 },
											]}
										>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis dataKey="age" />
											<YAxis />
											<Tooltip formatter={(value) => [`${value}%`, "Percentage"]} />
											<Bar dataKey="users" fill="#8884d8" name="Users (%)" />
										</BarChart>
									</ResponsiveContainer>
								</div>
							</CardContent>
						</Card>
					</div>

					<Card>
						<CardHeader>
							<CardTitle>Audience Interests</CardTitle>
							<CardDescription>Top interests of your audience</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="grid gap-4 md:grid-cols-2">
								<div className="space-y-4">
									{[
										{ interest: "Technology", percentage: 68 },
										{ interest: "Business", percentage: 52 },
										{ interest: "Marketing", percentage: 47 },
										{ interest: "Design", percentage: 41 },
										{ interest: "Development", percentage: 38 },
									].map((item) => (
										<div key={item.interest} className="space-y-2">
											<div className="flex items-center justify-between">
												<span className="text-sm font-medium">{item.interest}</span>
												<span className="text-sm font-medium">{item.percentage}%</span>
											</div>
											<div className="h-2 w-full rounded-full bg-muted">
												<div
													className="h-2 rounded-full bg-primary"
													style={{ width: `${item.percentage}%` }}
												></div>
											</div>
										</div>
									))}
								</div>
								<div className="space-y-4">
									{[
										{ interest: "Finance", percentage: 35 },
										{ interest: "Education", percentage: 32 },
										{ interest: "Entertainment", percentage: 29 },
										{ interest: "Health", percentage: 26 },
										{ interest: "Travel", percentage: 23 },
									].map((item) => (
										<div key={item.interest} className="space-y-2">
											<div className="flex items-center justify-between">
												<span className="text-sm font-medium">{item.interest}</span>
												<span className="text-sm font-medium">{item.percentage}%</span>
											</div>
											<div className="h-2 w-full rounded-full bg-muted">
												<div
													className="h-2 rounded-full bg-primary"
													style={{ width: `${item.percentage}%` }}
												></div>
											</div>
										</div>
									))}
								</div>
							</div>
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
