const GitHubService = require("../../application/services/GitHubService");
const db = require("../../infrastructure/database/knex");
const githubService = require("../../infrastructure/github/githubService");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getSubnetGithubContributions = async (req, res) => {
	try {
		const { id } = req.params;
		_;
		const subnetMetrics = await db("subnet_metrics").select("github_contributions").where({ netuid: id }).first();

		res.status(200).json(subnetMetrics?.github_contributions || []);
	} catch (error) {
		console.error("Error fetching contributions:", error);
		res.status(500).json({ error: "Failed to fetch contributions" });
	}
};

/* --- GITHUB API INTERACTIONS --- */

// Fetch GitHub contributions for a specific subnet
const updateAllSubnetsGithubContributions = async (req, res) => {
	try {
		const subnets = await db("subnets").select("netuid", "github_repo");

		for (const subnet of subnets) {
			const { netuid, github_repo } = subnet;
			if (!github_repo) {
				console.log(`Skipping subnet ${netuid} - no GitHub repo`);
				continue;
			}

			// Parse GitHub repository details
			const repoMatch = github_repo.match(/github.com[/:]([^/]+)\/([^/.#]+)/);
			if (!repoMatch) {
				console.log(`Invalid GitHub URL format for subnet ${netuid}: ${github_repo}`);
				continue;
			}
			const [_, owner, repo] = repoMatch;

			try {
				// Get existing metrics
				const existingMetrics = await db("subnet_metrics").where({ netuid }).first();

				// Parse existing contributions
				let existingData = [];
				let lastUpdate = null;
				if (existingMetrics?.github_contributions) {
					try {
						const contributions =
							typeof existingMetrics.github_contributions === "string"
								? JSON.parse(existingMetrics.github_contributions)
								: existingMetrics.github_contributions;

						existingData = contributions.data || [];
						lastUpdate = contributions.last_update_day;
					} catch (e) {
						console.error(`Invalid JSON for subnet ${netuid}:`, e.message);
					}
				}

				// Calculate since date
				const sinceDate = lastUpdate
					? new Date(new Date(lastUpdate).getTime() + 86400000) // Add 1 day
					: new Date(new Date().setFullYear(new Date().getFullYear() - 1));

				// Fetch new contributions
				const newContributions = await githubService.getCommitCountsSince(owner, repo, sinceDate);

				// Merge and deduplicate data
				const mergedData = [...existingData, ...(newContributions.data || [])]
					.reduce((acc, curr) => {
						if (!acc.some((item) => item.date === curr.date)) {
							acc.push(curr);
						}
						return acc;
					}, [])
					.sort((a, b) => a.date.localeCompare(b.date));

				// Prepare the update payload
				const updatePayload = {
					github_contributions: db.raw("?::jsonb", [
						JSON.stringify({
							data: mergedData,
							last_update_day: newContributions.last_update_day || new Date().toISOString().split("T")[0],
						}),
					]),
				};

				// Execute update/insert
				if (existingMetrics) {
					await db("subnet_metrics").where({ netuid }).update(updatePayload);
				} else {
					await db("subnet_metrics").insert({
						netuid,
						...updatePayload,
					});
				}

				console.log(`Successfully updated subnet ${netuid}`);
			} catch (error) {
				console.error(`Error processing subnet ${netuid}:`, error.message);
			}
		}

		updateEndpointStatus("/update/github-contributions", true, "GitHub contributions updated successfully");

		res.status(200).json({ message: "GitHub contributions update completed" });
	} catch (error) {
		console.error("Global update error:", error);

		updateEndpointStatus("/update/github-contributions", false, "Failed to complete GitHub contributions update");

		res.status(500).json({ error: "Failed to complete GitHub contributions update" });
	}
};

module.exports = {
	getSubnetGithubContributions,
	updateAllSubnetsGithubContributions,
};
