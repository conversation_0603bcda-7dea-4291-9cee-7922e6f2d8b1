const RateLimitService = require("../application/services/RateLimitService");
const AdsService = require("../application/services/AdsService");
const logger = require("../../logger");

class RateLimitMiddleware {
	constructor() {
		this.rateLimitService = new RateLimitService();
	}

	/**
	 * Create rate limit middleware for specific endpoint type
	 * @param {string} limitType - Type of rate limit to apply
	 * @param {Object} options - Additional options
	 * @returns {Function} Express middleware function
	 */
	createRateLimit(limitType, options = {}) {
		return async (req, res, next) => {
			// TEMPORARILY DISABLED: Rate limiting bypassed for development
			// TODO: Fix Redis connection issues and re-enable rate limiting
			return next();

			try {
				const identifier = await this.getIdentifier(req, options);
				const customLimit = await this.getCustomLimit(req, limitType, options);

				const result = await this.rateLimitService.checkRateLimit(identifier, limitType, customLimit);

				// Add rate limit headers
				this.addRateLimitHeaders(res, result);

				if (!result.allowed) {
					logger.warn("Rate limit exceeded", {
						identifier,
						limitType,
						ip: req.ip,
						userAgent: req.headers["user-agent"],
						endpoint: req.path,
					});

					return res.status(429).json({
						success: false,
						message: "Rate limit exceeded. Please try again later.",
						error: "RATE_LIMIT_EXCEEDED",
						retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
						limit: result.limit,
						remaining: result.remaining,
					});
				}

				// Add rate limit info to request for logging
				req.rateLimit = result;
				next();
			} catch (error) {
				logger.error("Rate limit middleware error", { error, limitType });
				// On error, allow request to proceed
				next();
			}
		};
	}

	/**
	 * Create multiple rate limit middleware
	 * @param {Array} limitTypes - Array of limit types to check
	 * @param {Object} options - Additional options
	 * @returns {Function} Express middleware function
	 */
	createMultipleRateLimit(limitTypes, options = {}) {
		return async (req, res, next) => {
			try {
				const identifier = await this.getIdentifier(req, options);

				const result = await this.rateLimitService.checkMultipleRateLimits(identifier, limitTypes);

				// Add rate limit headers
				this.addRateLimitHeaders(res, result);

				if (!result.allowed) {
					logger.warn("Multiple rate limit exceeded", {
						identifier,
						limitTypes,
						limitType: result.limitType,
						ip: req.ip,
						endpoint: req.path,
					});

					return res.status(429).json({
						success: false,
						message: "Rate limit exceeded. Please try again later.",
						error: "RATE_LIMIT_EXCEEDED",
						limitType: result.limitType,
						retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
						remaining: result.remaining,
					});
				}

				req.rateLimit = result;
				next();
			} catch (error) {
				logger.error("Multiple rate limit middleware error", { error, limitTypes });
				next();
			}
		};
	}

	/**
	 * Get identifier for rate limiting
	 * @param {Object} req - Express request object
	 * @param {Object} options - Options for identifier selection
	 * @returns {Promise<string>} Identifier string
	 */
	async getIdentifier(req, options = {}) {
		// Priority: user_id > api_key > ip_address

		// 1. Try to get user ID from JWT
		if (req.auth?.sub) {
			try {
				const UserService = require("../application/services/UserService");
				const user = await UserService.getUserByAuth0Id(req.auth.sub);
				if (user) {
					const isAdmin = await UserService.isAdminUser(req.auth.sub);
					const prefix = isAdmin ? "admin" : "user";
					return `${prefix}:${user.id}`;
				}
			} catch (error) {
				logger.warn("Failed to get user for rate limiting", { error });
			}
		}

		// 2. Try to get API key identifier
		if (req.headers["x-api-key"] || req.headers["x-internal-key"]) {
			const apiKey = req.headers["x-api-key"] || req.headers["x-internal-key"];
			return `api_key:${apiKey.substring(0, 8)}...`; // Partial key for logging
		}

		// 3. Fall back to IP address
		const ip = req.ip || req.connection.remoteAddress || "unknown";
		return `ip:${ip}`;
	}

	/**
	 * Get custom rate limit for user/endpoint
	 * @param {Object} req - Express request object
	 * @param {string} limitType - Type of limit
	 * @param {Object} options - Additional options
	 * @returns {Promise<Object|null>} Custom limit or null
	 */
	async getCustomLimit(req, limitType, options = {}) {
		try {
			// Check for user-specific custom limits
			if (req.auth?.sub) {
				const user = await AdsService.getUserByAuth0Id(req.auth.sub);
				if (user) {
					const customLimit = await this.rateLimitService.getCustomRateLimit(`user:${user.id}`, limitType);
					if (customLimit) {
						return customLimit;
					}
				}
			}

			// Check for IP-specific custom limits
			const ip = req.ip || req.connection.remoteAddress;
			if (ip) {
				const customLimit = await this.rateLimitService.getCustomRateLimit(`ip:${ip}`, limitType);
				if (customLimit) {
					return customLimit;
				}
			}

			return null;
		} catch (error) {
			logger.error("Get custom limit error", { error, limitType });
			return null;
		}
	}

	/**
	 * Add rate limit headers to response
	 * @param {Object} res - Express response object
	 * @param {Object} result - Rate limit result
	 */
	addRateLimitHeaders(res, result) {
		res.set({
			"X-RateLimit-Limit": result.limit || "unknown",
			"X-RateLimit-Remaining": result.remaining || 0,
			"X-RateLimit-Reset": result.resetTime ? Math.ceil(result.resetTime / 1000) : "unknown",
		});

		if (!result.allowed) {
			res.set({
				"Retry-After": Math.ceil((result.resetTime - Date.now()) / 1000),
			});
		}
	}

	/**
	 * Create adaptive rate limit that adjusts based on system load
	 * @param {string} limitType - Base limit type
	 * @param {Object} options - Options including load factor
	 * @returns {Function} Express middleware function
	 */
	createAdaptiveRateLimit(limitType, options = {}) {
		return async (req, res, next) => {
			try {
				const identifier = await this.getIdentifier(req, options);

				// Get base limit
				let customLimit = await this.getCustomLimit(req, limitType, options);

				// Apply load factor if specified
				if (options.loadFactor && customLimit) {
					customLimit = {
						...customLimit,
						requests: Math.floor(customLimit.requests * options.loadFactor),
					};
				}

				const result = await this.rateLimitService.checkRateLimit(identifier, limitType, customLimit);

				this.addRateLimitHeaders(res, result);

				if (!result.allowed) {
					logger.warn("Adaptive rate limit exceeded", {
						identifier,
						limitType,
						loadFactor: options.loadFactor,
						ip: req.ip,
						endpoint: req.path,
					});

					return res.status(429).json({
						success: false,
						message: "Rate limit exceeded due to high system load. Please try again later.",
						error: "RATE_LIMIT_EXCEEDED",
						retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
						remaining: result.remaining,
					});
				}

				req.rateLimit = result;
				next();
			} catch (error) {
				logger.error("Adaptive rate limit middleware error", { error, limitType });
				next();
			}
		};
	}
}

// Create singleton instance
const rateLimitMiddleware = new RateLimitMiddleware();

// Export commonly used middleware functions
module.exports = {
	// General API rate limiting
	apiGeneral: rateLimitMiddleware.createRateLimit("api:general"),
	apiAuth: rateLimitMiddleware.createRateLimit("api:auth"),
	apiUpload: rateLimitMiddleware.createRateLimit("api:upload"),
	apiAnalytics: rateLimitMiddleware.createRateLimit("api:analytics"),

	// User-specific rate limiting
	userGeneral: rateLimitMiddleware.createRateLimit("user:general"),
	userCampaigns: rateLimitMiddleware.createRateLimit("user:campaigns"),
	userAds: rateLimitMiddleware.createRateLimit("user:ads"),

	// Admin rate limiting
	adminGeneral: rateLimitMiddleware.createRateLimit("admin:general"),
	adminApproval: rateLimitMiddleware.createRateLimit("admin:approval"),

	// IP-based rate limiting
	ipGeneral: rateLimitMiddleware.createRateLimit("ip:general"),
	ipStrict: rateLimitMiddleware.createRateLimit("ip:strict"),

	// Multiple rate limits
	userMultiple: rateLimitMiddleware.createMultipleRateLimit(["user:general", "ip:general"]),
	adminMultiple: rateLimitMiddleware.createMultipleRateLimit(["admin:general", "ip:general"]),

	// Custom middleware creator
	create: rateLimitMiddleware.createRateLimit.bind(rateLimitMiddleware),
	createMultiple: rateLimitMiddleware.createMultipleRateLimit.bind(rateLimitMiddleware),
	createAdaptive: rateLimitMiddleware.createAdaptiveRateLimit.bind(rateLimitMiddleware),

	// Service instance for direct access
	service: rateLimitMiddleware.rateLimitService,
};
