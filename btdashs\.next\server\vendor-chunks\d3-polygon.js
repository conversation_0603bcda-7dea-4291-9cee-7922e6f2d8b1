"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-polygon";
exports.ids = ["vendor-chunks/d3-polygon"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-polygon/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-polygon/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n  var i = -1,\n      n = polygon.length,\n      a,\n      b = polygon[n - 1],\n      area = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    area += a[1] * b[0] - a[0] * b[1];\n  }\n\n  return area / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1wb2x5Z29uXFxzcmNcXGFyZWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocG9seWdvbikge1xuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgYSxcbiAgICAgIGIgPSBwb2x5Z29uW24gLSAxXSxcbiAgICAgIGFyZWEgPSAwO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgYSA9IGI7XG4gICAgYiA9IHBvbHlnb25baV07XG4gICAgYXJlYSArPSBhWzFdICogYlswXSAtIGFbMF0gKiBiWzFdO1xuICB9XG5cbiAgcmV0dXJuIGFyZWEgLyAyO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/centroid.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-polygon/src/centroid.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n  var i = -1,\n      n = polygon.length,\n      x = 0,\n      y = 0,\n      a,\n      b = polygon[n - 1],\n      c,\n      k = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    k += c = a[0] * b[1] - b[0] * a[1];\n    x += (a[0] + b[0]) * c;\n    y += (a[1] + b[1]) * c;\n  }\n\n  return k *= 3, [x / k, y / k];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY2VudHJvaWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLXBvbHlnb25cXHNyY1xcY2VudHJvaWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocG9seWdvbikge1xuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgeCA9IDAsXG4gICAgICB5ID0gMCxcbiAgICAgIGEsXG4gICAgICBiID0gcG9seWdvbltuIC0gMV0sXG4gICAgICBjLFxuICAgICAgayA9IDA7XG5cbiAgd2hpbGUgKCsraSA8IG4pIHtcbiAgICBhID0gYjtcbiAgICBiID0gcG9seWdvbltpXTtcbiAgICBrICs9IGMgPSBhWzBdICogYlsxXSAtIGJbMF0gKiBhWzFdO1xuICAgIHggKz0gKGFbMF0gKyBiWzBdKSAqIGM7XG4gICAgeSArPSAoYVsxXSArIGJbMV0pICogYztcbiAgfVxuXG4gIHJldHVybiBrICo9IDMsIFt4IC8gaywgeSAvIGtdO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-polygon/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n  var n = polygon.length,\n      p = polygon[n - 1],\n      x = point[0], y = point[1],\n      x0 = p[0], y0 = p[1],\n      x1, y1,\n      inside = false;\n\n  for (var i = 0; i < n; ++i) {\n    p = polygon[i], x1 = p[0], y1 = p[1];\n    if (((y1 > y) !== (y0 > y)) && (x < (x0 - x1) * (y - y1) / (y0 - y1) + x1)) inside = !inside;\n    x0 = x1, y0 = y1;\n  }\n\n  return inside;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY29udGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0IsT0FBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtcG9seWdvblxcc3JjXFxjb250YWlucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2x5Z29uLCBwb2ludCkge1xuICB2YXIgbiA9IHBvbHlnb24ubGVuZ3RoLFxuICAgICAgcCA9IHBvbHlnb25bbiAtIDFdLFxuICAgICAgeCA9IHBvaW50WzBdLCB5ID0gcG9pbnRbMV0sXG4gICAgICB4MCA9IHBbMF0sIHkwID0gcFsxXSxcbiAgICAgIHgxLCB5MSxcbiAgICAgIGluc2lkZSA9IGZhbHNlO1xuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbjsgKytpKSB7XG4gICAgcCA9IHBvbHlnb25baV0sIHgxID0gcFswXSwgeTEgPSBwWzFdO1xuICAgIGlmICgoKHkxID4geSkgIT09ICh5MCA+IHkpKSAmJiAoeCA8ICh4MCAtIHgxKSAqICh5IC0geTEpIC8gKHkwIC0geTEpICsgeDEpKSBpbnNpZGUgPSAhaW5zaWRlO1xuICAgIHgwID0geDEsIHkwID0geTE7XG4gIH1cblxuICByZXR1cm4gaW5zaWRlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/cross.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-polygon/src/cross.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvY3Jvc3MuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLXBvbHlnb25cXHNyY1xcY3Jvc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmV0dXJucyB0aGUgMkQgY3Jvc3MgcHJvZHVjdCBvZiBBQiBhbmQgQUMgdmVjdG9ycywgaS5lLiwgdGhlIHotY29tcG9uZW50IG9mXG4vLyB0aGUgM0QgY3Jvc3MgcHJvZHVjdCBpbiBhIHF1YWRyYW50IEkgQ2FydGVzaWFuIGNvb3JkaW5hdGUgc3lzdGVtICgreCBpc1xuLy8gcmlnaHQsICt5IGlzIHVwKS4gUmV0dXJucyBhIHBvc2l0aXZlIHZhbHVlIGlmIEFCQyBpcyBjb3VudGVyLWNsb2Nrd2lzZSxcbi8vIG5lZ2F0aXZlIGlmIGNsb2Nrd2lzZSwgYW5kIHplcm8gaWYgdGhlIHBvaW50cyBhcmUgY29sbGluZWFyLlxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oYSwgYiwgYykge1xuICByZXR1cm4gKGJbMF0gLSBhWzBdKSAqIChjWzFdIC0gYVsxXSkgLSAoYlsxXSAtIGFbMV0pICogKGNbMF0gLSBhWzBdKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/cross.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/hull.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-polygon/src/hull.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/./node_modules/d3-polygon/src/cross.js\");\n\n\nfunction lexicographicOrder(a, b) {\n  return a[0] - b[0] || a[1] - b[1];\n}\n\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n  const n = points.length,\n      indexes = [0, 1];\n  let size = 2, i;\n\n  for (i = 2; i < n; ++i) {\n    while (size > 1 && (0,_cross_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0) --size;\n    indexes[size++] = i;\n  }\n\n  return indexes.slice(0, size); // remove popped points\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(points) {\n  if ((n = points.length) < 3) return null;\n\n  var i,\n      n,\n      sortedPoints = new Array(n),\n      flippedPoints = new Array(n);\n\n  for (i = 0; i < n; ++i) sortedPoints[i] = [+points[i][0], +points[i][1], i];\n  sortedPoints.sort(lexicographicOrder);\n  for (i = 0; i < n; ++i) flippedPoints[i] = [sortedPoints[i][0], -sortedPoints[i][1]];\n\n  var upperIndexes = computeUpperHullIndexes(sortedPoints),\n      lowerIndexes = computeUpperHullIndexes(flippedPoints);\n\n  // Construct the hull polygon, removing possible duplicate endpoints.\n  var skipLeft = lowerIndexes[0] === upperIndexes[0],\n      skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1],\n      hull = [];\n\n  // Add upper hull in right-to-l order.\n  // Then add lower hull in left-to-right order.\n  for (i = upperIndexes.length - 1; i >= 0; --i) hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n  for (i = +skipLeft; i < lowerIndexes.length - skipRight; ++i) hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n\n  return hull;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/hull.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-polygon/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polygonArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   polygonCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   polygonContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   polygonHull: () => (/* reexport safe */ _hull_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   polygonLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-polygon/src/area.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-polygon/src/centroid.js\");\n/* harmony import */ var _hull_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hull.js */ \"(ssr)/./node_modules/d3-polygon/src/hull.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-polygon/src/contains.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-polygon/src/length.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpRDtBQUNRO0FBQ1I7QUFDUTtBQUNKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtcG9seWdvblxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgcG9seWdvbkFyZWF9IGZyb20gXCIuL2FyZWEuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uQ2VudHJvaWR9IGZyb20gXCIuL2NlbnRyb2lkLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcG9seWdvbkh1bGx9IGZyb20gXCIuL2h1bGwuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyBwb2x5Z29uQ29udGFpbnN9IGZyb20gXCIuL2NvbnRhaW5zLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcG9seWdvbkxlbmd0aH0gZnJvbSBcIi4vbGVuZ3RoLmpzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-polygon/src/length.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-polygon/src/length.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon) {\n  var i = -1,\n      n = polygon.length,\n      b = polygon[n - 1],\n      xa,\n      ya,\n      xb = b[0],\n      yb = b[1],\n      perimeter = 0;\n\n  while (++i < n) {\n    xa = xb;\n    ya = yb;\n    b = polygon[i];\n    xb = b[0];\n    yb = b[1];\n    xa -= xb;\n    ya -= yb;\n    perimeter += Math.hypot(xa, ya);\n  }\n\n  return perimeter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcG9seWdvbi9zcmMvbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1wb2x5Z29uXFxzcmNcXGxlbmd0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihwb2x5Z29uKSB7XG4gIHZhciBpID0gLTEsXG4gICAgICBuID0gcG9seWdvbi5sZW5ndGgsXG4gICAgICBiID0gcG9seWdvbltuIC0gMV0sXG4gICAgICB4YSxcbiAgICAgIHlhLFxuICAgICAgeGIgPSBiWzBdLFxuICAgICAgeWIgPSBiWzFdLFxuICAgICAgcGVyaW1ldGVyID0gMDtcblxuICB3aGlsZSAoKytpIDwgbikge1xuICAgIHhhID0geGI7XG4gICAgeWEgPSB5YjtcbiAgICBiID0gcG9seWdvbltpXTtcbiAgICB4YiA9IGJbMF07XG4gICAgeWIgPSBiWzFdO1xuICAgIHhhIC09IHhiO1xuICAgIHlhIC09IHliO1xuICAgIHBlcmltZXRlciArPSBNYXRoLmh5cG90KHhhLCB5YSk7XG4gIH1cblxuICByZXR1cm4gcGVyaW1ldGVyO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-polygon/src/length.js\n");

/***/ })

};
;