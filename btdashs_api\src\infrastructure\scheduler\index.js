const SchedulerService = require("../../application/services/SchedulerService");
const logger = require("../../../logger");

let schedulerInstance = null;

/**
 * Initialize the scheduler service
 * @returns {SchedulerService} Scheduler instance
 */
function initializeScheduler() {
	try {
		if (schedulerInstance) {
			logger.warn("Scheduler already initialized, returning existing instance");
			return schedulerInstance;
		}

		logger.info("Initializing scheduler service");
		schedulerInstance = new SchedulerService();
		schedulerInstance.initializeJobs();

		// Handle graceful shutdown
		process.on("SIGTERM", () => {
			logger.info("SIGTERM received, stopping scheduler");
			if (schedulerInstance) {
				schedulerInstance.stopAllJobs();
			}
		});

		process.on("SIGINT", () => {
			logger.info("SIGINT received, stopping scheduler");
			if (schedulerInstance) {
				schedulerInstance.stopAllJobs();
			}
		});

		logger.info("Scheduler service initialized successfully");
		return schedulerInstance;
	} catch (error) {
		logger.error("Failed to initialize scheduler service", { error });
		throw error;
	}
}

/**
 * Get the current scheduler instance
 * @returns {SchedulerService|null} Scheduler instance or null if not initialized
 */
function getScheduler() {
	return schedulerInstance;
}

/**
 * Stop the scheduler service
 */
function stopScheduler() {
	try {
		if (schedulerInstance) {
			logger.info("Stopping scheduler service");
			schedulerInstance.stopAllJobs();
			schedulerInstance = null;
			logger.info("Scheduler service stopped");
		} else {
			logger.warn("Scheduler not initialized, nothing to stop");
		}
	} catch (error) {
		logger.error("Error stopping scheduler service", { error });
	}
}

module.exports = {
	initializeScheduler,
	getScheduler,
	stopScheduler,
};
