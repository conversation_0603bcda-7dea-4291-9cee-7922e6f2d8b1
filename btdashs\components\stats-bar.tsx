"use client";

import { NetworkStats } from "@/lib/db/models";
import { formatNumber } from "@/utils/formatNumber";

interface StatsBarProps {
	networkStats: NetworkStats | null;
}

export function StatsBar({ networkStats }: StatsBarProps) {
	return (
		<div className="border-b bg-muted/50">
			<div className="max-w-[2000px] mx-auto px-6 sm:px-8 lg:px-12">
				<div className="flex h-10 items-center space-x-6 text-sm">
					<div>
						TTL Subnets: <span className="font-medium">{networkStats?.active_subnets ?? "N/A"}</span>
					</div>
					<div>
						Total TAO:{" "}
						<span className="text-green-600">${formatNumber(networkStats?.total_tao) ?? "N/A"}</span>
					</div>
					<div>
						24h Vol:{" "}
						<span className="text-green-600">{formatNumber(networkStats?.volume_24h) ?? "N/A"}</span>
					</div>
					<div>
						Dominance:{" "}
						<span className="font-medium">BTC: {formatNumber(networkStats?.dominance_btc) ?? "N/A"}%</span>
					</div>
				</div>
			</div>
		</div>
	);
}
