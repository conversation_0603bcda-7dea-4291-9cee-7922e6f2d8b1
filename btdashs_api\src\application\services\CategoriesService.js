// src/application/services/CategoriesService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Categories Service - Handles category management operations
 * 
 * This service manages categories used throughout the platform for
 * organizing jobs, events, companies, and other content.
 * 
 * Key responsibilities:
 * - Category CRUD operations
 * - Category filtering and search
 * - Data validation and sanitization
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class CategoriesService extends BaseService {
  constructor() {
    super("dtm_base.categories", "Category");
  }

  /**
   * Get all categories with optional filtering and sorting
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options (limit, offset, orderBy)
   * @returns {Promise<Array>} Array of categories
   */
  async getAllCategories(filters = {}, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'name', direction: 'desc' },
        ...options
      };

      return await this.getAll(filters, queryOptions);
    } catch (error) {
      logger.error("Error getting all categories", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get category by ID
   * @param {number} id - Category ID
   * @returns {Promise<Object|null>} Category object or null if not found
   */
  async getCategoryById(id) {
    try {
      return await this.getById(id);
    } catch (error) {
      logger.error("Error getting category by ID", { error, id });
      throw error;
    }
  }

  /**
   * Create a new category
   * @param {Object} categoryData - Category data
   * @returns {Promise<Object>} Created category object
   */
  async createCategory(categoryData) {
    try {
      const newCategory = await this.create(categoryData);
      logger.info("Category created", { category_id: newCategory.id });
      return newCategory;
    } catch (error) {
      logger.error("Error creating category", { error, categoryData });
      throw error;
    }
  }

  /**
   * Update a category
   * @param {number} id - Category ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated category object
   */
  async updateCategory(id, updateData) {
    try {
      const updatedCategory = await this.updateById(id, updateData);
      logger.info("Category updated", { category_id: id });
      return updatedCategory;
    } catch (error) {
      logger.error("Error updating category", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete a category
   * @param {number} id - Category ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteCategory(id) {
    try {
      const result = await this.deleteById(id);
      logger.info("Category deleted", { category_id: id });
      return result;
    } catch (error) {
      logger.error("Error deleting category", { error, id });
      throw error;
    }
  }
}

module.exports = new CategoriesService();
