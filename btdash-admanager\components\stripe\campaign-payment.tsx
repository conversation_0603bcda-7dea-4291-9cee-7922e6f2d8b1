"use client";

import { useState } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, CreditCard, DollarSign, Calendar, Target } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Campaign {
  id: number;
  name: string;
  total_budget: number;
  start_date: string;
  end_date: string;
  status: string;
}

interface CampaignPaymentProps {
  campaign: Campaign;
  onPaymentSuccess?: (paymentIntentId: string) => void;
  onPaymentError?: (error: string) => void;
  customerId?: string;
}

export function CampaignPayment({
  campaign,
  onPaymentSuccess,
  onPaymentError,
  customerId
}: CampaignPaymentProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentIntent, setPaymentIntent] = useState<string | null>(null);

  const handlePayment = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create payment intent for the campaign
      const response = await fetch('/api/stripe/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: campaign.total_budget,
          campaignId: campaign.id,
          customerId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }

      const { clientSecret, paymentIntentId } = await response.json();
      setPaymentIntent(paymentIntentId);

      const cardElement = elements.getElement(CardElement);
      
      if (!cardElement) {
        throw new Error('Card element not found');
      }

      // Confirm payment
      const { error: confirmError, paymentIntent: confirmedPayment } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: cardElement,
          },
        }
      );

      if (confirmError) {
        throw new Error(confirmError.message);
      }

      if (confirmedPayment?.status === 'succeeded') {
        toast({
          title: "Payment successful",
          description: `Payment of $${campaign.total_budget} for campaign "${campaign.name}" has been processed.`,
        });

        onPaymentSuccess?.(paymentIntentId);
      } else {
        throw new Error('Payment was not successful');
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      onPaymentError?.(errorMessage);
      
      toast({
        title: "Payment failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Campaign Payment
        </CardTitle>
        <CardDescription>
          Complete payment for your approved campaign
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Campaign Details */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Campaign:</span>
            <span>{campaign.name}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="font-medium">Budget:</span>
            <span className="text-lg font-bold">${campaign.total_budget}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="font-medium">Duration:</span>
            <span className="text-sm">
              {new Date(campaign.start_date).toLocaleDateString()} - {new Date(campaign.end_date).toLocaleDateString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="font-medium">Status:</span>
            <Badge variant={campaign.status === 'approved' ? 'default' : 'secondary'}>
              {campaign.status}
            </Badge>
          </div>
        </div>

        <Separator />

        {/* Payment Form */}
        <form onSubmit={handlePayment} className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Payment Information
            </label>
            <div className="p-3 border rounded-md">
              <CardElement options={cardElementOptions} />
            </div>
          </div>
          
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span>Campaign Budget:</span>
              <span>${campaign.total_budget}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span>Processing Fee:</span>
              <span>$0.00</span>
            </div>
            <Separator className="my-2" />
            <div className="flex items-center justify-between font-medium">
              <span>Total:</span>
              <span>${campaign.total_budget}</span>
            </div>
          </div>
          
          <Button 
            type="submit" 
            disabled={!stripe || loading || campaign.status !== 'approved'}
            className="w-full"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing Payment...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Pay ${campaign.total_budget}
              </>
            )}
          </Button>
          
          {campaign.status !== 'approved' && (
            <p className="text-sm text-muted-foreground text-center">
              Payment will be available once your campaign is approved.
            </p>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
