// app/page.tsx
import { But<PERSON> } from "@/components/ui/button";
import { auth0 } from "@/lib/auth0";
import { Layout } from "lucide-react";
import { redirect } from "next/navigation";

export default async function Home() {
	const session = await auth0.getSession();

	// If user is logged in, redirect to dashboard
	if (session?.user) {
		redirect("/dashboard");
	}

	return (
		<div className="flex min-h-screen flex-col items-center justify-center">
			<div className="flex flex-col items-center gap-8 p-8 max-w-md w-full">
				<div className="flex items-center gap-2">
					<Layout className="h-8 w-8" />
					<h1 className="text-2xl font-bold">AdManager</h1>
				</div>

				<div className="w-full space-y-4">
					<a href="/auth/login" className="block">
						<Button className="w-full">Login</Button>
					</a>
				</div>
			</div>
		</div>
	);
}
