"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { NetworkStats } from "@/lib/db/models";
import { formatNumber } from "@/utils/formatNumber";
import { Activity, Lock, Users, Zap } from "lucide-react";

type Props = {
	networkStats: NetworkStats;
};

export function SubnetsStats({ networkStats }: Props) {
	const renderChangeText = (change?: number, unit: string = "", decimals: number = 0) => {
		if (change === null || change === undefined) {
			return "";
		}

		const isPositive = change >= 0;
		const formattedChange =
			unit === "τ" ? formatNumber(Math.abs(change), decimals) : Math.abs(change).toLocaleString();

		return (
			<>
				{isPositive ? "+" : "-"}
				{formattedChange} {unit} from last month
			</>
		);
	};

	return (
		<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
			{/* Total Active Subnets */}
			<Card>
				<CardHeader className="flex flex-row justify-between items-center pb-2">
					<CardTitle className="text-sm font-medium">Total Active Subnets</CardTitle>
					<Activity className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">{networkStats.active_subnets?.toLocaleString() ?? "-"}</div>
					<p className="text-xs text-muted-foreground">
						{renderChangeText(networkStats.active_subnets_30d_change)}
					</p>
				</CardContent>
			</Card>

			{/* Total Validators */}
			<Card>
				<CardHeader className="flex flex-row justify-between items-center pb-2">
					<CardTitle className="text-sm font-medium">Total Validators</CardTitle>
					<Users className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">{networkStats.total_validators?.toLocaleString() ?? "-"}</div>
					<p className="text-xs text-muted-foreground">
						{renderChangeText(networkStats.total_validators_30d_change)}
					</p>
				</CardContent>
			</Card>

			{/* Total TAO Locked */}
			<Card>
				<CardHeader className="flex flex-row justify-between items-center pb-2">
					<CardTitle className="text-sm font-medium">Total TAO Locked</CardTitle>
					<Lock className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">
						{networkStats.total_staked_tao ? formatNumber(networkStats.total_staked_tao, 1) + " τ" : "-"}
					</div>
					<p className="text-xs text-muted-foreground">
						{renderChangeText(networkStats.total_staked_tao_30d_change, "τ", 1)}
					</p>
				</CardContent>
			</Card>

			{/* Network Emissions */}
			<Card>
				<CardHeader className="flex flex-row justify-between items-center pb-2">
					<CardTitle className="text-sm font-medium">Network Emissions</CardTitle>
					<Zap className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">
						{networkStats.network_emission ? formatNumber(networkStats.network_emission, 1) + " τ" : "-"}
					</div>
					<p className="text-xs text-muted-foreground">
						{renderChangeText(networkStats.network_emission_30d_change, "τ", 1)}
					</p>
				</CardContent>
			</Card>
		</div>
	);
}
