"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/redis-parser";
exports.ids = ["vendor-chunks/redis-parser"];
exports.modules = {

/***/ "(rsc)/./node_modules/redis-parser/index.js":
/*!********************************************!*\
  !*** ./node_modules/redis-parser/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./lib/parser */ \"(rsc)/./node_modules/redis-parser/lib/parser.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVkaXMtcGFyc2VyL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLDJHQUF3QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXHJlZGlzLXBhcnNlclxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9saWIvcGFyc2VyJylcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/redis-parser/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/redis-parser/lib/parser.js":
/*!*************************************************!*\
  !*** ./node_modules/redis-parser/lib/parser.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Buffer = (__webpack_require__(/*! buffer */ \"buffer\").Buffer)\nconst StringDecoder = (__webpack_require__(/*! string_decoder */ \"string_decoder\").StringDecoder)\nconst decoder = new StringDecoder()\nconst errors = __webpack_require__(/*! redis-errors */ \"(rsc)/./node_modules/redis-errors/index.js\")\nconst ReplyError = errors.ReplyError\nconst ParserError = errors.ParserError\nvar bufferPool = Buffer.allocUnsafe(32 * 1024)\nvar bufferOffset = 0\nvar interval = null\nvar counter = 0\nvar notDecreased = 0\n\n/**\n * Used for integer numbers only\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|number}\n */\nfunction parseSimpleNumbers (parser) {\n  const length = parser.buffer.length - 1\n  var offset = parser.offset\n  var number = 0\n  var sign = 1\n\n  if (parser.buffer[offset] === 45) {\n    sign = -1\n    offset++\n  }\n\n  while (offset < length) {\n    const c1 = parser.buffer[offset++]\n    if (c1 === 13) { // \\r\\n\n      parser.offset = offset + 1\n      return sign * number\n    }\n    number = (number * 10) + (c1 - 48)\n  }\n}\n\n/**\n * Used for integer numbers in case of the returnNumbers option\n *\n * Reading the string as parts of n SMI is more efficient than\n * using a string directly.\n *\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|string}\n */\nfunction parseStringNumbers (parser) {\n  const length = parser.buffer.length - 1\n  var offset = parser.offset\n  var number = 0\n  var res = ''\n\n  if (parser.buffer[offset] === 45) {\n    res += '-'\n    offset++\n  }\n\n  while (offset < length) {\n    var c1 = parser.buffer[offset++]\n    if (c1 === 13) { // \\r\\n\n      parser.offset = offset + 1\n      if (number !== 0) {\n        res += number\n      }\n      return res\n    } else if (number > 429496728) {\n      res += (number * 10) + (c1 - 48)\n      number = 0\n    } else if (c1 === 48 && number === 0) {\n      res += 0\n    } else {\n      number = (number * 10) + (c1 - 48)\n    }\n  }\n}\n\n/**\n * Parse a '+' redis simple string response but forward the offsets\n * onto convertBufferRange to generate a string.\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|string|Buffer}\n */\nfunction parseSimpleString (parser) {\n  const start = parser.offset\n  const buffer = parser.buffer\n  const length = buffer.length - 1\n  var offset = start\n\n  while (offset < length) {\n    if (buffer[offset++] === 13) { // \\r\\n\n      parser.offset = offset + 1\n      if (parser.optionReturnBuffers === true) {\n        return parser.buffer.slice(start, offset - 1)\n      }\n      return parser.buffer.toString('utf8', start, offset - 1)\n    }\n  }\n}\n\n/**\n * Returns the read length\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|number}\n */\nfunction parseLength (parser) {\n  const length = parser.buffer.length - 1\n  var offset = parser.offset\n  var number = 0\n\n  while (offset < length) {\n    const c1 = parser.buffer[offset++]\n    if (c1 === 13) {\n      parser.offset = offset + 1\n      return number\n    }\n    number = (number * 10) + (c1 - 48)\n  }\n}\n\n/**\n * Parse a ':' redis integer response\n *\n * If stringNumbers is activated the parser always returns numbers as string\n * This is important for big numbers (number > Math.pow(2, 53)) as js numbers\n * are 64bit floating point numbers with reduced precision\n *\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|number|string}\n */\nfunction parseInteger (parser) {\n  if (parser.optionStringNumbers === true) {\n    return parseStringNumbers(parser)\n  }\n  return parseSimpleNumbers(parser)\n}\n\n/**\n * Parse a '$' redis bulk string response\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|null|string}\n */\nfunction parseBulkString (parser) {\n  const length = parseLength(parser)\n  if (length === undefined) {\n    return\n  }\n  if (length < 0) {\n    return null\n  }\n  const offset = parser.offset + length\n  if (offset + 2 > parser.buffer.length) {\n    parser.bigStrSize = offset + 2\n    parser.totalChunkSize = parser.buffer.length\n    parser.bufferCache.push(parser.buffer)\n    return\n  }\n  const start = parser.offset\n  parser.offset = offset + 2\n  if (parser.optionReturnBuffers === true) {\n    return parser.buffer.slice(start, offset)\n  }\n  return parser.buffer.toString('utf8', start, offset)\n}\n\n/**\n * Parse a '-' redis error response\n * @param {JavascriptRedisParser} parser\n * @returns {ReplyError}\n */\nfunction parseError (parser) {\n  var string = parseSimpleString(parser)\n  if (string !== undefined) {\n    if (parser.optionReturnBuffers === true) {\n      string = string.toString()\n    }\n    return new ReplyError(string)\n  }\n}\n\n/**\n * Parsing error handler, resets parser buffer\n * @param {JavascriptRedisParser} parser\n * @param {number} type\n * @returns {undefined}\n */\nfunction handleError (parser, type) {\n  const err = new ParserError(\n    'Protocol error, got ' + JSON.stringify(String.fromCharCode(type)) + ' as reply type byte',\n    JSON.stringify(parser.buffer),\n    parser.offset\n  )\n  parser.buffer = null\n  parser.returnFatalError(err)\n}\n\n/**\n * Parse a '*' redis array response\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|null|any[]}\n */\nfunction parseArray (parser) {\n  const length = parseLength(parser)\n  if (length === undefined) {\n    return\n  }\n  if (length < 0) {\n    return null\n  }\n  const responses = new Array(length)\n  return parseArrayElements(parser, responses, 0)\n}\n\n/**\n * Push a partly parsed array to the stack\n *\n * @param {JavascriptRedisParser} parser\n * @param {any[]} array\n * @param {number} pos\n * @returns {undefined}\n */\nfunction pushArrayCache (parser, array, pos) {\n  parser.arrayCache.push(array)\n  parser.arrayPos.push(pos)\n}\n\n/**\n * Parse chunked redis array response\n * @param {JavascriptRedisParser} parser\n * @returns {undefined|any[]}\n */\nfunction parseArrayChunks (parser) {\n  const tmp = parser.arrayCache.pop()\n  var pos = parser.arrayPos.pop()\n  if (parser.arrayCache.length) {\n    const res = parseArrayChunks(parser)\n    if (res === undefined) {\n      pushArrayCache(parser, tmp, pos)\n      return\n    }\n    tmp[pos++] = res\n  }\n  return parseArrayElements(parser, tmp, pos)\n}\n\n/**\n * Parse redis array response elements\n * @param {JavascriptRedisParser} parser\n * @param {Array} responses\n * @param {number} i\n * @returns {undefined|null|any[]}\n */\nfunction parseArrayElements (parser, responses, i) {\n  const bufferLength = parser.buffer.length\n  while (i < responses.length) {\n    const offset = parser.offset\n    if (parser.offset >= bufferLength) {\n      pushArrayCache(parser, responses, i)\n      return\n    }\n    const response = parseType(parser, parser.buffer[parser.offset++])\n    if (response === undefined) {\n      if (!(parser.arrayCache.length || parser.bufferCache.length)) {\n        parser.offset = offset\n      }\n      pushArrayCache(parser, responses, i)\n      return\n    }\n    responses[i] = response\n    i++\n  }\n\n  return responses\n}\n\n/**\n * Called the appropriate parser for the specified type.\n *\n * 36: $\n * 43: +\n * 42: *\n * 58: :\n * 45: -\n *\n * @param {JavascriptRedisParser} parser\n * @param {number} type\n * @returns {*}\n */\nfunction parseType (parser, type) {\n  switch (type) {\n    case 36:\n      return parseBulkString(parser)\n    case 43:\n      return parseSimpleString(parser)\n    case 42:\n      return parseArray(parser)\n    case 58:\n      return parseInteger(parser)\n    case 45:\n      return parseError(parser)\n    default:\n      return handleError(parser, type)\n  }\n}\n\n/**\n * Decrease the bufferPool size over time\n *\n * Balance between increasing and decreasing the bufferPool.\n * Decrease the bufferPool by 10% by removing the first 10% of the current pool.\n * @returns {undefined}\n */\nfunction decreaseBufferPool () {\n  if (bufferPool.length > 50 * 1024) {\n    if (counter === 1 || notDecreased > counter * 2) {\n      const minSliceLen = Math.floor(bufferPool.length / 10)\n      const sliceLength = minSliceLen < bufferOffset\n        ? bufferOffset\n        : minSliceLen\n      bufferOffset = 0\n      bufferPool = bufferPool.slice(sliceLength, bufferPool.length)\n    } else {\n      notDecreased++\n      counter--\n    }\n  } else {\n    clearInterval(interval)\n    counter = 0\n    notDecreased = 0\n    interval = null\n  }\n}\n\n/**\n * Check if the requested size fits in the current bufferPool.\n * If it does not, reset and increase the bufferPool accordingly.\n *\n * @param {number} length\n * @returns {undefined}\n */\nfunction resizeBuffer (length) {\n  if (bufferPool.length < length + bufferOffset) {\n    const multiplier = length > 1024 * 1024 * 75 ? 2 : 3\n    if (bufferOffset > 1024 * 1024 * 111) {\n      bufferOffset = 1024 * 1024 * 50\n    }\n    bufferPool = Buffer.allocUnsafe(length * multiplier + bufferOffset)\n    bufferOffset = 0\n    counter++\n    if (interval === null) {\n      interval = setInterval(decreaseBufferPool, 50)\n    }\n  }\n}\n\n/**\n * Concat a bulk string containing multiple chunks\n *\n * Notes:\n * 1) The first chunk might contain the whole bulk string including the \\r\n * 2) We are only safe to fully add up elements that are neither the first nor any of the last two elements\n *\n * @param {JavascriptRedisParser} parser\n * @returns {String}\n */\nfunction concatBulkString (parser) {\n  const list = parser.bufferCache\n  const oldOffset = parser.offset\n  var chunks = list.length\n  var offset = parser.bigStrSize - parser.totalChunkSize\n  parser.offset = offset\n  if (offset <= 2) {\n    if (chunks === 2) {\n      return list[0].toString('utf8', oldOffset, list[0].length + offset - 2)\n    }\n    chunks--\n    offset = list[list.length - 2].length + offset\n  }\n  var res = decoder.write(list[0].slice(oldOffset))\n  for (var i = 1; i < chunks - 1; i++) {\n    res += decoder.write(list[i])\n  }\n  res += decoder.end(list[i].slice(0, offset - 2))\n  return res\n}\n\n/**\n * Concat the collected chunks from parser.bufferCache.\n *\n * Increases the bufferPool size beforehand if necessary.\n *\n * @param {JavascriptRedisParser} parser\n * @returns {Buffer}\n */\nfunction concatBulkBuffer (parser) {\n  const list = parser.bufferCache\n  const oldOffset = parser.offset\n  const length = parser.bigStrSize - oldOffset - 2\n  var chunks = list.length\n  var offset = parser.bigStrSize - parser.totalChunkSize\n  parser.offset = offset\n  if (offset <= 2) {\n    if (chunks === 2) {\n      return list[0].slice(oldOffset, list[0].length + offset - 2)\n    }\n    chunks--\n    offset = list[list.length - 2].length + offset\n  }\n  resizeBuffer(length)\n  const start = bufferOffset\n  list[0].copy(bufferPool, start, oldOffset, list[0].length)\n  bufferOffset += list[0].length - oldOffset\n  for (var i = 1; i < chunks - 1; i++) {\n    list[i].copy(bufferPool, bufferOffset)\n    bufferOffset += list[i].length\n  }\n  list[i].copy(bufferPool, bufferOffset, 0, offset - 2)\n  bufferOffset += offset - 2\n  return bufferPool.slice(start, bufferOffset)\n}\n\nclass JavascriptRedisParser {\n  /**\n   * Javascript Redis Parser constructor\n   * @param {{returnError: Function, returnReply: Function, returnFatalError?: Function, returnBuffers: boolean, stringNumbers: boolean }} options\n   * @constructor\n   */\n  constructor (options) {\n    if (!options) {\n      throw new TypeError('Options are mandatory.')\n    }\n    if (typeof options.returnError !== 'function' || typeof options.returnReply !== 'function') {\n      throw new TypeError('The returnReply and returnError options have to be functions.')\n    }\n    this.setReturnBuffers(!!options.returnBuffers)\n    this.setStringNumbers(!!options.stringNumbers)\n    this.returnError = options.returnError\n    this.returnFatalError = options.returnFatalError || options.returnError\n    this.returnReply = options.returnReply\n    this.reset()\n  }\n\n  /**\n   * Reset the parser values to the initial state\n   *\n   * @returns {undefined}\n   */\n  reset () {\n    this.offset = 0\n    this.buffer = null\n    this.bigStrSize = 0\n    this.totalChunkSize = 0\n    this.bufferCache = []\n    this.arrayCache = []\n    this.arrayPos = []\n  }\n\n  /**\n   * Set the returnBuffers option\n   *\n   * @param {boolean} returnBuffers\n   * @returns {undefined}\n   */\n  setReturnBuffers (returnBuffers) {\n    if (typeof returnBuffers !== 'boolean') {\n      throw new TypeError('The returnBuffers argument has to be a boolean')\n    }\n    this.optionReturnBuffers = returnBuffers\n  }\n\n  /**\n   * Set the stringNumbers option\n   *\n   * @param {boolean} stringNumbers\n   * @returns {undefined}\n   */\n  setStringNumbers (stringNumbers) {\n    if (typeof stringNumbers !== 'boolean') {\n      throw new TypeError('The stringNumbers argument has to be a boolean')\n    }\n    this.optionStringNumbers = stringNumbers\n  }\n\n  /**\n   * Parse the redis buffer\n   * @param {Buffer} buffer\n   * @returns {undefined}\n   */\n  execute (buffer) {\n    if (this.buffer === null) {\n      this.buffer = buffer\n      this.offset = 0\n    } else if (this.bigStrSize === 0) {\n      const oldLength = this.buffer.length\n      const remainingLength = oldLength - this.offset\n      const newBuffer = Buffer.allocUnsafe(remainingLength + buffer.length)\n      this.buffer.copy(newBuffer, 0, this.offset, oldLength)\n      buffer.copy(newBuffer, remainingLength, 0, buffer.length)\n      this.buffer = newBuffer\n      this.offset = 0\n      if (this.arrayCache.length) {\n        const arr = parseArrayChunks(this)\n        if (arr === undefined) {\n          return\n        }\n        this.returnReply(arr)\n      }\n    } else if (this.totalChunkSize + buffer.length >= this.bigStrSize) {\n      this.bufferCache.push(buffer)\n      var tmp = this.optionReturnBuffers ? concatBulkBuffer(this) : concatBulkString(this)\n      this.bigStrSize = 0\n      this.bufferCache = []\n      this.buffer = buffer\n      if (this.arrayCache.length) {\n        this.arrayCache[0][this.arrayPos[0]++] = tmp\n        tmp = parseArrayChunks(this)\n        if (tmp === undefined) {\n          return\n        }\n      }\n      this.returnReply(tmp)\n    } else {\n      this.bufferCache.push(buffer)\n      this.totalChunkSize += buffer.length\n      return\n    }\n\n    while (this.offset < this.buffer.length) {\n      const offset = this.offset\n      const type = this.buffer[this.offset++]\n      const response = parseType(this, type)\n      if (response === undefined) {\n        if (!(this.arrayCache.length || this.bufferCache.length)) {\n          this.offset = offset\n        }\n        return\n      }\n\n      if (type === 45) {\n        this.returnError(response)\n      } else {\n        this.returnReply(response)\n      }\n    }\n\n    this.buffer = null\n  }\n}\n\nmodule.exports = JavascriptRedisParser\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/redis-parser/lib/parser.js\n");

/***/ })

};
;