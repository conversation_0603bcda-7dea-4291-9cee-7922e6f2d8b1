// app/api/admin/recent-activity/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
	try {
		const { token } = await auth0.getAccessToken();
		const { searchParams } = new URL(req.url);
		
		const limit = searchParams.get('limit') || '10';

		const response = await fetch(`${process.env.API_BASE_URL}/admin/recent-activity?limit=${limit}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error) {
		console.error("Recent activity API error:", error);
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch recent activity",
			},
			{ status: 500 }
		);
	}
}
