// hooks/use-analytics.ts
import { CampaignAnalytics, UserAnalytics } from "@/lib/analytics";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export function useUserAnalytics(startDate?: string, endDate?: string) {
	const params = new URLSearchParams();
	if (startDate) params.append("start_date", startDate);
	if (endDate) params.append("end_date", endDate);

	const queryString = params.toString();
	const key = `/api/user/analytics${queryString ? `?${queryString}` : ""}`;

	const { data, error, isLoading, mutate } = useSWR<UserAnalytics>(key, fetcher, {
		refreshInterval: 60000, // Refresh every minute
		revalidateOnFocus: false,
	});

	return {
		analytics: data,
		isLoading,
		isError: error,
		mutate,
	};
}

export function useCampaignAnalytics(campaignId: string, startDate?: string, endDate?: string) {
	const params = new URLSearchParams();
	if (startDate) params.append("start_date", startDate);
	if (endDate) params.append("end_date", endDate);

	const queryString = params.toString();
	const key = campaignId ? `/api/campaigns/${campaignId}/analytics${queryString ? `?${queryString}` : ""}` : null;

	const { data, error, isLoading, mutate } = useSWR<CampaignAnalytics>(key, fetcher, {
		refreshInterval: 60000, // Refresh every minute
		revalidateOnFocus: false,
	});

	return {
		analytics: data,
		isLoading,
		isError: error,
		mutate,
	};
}

export function useAdvertiserBalance() {
	const { data, error, isLoading, mutate } = useSWR("/api/user/balance", fetcher, {
		refreshInterval: 300000, // Refresh every 5 minutes
	});

	return {
		balance: data,
		isLoading,
		isError: error,
		mutate,
	};
}

export function useSpendingHistory() {
	const { data, error, isLoading, mutate } = useSWR("/api/user/spending-history", fetcher, {
		refreshInterval: 300000, // Refresh every 5 minutes
	});

	return {
		spendingHistory: data,
		isLoading,
		isError: error,
		mutate,
	};
}
