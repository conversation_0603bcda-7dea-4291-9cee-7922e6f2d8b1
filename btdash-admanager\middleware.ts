// middleware.ts
import { auth0 } from "@/lib/auth0";
import { NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
	const response = await auth0.middleware(request);

	return response;
}

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico, sitemap.xml, robots.txt (metadata files)
		 */
		"/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)",
	],
};
