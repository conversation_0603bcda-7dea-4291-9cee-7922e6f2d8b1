"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, CheckCircle, Clock, CreditCard, ExternalLink, Mail, RefreshCw } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface PaymentStatusProps {
	campaignId: string;
	status: "pending" | "approved" | "active" | "payment_failed" | "payment_error" | "rejected";
	paymentAmount?: number;
	failureReason?: string;
	lastPaymentAttempt?: string;
	onRetryPayment?: () => void;
	onContactSupport?: () => void;
}

export function PaymentStatus({
	campaignId,
	status,
	paymentAmount,
	failureReason,
	lastPaymentAttempt,
	onRetryPayment,
	onContactSupport,
}: PaymentStatusProps) {
	const [isRetrying, setIsRetrying] = useState(false);

	const getStatusConfig = () => {
		switch (status) {
			case "pending":
				return {
					icon: Clock,
					color: "bg-yellow-100 text-yellow-800",
					title: "Payment Pending",
					description: "Waiting for admin approval before payment processing.",
				};
			case "approved":
				return {
					icon: CreditCard,
					color: "bg-blue-100 text-blue-800",
					title: "Payment Required",
					description: "Campaign approved. Complete payment to activate your campaign.",
				};
			case "active":
				return {
					icon: CheckCircle,
					color: "bg-green-100 text-green-800",
					title: "Payment Successful",
					description: "Payment completed successfully. Your campaign is now active.",
				};
			case "payment_failed":
				return {
					icon: AlertCircle,
					color: "bg-red-100 text-red-800",
					title: "Payment Failed",
					description: "Payment was declined. Please update your payment method and try again.",
				};
			case "payment_error":
				return {
					icon: AlertCircle,
					color: "bg-orange-100 text-orange-800",
					title: "Payment Processing Error",
					description: "There was an error processing your payment. Our team has been notified.",
				};
			case "rejected":
				return {
					icon: AlertCircle,
					color: "bg-red-100 text-red-800",
					title: "Campaign Rejected",
					description: "Your campaign was rejected and no payment is required.",
				};
			default:
				return {
					icon: Clock,
					color: "bg-gray-100 text-gray-800",
					title: "Unknown Status",
					description: "Campaign status is unknown.",
				};
		}
	};

	const handleRetryPayment = async () => {
		if (!onRetryPayment) return;

		setIsRetrying(true);
		try {
			await onRetryPayment();
			toast.success("Payment retry initiated successfully");
		} catch (error) {
			toast.error("Failed to retry payment. Please try again.");
		} finally {
			setIsRetrying(false);
		}
	};

	const handleContactSupport = () => {
		if (onContactSupport) {
			onContactSupport();
		} else {
			// Default support contact
			window.open("mailto:<EMAIL>?subject=Payment Issue - Campaign " + campaignId, "_blank");
		}
	};

	const config = getStatusConfig();
	const Icon = config.icon;
	const showRetryButton = ["payment_failed", "payment_error"].includes(status);
	const showSupportButton = ["payment_error", "payment_failed"].includes(status);

	return (
		<Card className="w-full">
			<CardHeader>
				<div className="flex items-center gap-3">
					<div className={`p-2 rounded-full ${config.color}`}>
						<Icon className="h-5 w-5" />
					</div>
					<div className="flex-1">
						<CardTitle className="text-lg">{config.title}</CardTitle>
						<CardDescription>{config.description}</CardDescription>
					</div>
					<Badge variant={status === "active" ? "default" : "secondary"} className={config.color}>
						{status.replace("_", " ").toUpperCase()}
					</Badge>
				</div>
			</CardHeader>

			<CardContent className="space-y-4">
				{paymentAmount && (
					<div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
						<span className="text-sm font-medium">Payment Amount:</span>
						<span className="text-lg font-bold">${paymentAmount.toFixed(2)}</span>
					</div>
				)}

				{failureReason && (
					<Alert variant="destructive">
						<AlertCircle className="h-4 w-4" />
						<AlertDescription>
							<strong>Failure Reason:</strong> {failureReason}
						</AlertDescription>
					</Alert>
				)}

				{lastPaymentAttempt && (
					<div className="text-sm text-muted-foreground">
						Last payment attempt: {new Date(lastPaymentAttempt).toLocaleString()}
					</div>
				)}

				{(showRetryButton || showSupportButton) && (
					<div className="flex gap-3 pt-2">
						{showRetryButton && (
							<Button onClick={handleRetryPayment} disabled={isRetrying} className="flex-1">
								{isRetrying ? (
									<>
										<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
										Retrying...
									</>
								) : (
									<>
										<CreditCard className="mr-2 h-4 w-4" />
										Retry Payment
									</>
								)}
							</Button>
						)}

						{showSupportButton && (
							<Button variant="outline" onClick={handleContactSupport} className="flex-1">
								<Mail className="mr-2 h-4 w-4" />
								Contact Support
							</Button>
						)}
					</div>
				)}

				{status === "approved" && (
					<div className="flex gap-3 pt-2">
						<Button className="flex-1" asChild>
							<a href={`/dashboard/campaigns/${campaignId}/payment`}>
								<CreditCard className="mr-2 h-4 w-4" />
								Complete Payment
							</a>
						</Button>
					</div>
				)}

				{status === "active" && (
					<div className="flex gap-3 pt-2">
						<Button variant="outline" className="flex-1" asChild>
							<a href={`/dashboard/campaigns/${campaignId}`}>
								<ExternalLink className="mr-2 h-4 w-4" />
								View Campaign
							</a>
						</Button>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
