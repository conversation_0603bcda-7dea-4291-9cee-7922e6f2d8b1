import { fetchWithFallback } from "@/lib/data/utils";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import ProductsClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

export const metadata: Metadata = generateSEOMetadata({
	title: "Explore Products | DynamicTaoMarketCap",
	description: "Discover featured applications, tools, and projects in the TAO ecosystem.",
	url: `${process.env.APP_BASE_URL}/products`,
	image: "default-product-og.png",
});

export default async function ApplicationsPage() {
	const [productsRes, catRes, compRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
	]);

	if (productsRes.error) console.error("Products fetch error:", productsRes.error);
	if (catRes.error) console.error("Categories fetch error:", catRes.error);
	if (compRes.error) console.error("Companies fetch error:", compRes.error);

	return (
		<ProductsClientWrapper
			products={productsRes.data || []}
			categories={catRes.data || []}
			companies={compRes.data || []}
		/>
	);
}
