// app/api/placements/[id]/route.ts
import { fetchInternal } from "@/lib/utils";
import { NextResponse } from "next/server";

export async function GET(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;

		const response = await fetchInternal(`${process.env.API_BASE_URL}/slots/${id}`);

		if (!response.ok) throw new Error("Failed to fetch slot details");

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
