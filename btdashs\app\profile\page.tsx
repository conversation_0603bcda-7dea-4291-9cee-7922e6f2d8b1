import { fetchWithFallback } from "@/lib/data/utils";
import { cookies } from "next/headers";
import ProfileDashboardClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

// Type definitions
type EndpointConfig<T = unknown> = {
	key: string;
	url: string;
	options?: RequestInit;
	required?: boolean;
	fallback: T;
};

export default async function ProfilePage() {
	const cookieHeader = (await cookies()).toString();
	const baseUrl = process.env.APP_BASE_URL;

	// Configuration: Centralize endpoint definitions
	const endpointConfigs: EndpointConfig[] = [
		{
			key: "userData",
			url: `${baseUrl}/api/user/me`,
			options: { headers: { Cookie: cookieHeader } },
			required: true,
			fallback: null,
		},
		{
			key: "skills",
			url: `${baseUrl}/api/skills`,
			fallback: [],
		},
		{
			key: "userSkills",
			url: `${baseUrl}/api/user/skills`,
			options: { headers: { Cookie: cookieHeader } },
			fallback: [],
		},
		{
			key: "preferences",
			url: `${baseUrl}/api/user/preferences`,
			options: { headers: { Cookie: cookieHeader } },
			fallback: null,
		},
		{
			key: "educations",
			url: `${baseUrl}/api/user/educations`,
			options: { headers: { Cookie: cookieHeader } },
			fallback: [],
		},
		{
			key: "experiences",
			url: `${baseUrl}/api/user/experiences`,
			options: { headers: { Cookie: cookieHeader } },
			fallback: [],
		},
		{
			key: "jobs",
			url: `${baseUrl}/api/user/jobs`,
			options: { headers: { Cookie: cookieHeader } },
			fallback: [],
		},
		{
			key: "events",
			url: `${baseUrl}/api/user/events`,
			options: { headers: { Cookie: cookieHeader } },
			fallback: [],
		},
		{
			key: "companies",
			url: `${baseUrl}/api/companies`,
			fallback: [],
		},
		{
			key: "categories",
			url: `${baseUrl}/api/categories`,
			fallback: [],
		},
		{
			key: "subnets",
			url: `${baseUrl}/api/subnets`,
			fallback: [],
		},
		{
			key: "products",
			url: `${baseUrl}/api/products`,
			fallback: [],
		},
		{
			key: "allEvents",
			url: `${baseUrl}/api/events`,
			fallback: [],
		},
	];

	// Fetch all data
	const results = await Promise.all(
		endpointConfigs.map(async (config) => {
			try {
				const response = await fetchWithFallback(config.url, config.options);
				return { config, result: response };
			} catch (error) {
				console.error(`Error fetching ${config.url}:`, error);
				return {
					config,
					result: {
						data: null,
						error: error instanceof Error ? error : new Error(String(error)),
					},
				};
			}
		})
	);

	// Check for critical errors
	const criticalError = results.find(({ config, result }) => config.required && result.error);
	if (criticalError) {
		throw new Error(`Failed to load ${criticalError.config.key}`);
	}

	// Data access helper
	const getData = <T,>(key: string): T => {
		const item = results.find((r) => r.config.key === key);
		return (item?.result.data ?? item?.config.fallback) as T;
	};

	// Prepare transformed data
	const companiesData = getData<Array<any>>("companies");
	const categoriesData = getData<Array<any>>("categories");
	const subnetsData = getData<Array<any>>("subnets");
	const productsData = getData<Array<any>>("products");
	const allEventsData = getData<Array<any>>("allEvents");

	const transformedData = {
		profile: getData<any>("userData"),
		skills: getData<Array<any>>("skills"),
		userSkills: getData<Array<any>>("userSkills"),
		preferences: getData<Array<any>>("preferences")?.[0] || null,
		educations: getData<Array<any>>("educations"),
		experiences: getData<Array<any>>("experiences"),
		jobs: getData<Array<any>>("jobs").map((job) => ({
			...job,
			company: companiesData.find((c) => c.id === job.company_id),
			categories: categoriesData.filter((cat) => job.category_ids?.includes(cat.id)),
			subnets: subnetsData.filter((s) => job.subnet_ids?.includes(s.netuid)),
			products: productsData.filter((p) => job.product_ids?.includes(p.id)),
		})),
		events: getData<Array<any>>("events").map((event) => ({
			...event,
			companies: companiesData.filter((c) => event.company_ids?.includes(c.id)),
			categories: categoriesData.filter((cat) => event.category_ids?.includes(cat.id)),
			subnets: subnetsData.filter((s) => event.subnet_ids?.includes(s.netuid)),
			products: productsData.filter((p) => event.product_ids?.includes(p.id)),
		})),
		companies: companiesData,
		categories: categoriesData,
		subnets: subnetsData,
		products: productsData,
		allEvents: allEventsData,
	};

	return (
		<div className="container mx-auto py-6">
			<ProfileDashboardClientWrapper {...transformedData} />
		</div>
	);
}
