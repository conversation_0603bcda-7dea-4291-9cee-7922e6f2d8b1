import { fetchWithFallback } from "@/lib/data/utils";
import type { Company } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import CompaniesClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

export const metadata: Metadata = generateSEOMetadata({
	title: "Companies | DynamicTaoMarketCap",
	description: "Explore companies building in the TAO ecosystem and their contributions.",
	url: "https://dynamictaomarketcap.com/companies",
	image: "/default-company-og.jpg",
});

export default async function CompaniesPage() {
	const [companiesRes, categoriesRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
	]);

	if (companiesRes.error || categoriesRes.error) {
		console.error("Partial data failure:", {
			companies: companiesRes,
			categories: categoriesRes,
		});
	}

	// filter out companies where hide is true
	const companies = (companiesRes.data || []).filter((company: Company) => !company.hide);

	return <CompaniesClientWrapper companies={companies || []} categories={categoriesRes.data || []} />;
}
