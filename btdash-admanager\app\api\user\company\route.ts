import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const POST = async function createCompany(req: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const body = await req.json();

		const response = await fetch(`${process.env.API_BASE_URL}/user/companies/with-owner`, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		const json = await response.json();
		if (!response.ok) {
			console.error("Backend API error:", {
				status: response.status,
				statusText: response.statusText,
				error: json,
				url: `${process.env.API_BASE_URL}/user/companies/with-owner`,
			});
			throw new Error(json.message || json.error || `Backend API error: ${response.status}`);
		}

		return NextResponse.json(json);
	} catch (err: any) {
		console.error("Company creation error:", err);
		return NextResponse.json({ error: err.message }, { status: 500 });
	}
};

export const GET = async function getUserCompany(req: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();
		if (!accessToken) return NextResponse.redirect(new URL("/api/auth/login", req.url));

		const res = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {
			headers: { Authorization: `Bearer ${accessToken}` },
		});

		const data = await res.json();
		if (!res.ok) throw new Error(data.message);

		return NextResponse.json(data);
	} catch (error: any) {
		console.error("Error fetching user company:", error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
};

export const PUT = async function updateUserCompany(req: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();
		if (!accessToken) return NextResponse.redirect(new URL("/api/auth/login", req.url));

		const body = await req.json();
		const companyId = body.id;

		const res = await fetch(`${process.env.API_BASE_URL}/user/companies/${companyId}`, {
			method: "PUT",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify(body),
		});

		const data = await res.json();
		if (!res.ok) throw new Error(data.message);

		return NextResponse.json(data);
	} catch (error: any) {
		console.error("Error updating company:", error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
};

export const DELETE = async function deleteUserCompany(req: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();
		if (!accessToken) return NextResponse.redirect(new URL("/api/auth/login", req.url));

		const { id } = await req.json(); // expect { id: string }

		const res = await fetch(`${process.env.API_BASE_URL}/user/companies/${id}`, {
			method: "DELETE",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
		});

		if (!res.ok) {
			const error = await res.json();
			throw new Error(error.message || "Failed to delete company");
		}

		return NextResponse.json({ success: true });
	} catch (error: any) {
		console.error("Error deleting company:", error);
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
};
