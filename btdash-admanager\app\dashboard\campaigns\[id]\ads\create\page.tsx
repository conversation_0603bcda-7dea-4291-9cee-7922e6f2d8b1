// app/dashboard/campaigns/[id]/ads/create/page.tsx
"use client";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Campaign {
	id: number;
	name: string;
	status: string;
}

interface Placement {
	id: number;
	name: string;
	description: string;
	width: number;
	height: number;
	page: string;
}

export default function CreateCampaignAdPage() {
	const router = useRouter();
	const params = useParams();
	const { toast } = useToast();
	const campaignId = params.id as string;

	const [loading, setLoading] = useState(true);
	const [submitting, setSubmitting] = useState(false);
	const [campaign, setCampaign] = useState<Campaign | null>(null);
	const [placements, setPlacements] = useState<Placement[]>([]);

	const [formData, setFormData] = useState({
		title: "",
		image_url: "",
		target_url: "",
		max_impressions: "",
		max_clicks: "",
		weight: "1",
		slot_id: "",
	});

	useEffect(() => {
		const fetchData = async () => {
			try {
				setLoading(true);

				// Fetch campaign details
				const campaignRes = await fetch(`/api/user/campaigns/${campaignId}`);
				if (campaignRes.ok) {
					const campaignData = await campaignRes.json();
					if (campaignData.success) {
						setCampaign(campaignData.data);
					}
				}

				// Fetch available placements
				const placementsRes = await fetch("/api/placements");
				if (placementsRes.ok) {
					const placementsData = await placementsRes.json();
					if (placementsData.success) {
						setPlacements(placementsData.data);
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
				toast({
					title: "Error",
					description: "Failed to load data. Please try again.",
					variant: "destructive",
				});
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [campaignId, toast]);

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		try {
			setSubmitting(true);

			const adData = {
				campaign_id: parseInt(campaignId),
				slot_id: formData.slot_id ? parseInt(formData.slot_id) : null,
				title: formData.title,
				image_url: formData.image_url,
				target_url: formData.target_url,
				max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,
				max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,
				weight: parseInt(formData.weight),
			};

			const response = await fetch("/api/user/ads", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(adData),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || "Failed to create ad");
			}

			const result = await response.json();
			if (result.success) {
				toast({
					title: "Ad Created",
					description: "Your ad has been created successfully.",
				});
				router.push(`/dashboard/campaigns/${campaignId}`);
			}
		} catch (error) {
			console.error("Error creating ad:", error);
			toast({
				title: "Creation Failed",
				description: error instanceof Error ? error.message : "Failed to create ad",
				variant: "destructive",
			});
		} finally {
			setSubmitting(false);
		}
	};

	const selectedPlacement = placements.find((p) => p.id.toString() === formData.slot_id);

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading...</p>
				</div>
			</div>
		);
	}

	if (!campaign) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p className="text-gray-600 mb-4">Campaign not found</p>
					<Button onClick={() => router.back()}>Go Back</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="max-w-4xl mx-auto px-4">
				<div className="mb-6">
					<Button variant="ghost" onClick={() => router.back()} className="mb-4">
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Campaign
					</Button>
					<h1 className="text-3xl font-bold text-gray-900">Add Ad to Campaign</h1>
					<p className="text-gray-600">Create a new ad for "{campaign.name}"</p>
				</div>

				<div className="grid gap-6 lg:grid-cols-3">
					<div className="lg:col-span-2">
						<Card>
							<CardHeader>
								<CardTitle>Ad Details</CardTitle>
								<CardDescription>Enter your ad information for this campaign</CardDescription>
							</CardHeader>
							<CardContent>
								<form onSubmit={handleSubmit} className="space-y-6">
									{/* Placement Selection */}
									<div className="space-y-2">
										<Label htmlFor="slot_id">Ad Placement</Label>
										<Select
											value={formData.slot_id}
											onValueChange={(value) => handleInputChange("slot_id", value)}
										>
											<SelectTrigger>
												<SelectValue placeholder="Choose a placement" />
											</SelectTrigger>
											<SelectContent>
												{placements.map((placement) => (
													<SelectItem key={placement.id} value={placement.id.toString()}>
														{placement.name} ({placement.width}x{placement.height}px) -{" "}
														{placement.page}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>

									{/* Ad Creative */}
									<div className="space-y-4">
										<h3 className="text-lg font-medium">Ad Creative</h3>
										<div>
											<Label htmlFor="title">Ad Title *</Label>
											<Input
												id="title"
												value={formData.title}
												onChange={(e) => handleInputChange("title", e.target.value)}
												placeholder="Enter ad title"
												required
											/>
										</div>
										<div>
											<Label htmlFor="image_url">Image URL *</Label>
											<Input
												id="image_url"
												type="url"
												value={formData.image_url}
												onChange={(e) => handleInputChange("image_url", e.target.value)}
												placeholder="https://example.com/image.jpg"
												required
											/>
											{selectedPlacement && (
												<p className="text-xs text-muted-foreground mt-1">
													Required dimensions: {selectedPlacement.width}x
													{selectedPlacement.height}px
												</p>
											)}
											{/* Image Preview */}
											{formData.image_url && (
												<div className="mt-3">
													<Label className="text-sm font-medium">Preview:</Label>
													<div className="mt-2 relative w-full max-w-md border rounded-lg overflow-hidden bg-gray-50">
														<img
															src={formData.image_url}
															alt="Ad preview"
															className="w-full h-auto object-cover"
															onError={(e) => {
																const target = e.target as HTMLImageElement;
																target.style.display = "none";
																const errorDiv =
																	target.nextElementSibling as HTMLElement;
																if (errorDiv) errorDiv.style.display = "block";
															}}
															onLoad={(e) => {
																const target = e.target as HTMLImageElement;
																target.style.display = "block";
																const errorDiv =
																	target.nextElementSibling as HTMLElement;
																if (errorDiv) errorDiv.style.display = "none";
															}}
														/>
														<div
															className="hidden p-4 text-center text-sm text-red-600"
															style={{ display: "none" }}
														>
															Failed to load image. Please check the URL.
														</div>
													</div>
													{selectedPlacement && (
														<p className="text-xs text-muted-foreground mt-1">
															Target: {selectedPlacement.width}x{selectedPlacement.height}
															px
														</p>
													)}
												</div>
											)}
										</div>
										<div>
											<Label htmlFor="target_url">Target URL *</Label>
											<Input
												id="target_url"
												type="url"
												value={formData.target_url}
												onChange={(e) => handleInputChange("target_url", e.target.value)}
												placeholder="https://example.com"
												required
											/>
										</div>
									</div>

									{/* Performance Settings */}
									<div className="space-y-4">
										<h3 className="text-lg font-medium">Performance Settings</h3>
										<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
											<div>
												<Label htmlFor="max_impressions">Max Impressions</Label>
												<Input
													id="max_impressions"
													type="number"
													value={formData.max_impressions}
													onChange={(e) =>
														handleInputChange("max_impressions", e.target.value)
													}
													placeholder="Unlimited"
												/>
											</div>
											<div>
												<Label htmlFor="max_clicks">Max Clicks</Label>
												<Input
													id="max_clicks"
													type="number"
													value={formData.max_clicks}
													onChange={(e) => handleInputChange("max_clicks", e.target.value)}
													placeholder="Unlimited"
												/>
											</div>
											<div>
												<Label htmlFor="weight">Weight *</Label>
												<Input
													id="weight"
													type="number"
													min="1"
													max="1000"
													value={formData.weight}
													onChange={(e) => handleInputChange("weight", e.target.value)}
													required
												/>
											</div>
										</div>
									</div>

									<Button type="submit" className="w-full" disabled={submitting}>
										{submitting ? "Creating Ad..." : "Create Ad"}
									</Button>
								</form>
							</CardContent>
						</Card>
					</div>

					{/* Sidebar */}
					<div className="space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Campaign Info</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<div className="flex justify-between">
										<span className="text-sm font-medium">Name:</span>
										<span className="text-sm">{campaign.name}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-sm font-medium">Status:</span>
										<span className="text-sm capitalize">{campaign.status}</span>
									</div>
								</div>
							</CardContent>
						</Card>

						{selectedPlacement && (
							<Card>
								<CardHeader>
									<CardTitle>Selected Placement</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="flex justify-between">
											<span className="text-sm font-medium">Name:</span>
											<span className="text-sm">{selectedPlacement.name}</span>
										</div>
										<div className="flex justify-between">
											<span className="text-sm font-medium">Page:</span>
											<span className="text-sm">{selectedPlacement.page}</span>
										</div>
										<div className="flex justify-between">
											<span className="text-sm font-medium">Dimensions:</span>
											<span className="text-sm">
												{selectedPlacement.width}x{selectedPlacement.height}px
											</span>
										</div>
									</div>
								</CardContent>
							</Card>
						)}

						<Card>
							<CardHeader>
								<CardTitle>Tips</CardTitle>
							</CardHeader>
							<CardContent className="text-sm text-muted-foreground">
								<ul className="space-y-2">
									<li>• Use high-quality images that match the placement dimensions</li>
									<li>• Write compelling ad titles that grab attention</li>
									<li>• Set reasonable impression and click limits</li>
									<li>• Higher weight gives your ad more visibility</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	);
}
