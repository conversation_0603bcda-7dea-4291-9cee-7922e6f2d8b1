const ValidatorsService = require("../../application/services/ValidatorsService");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllValidators = asyncHandler(async (req, res) => {
	const validators = await ValidatorsService.getAllValidators();
	return sendSuccess(res, validators, "Validators retrieved successfully");
});

const getValidatorById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const validator = await ValidatorsService.getValidatorById(id);
	if (!validator) {
		return sendNotFound(res, "Validator not found");
	}
	return sendSuccess(res, validator, "Validator retrieved successfully");
});

const createValidator = asyncHandler(async (req, res) => {
	const {
		hotkey,
		name,
		rank,
		system_stake,
		stake,
		stake_24h_change,
		dominance,
		nominators,
		nominators_24h_change,
		nom_24h_per_1000tao,
		weight,
		take,
	} = req.body;

	const newValidator = await ValidatorsService.createValidator({
		hotkey,
		name,
		rank,
		system_stake,
		stake,
		stake_24h_change,
		dominance,
		nominators,
		nominators_24h_change,
		nom_24h_per_1000tao,
		weight,
		take,
	});

	return sendSuccess(res, newValidator, "Validator created successfully", 201);
});

const updateValidator = async (req, res) => {
	try {
		const { id } = req.params;
		const {
			hotkey,
			name,
			rank,
			system_stake,
			stake,
			stake_24h_change,
			dominance,
			nominators,
			nominators_24h_change,
			nom_24h_per_1000tao,
			weight,
			take,
		} = req.body;
		const [updatedValidator] = await db("dtm_base.validators")
			.where({ validator_id: id })
			.update({
				hotkey,
				name,
				rank,
				system_stake,
				stake,
				stake_24h_change,
				dominance,
				nominators,
				nominators_24h_change,
				nom_24h_per_1000tao,
				weight,
				take,
			})
			.returning("*");
		if (!updatedValidator) {
			return res.status(404).json({ message: "Validator not found" });
		}
		res.status(200).json({ data: updatedValidator });
	} catch (error) {
		logger.error("Error updating validator:", error);
		res.status(500).json({ message: "Error updating validator" });
	}
};

const deleteValidator = async (req, res) => {
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.validators").where({ validator_id: id }).del();
		if (!deleted) {
			return res.status(404).json({ message: "Validator not found" });
		}
		res.status(204).send();
	} catch (error) {
		logger.error("Error deleting validator:", error);
		res.status(500).json({ message: "Error deleting validator" });
	}
};

/* --- TAOSTATS API INTERACTIONS --- */

// Update Validators with data fetched from TaoStats API
const updateValidatorsWithTaoStats = async (req, res) => {
	try {
		const validatorData = await fetchAllPages("/validator/latest/v1");

		await updateValidators(validatorData);

		updateEndpointStatus("/update/validators", true, "Validators updated successfully");
		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("Error updating validator:", error);

		updateEndpointStatus("/update/validators", false, error.message);
		res.status(500).json({ error: error.message });
	}
};

// Update Validators with given data
const updateValidators = async (validatorData) => {
	logger.info("Updating validators with TaoStats data...");

	const validatorsToInsert = [];
	const validatorsToUpdate = [];

	for (const validator of validatorData) {
		const {
			hotkey,
			name,
			rank,
			system_stake,
			stake,
			stake_24_hr_change,
			dominance,
			nominators,
			nominators_24_hr_change,
			nominator_return_per_k,
			take,
		} = validator;

		// Map data to your schema
		const validatorToInsertOrUpdate = {
			hotkey: hotkey?.hex || null,
			name: name || null,
			rank: rank || null,
			system_stake: system_stake || null,
			stake: stake || null,
			stake_24h_change: stake_24_hr_change || null,
			dominance: dominance || null,
			nominators: nominators || null,
			nominators_24h_change: nominators_24_hr_change || null,
			nom_24h_per_1000tao: nominator_return_per_k || null,
			take: take || null,
		};

		// Check if the validator already exists
		const existingValidator = await db("dtm_base.validators")
			.where({ hotkey: validatorToInsertOrUpdate.hotkey })
			.first();

		if (existingValidator) {
			validatorsToUpdate.push(validatorToInsertOrUpdate);
		} else {
			validatorsToInsert.push(validatorToInsertOrUpdate);
		}
	}

	// Perform bulk insert for new validators
	if (validatorsToInsert.length > 0) {
		await db("dtm_base.validators").insert(validatorsToInsert);
		logger.info(`Inserted ${validatorsToInsert.length} new validators`);
	}

	// Perform bulk update for existing validators
	if (validatorsToUpdate.length > 0) {
		await Promise.all(
			validatorsToUpdate.map(async (validator) => {
				await db("dtm_base.validators").where({ hotkey: validator.hotkey }).update(validator);
			})
		);
		logger.info(`Updated ${validatorsToUpdate.length} validators`);
	}
};

module.exports = {
	getAllValidators,
	getValidatorById,
	createValidator,
	updateValidator,
	deleteValidator,
	updateValidatorsWithTaoStats,
	updateValidators,
};
