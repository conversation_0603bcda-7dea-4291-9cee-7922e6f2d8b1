@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .hide-scrollbar {
    -ms-overflow-style: none; /* I<PERSON> and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

     .grid-cols-52 {
       grid-template-columns: repeat(52, minmax(0, 1fr));
     }
  
     .grid-rows-7 {
       grid-template-rows: repeat(7, minmax(0, 1fr));
     }
  
     /* GitHub contribution grid specific utilities */
     .grid-cols-52 {
       grid-template-columns: repeat(52, minmax(0, 1fr));
     }
  
     .grid-rows-7 {
       grid-template-rows: repeat(7, minmax(0, 1fr));
     }
  
     /* Explicit grid positioning classes */
     .col-start-1 {
       grid-column-start: 1;
     }
  
     .col-start-2 {
       grid-column-start: 2;
     }
  
     .col-start-3 {
       grid-column-start: 3;
     }
  
     .col-start-4 {
       grid-column-start: 4;
     }
  
     .col-start-5 {
       grid-column-start: 5;
     }
  
     .col-start-6 {
       grid-column-start: 6;
     }
  
     .col-start-7 {
       grid-column-start: 7;
     }
  
     .col-start-8 {
       grid-column-start: 8;
     }
  
     .col-start-9 {
       grid-column-start: 9;
     }
  
     .col-start-10 {
       grid-column-start: 10;
     }
  
     .col-start-11 {
       grid-column-start: 11;
     }
  
     .col-start-12 {
       grid-column-start: 12;
     }
  
     .col-start-13 {
       grid-column-start: 13;
     }
  
     .col-start-14 {
       grid-column-start: 14;
     }
  
     .col-start-15 {
       grid-column-start: 15;
     }
  
     .col-start-16 {
       grid-column-start: 16;
     }
  
     .col-start-17 {
       grid-column-start: 17;
     }
  
     .col-start-18 {
       grid-column-start: 18;
     }
  
     .col-start-19 {
       grid-column-start: 19;
     }
  
     .col-start-20 {
       grid-column-start: 20;
     }
  
     .col-start-21 {
       grid-column-start: 21;
     }
  
     .col-start-22 {
       grid-column-start: 22;
     }
  
     .col-start-23 {
       grid-column-start: 23;
     }
  
     .col-start-24 {
       grid-column-start: 24;
     }
  
     .col-start-25 {
       grid-column-start: 25;
     }
  
     .col-start-26 {
       grid-column-start: 26;
     }
  
     .col-start-27 {
       grid-column-start: 27;
     }
  
     .col-start-28 {
       grid-column-start: 28;
     }
  
     .col-start-29 {
       grid-column-start: 29;
     }
  
     .col-start-30 {
       grid-column-start: 30;
     }
  
     .col-start-31 {
       grid-column-start: 31;
     }
  
     .col-start-32 {
       grid-column-start: 32;
     }
  
     .col-start-33 {
       grid-column-start: 33;
     }
  
     .col-start-34 {
       grid-column-start: 34;
     }
  
     .col-start-35 {
       grid-column-start: 35;
     }
  
     .col-start-36 {
       grid-column-start: 36;
     }
  
     .col-start-37 {
       grid-column-start: 37;
     }
  
     .col-start-38 {
       grid-column-start: 38;
     }
  
     .col-start-39 {
       grid-column-start: 39;
     }
  
     .col-start-40 {
       grid-column-start: 40;
     }
  
     .col-start-41 {
       grid-column-start: 41;
     }
  
     .col-start-42 {
       grid-column-start: 42;
     }
  
     .col-start-43 {
       grid-column-start: 43;
     }
  
     .col-start-44 {
       grid-column-start: 44;
     }
  
     .col-start-45 {
       grid-column-start: 45;
     }
  
     .col-start-46 {
       grid-column-start: 46;
     }
  
     .col-start-47 {
       grid-column-start: 47;
     }
  
     .col-start-48 {
       grid-column-start: 48;
     }
  
     .col-start-49 {
       grid-column-start: 49;
     }
  
     .col-start-50 {
       grid-column-start: 50;
     }
  
     .col-start-51 {
       grid-column-start: 51;
     }
  
     .col-start-52 {
       grid-column-start: 52;
     }
  
     .row-start-1 {
       grid-row-start: 1;
     }
  
     .row-start-2 {
       grid-row-start: 2;
     }
  
     .row-start-3 {
       grid-row-start: 3;
     }
  
     .row-start-4 {
       grid-row-start: 4;
     }
  
     .row-start-5 {
       grid-row-start: 5;
     }
  
     .row-start-6 {
       grid-row-start: 6;
     }
  
     .row-start-7 {
       grid-row-start: 7;
     }
     
}

.horizontal-ad-container {
  width: 100%;
  overflow: hidden;
}

.horizontal-ad-container ins {
  margin: 0 auto;
}

.ad-square {
  max-width: 100%;
  height: auto;
  aspect-ratio: 300/250;
}

.adsbygoogle {
  margin: 0 auto;
}

.adsbygoogle[data-ad-status="unfilled"] {
  background: transparent;
}