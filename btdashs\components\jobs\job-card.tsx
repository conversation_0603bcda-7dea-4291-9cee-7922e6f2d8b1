"use client";

import { Card } from "@/components/ui/card";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import type { Category, Company, Job, Product, Subnet } from "@/lib/db/models";
import { Circle, Edit, Eye, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import ReactMarkdown from "react-markdown";

interface JobCardProps {
  job: Job & {
    company?: Company;
    categories?: Category[];
    subnets?: Subnet[];
    products?: Product[];
  };
  variant?: "default" | "compact";
  editMode?: boolean;
  onEdit?: (job: Job) => void;
  onDelete?: (jobId: number) => void;
}

function getJobGradient(jobId: number): string {
  const gradients = [
    "from-purple-500 to-indigo-600",
    "from-blue-500 to-cyan-600",
    "from-emerald-500 to-teal-600",
    "from-rose-500 to-pink-600",
    "from-amber-500 to-orange-600",
    "from-violet-500 to-fuchsia-600",
    "from-sky-500 to-blue-600",
    "from-green-500 to-emerald-600",
    "from-red-500 to-rose-600",
    "from-pink-500 to-purple-600",
  ];

  const index = parseInt(jobId.toString().slice(-1)) % gradients.length;
  return gradients[index];
}

function formatSalary(job: Job): string {
  if (job.min_salary && job.max_salary) {
    return `${job.min_salary} - ${job.max_salary} ${job.currency || ""}`;
  }
  return "Competitive";
}

export function JobCard({
  job,
  editMode = false,
  onEdit,
  onDelete,
}: JobCardProps) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const postedAt = job.published_date ?? job.created_at ?? new Date();
  const daysSincePosted = Math.floor(
    (Date.now() - new Date(postedAt).getTime()) / (1000 * 60 * 60 * 24)
  );
  const postedText =
    daysSincePosted === 0
      ? "Posted today"
      : daysSincePosted === 1
      ? "Posted yesterday"
      : `Posted ${daysSincePosted} days ago`;

  const gradientClass = getJobGradient(job.id);
  const companyName =
    job.company?.name || job.subnets?.[0]?.name || "Unknown Company";
  const companyLogo =
    job.company?.logo_url ||
    job.subnets?.[0].subnet_symbol ||
    "/placeholder.svg";

  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onEdit) onEdit(job);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!onDelete) return;
    setIsDeleting(true);
    try {
      await onDelete(job.id);
      setShowDeleteDialog(false);
    } finally {
      setIsDeleting(false);
    }
  };

  const cardContent = (
    <Card
      className={`relative w-full text-white hover:opacity-90 transition-opacity overflow-hidden border-0 bg-gradient-to-br ${gradientClass}`}
    >
      <div className="p-4 sm:p-6 h-full flex flex-col">
        <div className="flex items-start justify-between mb-2">
          <div className="flex flex-wrap items-center gap-2">
            <div className="flex items-center gap-1.5 bg-black/20 py-1 px-2 rounded-full text-xs">
              <Circle
                className={`h-2 w-2 ${
                  job.type === "Full-time"
                    ? "fill-green-400 text-green-400 animate-pulse"
                    : "fill-yellow-400 text-yellow-400"
                }`}
              />
              {job.type}
            </div>
            {job.categories?.map((category) => (
              <div
                key={category.id}
                className="bg-black/20 py-1 px-2 rounded-full text-xs"
              >
                {category.name}
              </div>
            ))}
          </div>
        </div>

        {editMode && (
          <div className="absolute top-2 right-2 flex gap-1 z-10">
            <Link
              href={`/jobs/${job.id}`}
              className="bg-white/20 hover:bg-white/30 p-1.5 rounded-full transition-colors"
              title="View"
            >
              <Eye className="h-5 w-5 text-white" />
            </Link>

            <button
              onClick={handleEditClick}
              className="bg-white/20 hover:bg-white/30 p-1.5 rounded-full transition-colors"
              title="Edit"
            >
              <Edit className="h-5 w-5 text-white" />
            </button>

            <button
              onClick={handleDeleteClick}
              className="bg-red-500/80 hover:bg-red-500 p-1.5 rounded-full transition-colors"
              disabled={isDeleting}
              title="Delete"
            >
              <Trash2 className="h-5 w-5 text-white" />
            </button>
          </div>
        )}

        <div className="flex items-center gap-3 mb-2">
          {job.company ? (
            <div className="relative w-10 h-10 rounded-md overflow-hidden bg-white/20">
              <Image
                src={companyLogo}
                alt={companyName}
                fill
                className="object-cover"
              />
            </div>
          ) : job.subnets && job.subnets[0] ? (
            <div className="relative w-10 h-10 rounded-md overflow-hidden bg-primary flex items-center justify-center text-black text-3xl font-bold">
              {job.subnets[0].subnet_symbol || job.subnets[0].name.charAt(0)}
            </div>
          ) : null}
          <div>
            <h3 className="font-semibold text-xl">{job.title}</h3>
            <p className="text-sm text-white/90">{companyName}</p>
          </div>
        </div>

        <div className="text-sm text-white/90 mb-4 line-clamp-2">
          <ReactMarkdown>
            {job.description || "No job description provided."}
          </ReactMarkdown>
        </div>

        <div className="mt-auto flex items-end justify-between">
          <div>
            <div className="text-2xl font-bold">{formatSalary(job)}</div>
            <div className="flex items-center gap-2">
              <div className="text-xs text-white/75">
                {job.location || "Remote"}
              </div>
              <div className="text-xs text-white/75">•</div>
              <div className="text-xs text-white/75">{postedText}</div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <>
      {editMode ? (
        <div className="relative">{cardContent}</div>
      ) : (
        <div
          onClick={() => router.push(`/jobs/${job.id}`)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => e.key === "Enter" && router.push(`/jobs/${job.id}`)}
          className="block cursor-pointer"
        >
          {cardContent}
        </div>
      )}

      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Job Posting"
        description={`Are you sure you want to delete "${job.title}"? This action cannot be undone.`}
        onConfirm={confirmDelete}
        confirmText="Delete"
        loading={isDeleting}
      />
    </>
  );
}
