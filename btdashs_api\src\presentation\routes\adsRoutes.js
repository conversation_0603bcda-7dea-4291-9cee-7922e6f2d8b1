// src/presentation/routes/adsRoutes.js
const express = require("express");
const router = express.Router();
const checkJwt = require("../../middleware/auth");
const checkInternalKey = require("../../middleware/internal");
const { uploadMiddleware } = require("../../middleware/uploadMiddleware");
const rateLimits = require("../../middleware/rateLimitMiddleware");
const { validateCampaign, validateAd, validateSlot } = require("../../validators/adsValidators");
const adsController = require("../controllers/adsController");

// Campaigns routes (require JWT auth + rate limiting)
router.get("/campaigns", checkJwt, rateLimits.userGeneral, adsController.getAllCampaigns);
router.get("/campaigns/:id", checkJwt, rateLimits.userGeneral, adsController.getCampaignById);
router.post("/campaigns", checkJwt, rateLimits.userCampaigns, validateCampaign, adsController.createCampaign);
router.put("/campaigns/:id", checkJwt, rateLimits.userCampaigns, validateCampaign, adsController.updateCampaign);
router.put("/campaigns/:id/status", checkJwt, rateLimits.userCampaigns, adsController.updateCampaignStatus);
router.delete("/campaigns/:id", checkJwt, rateLimits.userCampaigns, adsController.deleteCampaign);

// Ads routes (require JWT auth + rate limiting)
router.get("/ads", checkJwt, rateLimits.userGeneral, adsController.getAllAds);
router.get("/ads/:id", checkJwt, rateLimits.userGeneral, adsController.getAdById);
router.get("/campaigns/:id/ads", checkJwt, rateLimits.userGeneral, adsController.getCampaignAds);
router.post("/ads", checkJwt, rateLimits.userAds, validateAd, adsController.createAd);
router.put("/ads/:id", checkJwt, rateLimits.userAds, validateAd, adsController.updateAd);
router.put("/ads/:id/status", checkJwt, rateLimits.userAds, adsController.updateAdStatus);
router.delete("/ads/:id", checkJwt, rateLimits.userAds, adsController.deleteAd);

// Slots routes (internal access only)
router.get("/slots", checkInternalKey, adsController.getAllSlots);
router.get("/slots/:id", checkInternalKey, adsController.getSlotById);

// Ad serving and tracking (internal access only)
router.get("/serve-ad", checkInternalKey, adsController.serveAd);
router.post("/track/impression", checkInternalKey, adsController.trackImpression);
router.get("/track/click/:adId", checkInternalKey, adsController.trackClick);

// Analytics routes (require JWT auth + rate limiting)
router.get("/campaigns/:id/analytics", checkJwt, rateLimits.apiAnalytics, adsController.getCampaignAnalytics);
router.get("/ads/:id/analytics", checkJwt, rateLimits.apiAnalytics, adsController.getAdAnalytics);
router.get("/user/analytics", checkJwt, rateLimits.apiAnalytics, adsController.getUserAnalytics);
router.get("/campaigns/:id/conversions", checkJwt, rateLimits.apiAnalytics, adsController.getConversionTracking);

// Budget management routes (require JWT auth)
router.get("/user/balance", checkJwt, adsController.getAdvertiserBalance);
router.post("/user/add-funds", checkJwt, adsController.addFunds);
router.get("/user/spending-history", checkJwt, adsController.getSpendingHistory);
router.get("/campaigns/:id/spend-summary", checkJwt, adsController.getCampaignSpendSummary);

// File upload routes (require JWT auth + rate limiting)
router.post("/upload/image", checkJwt, rateLimits.apiUpload, uploadMiddleware, adsController.uploadAdImage);
router.delete("/upload/image/:filename", checkJwt, rateLimits.userGeneral, adsController.deleteAdImage);
router.get("/upload/image/:filename/info", checkJwt, rateLimits.userGeneral, adsController.getImageInfo);

// Campaign targeting routes (require JWT auth)
router.post("/campaigns/:campaignId/targeting", checkJwt, rateLimits.userGeneral, adsController.setCampaignTargeting);
router.get("/campaigns/:campaignId/targeting", checkJwt, rateLimits.userGeneral, adsController.getCampaignTargeting);
router.get("/targeting/options", checkJwt, rateLimits.userGeneral, adsController.getTargetingOptions);

// Notification routes (require JWT auth + rate limiting)
router.get("/user/notifications", checkJwt, rateLimits.userGeneral, adsController.getUserNotifications);
router.put("/user/notifications/:id/read", checkJwt, rateLimits.userGeneral, adsController.markNotificationRead);
router.put("/user/notifications/read-all", checkJwt, rateLimits.userGeneral, adsController.markAllNotificationsRead);
router.post("/user/notifications/test", checkJwt, rateLimits.userGeneral, adsController.sendTestNotification);

module.exports = router;
