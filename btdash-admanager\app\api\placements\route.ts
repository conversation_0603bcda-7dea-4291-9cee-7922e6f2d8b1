// app/api/placements/route.ts
import { fetchInternal } from "@/lib/utils";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const response = await fetchInternal(`${process.env.API_BASE_URL}/slots`, {
			headers: {
				"Content-Type": "application/json",
			},
		});

		if (!response.ok) throw new Error("Failed to fetch placements");

		const data = await response.json();
		return NextResponse.json(data);
	} catch (error: any) {
		return NextResponse.json({ error: error.message }, { status: 500 });
	}
}
