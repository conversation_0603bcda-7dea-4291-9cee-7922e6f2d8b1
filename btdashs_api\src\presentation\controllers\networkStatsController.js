const NetworkStatsService = require("../../application/services/NetworkStatsService");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllNetworkStats = asyncHandler(async (req, res) => {
	const stats = await NetworkStatsService.getAllNetworkStats();
	return sendSuccess(res, stats, "Network stats retrieved successfully");
});

const getLatestNetworkStats = asyncHandler(async (req, res) => {
	const latestStats = await NetworkStatsService.getLatestNetworkStats(1);
	const latestStat = latestStats.length > 0 ? latestStats[0] : null;

	if (!latestStat) {
		return sendNotFound(res, "No network stats found");
	}
	return sendSuccess(res, latestStat, "Latest network stats retrieved successfully");
});

const getNetworkStatById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const stat = await NetworkStatsService.getNetworkStatById(id);
	if (!stat) {
		return sendNotFound(res, "Network stat not found");
	}
	return sendSuccess(res, stat, "Network stat retrieved successfully");
});

const createNetworkStat = asyncHandler(async (req, res) => {
	const statData = {
		...req.body,
		recorded_at: new Date(),
	};

	const newStat = await NetworkStatsService.createNetworkStat(statData);
	return sendSuccess(res, newStat, "Network stat created successfully", 201);
});

const updateNetworkStat = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const updateData = {
		...req.body,
		recorded_at: new Date(),
	};

	const updatedStat = await NetworkStatsService.updateNetworkStat(id, updateData);
	return sendSuccess(res, updatedStat, "Network stat updated successfully");
});

const deleteNetworkStat = asyncHandler(async (req, res) => {
	const { id } = req.params;
	await NetworkStatsService.deleteNetworkStat(id);
	return sendSuccess(res, null, "Network stat deleted successfully");
});

/* --- TAO STATS API INTERACTIONS --- */

// Update network stats with data fetched from TaoStats API
const updateNetworkStatsWithTaoStats = async (req, res) => {
	try {
		// Current data fetches
		const latestNetworkStatsResponse = await callTaoStatsAPI("/stats/latest/v1");
		const latestNetworkStatsData = latestNetworkStatsResponse.data[0];
		const validatorData = await fetchAllPages("/validator/latest/v1");
		const latestPriceResponse = await callTaoStatsAPI("/price/latest/v1", { asset: "TAO" });
		const latestPriceData = latestPriceResponse.data[0];
		const totalEmissionResult = await db("dtm_base.subnet_metrics").sum("emission as total").first();

		// Update network stats
		await updateNetworkStats(latestNetworkStatsData, validatorData, latestPriceData, totalEmissionResult);

		updateEndpointStatus("/update/network-stats", true, "Network stats updated successfully");
		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("Error updating network stats:", error);
		updateEndpointStatus("/update/network-stats", false, error.message);
		res.status(500).json({ error: error.message });
	}
};

// Update network stats with given data
const updateNetworkStats = async (latestNetworkStatsData, validatorData, latestPriceData, totalEmissionResult) => {
	logger.info("Updating network stats with TaoStats data...");

	// Current metrics calculation
	const uniqueHotkeys = new Set(validatorData.map((v) => v.hotkey?.hex || null));
	const validatorCount = uniqueHotkeys.size;
	const totalEmission = totalEmissionResult?.total ?? null;
	const now = new Date();

	// Fetch historical data from 30 days ago
	const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
	const startTimestamp = Math.floor(thirtyDaysAgo.getTime() / 1000);
	const endTimestamp = startTimestamp + 86400; // 24h window

	let historicalStatsData, historicalPriceData;
	try {
		const [statsRes, priceRes] = await Promise.all([
			callTaoStatsAPI("/stats/history/v1", {
				timestamp_start: startTimestamp,
				timestamp_end: endTimestamp,
				frequency: "by_day",
			}),
			callTaoStatsAPI("/price/history/v1", {
				asset: "TAO",
				timestamp_start: startTimestamp,
				timestamp_end: endTimestamp,
			}),
		]);
		historicalStatsData = statsRes.data[0];
		historicalPriceData = priceRes.data[0];
	} catch (error) {
		logger.error("Error fetching historical data:", error);
		historicalStatsData = null;
		historicalPriceData = null;
	}

	// Calculate absolute changes (delta)
	const calculateDelta = (current, old) => (current != null && old != null ? current - old : null);

	// API-based deltas
	const activeSubnetsDelta = calculateDelta(latestNetworkStatsData?.subnets, historicalStatsData?.subnets);
	const totalStakedDelta = calculateDelta(latestNetworkStatsData?.staked, historicalStatsData?.staked);
	const totalTaoDelta = calculateDelta(latestPriceData?.total_supply, historicalPriceData?.total_supply);

	// DB-based deltas
	const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split("T")[0];
	const oldDailyMetrics = await db("dtm_base.network_daily_metrics").where("date", thirtyDaysAgoStr).first();

	const validatorsDelta = calculateDelta(validatorCount, oldDailyMetrics?.total_validators);
	const emissionDelta = calculateDelta(totalEmission, oldDailyMetrics?.network_emission);

	// Prepare network stats data with absolute changes
	const networkStatToInsert = {
		recorded_at: latestPriceData.created_at || now.toISOString(),
		total_validators: validatorCount || 0,
		total_validators_30d_change: validatorsDelta,
		active_subnets: latestNetworkStatsData?.subnets || null,
		active_subnets_30d_change: activeSubnetsDelta,
		transactions_24h: latestNetworkStatsData?.transfers || null,
		total_tao: latestPriceData?.total_supply || null,
		total_tao_30d_change: totalTaoDelta,
		network_emission: totalEmission,
		network_emission_30d_change: emissionDelta,
		volume_24h: latestPriceData?.volume_24h || null,
		dominance_btc: latestPriceData?.market_cap_dominance || null,
		total_staked_tao: latestNetworkStatsData?.staked || null,
		total_staked_tao_30d_change: totalStakedDelta,
	};

	// Insert network stats
	await db("dtm_base.network_stats").insert(networkStatToInsert);

	// Update daily metrics (upsert)
	const todayStr = now.toISOString().split("T")[0];
	await db("dtm_base.network_daily_metrics")
		.insert({
			date: todayStr,
			total_validators: validatorCount,
			network_emission: totalEmission,
		})
		.onConflict("date")
		.merge();

	// Prune data older than 30 days
	await db("dtm_base.network_daily_metrics").where("date", "<", db.raw(`CURRENT_DATE - INTERVAL '30 days'`)).del();

	logger.info(`Inserted network stats for ${networkStatToInsert.recorded_at}`);
};

module.exports = {
	getAllNetworkStats,
	getLatestNetworkStats,
	getNetworkStatById,
	createNetworkStat,
	updateNetworkStat,
	deleteNetworkStat,
	updateNetworkStatsWithTaoStats,
	updateNetworkStats,
};
