// app/jobs/[id]/page.tsx

import { fetchWithFallback } from "@/lib/data/utils";
import { Category, Company, Job, Product, Subnet } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import JobDetailClient from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

interface Params {
  params: { id: string };
}

// Dynamic SEO metadata for a single job detail page
export async function generateMetadata({ params }: Params): Promise<Metadata> {
  const paramsData = await params;
  const id = paramsData.id;
  const jobRes = await fetchWithFallback(
    `${process.env.APP_BASE_URL}/api/jobs/${id}`
  );
  const job: Job = jobRes.data;

  // fetch the company too
  let company: Company | undefined = undefined;
  if (job?.company_id) {
    const companyRes = await fetchWithFallback(
      `${process.env.APP_BASE_URL}/api/companies/${job.company_id}`
    );
    company = companyRes.data;
  }

  const shortDescription =
    job.description && job.description.length > 190
      ? job.description.slice(0, 190)
      : job.description;

  return generateSEOMetadata({
    title: `${job.title} | DynamicTaoMarketCap`,
    description:
      shortDescription || `Job opening: ${job.title} in the TAO ecosystem.`,
    url: `${process.env.APP_BASE_URL}/jobs/${id}`,
    image:
      company && company.logo_url
        ? company.logo_url
        : `${process.env.APP_BASE_URL}/default-job-og.jpg`,
  });
}

export default async function JobDetailPage({ params }: Params) {
  const paramsData = await params;
  const id = paramsData.id;

  // Fetch job and all related data
  const [jobRes, categoriesRes, companiesRes, subnetsRes, productsRes] =
    await Promise.all([
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/jobs/${id}`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
    ]);

  // Log any API errors
  if (jobRes.error) console.error("Job fetch error:", jobRes.error);
  if (categoriesRes.error)
    console.error("Category fetch error:", categoriesRes.error);
  if (companiesRes.error)
    console.error("Company fetch error:", companiesRes.error);
  if (subnetsRes.error) console.error("Subnet fetch error:", subnetsRes.error);
  if (productsRes.error)
    console.error("Product fetch error:", productsRes.error);

  // Extract job and associations
  const job: Job | null = jobRes.data || null;
  const relatedCategories: Category[] =
    categoriesRes.data?.filter((cat: Category) =>
      job?.category_ids?.includes(cat.id)
    ) || [];
  const company: Company | undefined = job?.company_id
    ? companiesRes.data?.find((c: Company) => c.id === job.company_id)
    : undefined;
  const relatedSubnets: Subnet[] =
    subnetsRes.data?.filter((s: Subnet) =>
      job?.subnet_ids?.includes(s.netuid)
    ) || [];
  const relatedProducts: Product[] =
    productsRes.data?.filter((p: Product) =>
      job?.product_ids?.includes(p.id)
    ) || [];

  return (
    <JobDetailClient
      job={job}
      categories={relatedCategories}
      company={company}
      subnets={relatedSubnets}
      products={relatedProducts}
    />
  );
}
