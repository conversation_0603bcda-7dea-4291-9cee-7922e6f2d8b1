// src/application/services/UserService.js

const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

/**
 * @fileoverview User Service - Handles all user-related business logic
 *
 * This service provides a clean abstraction layer for user operations,
 * separating business logic from controllers for better maintainability,
 * testability, and code organization.
 *
 * Key responsibilities:
 * - User authentication and validation
 * - User profile management
 * - Auth0 integration and synchronization
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class UserService {
	/**
	 * Sync a user from Auth0 into our database
	 * @param {Object} userData - User data from Auth0
	 * @param {string} userData.user_id - Auth0 user ID
	 * @param {string} userData.email - User email
	 * @param {string} userData.name - User name
	 * @param {string} userData.picture - User profile picture URL
	 * @returns {Promise<Object>} Created user object
	 */
	async syncUser(userData) {
		const { user_id, email, name, picture } = userData;

		try {
			// Check if user already exists
			const existingUser = await db("dtm_base.users").where({ auth0_user_id: user_id }).first();

			if (existingUser) {
				logger.info("User already exists", { user_id });
				return existingUser;
			}

			// Create new user
			const [user] = await db("dtm_base.users")
				.insert({
					auth0_user_id: user_id,
					email,
					username: name ?? "undefined",
					image_url: picture,
					created_at: new Date(),
				})
				.returning("*");

			// Create default preferences
			await db("dtm_base.user_preferences").insert({
				user_id: user.id,
				created_at: new Date(),
			});

			logger.info("User synced successfully", { user_id: user.id });
			return user;
		} catch (error) {
			logger.error("Error syncing user", { error, user_id });
			throw new Error(`Failed to sync user: ${error.message}`);
		}
	}

	/**
	 * Get user by Auth0 ID
	 * @param {string} auth0_id - Auth0 user ID
	 * @returns {Promise<Object|null>} User object or null if not found
	 */
	async getUserByAuth0Id(auth0_id) {
		try {
			const user = await db("dtm_base.users").where({ auth0_user_id: auth0_id }).first();

			return user || null;
		} catch (error) {
			logger.error("Error getting user by Auth0 ID", { error, auth0_id });
			throw new Error(`Failed to get user: ${error.message}`);
		}
	}

	/**
	 * Update user profile data
	 * @param {string} auth0_id - Auth0 user ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated user object
	 */
	async updateUserProfile(auth0_id, updateData) {
		try {
			const user = await this.getUserByAuth0Id(auth0_id);
			if (!user) {
				throw new Error("User not found");
			}

			const {
				first_name,
				last_name,
				image_url,
				headline,
				location,
				bio,
				phone,
				expert_job_title,
				website,
				skill_ids,
			} = updateData;

			const updatePayload = {
				first_name,
				last_name,
				image_url,
				headline,
				location,
				bio,
				phone,
				expert_job_title,
				website,
				skill_ids,
				updated_at: new Date(),
			};

			// Remove undefined values
			Object.keys(updatePayload).forEach((key) => {
				if (updatePayload[key] === undefined) {
					delete updatePayload[key];
				}
			});

			const [updatedUser] = await db("dtm_base.users")
				.where({ auth0_user_id: auth0_id })
				.update(updatePayload)
				.returning("*");

			logger.info("User profile updated", { user_id: user.id });
			return updatedUser;
		} catch (error) {
			logger.error("Error updating user profile", { error, auth0_id });
			throw new Error(`Failed to update user profile: ${error.message}`);
		}
	}

	/**
	 * Validate user authentication and return user data
	 * @param {string} auth0_id - Auth0 user ID
	 * @returns {Promise<Object>} User object
	 * @throws {Error} If user not found or invalid
	 */
	async validateAndGetUser(auth0_id) {
		if (!auth0_id) {
			throw new Error("Authentication required: No valid token provided");
		}

		const user = await this.getUserByAuth0Id(auth0_id);
		if (!user) {
			throw new Error("User not found");
		}

		return user;
	}

	// ─── ADMIN METHODS ─────────────────────────────────────────────────

	/**
	 * Check if user is admin
	 * @param {string} auth0UserId - Auth0 user ID
	 * @returns {Promise<boolean>} Whether user is admin
	 */
	async isAdminUser(auth0UserId) {
		try {
			const adminUser = await db("dtm_base.admin_users").where({ auth0_user_id: auth0UserId }).first();

			return !!adminUser;
		} catch (error) {
			logger.error("Error checking admin status", { error, auth0UserId });
			return false;
		}
	}

	/**
	 * Get admin users with their details
	 * @returns {Promise<Array>} Array of admin users
	 */
	async getAdminUsers() {
		try {
			const admins = await db("dtm_base.admin_users as au")
				.join("dtm_base.users as u", "au.user_id", "u.id")
				.select(
					"u.id",
					"u.username",
					"u.email",
					"u.first_name",
					"u.last_name",
					"au.role",
					"au.created_at as admin_since"
				);

			return admins;
		} catch (error) {
			logger.error("Error fetching admin users", { error });
			return [];
		}
	}

	/**
	 * Add user to admin_users table
	 * @param {string} auth0UserId - Auth0 user ID
	 * @param {string} role - Admin role (default: 'admin')
	 * @returns {Promise<boolean>} Success status
	 */
	async makeUserAdmin(auth0UserId, role = "admin") {
		try {
			// Get user first
			const user = await this.getUserByAuth0Id(auth0UserId);
			if (!user) {
				logger.error("Cannot make non-existent user admin", { auth0UserId });
				return false;
			}

			// Check if already admin
			const existingAdmin = await db("dtm_base.admin_users").where({ auth0_user_id: auth0UserId }).first();
			if (existingAdmin) {
				logger.info("User is already an admin", { auth0UserId });
				return true;
			}

			// Add to admin_users table
			await db("dtm_base.admin_users").insert({
				auth0_user_id: auth0UserId,
				user_id: user.id,
				role,
				created_at: new Date(),
				updated_at: new Date(),
			});

			logger.info("User made admin successfully", { auth0UserId, userId: user.id, role });
			return true;
		} catch (error) {
			logger.error("Error making user admin", { error, auth0UserId, role });
			return false;
		}
	}

	/**
	 * Remove user from admin_users table
	 * @param {string} auth0UserId - Auth0 user ID
	 * @returns {Promise<boolean>} Success status
	 */
	async removeUserAdmin(auth0UserId) {
		try {
			const deletedRows = await db("dtm_base.admin_users").where({ auth0_user_id: auth0UserId }).del();

			if (deletedRows > 0) {
				logger.info("User admin privileges removed", { auth0UserId });
				return true;
			} else {
				logger.warn("User was not an admin", { auth0UserId });
				return false;
			}
		} catch (error) {
			logger.error("Error removing user admin privileges", { error, auth0UserId });
			return false;
		}
	}

	/**
	 * Get admin user details by Auth0 ID
	 * @param {string} auth0UserId - Auth0 user ID
	 * @returns {Promise<Object|null>} Admin user details or null
	 */
	async getAdminUserDetails(auth0UserId) {
		try {
			const adminUser = await db("dtm_base.admin_users as au")
				.join("dtm_base.users as u", "au.user_id", "u.id")
				.where("au.auth0_user_id", auth0UserId)
				.select(
					"u.id",
					"u.username",
					"u.email",
					"u.first_name",
					"u.last_name",
					"au.role",
					"au.created_at as admin_since"
				)
				.first();

			return adminUser || null;
		} catch (error) {
			logger.error("Error fetching admin user details", { error, auth0UserId });
			return null;
		}
	}
}

module.exports = new UserService();
