"use client";

import { Badge } from "@/components/ui/badge";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import type { Category, Company, Event, Product, Subnet } from "@/lib/db/models";
import { cn, slugify } from "@/lib/utils";
import { Calendar, Edit, Eye, Heart, MapPin, Trash2, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

const gradients = [
	"bg-gradient-to-br from-purple-500 to-indigo-600",
	"bg-gradient-to-br from-blue-500 to-cyan-600",
	"bg-gradient-to-br from-emerald-500 to-teal-600",
	"bg-gradient-to-br from-rose-500 to-pink-600",
	"bg-gradient-to-br from-amber-500 to-orange-600",
	"bg-gradient-to-br from-violet-500 to-purple-600",
	"bg-gradient-to-br from-green-500 to-emerald-600",
	"bg-gradient-to-br from-red-500 to-rose-600",
];

interface EventCardProps {
	event: Event & {
		companies?: Company[];
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	};
	editMode?: boolean;
	onEdit?: (event: Event) => void;
	onDelete?: (eventId: number) => void;
}

function getEventStatus(event: Event) {
	const now = new Date();
	const start = new Date(event.start_date);
	const end = event.end_date ? new Date(event.end_date) : null;

	if (start > now) {
		const diffDays = Math.ceil((start.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
		return { text: `Starts in ${diffDays}d`, variant: "upcoming" };
	} else if ((end && now <= end) || (!end && now >= start)) {
		return { text: "Happening now", variant: "live" };
	} else {
		const diffDays = Math.ceil((now.getTime() - (end ?? start).getTime()) / (1000 * 60 * 60 * 24));
		return { text: `${diffDays}d ago`, variant: "past" };
	}
}

function formatDateRange(startDate: Date, endDate: Date | null) {
	const start = new Date(startDate);
	const end = endDate ? new Date(endDate) : null;

	const startStr = start.toLocaleDateString("en-US", { month: "short", day: "numeric" });

	if (!end) return startStr;

	const endStr = end.toLocaleDateString("en-US", {
		month: start.getMonth() === end.getMonth() ? undefined : "short",
		day: "numeric",
	});

	return `${startStr} - ${endStr}`;
}

export function EventCard({ event, editMode = false, onEdit, onDelete }: EventCardProps) {
	const router = useRouter();
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);

	const status = getEventStatus(event);
	const gradientIndex = Math.abs(event.id) % gradients.length;
	const gradientClass = gradients[gradientIndex];

	const handleEditClick = (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		if (onEdit) onEdit(event);
	};

	const handleDeleteClick = (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setShowDeleteDialog(true);
	};

	const confirmDelete = async () => {
		if (!onDelete) return;
		setIsDeleting(true);
		try {
			await onDelete(event.id);
			setShowDeleteDialog(false);
		} finally {
			setIsDeleting(false);
		}
	};

	const content = (
		<div
			className={cn(
				"flex h-[180px] relative group rounded-xl overflow-hidden transition-all hover:shadow-lg",
				gradientClass
			)}
		>
			{/* Left side - Image */}
			<div className="relative w-[180px] h-full flex-shrink-0">
				<Image
					src={event.image_url || event.image_url_banner || "/placeholder.svg"}
					alt={event.name}
					fill
					sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
					className="object-cover"
				/>
				<div className="absolute inset-0 bg-black/30" />

				{/* Company logos */}
				{event.companies && event.companies.length > 0 && (
					<div className="flex items-center pt-2 pl-2">
						<div className="flex -space-x-2">
							{event.companies.slice(0, 6).map((c, i) => (
								<div
									key={c.id || i}
									className="relative h-8 w-8 rounded-full border-2 border-white bg-background overflow-hidden"
									title={c.name}
								>
									<Image
										src={c.logo_url || "/placeholder.svg"}
										alt={c.name || ""}
										fill
										className="object-cover"
									/>
								</div>
							))}
						</div>
						{Array.isArray(event.subnets) &&
							Array.isArray(event?.subnet_ids) &&
							event.companies.length < 7 &&
							event.subnets
								.filter((subnet) => event.subnet_ids?.includes(subnet.netuid))
								.map((subnet, index) => (
									<Link
										key={subnet.id || index}
										href={`/subnets/${subnet.netuid}`}
										target="_blank"
										rel="noopener noreferrer"
										className="relative h-8 w-8 rounded-full border-2 border-white bg-background overflow-hidden"
										title={subnet.name}
									>
										<div className="w-7 h-7 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold">
											{subnet.subnet_symbol || subnet.name.charAt(0)}
										</div>
									</Link>
								))}
					</div>
				)}

				<Badge
					className={cn(
						"absolute bottom-2 left-2 bg-black/40 backdrop-blur-sm text-white border-0",
						status.variant === "upcoming" && "bg-blue-500/80",
						status.variant === "live" && "bg-green-500/80",
						status.variant === "past" && "bg-gray-500/80"
					)}
				>
					{status.text}
				</Badge>
			</div>

			{/* Right side - Content */}
			<div className="flex-1 p-4 flex flex-col justify-between">
				<div>
					<h3 className="font-bold text-white text-lg line-clamp-2">{event.name}</h3>
					{event.description && (
						<p className="text-white/80 text-sm mt-1 line-clamp-2">{event.description}</p>
					)}
				</div>

				<div>
					<div className="flex flex-wrap gap-3 mt-2">
						<div className="flex items-center text-white/90 text-sm">
							<Calendar className="h-4 w-4 mr-1" />
							<span>{formatDateRange(event.start_date, event.end_date)}</span>
						</div>
						{event.location && (
							<div className="flex items-center text-white/90 text-sm">
								<MapPin className="h-4 w-4 mr-1" />
								<span>{event.location}</span>
							</div>
						)}
					</div>

					<div className="flex items-center justify-between mt-3 pt-2 border-t border-white/20">
						<div className="flex items-center">
							<div className="h-5 w-5 rounded-full bg-white/20 flex items-center justify-center">
								<Users className="h-3 w-3 text-white" />
							</div>
							<span className="text-sm text-white/80 ml-1.5">
								{event.organizer_ids?.length || "No"} organizer
								{event.organizer_ids?.length !== 1 ? "s" : ""}
							</span>
						</div>
						<div className="flex items-center bg-black/30 backdrop-blur-sm rounded-full px-2 py-1">
							<Heart className="h-3 w-3 mr-1 fill-white" />
							<span className="text-xs text-white">0</span>
						</div>
					</div>
				</div>
			</div>

			{editMode && (
				<div className="absolute top-2 right-2 flex gap-1 z-10">
					<Link
						href={`/events/${slugify(event.name)}-${event.id}`}
						className="bg-white/20 hover:bg-white/30 p-1.5 rounded-full transition-colors"
						title="View"
					>
						<Eye className="h-5 w-5 text-white" />
					</Link>
					<button
						onClick={handleEditClick}
						className="bg-white/20 hover:bg-white/30 p-1.5 rounded-full transition-colors"
						title="Edit"
					>
						<Edit className="h-5 w-5 text-white" />
					</button>
					<button
						onClick={handleDeleteClick}
						className="bg-red-500/80 hover:bg-red-500 p-1.5 rounded-full transition-colors"
						disabled={isDeleting}
						title="Delete"
					>
						<Trash2 className="h-5 w-5 text-white" />
					</button>
				</div>
			)}
		</div>
	);

	return (
		<>
			{editMode ? (
				<div className="relative">{content}</div>
			) : (
				<div
					onClick={() => router.push(`/events/${slugify(event.name)}-${event.id}`)}
					role="button"
					tabIndex={0}
					onKeyDown={(e) => e.key === "Enter" && router.push(`/events/${slugify(event.name)}-${event.id}`)}
					className="block cursor-pointer"
				>
					{content}
				</div>
			)}

			<ConfirmDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
				title="Delete Event"
				description={`Are you sure you want to delete "${event.name}"? This action cannot be undone.`}
				onConfirm={confirmDelete}
				confirmText="Delete"
				loading={isDeleting}
			/>
		</>
	);
}
