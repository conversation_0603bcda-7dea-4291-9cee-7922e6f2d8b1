import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { stripeHelpers } from '@/lib/stripe-server';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customerId');

    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID is required' },
        { status: 400 }
      );
    }

    // List payment methods for the customer
    const paymentMethods = await stripeHelpers.listPaymentMethods(customerId);

    return NextResponse.json({
      paymentMethods: paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        card: pm.card,
        created: pm.created,
      })),
    });

  } catch (error) {
    console.error('Error fetching payment methods:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch payment methods';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
