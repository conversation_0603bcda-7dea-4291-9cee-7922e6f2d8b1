import type { Metadata } from "next";

interface BaseSEOInput {
	title?: string;
	description?: string;
	url: string;
	image?: string;
	siteName?: string;
	type?: "website" | "article";
	twitterCard?: "summary_large_image" | "summary";
	imageAlt?: string;
	imageWidth?: number;
	imageHeight?: number;
}

function getImageTypeFromUrl(url: string): string {
	if (!url) return "image/png"; // default fallback

	const extension = url.split(".").pop()?.toLowerCase();

	switch (extension) {
		case "jpg":
		case "jpeg":
			return "image/jpeg";
		case "png":
			return "image/png";
		case "gif":
			return "image/gif";
		case "webp":
			return "image/webp";
		case "svg":
			return "image/svg+xml";
		default:
			return "image/png"; // default fallback
	}
}

export function generateSEOMetadata({
	title,
	description,
	url,
	image,
	siteName = "DynamicTaoMarketCap",
	type = "website",
	twitterCard = "summary_large_image",
	imageAlt,
	imageWidth = 1200,
	imageHeight = 630,
}: BaseSEOInput): Metadata {
	const finalImage = image?.startsWith("http") ? image : `${process.env.APP_BASE_URL}/${image}`;
	const imageType = finalImage ? getImageTypeFromUrl(finalImage) : undefined;

	const metadata: Metadata = {
		title,
		description,
		openGraph: {
			title,
			description,
			url,
			siteName,
			type,
			...(finalImage && {
				images: [
					{
						url: finalImage,
						width: imageWidth,
						height: imageHeight,
						alt: imageAlt || title || "",
						...(imageType && { type: imageType }),
					},
				],
			}),
		},
		twitter: {
			card: twitterCard,
			title,
			description,
			...(finalImage && { images: [finalImage] }),
		},
		metadataBase: new URL(`${process.env.APP_BASE_URL}`),
	};

	return metadata;
}
