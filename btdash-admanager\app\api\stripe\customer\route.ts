import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { stripeHelpers } from '@/lib/stripe-server';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customerId');

    if (!customerId) {
      return NextResponse.json(
        { error: 'Customer ID is required' },
        { status: 400 }
      );
    }

    // Get customer from Stripe
    const customer = await stripeHelpers.getCustomer(customerId);

    return NextResponse.json({
      customer: {
        id: customer.id,
        email: customer.email,
        name: customer.name,
        created: customer.created,
      },
    });

  } catch (error) {
    console.error('Error fetching customer:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch customer';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { email, name } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Create customer in Stripe
    const customer = await stripeHelpers.createCustomer(
      email,
      name,
      {
        auth0_id: session.user.sub,
        created_from: 'btdash-admanager',
      }
    );

    // Store customer ID in our database
    const response = await fetch(`${process.env.API_BASE_URL}/user/stripe-customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
        'X-Internal-Key': process.env.INTERNAL_API_KEY || '',
      },
      body: JSON.stringify({
        stripe_customer_id: customer.id,
      }),
    });

    if (!response.ok) {
      console.error('Failed to store customer ID in database');
      // Continue anyway, as the customer was created successfully in Stripe
    }

    return NextResponse.json({
      customer: {
        id: customer.id,
        email: customer.email,
        name: customer.name,
        created: customer.created,
      },
    });

  } catch (error) {
    console.error('Error creating customer:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to create customer';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
