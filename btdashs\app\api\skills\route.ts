import { getAllItems } from "@/lib/data/utils";
import { NextResponse } from "next/server";

export async function GET() {
	try {
		const data = await getAllItems("skills");
		return NextResponse.json({
			success: true,
			data,
			message: "Skills fetched successfully",
		});
	} catch (err) {
		return NextResponse.json(
			{
				success: false,
				message: "Failed to fetch skills",
				errors: err instanceof Error ? err.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
