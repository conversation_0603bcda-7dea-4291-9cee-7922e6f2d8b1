"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Subnet, SubnetMetric } from "@/lib/db/models";
import { getSubnetSymbol } from "@/lib/utils";
import { ArrowDownRight, ArrowUpRight } from "lucide-react";
import Link from "next/link";
import { Line, LineChart, ResponsiveContainer, YAxis } from "recharts";

interface TrendingSubnet extends Subnet {
	metrics?: SubnetMetric;
	growthPercentage: number;
}

interface TrendingSubnetsProps {
	subnets: Subnet[];
	metrics: SubnetMetric[];
}

export function TrendingSubnets({ subnets, metrics }: TrendingSubnetsProps) {
	// Process subnets with metrics and growth calculations
	const trendingSubnets = subnets
		.map((subnet) => {
			const subnetMetrics = metrics.find((m) => m.netuid === subnet.netuid);
			return {
				...subnet,
				metrics: subnetMetrics,
				growthPercentage: calculateGrowthPercentage(subnetMetrics),
			};
		})
		.sort((a, b) => (b.growthPercentage || 0) - (a.growthPercentage || 0))
		.slice(0, 4);

	return (
		<div className="grid grid-cols-2 md:grid-cols-4 gap-3">
			{trendingSubnets.map((subnet, i) => {
				const chartData = formatMetricsForChart(subnet.metrics);
				const isPositive = (subnet.growthPercentage || 0) > 0;

				return (
					<Link key={i} href={`/subnets/${subnet.netuid}`} className="hover:shadow-sm transition-all">
						<Card className="p-3 h-full border dark:border-border hover:bg-muted/50 transition-colors">
							<div className="flex items-center justify-between mb-1">
								<div className="flex items-center space-x-2">
									<div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold text-primary">
										{getSubnetSymbol(subnet)}
									</div>
									<span className="font-medium text-sm">{subnet.name}</span>
								</div>
								<div
									className={`flex items-center text-xs ${
										isPositive ? "text-green-600" : "text-red-600"
									}`}
								>
									{isPositive ? (
										<ArrowUpRight className="h-3 w-3 mr-0.5" />
									) : (
										<ArrowDownRight className="h-3 w-3 mr-0.5" />
									)}
									{Math.abs(subnet.growthPercentage || 0).toFixed(1)}%
								</div>
							</div>
							<div className="flex items-center gap-1 mb-1">
								<Badge variant="outline" className="text-xs">
									{subnet.netuid}
								</Badge>
							</div>
							<div className="text-base font-bold mb-1">
								{subnet.metrics?.alpha_price_tao
									? Number(subnet.metrics.alpha_price_tao).toFixed(6)
									: "N/A"}{" "}
								τ
							</div>

							<div className="h-16 w-full flex justify-end">
								<div className="w-3/4 h-full">
									<ResponsiveContainer width="100%" height="100%">
										<LineChart data={chartData} margin={{ top: 2, right: 0, bottom: 2, left: 0 }}>
											<YAxis domain={["auto", "auto"]} hide />
											<Line
												type="monotone"
												dataKey="value"
												stroke={isPositive ? "#22c55e" : "#ef4444"}
												strokeWidth={2}
												dot={false}
												isAnimationActive={false}
											/>
										</LineChart>
									</ResponsiveContainer>
								</div>
							</div>
						</Card>
					</Link>
				);
			})}
		</div>
	);
}

function calculateGrowthPercentage(metrics?: SubnetMetric): number {
	return metrics?.price_change_1_week || 0;
}

function formatMetricsForChart(metrics?: SubnetMetric): { value: number; name: string }[] {
	if (!metrics?.seven_day_prices) return [];

	try {
		const prices =
			typeof metrics.seven_day_prices === "string"
				? JSON.parse(metrics.seven_day_prices)
				: metrics.seven_day_prices;

		// Convert to percentage change relative to first day
		if (prices.length > 0) {
			const baseValue = parseFloat(prices[0]) || 1;
			return prices.map((price: string, index: number) => ({
				name: `${index + 1}`,
				value: ((parseFloat(price) - baseValue) / baseValue) * 100,
			}));
		}

		return [];
	} catch (e) {
		console.error("Error processing seven_day_prices:", e);
		return [];
	}
}
