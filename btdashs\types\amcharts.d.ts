// Type definitions for amCharts 5
interface Am5Root {
    container: any
    dispose: () => void
    setThemes: (themes: any[]) => void
    interfaceColors: {
      get: (color: string) => any
    }
  }
  
  interface Am5 {
    Root: {
      new: (element: HTMLElement | null) => Am5Root
    }
    Bullet: {
      new: (root: Am5Root, config: any) => any
    }
    Circle: {
      new: (root: Am5Root, config: any) => any
    }
  }
  
  interface Am5Flow {
    ChordDirected: {
      new: (root: Am5Root, config: any) => any
    }
  }
  
  interface Am5ThemesAnimated {
    new: (root: Am5Root) => any
  }
  
  interface Window {
    am5?: Am5
    am5flow?: Am5Flow
    am5themes_Animated?: {
      new: (root: Am5Root) => any
    }
  }
  