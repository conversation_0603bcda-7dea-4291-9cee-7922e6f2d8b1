const PublicCompaniesService = require("../../application/services/PublicCompaniesService");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllCompanies = asyncHandler(async (req, res) => {
	const companies = await PublicCompaniesService.getAllCompanies();
	return sendSuccess(res, companies, "Companies retrieved successfully");
});

const getCompanyById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const company = await PublicCompaniesService.getCompanyById(id);
	if (!company) {
		return sendNotFound(res, "Company not found");
	}
	return sendSuccess(res, company, "Company retrieved successfully");
});

const createCompany = asyncHandler(async (req, res) => {
	const { name, description, website, logo_url } = req.body;
	const newCompany = await PublicCompaniesService.createCompany({
		name,
		description,
		website,
		logo_url,
	});
	return sendSuccess(res, newCompany, "Company created successfully", 201);
});

const updateCompany = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const { name, description, website, logo_url } = req.body;
	const updatedCompany = await PublicCompaniesService.updateCompany(id, {
		name,
		description,
		website,
		logo_url,
	});
	if (!updatedCompany) {
		return sendNotFound(res, "Company not found");
	}
	return sendSuccess(res, updatedCompany, "Company updated successfully");
});

const deleteCompany = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const deleted = await PublicCompaniesService.deleteCompany(id);
	if (!deleted) {
		return sendNotFound(res, "Company not found");
	}
	return sendSuccess(res, null, "Company deleted successfully");
});

module.exports = {
	getAllCompanies,
	getCompanyById,
	createCompany,
	updateCompany,
	deleteCompany,
};
