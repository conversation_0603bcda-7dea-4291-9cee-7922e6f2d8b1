// Real analytics API integration - no more mock data

// Types for analytics data
export type DailyMetric = {
	date: string;
	impressions: number;
	clicks: number;
	ctr: number;
};

export type CountryMetric = {
	country_code: string;
	impressions: number;
	clicks: number;
	ctr: number;
};

export type DeviceMetric = {
	device_type: string;
	impressions: number;
	clicks: number;
	ctr: number;
};

export type CampaignAnalytics = {
	campaign_id: number;
	campaign_name: string;
	total_impressions: number;
	total_clicks: number;
	ctr: number;
	total_spend: number;
	avg_cpc: number;
	avg_cpm: number;
	daily_metrics: DailyMetric[];
	country_metrics: CountryMetric[];
	device_metrics: DeviceMetric[];
};

export type UserAnalytics = {
	user_id: number;
	total_impressions: number;
	total_clicks: number;
	ctr: number;
	total_spend: number;
	campaigns: CampaignAnalytics[];
	daily_metrics: DailyMetric[];
	country_metrics: CountryMetric[];
	device_metrics: DeviceMetric[];
};

// API service for analytics
export class AnalyticsAPI {
	private static async fetchWithAuth(url: string, options: RequestInit = {}) {
		const response = await fetch(url, {
			...options,
			headers: {
				"Content-Type": "application/json",
				...options.headers,
			},
		});

		if (!response.ok) {
			throw new Error(`API Error: ${response.status} - ${response.statusText}`);
		}

		const result = await response.json();
		if (!result.success) {
			throw new Error(result.message || "API request failed");
		}

		return result.data;
	}

	static async getUserAnalytics(startDate?: string, endDate?: string): Promise<UserAnalytics> {
		const params = new URLSearchParams();
		if (startDate) params.append("start_date", startDate);
		if (endDate) params.append("end_date", endDate);

		const queryString = params.toString();
		const url = `/api/user/analytics${queryString ? `?${queryString}` : ""}`;

		return this.fetchWithAuth(url);
	}

	static async getCampaignAnalytics(
		campaignId: string,
		startDate?: string,
		endDate?: string
	): Promise<CampaignAnalytics> {
		const params = new URLSearchParams();
		if (startDate) params.append("start_date", startDate);
		if (endDate) params.append("end_date", endDate);

		const queryString = params.toString();
		const url = `/api/campaigns/${campaignId}/analytics${queryString ? `?${queryString}` : ""}`;

		return this.fetchWithAuth(url);
	}

	static async getAdAnalytics(adId: string, startDate?: string, endDate?: string) {
		const params = new URLSearchParams();
		if (startDate) params.append("start_date", startDate);
		if (endDate) params.append("end_date", endDate);

		const queryString = params.toString();
		const url = `/api/ads/${adId}/analytics${queryString ? `?${queryString}` : ""}`;

		return this.fetchWithAuth(url);
	}

	static async getConversionTracking(campaignId: string, startDate?: string, endDate?: string) {
		const params = new URLSearchParams();
		if (startDate) params.append("start_date", startDate);
		if (endDate) params.append("end_date", endDate);

		const queryString = params.toString();
		const url = `/api/campaigns/${campaignId}/conversions${queryString ? `?${queryString}` : ""}`;

		return this.fetchWithAuth(url);
	}

	static async getAdvertiserBalance() {
		return this.fetchWithAuth("/api/user/balance");
	}

	static async getSpendingHistory() {
		return this.fetchWithAuth("/api/user/spending-history");
	}

	static async getCampaignSpendSummary(campaignId: string) {
		return this.fetchWithAuth(`/api/campaigns/${campaignId}/spend-summary`);
	}
}

// All analytics data now comes from real API calls - no more mock data
