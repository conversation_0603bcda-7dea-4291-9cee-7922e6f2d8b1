import { AdSenseProvider } from "@/components/ads-placements/adsense-provider";
import { BetaFooter } from "@/components/beta-footer";
import { Footer } from "@/components/footer";
import { StatsBar } from "@/components/stats-bar";
import { ThemeProvider } from "@/components/theme-provider";
import { TopNav } from "@/components/top-nav";
import { auth0 } from "@/lib/auth0";
import { fetchWithFallback } from "@/lib/data/utils";
import "@/styles/globals.css";
import { User } from "lib/db/models";
import { Inter } from "next/font/google";
import { cookies } from "next/headers";
import Script from "next/script";

const inter = Inter({ subsets: ["latin"] });

export default async function RootLayout({ children }: { children: React.ReactNode }) {
	const networkStats = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/network-stats/latest`);

	// Check if user is logged in
	let user = null;
	try {
		const session = await auth0.getSession();
		if (session) {
			// Try to fetch user data, but don't fail if it doesn't work
			try {
				const cookieHeader = (await cookies()).toString();
				const userData = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/me`, {
					headers: { Cookie: cookieHeader },
				});

				if (userData.data && !userData.error) {
					user = userData.data as User;
				}
			} catch (error) {
				console.log("Could not fetch user data, user will be null");
			}
		}
	} catch (error) {
		console.log("Auth0 session check failed, user will be null");
	}

	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<link rel="canonical" href={process.env.APP_BASE_URL} />
				{/* Google Analytics */}
				<Script
					src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GA_MEASUREMENT_ID}`}
					strategy="afterInteractive"
				/>
				<Script id="google-analytics" strategy="afterInteractive">
					{`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.GA_MEASUREMENT_ID}');
          `}
				</Script>
				{/* AdSense Provider */}
				<AdSenseProvider />
			</head>
			<body className={inter.className}>
				<ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
					<div className="min-h-screen bg-background text-foreground flex flex-col">
						<TopNav user={user} />
						<StatsBar networkStats={networkStats.data} />
						<main className="flex-1 w-full max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8">{children}</main>
						<BetaFooter />
						<Footer />
					</div>
				</ThemeProvider>
			</body>
		</html>
	);
}
