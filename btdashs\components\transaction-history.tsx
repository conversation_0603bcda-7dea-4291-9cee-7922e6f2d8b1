import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"

const transactions = [
  { id: 1, type: "Swap", from: "TAO", to: "Subnet 1", amount: "100", timestamp: "2 mins ago" },
  { id: 2, type: "Swap", from: "Subnet 2", to: "TAO", amount: "50", timestamp: "5 mins ago" },
  { id: 3, type: "Add Liquidity", from: "TAO", to: "Subnet 1", amount: "200", timestamp: "10 mins ago" },
]

export function TransactionHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Transactions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((tx) => (
            <div key={tx.id} className="flex justify-between items-center">
              <div>
                <p className="font-medium">{tx.type}</p>
                <p className="text-sm text-muted-foreground">
                  {tx.from} → {tx.to}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm">{tx.amount}</p>
                <p className="text-xs text-muted-foreground">{tx.timestamp}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

