// app/profile/page.tsx
"use client";

import ProfileBasicInfoClient from "@/components/profile/profile-basic-info";
import ProfilePreferencesClient from "@/components/profile/profile-preferences";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { User, UserPreferences } from "@/lib/db/models";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function ProfileDashboardClient({
	profile,
	preferences,
}: {
	profile: User;
	preferences: UserPreferences;
}) {
	const tabs = [
		{ value: "basic-info", label: "Profile" },
		{ value: "preferences", label: "Preferences" },
	];

	const [activeTab, setActiveTab] = useState("basic-info");

	useEffect(() => {
		const handleHashChange = () => {
			const hash = window.location.hash.substring(1);
			if (hash && tabs.some((tab) => tab.value === hash)) {
				setActiveTab(hash);
			}
		};

		handleHashChange();
		window.addEventListener("hashchange", handleHashChange);

		return () => {
			window.removeEventListener("hashchange", handleHashChange);
		};
	}, []);

	const handleTabChange = (value: string) => {
		setActiveTab(value);
		window.location.hash = value;
	};

	return (
		<div className="bg-background rounded-lg shadow-md dark:border dark:border-border">
			<div className="flex justify-between items-center p-6">
				<h2 className="text-2xl font-semibold">
					{profile?.first_name} {profile?.last_name}
				</h2>
				<Link href="/api/auth/logout" prefetch={false}>
					<Button variant="destructive">Logout</Button>
				</Link>
			</div>

			<Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
				<div className="border-b dark:border-border">
					<TabsList>
						{tabs.map((tab) => (
							<TabsTrigger key={tab.value} value={tab.value}>
								{tab.label}
							</TabsTrigger>
						))}
					</TabsList>
				</div>

				<div className="p-6">
					<TabsContent value="basic-info">
						<ProfileBasicInfoClient initialProfile={profile} />
					</TabsContent>
					<TabsContent value="preferences">
						<ProfilePreferencesClient initialPreferences={preferences} />
					</TabsContent>
				</div>
			</Tabs>
		</div>
	);
}
