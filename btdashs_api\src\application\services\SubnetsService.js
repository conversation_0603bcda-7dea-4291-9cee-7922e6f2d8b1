// src/application/services/SubnetsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Subnets Service - Handles subnet data operations
 *
 * This service manages subnet information from the TaoStats API,
 * providing CRUD operations and data management for subnet metrics.
 *
 * Key responsibilities:
 * - Subnet data CRUD operations
 * - Subnet metrics management
 * - Data validation and sanitization
 * - Integration with TaoStats API data
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class SubnetsService extends BaseService {
	constructor() {
		super("dtm_base.subnets", "Subnet");
	}

	/**
	 * Get all subnets with default sorting
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of subnets
	 */
	async getAllSubnets(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "netuid", direction: "asc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all subnets", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get subnet by ID
	 * @param {number} id - Subnet ID
	 * @returns {Promise<Object|null>} Subnet object or null if not found
	 */
	async getSubnetById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting subnet by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new subnet
	 * @param {Object} subnetData - Subnet data
	 * @returns {Promise<Object>} Created subnet object
	 */
	async createSubnet(subnetData) {
		try {
			const newSubnet = await this.create(subnetData);
			logger.info("Subnet created", { subnet_id: newSubnet.id });
			return newSubnet;
		} catch (error) {
			logger.error("Error creating subnet", { error, subnetData });
			throw error;
		}
	}

	/**
	 * Update a subnet
	 * @param {number} id - Subnet ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated subnet object
	 */
	async updateSubnet(id, updateData) {
		try {
			const updatedSubnet = await this.updateById(id, updateData);
			logger.info("Subnet updated", { subnet_id: id });
			return updatedSubnet;
		} catch (error) {
			logger.error("Error updating subnet", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a subnet
	 * @param {number} id - Subnet ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteSubnet(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Subnet deleted", { subnet_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting subnet", { error, id });
			throw error;
		}
	}

	/**
	 * Get subnet by netuid
	 * @param {number} netuid - Network UID
	 * @returns {Promise<Object|null>} Subnet object or null if not found
	 */
	async getSubnetByNetuid(netuid) {
		try {
			const subnets = await this.getAll({ netuid });
			return subnets.length > 0 ? subnets[0] : null;
		} catch (error) {
			logger.error("Error getting subnet by netuid", { error, netuid });
			throw new Error(`Failed to get subnet by netuid: ${error.message}`);
		}
	}

	/**
	 * Get filtered subnets (excludes unknown/unnamed and requires github_repo)
	 * @returns {Promise<Array>} Array of filtered subnets
	 */
	async getFilteredSubnets() {
		try {
			const db = require("../../infrastructure/database/knex");

			const subnets = await db(this.tableName)
				.select("*")
				.whereNotNull("github_repo")
				.whereRaw("LOWER(name) NOT LIKE '%unknown%'")
				.whereRaw("LOWER(name) NOT LIKE '%reserved%'")
				.whereRaw("LOWER(name) NOT LIKE '%unnamed%'")
				.orderBy("netuid", "asc");

			logger.info("Filtered subnets retrieved", { count: subnets.length });
			return subnets;
		} catch (error) {
			logger.error("Error getting filtered subnets", { error });
			throw new Error(`Failed to get filtered subnets: ${error.message}`);
		}
	}
}

module.exports = new SubnetsService();
