"use client";

/* import { SupportPopover } from "@/components/SupportPopover";
 */ import { Button } from "@/components/ui/button";
/* import { Input } from "@/components/ui/input";
 */ import { Separator } from "@/components/ui/separator";
import { DiscIcon as Discord, Gith<PERSON>, Twitter } from "lucide-react";
import Link from "next/link";
import type React from "react";

export function Footer() {
  /* const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log("Newsletter subscription");
  }; */

  return (
    <footer className="border-t bg-background">
      <div className="max-w-[2000px] mx-auto px-6 sm:px-8 lg:px-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 py-12">
          <div className="space-y-4">
            <h3 className="font-bold text-lg">
              About Dynamic TAO MarketCap (DTM)
            </h3>
            <p className="text-sm text-muted-foreground">
              DTM empowers Bittensor network participants with insights into TAO
              and alpha token dynamics, enabling informed decision-making in a
              decentralized, market-driven ecosystem.
            </p>
            <div className="flex space-x-4 items-center">
              <Button variant="ghost" size="icon" asChild>
                <Link href="https://github.com/opentensor" target="_blank">
                  <Github className="h-5 w-5" />
                  <span className="sr-only">GitHub</span>
                </Link>
              </Button>
              <Button variant="ghost" size="icon" asChild>
                <Link href="https://x.com/opentensor" target="_blank">
                  <Twitter className="h-5 w-5" />
                  <span className="sr-only">Twitter</span>
                </Link>
              </Button>
              <Button variant="ghost" size="icon" asChild>
                <Link href="https://discord.gg/qasY3HA9F9" target="_blank">
                  <Discord className="h-5 w-5" />
                  <span className="sr-only">Discord</span>
                </Link>
              </Button>
              {/*  <SupportPopover /> */}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-bold text-lg">Resources</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/advertise" className="hover:underline">
                  Advertise
                </Link>
              </li>
              {/* <li>
                <Link href="#" className="hover:underline">
                  Documentation
                </Link>
              </li> */}
              <li>
                <Link
                  href="https://bittensor.com/whitepaper?utm_source=dynamictoamarketcap&utm_medium=footer&utm_campaign=dynamictaomarketcap"
                  className="hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Whitepaper
                </Link>
              </li>
              <li>
                <Link
                  href="https://docs.bittensor.com?utm_source=dynamictoamarketcap&utm_medium=footer&utm_campaign=dynamictaomarketcap"
                  className="hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  API Reference
                </Link>
              </li>
              <li>
                <Link
                  href="https://github.com/opentensor/developer-docs?utm_source=dynamictoamarketcap&utm_medium=footer&utm_campaign=dynamictaomarketcap"
                  className="hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  GitHub Repository
                </Link>
              </li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="font-bold text-lg">Community</h3>
            <ul className="space-y-2 text-sm">
              {/*  <li>
                <Link href="#" className="hover:underline">
                  Forum
                </Link>
              </li> */}
              <li>
                <Link
                  href="https://discord.gg/qasY3HA9F9?utm_source=dynamictoamarketcap&utm_medium=footer&utm_campaign=dynamictaomarketcap"
                  className="hover:underline"
                >
                  Discord Server
                </Link>
              </li>
              <li>
                <Link
                  href="https://x.com/dtaoMarketCap?utm_source=dynamictoamarketcap&utm_medium=footer&utm_campaign=dynamictaomarketcap"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:underline"
                >
                  Twitter
                </Link>
              </li>
              <li>
                <Link href="/news" className="hover:underline">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/events" className="hover:underline">
                  Events
                </Link>
              </li>
            </ul>
          </div>

          {/* <div className="space-y-4">
            <h3 className="font-bold text-lg">Newsletter</h3>
            <p className="text-sm text-muted-foreground">
              Subscribe to our newsletter for updates on the latest features,
              releases, and ecosystem news.
            </p>
            <form className="flex space-x-2" onSubmit={handleSubmit}>
              <Input
                type="email"
                placeholder="Enter your email"
                className="max-w-[240px]"
              />
              <Button type="submit">Subscribe</Button>
            </form>
          </div> */}
        </div>

        <Separator />

        <div className="py-6 flex flex-col sm:flex-row justify-between items-center gap-4 text-sm text-muted-foreground">
          <div className="flex flex-wrap justify-center sm:justify-start gap-4">
            <Link href="#" className="hover:underline">
              Terms of Service
            </Link>
            <Link href="#" className="hover:underline">
              Privacy Policy
            </Link>
            <Link href="#" className="hover:underline">
              Cookie Policy
            </Link>
          </div>
          <div>
            © {new Date().getFullYear()} FusionBlock. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
}
