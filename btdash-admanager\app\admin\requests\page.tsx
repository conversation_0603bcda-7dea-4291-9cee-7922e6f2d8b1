"use client";

import { <PERSON>R<PERSON>, Calendar, Loader2 } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import useSWR from "swr";

interface Campaign {
	id: string;
	name: string;
	status: "pending" | "approved" | "rejected";
	startDate: string;
	endDate: string;
	createdAt: string;
	advertiser_name?: string;
	manager_name?: string;
}

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function AdminRequestsPage() {
	const { toast } = useToast();

	// Fetch pending campaigns
	const {
		data: pendingCampaigns,
		error: pendingError,
		isLoading: pendingLoading,
	} = useSWR("/api/admin/campaigns/pending", fetcher, {
		refreshInterval: 30000,
	});

	// Fetch approved campaigns
	const {
		data: approvedCampaigns,
		error: approvedError,
		isLoading: approvedLoading,
	} = useSWR("/api/admin/campaigns/approved", fetcher, {
		refreshInterval: 30000,
	});

	// Fetch rejected campaigns
	const {
		data: rejectedCampaigns,
		error: rejectedError,
		isLoading: rejectedLoading,
	} = useSWR("/api/admin/campaigns/rejected", fetcher, {
		refreshInterval: 30000,
	});

	const loading = pendingLoading || approvedLoading || rejectedLoading;
	const hasError = pendingError || approvedError || rejectedError;

	useEffect(() => {
		if (hasError) {
			toast({
				title: "Error",
				description: "Failed to load campaign data. Please try again.",
				variant: "destructive",
			});
		}
	}, [hasError, toast]);

	// Format date for display
	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	if (loading) {
		return (
			<div className="container mx-auto flex h-[70vh] items-center justify-center p-6">
				<div className="flex flex-col items-center">
					<Loader2 className="h-12 w-12 animate-spin text-primary" />
					<p className="mt-4 text-lg">Loading campaign data...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6">
			<h1 className="mb-6 text-3xl font-bold">Ad Requests</h1>

			<Tabs defaultValue="pending" className="w-full">
				<TabsList className="mb-4">
					<TabsTrigger value="pending">
						Pending
						<span className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium">
							{pendingCampaigns?.length || 0}
						</span>
					</TabsTrigger>
					<TabsTrigger value="approved">
						Approved
						<span className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium">
							{approvedCampaigns?.length || 0}
						</span>
					</TabsTrigger>
					<TabsTrigger value="rejected">
						Rejected
						<span className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium">
							{rejectedCampaigns?.length || 0}
						</span>
					</TabsTrigger>
				</TabsList>

				<TabsContent value="pending">
					<Card>
						<CardHeader>
							<CardTitle>Pending Requests</CardTitle>
							<CardDescription>Ad campaigns awaiting review</CardDescription>
						</CardHeader>
						<CardContent>
							{pendingCampaigns && pendingCampaigns.length > 0 ? (
								<div className="space-y-4">
									{pendingCampaigns.map((campaign) => (
										<div key={campaign.id} className="rounded-lg border p-4">
											<div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
												<div>
													<div className="flex items-center gap-2">
														<h3 className="font-medium">{campaign.name}</h3>
													</div>
													<div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
														<Calendar className="h-4 w-4" />
														<span>
															{formatDate(campaign.startDate)} -{" "}
															{formatDate(campaign.endDate)}
														</span>
													</div>
													<p className="mt-1 text-sm text-muted-foreground">
														Submitted on {formatDate(campaign.createdAt)}
													</p>
												</div>
												<Link href={`/admin/requests/${campaign.id}`}>
													<Button>
														Review Request
														<ArrowRight className="ml-2 h-4 w-4" />
													</Button>
												</Link>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
									<h3 className="text-lg font-medium">No pending requests</h3>
									<p className="text-sm text-muted-foreground">
										All ad campaigns have been reviewed.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="approved">
					<Card>
						<CardHeader>
							<CardTitle>Approved Campaigns</CardTitle>
							<CardDescription>Ad campaigns that have been approved</CardDescription>
						</CardHeader>
						<CardContent>
							{approvedCampaigns && approvedCampaigns.length > 0 ? (
								<div className="space-y-4">
									{approvedCampaigns.map((campaign) => (
										<div key={campaign.id} className="rounded-lg border p-4">
											<div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
												<div>
													<h3 className="font-medium">{campaign.name}</h3>
													<div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
														<Calendar className="h-4 w-4" />

														<span>
															{formatDate(campaign.startDate)} -{" "}
															{formatDate(campaign.endDate)}
														</span>
													</div>
													<p className="mt-1 text-sm text-muted-foreground">
														Approved on {formatDate(campaign.createdAt)}
													</p>
												</div>
												<Link href={`/admin/campaigns/${campaign.id}`}>
													<Button variant="outline">
														View Details
														<ArrowRight className="ml-2 h-4 w-4" />
													</Button>
												</Link>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
									<h3 className="text-lg font-medium">No approved campaigns</h3>
									<p className="text-sm text-muted-foreground">
										No campaigns have been approved yet.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="rejected">
					<Card>
						<CardHeader>
							<CardTitle>Rejected Campaigns</CardTitle>
							<CardDescription>Ad campaigns that have been rejected</CardDescription>
						</CardHeader>
						<CardContent>
							{rejectedCampaigns && rejectedCampaigns.length > 0 ? (
								<div className="space-y-4">
									{rejectedCampaigns.map((campaign) => (
										<div key={campaign.id} className="rounded-lg border p-4">
											<div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
												<div>
													<h3 className="font-medium">{campaign.name}</h3>
													<div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
														<Calendar className="h-4 w-4" />
														<span>
															{formatDate(campaign.startDate)} -{" "}
															{formatDate(campaign.endDate)}
														</span>
													</div>
													<p className="mt-1 text-sm text-muted-foreground">
														Rejected on {formatDate(campaign.createdAt)}
													</p>
												</div>
												<Link href={`/admin/campaigns/${campaign.id}`}>
													<Button variant="outline">
														View Details
														<ArrowRight className="ml-2 h-4 w-4" />
													</Button>
												</Link>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
									<h3 className="text-lg font-medium">No rejected campaigns</h3>
									<p className="text-sm text-muted-foreground">
										No campaigns have been rejected yet.
									</p>
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
