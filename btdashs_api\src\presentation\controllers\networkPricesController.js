const NetworkPricesService = require("../../application/services/NetworkPricesService");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getAllNetworkPrices = asyncHandler(async (req, res) => {
	const prices = await NetworkPricesService.getAllNetworkPrices();
	return sendSuccess(res, prices, "Network prices retrieved successfully");
});

const getNetworkPriceById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const price = await NetworkPricesService.getNetworkPriceById(id);
	if (!price) {
		return sendNotFound(res, "Network price not found");
	}
	return sendSuccess(res, price, "Network price retrieved successfully");
});

const createNetworkPrice = async (req, res) => {
	try {
		const {
			recorded_at,
			price_usd,
			price_24h_change,
			volume_24h_usd,
			current_supply,
			total_supply,
			delegated_supply,
			market_cap_usd,
			next_halvening_date,
			daily_return_per_1000t,
			validating_apy,
			staking_apy,
		} = req.body;
		const [newPrice] = await db("dtm_base.network_prices")
			.insert({
				recorded_at,
				price_usd,
				price_24h_change,
				volume_24h_usd,
				current_supply,
				total_supply,
				delegated_supply,
				market_cap_usd,
				next_halvening_date,
				daily_return_per_1000t,
				validating_apy,
				staking_apy,
			})
			.returning("*");
		res.status(201).json({ data: newPrice });
	} catch (error) {
		logger.error("Error creating network price:", error);
		res.status(500).json({ message: "Error creating network price" });
	}
};

const updateNetworkPrice = async (req, res) => {
	try {
		const { id } = req.params;
		const {
			recorded_at,
			price_usd,
			price_24h_change,
			volume_24h_usd,
			current_supply,
			total_supply,
			delegated_supply,
			market_cap_usd,
			next_halvening_date,
			daily_return_per_1000t,
			validating_apy,
			staking_apy,
		} = req.body;
		const [updatedPrice] = await db("dtm_base.network_prices")
			.where({ id })
			.update({
				recorded_at,
				price_usd,
				price_24h_change,
				volume_24h_usd,
				current_supply,
				total_supply,
				delegated_supply,
				market_cap_usd,
				next_halvening_date,
				daily_return_per_1000t,
				validating_apy,
				staking_apy,
			})
			.returning("*");
		if (!updatedPrice) {
			return res.status(404).json({ message: "Network price not found" });
		}
		res.status(200).json({ data: updatedPrice });
	} catch (error) {
		logger.error("Error updating network price:", error);
		res.status(500).json({ message: "Error updating network price" });
	}
};

const deleteNetworkPrice = async (req, res) => {
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.network_prices").where({ id }).del();
		if (!deleted) {
			return res.status(404).json({ message: "Network price not found" });
		}
		res.status(204).send();
	} catch (error) {
		logger.error("Error deleting network price:", error);
		res.status(500).json({ message: "Error deleting network price" });
	}
};

/* --- TAOSTATS API INTERACTIONS --- */

// Update Network's prices with data fetched from TaoStats API
const updateNetworkPricesWithTaoStats = async (req, res) => {
	try {
		const latestPriceResponse = await callTaoStatsAPI("/price/latest/v1", { asset: "TAO" });
		const latestPriceData = latestPriceResponse.data[0];

		await _updatePrices(latestPriceData);

		updateEndpointStatus("/update/prices", true, "Prices updated successfully");

		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("Error updating network prices:", error);

		updateEndpointStatus("/update/prices", false, error.message);

		res.status(500).json({ error: error.message });
	}
};

// Update Network's prices with given data
const updateNetworkPrices = async (latestPriceData) => {
	logger.info("Updating network prices with TaoStats data...");

	if (!latestPriceData) {
		console.warn("No price data found. Skipping...");
		return;
	}

	// Map data to our schema
	const priceToInsert = {
		recorded_at: latestPriceData.created_at,
		price_usd: latestPriceData.price,
		price_24h_change: latestPriceData.percent_change_24h,
		volume_24h_usd: latestPriceData.volume_24h,
		current_supply: latestPriceData.circulating_supply,
		total_supply: latestPriceData.total_supply,
		delegated_supply: null, // (TBD)
		market_cap_usd: latestPriceData.market_cap,
		next_halvening_date: null, // (TBD)
		daily_return_per_1000t: null, // (TBD)
		validating_apy: null, // (TBD)
		staking_apy: null, // (TBD)
	};

	// Insert or update the price data
	const existingPrice = await db("dtm_base.network_prices").where({ recorded_at: priceToInsert.recorded_at }).first();

	if (existingPrice) {
		await db("dtm_base.network_prices").where({ recorded_at: priceToInsert.recorded_at }).update(priceToInsert);
		logger.info(`Updated price data for ${priceToInsert.recorded_at}`);
	} else {
		await db("dtm_base.network_prices").insert(priceToInsert);
		logger.info(`Inserted price data for ${priceToInsert.recorded_at}`);
	}
};

module.exports = {
	getAllNetworkPrices,
	getNetworkPriceById,
	createNetworkPrice,
	updateNetworkPrice,
	deleteNetworkPrice,
	updateNetworkPricesWithTaoStats,
	updateNetworkPrices,
};
