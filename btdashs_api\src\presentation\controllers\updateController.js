const UpdateService = require("../../application/services/UpdateService");
const db = require("../../infrastructure/database/knex");
const { fetchAllPages, callTaoStatsAPI } = require("../../infrastructure/taostats/taoStatsService");
const { updateSubnets } = require("./subnetsController");
const { updateSubnetsMetrics } = require("./subnetMetricsController");
const { updateNetworkPrices } = require("./networkPricesController");
const { updateNetworkStats } = require("./networkStatsController");
const { updateValidators } = require("./validatorsController");
const { updateValidatorsPerformanceWithTaoStats } = require("./validatorPerformanceController");
const { updateEndpointStatus, getEndpointStatuses } = require("../../infrastructure/statusMonitor");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

const getUpdateStatus = asyncHandler(async (req, res) => {
	const statuses = getEndpointStatuses();
	return sendSuccess(res, statuses, "Update status retrieved successfully");
});

// Public method to update all Taostats related data
const updateAllTaostats = async (req, res) => {
	try {
		logger.info("Starting update of all Tao data...");

		// Fetch all commun necessary data in one go
		const subnetsData = await fetchAllPages("/subnet/latest/v1");
		const poolsData = await fetchAllPages("/dtao/pool/latest/v1");
		const identityData = await fetchAllPages("/subnet/identity/v1");
		const registrationData = await fetchAllPages("/subnet/registration/v1");
		const latestPriceResponse = await callTaoStatsAPI("/price/latest/v1", { asset: "TAO" });
		const latestPriceData = latestPriceResponse?.data[0];
		const validatorData = await fetchAllPages("/validator/latest/v1");
		const latestNetworkStatsResponse = await callTaoStatsAPI("/stats/latest/v1");
		const latestNetworkStatsData = latestNetworkStatsResponse?.data[0];

		// Update all data
		await updateSubnets(subnetsData, poolsData, identityData);
		await updateSubnetsMetrics(subnetsData, registrationData, poolsData);
		await updateNetworkPrices(latestPriceData);
		await updateValidators(validatorData);
		const totalEmissionResult = await db("dtm_base.subnet_metrics").sum("emission as total").first();
		await updateNetworkStats(latestNetworkStatsData, validatorData, latestPriceData, totalEmissionResult);
		// await updateValidatorsPerformanceWithTaoStats();

		// Return success response
		updateEndpointStatus("/update/all-tao", true, "All Tao data updated successfully");
		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("Error updating all Tao data:", {
			message: error.message,
			stack: error.stack,
			cause: error.cause,
		});

		updateEndpointStatus("/update/all-tao", false, error.message);
		res.status(500).json({
			success: false,
			message: error.message,
			stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
		});
	}
};

const updateAllTablesRelationships = async () => {
	try {
		// TODO: Implement the logic to update all tables relationships

		updateEndpointStatus("/update/update-all-links", true, "All tables relationships updated successfully");
		res.status(200).json({ success: true });
	} catch (error) {
		updateEndpointStatus("/update/update-all-links", false, error.message);
		res.status(500).json({ error: error.message });
		throw error; // Rethrow the error to be caught in the main function
	}
};

module.exports = {
	updateAllTaostats,
	updateAllTablesRelationships,
	getUpdateStatus,
};
