// app/news/[id]/page.tsx

import { fetchWithFallback } from "@/lib/data/utils";
import { Category, Company, News, Product, Subnet } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import NewsArticleClient from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

interface Params {
	params: { id: string };
}

// Dynamic SEO metadata for a specific news article
export async function generateMetadata({ params }: Params): Promise<Metadata> {
	const id = await params.id;
	const newsRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/news/${id}`);
	const news: News = newsRes.data;

	const shortDescription = news.content && news.content.length > 190 ? news.content.slice(0, 190) : news.content;

	return generateSEOMetadata({
		title: `${news.title} | DynamicTaoMarketCap`,
		description: shortDescription || "Explore the latest update from the TAO ecosystem.",
		url: `${process.env.APP_BASE_URL}/news/${id}`,
		image: news.image_url || `${process.env.APP_BASE_URL}/default-news-og.jpg`,
	});
}

export default async function NewsArticlePage({ params }: Params) {
	const id = await params.id;

	const [newsRes, subRes, catRes, CompRes, productsRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/news/${id}`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/company`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
	]);

	if (newsRes.error) console.error("News fetch error", newsRes.error);
	if (subRes.error) console.error("Subnet fetch error", subRes.error);
	if (CompRes.error) console.error("Company fetch error", CompRes.error);
	if (catRes.error) console.error("Categories fetch error", catRes.error);
	if (productsRes.error) console.error("Products fetch error", productsRes.error);

	const article = newsRes.data;

	// Filter subnets to only include those connected to the news article
	const filteredSubnets = subRes.data?.filter((sub: Subnet) => article?.netuids?.includes(sub.netuid));

	// Filter companies to only include those connected to the news article
	const filteredCompanies = CompRes.data?.filter((comp: Company) => article?.company_ids?.includes(comp.id));

	// Filter categories to only include those connected to the news article
	const filteredCategories = catRes.data?.filter((cat: Category) => article?.category_ids?.includes(cat.id));

	// Filter products to only include those connected to the news article
	const filteredProducts = productsRes.data?.filter((product: Product) => article?.product_ids?.includes(product.id));

	return (
		<NewsArticleClient
			article={article || null}
			subnets={filteredSubnets}
			companies={filteredCompanies}
			categories={filteredCategories}
			products={filteredProducts}
		/>
	);
}
