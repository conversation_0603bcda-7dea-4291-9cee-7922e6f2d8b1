// app/dashboard/ads/create/page.tsx
"use client";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface Campaign {
	id: number;
	name: string;
	status: string;
}

interface Placement {
	id: number;
	name: string;
	description: string;
	width: number;
	height: number;
	page: string;
	allowed_ad_types?: string[];
}

export default function CreateAdPage() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { toast } = useToast();

	const placementId = searchParams.get("placement");
	const campaignId = searchParams.get("campaign");

	const [loading, setLoading] = useState(true);
	const [submitting, setSubmitting] = useState(false);
	const [campaigns, setCampaigns] = useState<Campaign[]>([]);
	const [placement, setPlacement] = useState<Placement | null>(null);
	const [selectedOption, setSelectedOption] = useState<"existing" | "new" | null>(null);

	const [formData, setFormData] = useState({
		title: "",
		image_url: "",
		target_url: "",
		max_impressions: "",
		max_clicks: "",
		weight: "1",
		campaign_id: campaignId || "",
		// New campaign fields
		campaign_name: "",
		total_budget: "",
		budget_cpc: "",
		budget_cpm: "",
		start_date: "",
		end_date: "",
	});

	useEffect(() => {
		const fetchData = async () => {
			try {
				setLoading(true);

				// Fetch placement details if provided
				if (placementId) {
					const placementRes = await fetch(`/api/placements/${placementId}`);
					if (placementRes.ok) {
						const placementData = await placementRes.json();
						if (placementData.success) {
							setPlacement(placementData.data);
						}
					}
				}

				// Fetch user campaigns
				const campaignsRes = await fetch("/api/user/campaigns");
				if (campaignsRes.ok) {
					const campaignsData = await campaignsRes.json();
					if (campaignsData.success) {
						const activeCampaigns = campaignsData.data.filter((c: Campaign) => c.status === "pending");
						setCampaigns(activeCampaigns);

						// Auto-select option based on available campaigns
						if (campaignId) {
							setSelectedOption("existing");
							setFormData((prev) => ({ ...prev, campaign_id: campaignId }));
						} else if (activeCampaigns.length === 0) {
							setSelectedOption("new");
						}
					}
				}
			} catch (error) {
				console.error("Error fetching data:", error);
				toast({
					title: "Error",
					description: "Failed to load data. Please try again.",
					variant: "destructive",
				});
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [placementId, campaignId, toast]);

	const handleInputChange = (field: string, value: string) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!selectedOption) {
			toast({
				title: "Selection Required",
				description: "Please choose to add to existing campaign or create new campaign.",
				variant: "destructive",
			});
			return;
		}

		try {
			setSubmitting(true);

			if (selectedOption === "new") {
				// Create new campaign with ad
				const campaignData = {
					name: formData.campaign_name,
					total_budget: formData.total_budget ? parseFloat(formData.total_budget) : null,
					budget_cpc: formData.budget_cpc ? parseFloat(formData.budget_cpc) : null,
					budget_cpm: formData.budget_cpm ? parseFloat(formData.budget_cpm) : null,
					start_date: formData.start_date,
					end_date: formData.end_date,
					// Ad data
					ad_title: formData.title,
					image_url: formData.image_url,
					target_url: formData.target_url,
					max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,
					max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,
					weight: parseInt(formData.weight),
					slot_id: placementId ? parseInt(placementId) : null,
				};

				const response = await fetch("/api/user/campaigns", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(campaignData),
				});

				if (!response.ok) {
					let errorMessage = "Failed to create campaign";
					try {
						const errorData = await response.json();
						errorMessage = errorData.message || errorMessage;
					} catch (jsonError) {
						// If response is not JSON, use status text
						errorMessage = `${response.status}: ${response.statusText}`;
					}
					throw new Error(errorMessage);
				}

				const result = await response.json();
				if (result.success) {
					toast({
						title: "Campaign Created",
						description: "Your campaign and ad have been created successfully.",
					});
					router.push(`/dashboard/campaigns/${result.data.campaign.id}`);
				}
			} else {
				// Add ad to existing campaign
				const adData = {
					campaign_id: parseInt(formData.campaign_id),
					slot_id: placementId ? parseInt(placementId) : null,
					title: formData.title,
					image_url: formData.image_url,
					target_url: formData.target_url,
					max_impressions: formData.max_impressions ? parseInt(formData.max_impressions) : null,
					max_clicks: formData.max_clicks ? parseInt(formData.max_clicks) : null,
					weight: parseInt(formData.weight),
				};

				const response = await fetch("/api/user/ads", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(adData),
				});

				if (!response.ok) {
					let errorMessage = "Failed to create ad";
					try {
						const errorData = await response.json();
						errorMessage = errorData.message || errorMessage;
					} catch (jsonError) {
						// If response is not JSON, use status text
						errorMessage = `${response.status}: ${response.statusText}`;
					}
					throw new Error(errorMessage);
				}

				const result = await response.json();
				if (result.success) {
					toast({
						title: "Ad Created",
						description: "Your ad has been created successfully.",
					});
					router.push(`/dashboard/campaigns/${formData.campaign_id}`);
				}
			}
		} catch (error) {
			console.error("Error creating ad:", error);
			toast({
				title: "Creation Failed",
				description: error instanceof Error ? error.message : "Failed to create ad",
				variant: "destructive",
			});
		} finally {
			setSubmitting(false);
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
					<p className="text-sm text-muted-foreground">Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="max-w-4xl mx-auto px-4">
				<div className="mb-6">
					<Link href="/dashboard/placements">
						<Button variant="ghost" className="mb-4">
							<ArrowLeft className="mr-2 h-4 w-4" />
							Back to Placements
						</Button>
					</Link>
					<h1 className="text-3xl font-bold text-gray-900">Create New Ad</h1>
					<p className="text-gray-600">Add an ad to an existing campaign or create a new campaign</p>
				</div>

				<div className="grid gap-6 lg:grid-cols-3">
					<div className="lg:col-span-2">
						{/* Campaign Selection */}
						{!campaignId && (
							<Card className="mb-6">
								<CardHeader>
									<CardTitle>Campaign Selection</CardTitle>
									<CardDescription>
										Choose whether to add this ad to an existing campaign or create a new one
									</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										{campaigns.length > 0 && (
											<div className="flex items-center space-x-2">
												<input
													type="radio"
													id="existing"
													name="campaign-option"
													checked={selectedOption === "existing"}
													onChange={() => setSelectedOption("existing")}
												/>
												<Label htmlFor="existing">Add to existing campaign</Label>
											</div>
										)}
										<div className="flex items-center space-x-2">
											<input
												type="radio"
												id="new"
												name="campaign-option"
												checked={selectedOption === "new"}
												onChange={() => setSelectedOption("new")}
											/>
											<Label htmlFor="new">Create new campaign</Label>
										</div>
									</div>

									{selectedOption === "existing" && campaigns.length > 0 && (
										<div className="mt-4">
											<Label htmlFor="campaign">Select Campaign</Label>
											<Select
												value={formData.campaign_id}
												onValueChange={(value) => handleInputChange("campaign_id", value)}
											>
												<SelectTrigger>
													<SelectValue placeholder="Choose a campaign" />
												</SelectTrigger>
												<SelectContent>
													{campaigns.map((campaign) => (
														<SelectItem key={campaign.id} value={campaign.id.toString()}>
															{campaign.name} ({campaign.status})
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</div>
									)}
								</CardContent>
							</Card>
						)}

						{/* Ad Creation Form */}
						<Card>
							<CardHeader>
								<CardTitle>Ad Details</CardTitle>
								<CardDescription>
									{placement ? `Creating ad for ${placement.name}` : "Enter your ad information"}
								</CardDescription>
							</CardHeader>
							<CardContent>
								<form onSubmit={handleSubmit} className="space-y-6">
									{/* New Campaign Fields */}
									{selectedOption === "new" && (
										<>
											<div className="space-y-4">
												<h3 className="text-lg font-medium">Campaign Information</h3>
												<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
													<div>
														<Label htmlFor="campaign_name">Campaign Name *</Label>
														<Input
															id="campaign_name"
															value={formData.campaign_name}
															onChange={(e) =>
																handleInputChange("campaign_name", e.target.value)
															}
															placeholder="Enter campaign name"
															required
														/>
													</div>
													<div>
														<Label htmlFor="total_budget">Total Budget ($)</Label>
														<Input
															id="total_budget"
															type="number"
															step="0.01"
															value={formData.total_budget}
															onChange={(e) =>
																handleInputChange("total_budget", e.target.value)
															}
															placeholder="0.00"
														/>
													</div>
												</div>
												<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
													<div>
														<Label htmlFor="start_date">Start Date *</Label>
														<Input
															id="start_date"
															type="date"
															value={formData.start_date}
															onChange={(e) =>
																handleInputChange("start_date", e.target.value)
															}
															required
														/>
													</div>
													<div>
														<Label htmlFor="end_date">End Date *</Label>
														<Input
															id="end_date"
															type="date"
															value={formData.end_date}
															onChange={(e) =>
																handleInputChange("end_date", e.target.value)
															}
															required
														/>
													</div>
												</div>
											</div>
											<Separator />
										</>
									)}

									{/* Ad Fields */}
									<div className="space-y-4">
										<h3 className="text-lg font-medium">Ad Creative</h3>
										<div>
											<Label htmlFor="title">Ad Title *</Label>
											<Input
												id="title"
												value={formData.title}
												onChange={(e) => handleInputChange("title", e.target.value)}
												placeholder="Enter ad title"
												required
											/>
										</div>
										<div>
											<Label htmlFor="image_url">Image URL *</Label>
											<Input
												id="image_url"
												type="url"
												value={formData.image_url}
												onChange={(e) => handleInputChange("image_url", e.target.value)}
												placeholder="https://example.com/image.jpg"
												required
											/>
											{placement && (
												<p className="text-xs text-muted-foreground mt-1">
													Required dimensions: {placement.width}x{placement.height}px
												</p>
											)}
											{/* Image Preview */}
											{formData.image_url && (
												<div className="mt-3">
													<Label className="text-sm font-medium">Preview:</Label>
													<div className="mt-2 relative w-full max-w-md border rounded-lg overflow-hidden bg-gray-50">
														<img
															src={formData.image_url}
															alt="Ad preview"
															className="w-full h-auto object-cover"
															onError={(e) => {
																const target = e.target as HTMLImageElement;
																target.style.display = "none";
																const errorDiv =
																	target.nextElementSibling as HTMLElement;
																if (errorDiv) errorDiv.style.display = "block";
															}}
															onLoad={(e) => {
																const target = e.target as HTMLImageElement;
																target.style.display = "block";
																const errorDiv =
																	target.nextElementSibling as HTMLElement;
																if (errorDiv) errorDiv.style.display = "none";
															}}
														/>
														<div
															className="hidden p-4 text-center text-sm text-red-600"
															style={{ display: "none" }}
														>
															Failed to load image. Please check the URL.
														</div>
													</div>
													{placement && (
														<p className="text-xs text-muted-foreground mt-1">
															Target: {placement.width}x{placement.height}px
														</p>
													)}
												</div>
											)}
										</div>
										<div>
											<Label htmlFor="target_url">Target URL *</Label>
											<Input
												id="target_url"
												type="url"
												value={formData.target_url}
												onChange={(e) => handleInputChange("target_url", e.target.value)}
												placeholder="https://example.com"
												required
											/>
										</div>
										<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
											<div>
												<Label htmlFor="max_impressions">Max Impressions</Label>
												<Input
													id="max_impressions"
													type="number"
													value={formData.max_impressions}
													onChange={(e) =>
														handleInputChange("max_impressions", e.target.value)
													}
													placeholder="Unlimited"
												/>
											</div>
											<div>
												<Label htmlFor="max_clicks">Max Clicks</Label>
												<Input
													id="max_clicks"
													type="number"
													value={formData.max_clicks}
													onChange={(e) => handleInputChange("max_clicks", e.target.value)}
													placeholder="Unlimited"
												/>
											</div>
											<div>
												<Label htmlFor="weight">Weight</Label>
												<Input
													id="weight"
													type="number"
													min="1"
													max="1000"
													value={formData.weight}
													onChange={(e) => handleInputChange("weight", e.target.value)}
													required
												/>
											</div>
										</div>
									</div>

									<Button type="submit" className="w-full" disabled={submitting}>
										{submitting
											? "Creating..."
											: selectedOption === "new"
											? "Create Campaign & Ad"
											: "Create Ad"}
									</Button>
								</form>
							</CardContent>
						</Card>
					</div>

					{/* Sidebar */}
					<div className="space-y-6">
						{placement && (
							<Card>
								<CardHeader>
									<CardTitle>Selected Placement</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="space-y-2">
										<div className="flex justify-between">
											<span className="text-sm font-medium">Name:</span>
											<span className="text-sm">{placement.name}</span>
										</div>
										<div className="flex justify-between">
											<span className="text-sm font-medium">Page:</span>
											<span className="text-sm">{placement.page}</span>
										</div>
										<div className="flex justify-between">
											<span className="text-sm font-medium">Dimensions:</span>
											<span className="text-sm">
												{placement.width}x{placement.height}px
											</span>
										</div>
										{placement.allowed_ad_types && (
											<div className="flex justify-between">
												<span className="text-sm font-medium">Formats:</span>
												<span className="text-sm">{placement.allowed_ad_types.join(", ")}</span>
											</div>
										)}
									</div>
								</CardContent>
							</Card>
						)}

						<Card>
							<CardHeader>
								<CardTitle>Tips</CardTitle>
							</CardHeader>
							<CardContent className="text-sm text-muted-foreground">
								<ul className="space-y-2">
									<li>• Use high-quality images that match the placement dimensions</li>
									<li>• Write compelling ad titles that grab attention</li>
									<li>• Set reasonable impression and click limits</li>
									<li>• Higher weight gives your ad more visibility</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	);
}
