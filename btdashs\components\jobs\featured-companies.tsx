// components/Jobs/featured-companies.tsx
"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Category, Company, Job, Product, Subnet } from "@/lib/db/models";

interface FeaturedCompaniesProps {
	jobs: (Job & {
		company?: Company;
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	title?: string;
	maxCompanies?: number;
}

export function FeaturedCompanies({ jobs, title = "Featured Companies", maxCompanies = 4 }: FeaturedCompaniesProps) {
	// Get unique companies from jobs
	const companiesMap = jobs.reduce((acc, job) => {
		if (job.company) {
			acc[job.company.id] = job.company;
		}
		return acc;
	}, {} as Record<string | number, Company>);

	const companies = Object.values(companiesMap);

	// Sort companies by some criteria (here we'll just take first few)
	const featuredCompanies = companies.slice(0, maxCompanies);

	// If no companies found, use fallback data
	if (featuredCompanies.length === 0) {
		return (
			<Card>
				<CardContent className="p-4">
					<h3 className="font-medium mb-3">{title}</h3>
					<div className="space-y-3">No featured companies found.</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardContent className="p-4">
				<h3 className="font-medium mb-3">{title}</h3>
				<div className="space-y-3">
					{featuredCompanies.map((company) => (
						<div key={company.id} className="flex items-center gap-2">
							{company.logo_url ? (
								<img
									src={company.logo_url}
									alt={company.name}
									className="w-8 h-8 rounded-full object-cover"
								/>
							) : (
								<div className="w-8 h-8 bg-gray-200 dark:bg-gray-800 rounded-full flex items-center justify-center text-xs">
									{company.name.charAt(0).toUpperCase()}
								</div>
							)}
							<span className="truncate">{company.name}</span>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
