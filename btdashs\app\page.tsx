import { fetchWithFallback } from "@/lib/data/utils";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import HomeClientWrapper from "./home-client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

export const metadata: Metadata = generateSEOMetadata({
	title: "Explore Bittensor's Subnets and more | DynamicTaoMarketCap",
	description: "Discover detailed metrics and analysis for Bittensor's subnets and more.",
	url: `${process.env.APP_BASE_URL}/`,
	image: "dtm_logo.png",
});

export default async function HomePage() {
	const [categories, subnets, metrics, networkStats, companies, news, jobs, events, products] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnet-metrics`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/network-stats`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/news`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/jobs`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
	]);

	// Process categories with subnet counts (server-side)
	const processedCategories = (categories.data || [])
		.map((category: any) => ({
			...category,
			subnetCount: category.netuids?.length || 0,
		}))
		.filter((c: any) => c.subnetCount > 0);

	return (
		<div className="py-6 max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
			<HomeClientWrapper
				categories={processedCategories}
				subnets={subnets.data || []}
				metrics={metrics.data || []}
				networkStats={networkStats.data || []}
				companies={companies.data || []}
				news={news.data || []}
				jobs={jobs.data || []}
				events={events.data || []}
				products={products.data || []}
			/>
		</div>
	);
}
