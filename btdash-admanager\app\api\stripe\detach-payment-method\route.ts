import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { stripeHelpers } from '@/lib/stripe-server';

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { paymentMethodId } = await request.json();

    if (!paymentMethodId) {
      return NextResponse.json(
        { error: 'Payment method ID is required' },
        { status: 400 }
      );
    }

    // Detach payment method
    const paymentMethod = await stripeHelpers.detachPaymentMethod(paymentMethodId);

    return NextResponse.json({
      success: true,
      paymentMethod: {
        id: paymentMethod.id,
        type: paymentMethod.type,
      },
    });

  } catch (error) {
    console.error('Error detaching payment method:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to remove payment method';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
