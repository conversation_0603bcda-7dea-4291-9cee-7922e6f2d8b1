"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/analytics/bar-chart";
import { <PERSON><PERSON><PERSON> } from "@/components/analytics/line-chart";
import { MetricsSummary } from "@/components/analytics/metrics-summary";
import { countries } from "@/components/country-selector";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { generateCampaignAnalytics } from "@/lib/analytics";
import { ArrowLeft, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useState } from "react";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function CampaignAnalyticsPage({ params }: { params: Promise<{ id: string }> }) {
	const { id } = use(params) as { id: string };
	const router = useRouter();
	const [dateRange, setDateRange] = useState<"7d" | "30d" | "90d">("30d");

	// Fetch campaign data from API
	const {
		data: campaign,
		error: campaignError,
		isLoading: campaignLoading,
	} = useSWR(`/api/user/campaigns/${id}`, fetcher);

	// Fetch placement data if campaign has slot_id
	const { data: placement, error: placementError } = useSWR(
		campaign?.slot_id ? `/api/placements/${campaign.slot_id}` : null,
		fetcher
	);

	if (campaignLoading) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<Loader2 className="h-8 w-8 animate-spin" />
			</div>
		);
	}

	if (campaignError || !campaign) {
		return (
			<div className="flex flex-col items-center justify-center min-h-[400px] gap-4">
				<h3 className="text-lg font-medium">Campaign not found</h3>
				<p className="text-sm text-muted-foreground">
					{campaignError?.message || "The requested campaign could not be found."}
				</p>
				<Button variant="outline" onClick={() => router.back()}>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Go Back
				</Button>
			</div>
		);
	}

	// Get analytics for the campaign (using the existing mock analytics for now)
	const analytics = generateCampaignAnalytics(id);
	if (!analytics) {
		return (
			<div className="flex flex-col items-center justify-center min-h-[400px] gap-4">
				<h3 className="text-lg font-medium">Analytics not available</h3>
				<p className="text-sm text-muted-foreground">Analytics data is not available for this campaign yet.</p>
				<Button variant="outline" onClick={() => router.back()}>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Go Back
				</Button>
			</div>
		);
	}

	// Filter daily metrics based on date range
	const filteredDailyMetrics = analytics.daily_metrics.slice(-Number.parseInt(dateRange));

	return (
		<div className="flex flex-col gap-6">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-2">
					<Button variant="ghost" size="icon" onClick={() => router.back()}>
						<ArrowLeft className="h-4 w-4" />
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">{campaign.name} Analytics</h1>
						<p className="text-muted-foreground">
							{placement?.name || "Unknown Placement"} •{" "}
							{new Date(campaign.start_date).toLocaleDateString()} -{" "}
							{new Date(campaign.end_date).toLocaleDateString()}
						</p>
					</div>
				</div>
				<div className="flex items-center gap-2">
					<Label htmlFor="date-range">Date Range:</Label>
					<Select value={dateRange} onValueChange={(value) => setDateRange(value as any)}>
						<SelectTrigger id="date-range" className="w-[120px]">
							<SelectValue placeholder="Select range" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="7d">Last 7 days</SelectItem>
							<SelectItem value="30d">Last 30 days</SelectItem>
							<SelectItem value="90d">Last 90 days</SelectItem>
						</SelectContent>
					</Select>
				</div>
			</div>

			<MetricsSummary
				impressions={analytics.total_impressions}
				clicks={analytics.total_clicks}
				ctr={analytics.ctr}
			/>

			<Tabs defaultValue="performance" className="space-y-6">
				<TabsList>
					<TabsTrigger value="performance">Performance</TabsTrigger>
					<TabsTrigger value="geography">Geography</TabsTrigger>
					<TabsTrigger value="devices">Devices</TabsTrigger>
				</TabsList>

				<TabsContent value="performance" className="space-y-6">
					<div className="grid gap-6 md:grid-cols-2">
						<LineChart
							data={filteredDailyMetrics}
							title="Impressions Over Time"
							description="Daily ad impressions"
							metric="impressions"
						/>
						<LineChart
							data={filteredDailyMetrics}
							title="Clicks Over Time"
							description="Daily ad clicks"
							metric="clicks"
						/>
					</div>

					<LineChart
						data={filteredDailyMetrics}
						title="Click-Through Rate (CTR) Over Time"
						description="Daily click-through rate"
						metric="ctr"
						height={400}
					/>

					<Card>
						<CardHeader>
							<CardTitle>Performance Insights</CardTitle>
							<CardDescription>Key observations about your campaign performance</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="rounded-lg border p-4">
									<h3 className="font-medium mb-2">CTR Trend</h3>
									<p className="text-sm text-muted-foreground">
										Your click-through rate has {Math.random() > 0.5 ? "increased" : "decreased"} by{" "}
										{(Math.random() * 10).toFixed(1)}% compared to the previous period.
									</p>
								</div>
								<div className="rounded-lg border p-4">
									<h3 className="font-medium mb-2">Best Performing Day</h3>
									<p className="text-sm text-muted-foreground">
										Your ads perform best on{" "}
										{
											["Mondays", "Tuesdays", "Wednesdays", "Thursdays", "Fridays", "Weekends"][
												Math.floor(Math.random() * 6)
											]
										}
										, with CTR up to {(Math.random() * 2 + 2).toFixed(1)}%.
									</p>
								</div>
								<div className="rounded-lg border p-4">
									<h3 className="font-medium mb-2">Optimization Opportunity</h3>
									<p className="text-sm text-muted-foreground">
										Consider{" "}
										{Math.random() > 0.5 ? "updating your creative" : "adjusting your targeting"} to
										improve performance.
									</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="geography" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Geographic Performance</CardTitle>
							<CardDescription>Performance metrics by country</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="grid grid-cols-4 font-medium text-sm">
									<div>Country</div>
									<div className="text-right">Impressions</div>
									<div className="text-right">Clicks</div>
									<div className="text-right">CTR</div>
								</div>
								<div className="space-y-2">
									{analytics.country_metrics.map((metric) => {
										const country = countries.find((c) => c.value === metric.country_code);
										return (
											<div
												key={metric.country_code}
												className="grid grid-cols-4 items-center text-sm py-2 border-b"
											>
												<div className="font-medium">
													{country ? country.label : metric.country_code}
												</div>
												<div className="text-right">{metric.impressions.toLocaleString()}</div>
												<div className="text-right">{metric.clicks.toLocaleString()}</div>
												<div className="text-right">{(metric.ctr * 100).toFixed(2)}%</div>
											</div>
										);
									})}
								</div>
							</div>
						</CardContent>
					</Card>

					<BarChart
						data={analytics.country_metrics}
						title="Impressions by Country"
						description="Distribution of impressions across countries"
						metric="impressions"
						height={400}
						type="country"
					/>

					<BarChart
						data={analytics.country_metrics}
						title="CTR by Country"
						description="Click-through rate performance by country"
						metric="ctr"
						height={400}
						type="country"
					/>
				</TabsContent>

				<TabsContent value="devices" className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Device Performance</CardTitle>
							<CardDescription>Performance metrics by device type</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="grid grid-cols-4 font-medium text-sm">
									<div>Device</div>
									<div className="text-right">Impressions</div>
									<div className="text-right">Clicks</div>
									<div className="text-right">CTR</div>
								</div>
								<div className="space-y-2">
									{analytics.device_metrics.map((metric) => (
										<div
											key={metric.device_type}
											className="grid grid-cols-4 items-center text-sm py-2 border-b"
										>
											<div className="font-medium capitalize">{metric.device_type}</div>
											<div className="text-right">{metric.impressions.toLocaleString()}</div>
											<div className="text-right">{metric.clicks.toLocaleString()}</div>
											<div className="text-right">{(metric.ctr * 100).toFixed(2)}%</div>
										</div>
									))}
								</div>
							</div>
						</CardContent>
					</Card>

					<BarChart
						data={analytics.device_metrics}
						title="Impressions by Device"
						description="Distribution of impressions across devices"
						metric="impressions"
						height={400}
						type="device"
					/>

					<BarChart
						data={analytics.device_metrics}
						title="CTR by Device"
						description="Click-through rate performance by device"
						metric="ctr"
						height={400}
						type="device"
					/>
				</TabsContent>
			</Tabs>
		</div>
	);
}
