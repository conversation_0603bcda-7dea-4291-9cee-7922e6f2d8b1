const EventService = require("../../application/services/EventService");
const { sendSuccess, sendNotFound } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

// Get all events
const getAllEvents = asyncHandler(async (req, res) => {
	const events = await EventService.getAllEvents();
	return sendSuccess(res, events, "Events retrieved successfully");
});

// Get event by ID
const getEventById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const event = await EventService.getEventById(id);
	if (!event) {
		return sendNotFound(res, "Event not found");
	}
	return sendSuccess(res, event, "Event retrieved successfully");
});

// Create a new event
const createEvent = asyncHandler(async (req, res) => {
	const newEvent = await EventService.createEvent(req.body);
	return sendSuccess(res, newEvent, "Event created successfully", 201);
});

// Update an event
const updateEvent = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const updatedEvent = await EventService.updateEvent(id, req.body);
	return sendSuccess(res, updatedEvent, "Event updated successfully");
});

// Delete an event
const deleteEvent = asyncHandler(async (req, res) => {
	const { id } = req.params;
	await EventService.deleteEvent(id);
	return sendSuccess(res, null, "Event deleted successfully");
});

module.exports = {
	getAllEvents,
	getEventById,
	createEvent,
	updateEvent,
	deleteEvent,
};
