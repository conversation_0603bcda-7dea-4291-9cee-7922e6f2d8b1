const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const nodemailer = require("nodemailer");
const axios = require("axios");

class NotificationService {
	constructor() {
		this.emailTransporter = null;
		this.initializeEmailTransporter();
	}

	/**
	 * Initialize email transporter
	 */
	initializeEmailTransporter() {
		try {
			const emailConfig = {
				host: process.env.SMTP_HOST || "localhost",
				port: process.env.SMTP_PORT || 587,
				secure: process.env.SMTP_SECURE === "true", // true for 465, false for other ports
				auth: {
					user: process.env.SMTP_USER,
					pass: process.env.SMTP_PASS,
				},
			};

			// Only create transporter if SMTP credentials are provided
			if (emailConfig.auth.user && emailConfig.auth.pass) {
				this.emailTransporter = nodemailer.createTransporter(emailConfig);
				logger.info("Email transporter initialized successfully");
			} else {
				logger.warn("Email credentials not provided, email notifications disabled");
			}
		} catch (error) {
			logger.error("Failed to initialize email transporter", { error });
		}
	}

	/**
	 * Create a notification in the database
	 * @param {Object} notificationData - Notification data
	 * @returns {Promise<Object>} Created notification
	 */
	async createNotification(notificationData) {
		try {
			const {
				user_id,
				type,
				title,
				message,
				related_id = null,
				metadata = null,
				priority = "normal",
			} = notificationData;

			const [notification] = await db("dtm_ads.ad_notifications")
				.insert({
					user_id,
					type,
					title,
					message,
					is_read: false,
					related_id,
					metadata,
					priority,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			logger.info("Notification created", {
				notificationId: notification.id,
				userId: user_id,
				type,
			});

			return notification;
		} catch (error) {
			logger.error("Error creating notification", { error, notificationData });
			throw error;
		}
	}

	/**
	 * Send email notification
	 * @param {Object} emailData - Email data
	 * @returns {Promise<boolean>} Success status
	 */
	async sendEmail(emailData) {
		try {
			if (!this.emailTransporter) {
				logger.warn("Email transporter not available, skipping email");
				return false;
			}

			const { to, subject, text, html, template = null, templateData = {} } = emailData;

			let emailContent = { text, html };

			// Use template if provided
			if (template) {
				emailContent = await this.renderEmailTemplate(template, templateData);
			}

			const mailOptions = {
				from: process.env.SMTP_FROM || process.env.SMTP_USER,
				to,
				subject,
				text: emailContent.text,
				html: emailContent.html,
			};

			const result = await this.emailTransporter.sendMail(mailOptions);

			logger.info("Email sent successfully", {
				to,
				subject,
				messageId: result.messageId,
			});

			return true;
		} catch (error) {
			logger.error("Error sending email", { error, emailData });
			return false;
		}
	}

	/**
	 * Send webhook notification
	 * @param {string} webhookUrl - Webhook URL
	 * @param {Object} payload - Webhook payload
	 * @param {Object} options - Additional options
	 * @returns {Promise<boolean>} Success status
	 */
	async sendWebhook(webhookUrl, payload, options = {}) {
		try {
			const { timeout = 10000, retries = 3, headers = {} } = options;

			const webhookPayload = {
				timestamp: new Date().toISOString(),
				source: "btdash-api",
				...payload,
			};

			const config = {
				method: "POST",
				url: webhookUrl,
				data: webhookPayload,
				timeout,
				headers: {
					"Content-Type": "application/json",
					"User-Agent": "BTDash-API/1.0",
					...headers,
				},
			};

			let lastError;
			for (let attempt = 1; attempt <= retries; attempt++) {
				try {
					const response = await axios(config);

					if (response.status >= 200 && response.status < 300) {
						logger.info("Webhook sent successfully", {
							webhookUrl,
							status: response.status,
							attempt,
						});
						return true;
					} else {
						throw new Error(`HTTP ${response.status}: ${response.statusText}`);
					}
				} catch (error) {
					lastError = error;
					if (attempt < retries) {
						const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
						await new Promise((resolve) => setTimeout(resolve, delay));
						logger.warn("Webhook attempt failed, retrying", {
							webhookUrl,
							attempt,
							error: error.message,
						});
					}
				}
			}

			logger.error("Webhook failed after all retries", {
				webhookUrl,
				retries,
				error: lastError.message,
			});
			return false;
		} catch (error) {
			logger.error("Error sending webhook", { error, webhookUrl });
			return false;
		}
	}

	/**
	 * Send comprehensive notification (database + email + webhook)
	 * @param {Object} notificationData - Complete notification data
	 * @returns {Promise<Object>} Notification results
	 */
	async sendNotification(notificationData) {
		try {
			const {
				user_id,
				type,
				title,
				message,
				related_id = null,
				metadata = null,
				priority = "normal",
				email = null,
				webhook = null,
			} = notificationData;

			const results = {
				database: false,
				email: false,
				webhook: false,
				notification: null,
			};

			// 1. Create database notification
			try {
				const notification = await this.createNotification({
					user_id,
					type,
					title,
					message,
					related_id,
					metadata,
					priority,
				});
				results.database = true;
				results.notification = notification;
			} catch (error) {
				logger.error("Failed to create database notification", { error });
			}

			// 2. Send email if requested and user email available
			if (email && email.enabled) {
				try {
					const user = await db("dtm_base.users").where({ id: user_id }).select("email", "name").first();

					if (user && user.email) {
						const emailData = {
							to: user.email,
							subject: email.subject || title,
							template: email.template || "default",
							templateData: {
								userName: user.name,
								title,
								message,
								...email.templateData,
							},
						};

						results.email = await this.sendEmail(emailData);
					}
				} catch (error) {
					logger.error("Failed to send email notification", { error });
				}
			}

			// 3. Send webhook if configured
			if (webhook && webhook.enabled && webhook.url) {
				try {
					const webhookPayload = {
						event: type,
						user_id,
						title,
						message,
						related_id,
						metadata,
						priority,
						...webhook.payload,
					};

					results.webhook = await this.sendWebhook(webhook.url, webhookPayload, webhook.options);
				} catch (error) {
					logger.error("Failed to send webhook notification", { error });
				}
			}

			logger.info("Notification processing completed", {
				user_id,
				type,
				results,
			});

			return results;
		} catch (error) {
			logger.error("Error in sendNotification", { error, notificationData });
			throw error;
		}
	}

	/**
	 * Render email template
	 * @param {string} template - Template name
	 * @param {Object} data - Template data
	 * @returns {Promise<Object>} Rendered email content
	 */
	async renderEmailTemplate(template, data) {
		try {
			// Simple template rendering - in production, use a proper template engine
			const templates = {
				default: {
					text: `${data.title}\n\n${data.message}\n\nBest regards,\nBTDash Team`,
					html: `
						<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
							<h2 style="color: #333;">${data.title}</h2>
							<p style="color: #666; line-height: 1.6;">${data.message}</p>
							<hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
							<p style="color: #999; font-size: 12px;">
								Best regards,<br>
								BTDash Team
							</p>
						</div>
					`,
				},
				campaign_approval: {
					text: `Campaign Approved\n\nHi ${data.userName},\n\nGreat news! Your campaign "${data.campaignName}" has been approved and is now active.\n\n${data.message}\n\nBest regards,\nBTDash Team`,
					html: `
						<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
							<h2 style="color: #28a745;">✅ Campaign Approved</h2>
							<p>Hi ${data.userName},</p>
							<p style="color: #666; line-height: 1.6;">
								Great news! Your campaign <strong>"${data.campaignName}"</strong> has been approved and is now active.
							</p>
							<p style="color: #666; line-height: 1.6;">${data.message}</p>
							<hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
							<p style="color: #999; font-size: 12px;">
								Best regards,<br>
								BTDash Team
							</p>
						</div>
					`,
				},
				campaign_rejection: {
					text: `Campaign Rejected\n\nHi ${data.userName},\n\nUnfortunately, your campaign "${data.campaignName}" has been rejected.\n\nReason: ${data.reason}\n\n${data.message}\n\nBest regards,\nBTDash Team`,
					html: `
						<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
							<h2 style="color: #dc3545;">❌ Campaign Rejected</h2>
							<p>Hi ${data.userName},</p>
							<p style="color: #666; line-height: 1.6;">
								Unfortunately, your campaign <strong>"${data.campaignName}"</strong> has been rejected.
							</p>
							<p style="color: #666; line-height: 1.6;"><strong>Reason:</strong> ${data.reason}</p>
							<p style="color: #666; line-height: 1.6;">${data.message}</p>
							<hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
							<p style="color: #999; font-size: 12px;">
								Best regards,<br>
								BTDash Team
							</p>
						</div>
					`,
				},
				low_balance: {
					text: `Low Balance Warning\n\nHi ${data.userName},\n\nYour account balance is low ($${data.balance}). Please add funds to continue running campaigns.\n\nBest regards,\nBTDash Team`,
					html: `
						<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
							<h2 style="color: #ffc107;">⚠️ Low Balance Warning</h2>
							<p>Hi ${data.userName},</p>
							<p style="color: #666; line-height: 1.6;">
								Your account balance is low (<strong>$${data.balance}</strong>). Please add funds to continue running campaigns.
							</p>
							<a href="${process.env.FRONTEND_URL}/billing" style="display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0;">Add Funds</a>
							<hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
							<p style="color: #999; font-size: 12px;">
								Best regards,<br>
								BTDash Team
							</p>
						</div>
					`,
				},
			};

			return templates[template] || templates.default;
		} catch (error) {
			logger.error("Error rendering email template", { error, template });
			return {
				text: `${data.title}\n\n${data.message}`,
				html: `<p>${data.title}</p><p>${data.message}</p>`,
			};
		}
	}

	/**
	 * Get user notifications
	 * @param {number} userId - User ID
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} User notifications
	 */
	async getUserNotifications(userId, options = {}) {
		try {
			const { limit = 50, offset = 0, unreadOnly = false, type = null } = options;

			let query = db("dtm_ads.ad_notifications").where({ user_id: userId });

			if (unreadOnly) {
				query = query.where({ is_read: false });
			}

			if (type) {
				query = query.where({ type });
			}

			const notifications = await query.orderBy("created_at", "desc").limit(limit).offset(offset);

			return notifications;
		} catch (error) {
			logger.error("Error getting user notifications", { error, userId });
			throw error;
		}
	}

	/**
	 * Mark notification as read
	 * @param {number} notificationId - Notification ID
	 * @param {number} userId - User ID (for security)
	 * @returns {Promise<boolean>} Success status
	 */
	async markAsRead(notificationId, userId) {
		try {
			const result = await db("dtm_ads.ad_notifications").where({ id: notificationId, user_id: userId }).update({
				is_read: true,
				read_at: new Date(),
				updated_at: new Date(),
			});

			return result > 0;
		} catch (error) {
			logger.error("Error marking notification as read", { error, notificationId, userId });
			throw error;
		}
	}

	/**
	 * Mark all notifications as read for a user
	 * @param {number} userId - User ID
	 * @returns {Promise<number>} Number of notifications marked as read
	 */
	async markAllAsRead(userId) {
		try {
			const result = await db("dtm_ads.ad_notifications").where({ user_id: userId, is_read: false }).update({
				is_read: true,
				read_at: new Date(),
				updated_at: new Date(),
			});

			logger.info("All notifications marked as read", { userId, count: result });
			return result;
		} catch (error) {
			logger.error("Error marking all notifications as read", { error, userId });
			throw error;
		}
	}

	/**
	 * Delete old notifications
	 * @param {number} daysOld - Days old threshold
	 * @returns {Promise<number>} Number of notifications deleted
	 */
	async cleanupOldNotifications(daysOld = 30) {
		try {
			const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);

			const result = await db("dtm_ads.ad_notifications")
				.where("created_at", "<", cutoffDate)
				.where("is_read", true)
				.del();

			logger.info("Old notifications cleaned up", { daysOld, deletedCount: result });
			return result;
		} catch (error) {
			logger.error("Error cleaning up old notifications", { error, daysOld });
			throw error;
		}
	}
}

module.exports = NotificationService;
