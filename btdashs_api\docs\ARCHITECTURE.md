# BTDash API Architecture Documentation

## Overview

The BTDash API is built using a clean architecture pattern that promotes separation of concerns, maintainability, and testability. This document outlines the architectural decisions, patterns, and principles used in the project.

## Architecture Principles

### 1. Clean Architecture
The application follows clean architecture principles with clear boundaries between layers:

- **Presentation Layer**: Controllers and routes that handle HTTP requests
- **Application Layer**: Business logic and use cases (Services)
- **Infrastructure Layer**: Database access, external APIs, and technical concerns

### 2. Dependency Inversion
Dependencies flow inward, with higher-level modules not depending on lower-level modules. Both depend on abstractions.

### 3. Single Responsibility Principle
Each class and module has a single reason to change, promoting maintainability.

### 4. Open/Closed Principle
Software entities are open for extension but closed for modification.

## Project Structure

```
src/
├── application/
│   └── services/              # Business logic layer
│       ├── BaseService.js     # Common CRUD operations
│       ├── UserService.js     # User-related business logic
│       ├── CompanyService.js  # Company management logic
│       └── ...
├── infrastructure/
│   ├── database/              # Database configuration
│   │   ├── knex.js           # Database connection
│   │   ├── migrations/       # Database schema changes
│   │   └── seeds/           # Test data
│   └── monitoring/           # Application monitoring
│       └── MetricsCollector.js
├── presentation/
│   ├── controllers/          # HTTP request handlers
│   │   ├── usersController.js
│   │   └── ...
│   └── routes/              # Route definitions
│       ├── index.js
│       ├── docs.js          # API documentation
│       └── monitoring.js    # Monitoring endpoints
├── middleware/              # Express middleware
│   ├── auth.js             # Authentication middleware
│   ├── errorHandler.js     # Error handling
│   ├── metricsMiddleware.js # Metrics collection
│   └── validation.js       # Input validation
└── utils/                  # Utility functions
    ├── responseWrapper.js  # Standardized responses
    └── ...
```

## Layer Responsibilities

### Presentation Layer (Controllers & Routes)

**Responsibilities:**
- Handle HTTP requests and responses
- Input validation and sanitization
- Authentication and authorization
- Route parameter extraction
- Response formatting

**Key Files:**
- `src/presentation/controllers/` - Request handlers
- `src/presentation/routes/` - Route definitions
- `src/middleware/` - Cross-cutting concerns

**Example:**
```javascript
// Controller handles HTTP concerns only
const getUserData = async (req, res) => {
  try {
    const auth0_id = req.auth.sub;
    const user = await UserService.getUserByAuth0Id(auth0_id);
    return sendSuccess(res, user, "User data retrieved successfully");
  } catch (error) {
    logger.error("getUserData: error", { error });
    return sendInternalError(res, "Internal server error");
  }
};
```

### Application Layer (Services)

**Responsibilities:**
- Business logic implementation
- Data validation and transformation
- Orchestration of infrastructure services
- Transaction management
- Error handling and logging

**Key Files:**
- `src/application/services/` - Business logic services
- `src/application/services/BaseService.js` - Common operations

**Example:**
```javascript
// Service handles business logic
class UserService {
  async updateUserProfile(auth0_id, updateData) {
    // Business validation
    const user = await this.validateAndGetUser(auth0_id);
    
    // Data transformation
    const sanitizedData = this.sanitizeUpdateData(updateData);
    
    // Database operation
    const updatedUser = await db("users")
      .where({ auth0_user_id: auth0_id })
      .update(sanitizedData)
      .returning("*");
    
    logger.info("User profile updated", { user_id: user.id });
    return updatedUser;
  }
}
```

### Infrastructure Layer

**Responsibilities:**
- Database access and configuration
- External API integrations
- File system operations
- Monitoring and metrics
- Configuration management

**Key Files:**
- `src/infrastructure/database/` - Database configuration
- `src/infrastructure/monitoring/` - Application monitoring

## Design Patterns

### 1. Service Layer Pattern

Business logic is encapsulated in service classes that are independent of the presentation layer.

**Benefits:**
- Reusable business logic
- Easier testing
- Clear separation of concerns
- Better maintainability

### 2. Repository Pattern (via Services)

Data access is abstracted through service methods, providing a consistent interface for data operations.

**Benefits:**
- Database independence
- Easier testing with mocks
- Centralized query logic
- Consistent error handling

### 3. Middleware Pattern

Cross-cutting concerns are handled through Express middleware.

**Examples:**
- Authentication (`auth.js`)
- Error handling (`errorHandler.js`)
- Metrics collection (`metricsMiddleware.js`)
- Request logging

### 4. Factory Pattern

Used for creating database connections and service instances.

### 5. Observer Pattern

Used in monitoring and logging systems for event-driven updates.

## Data Flow

### Request Processing Flow

1. **HTTP Request** → Express Router
2. **Middleware Chain** → Authentication, Validation, Metrics
3. **Controller** → Extract parameters, call service
4. **Service Layer** → Business logic, validation
5. **Infrastructure** → Database operations
6. **Response** → Standardized format via responseWrapper

```mermaid
graph TD
    A[HTTP Request] --> B[Middleware Chain]
    B --> C[Controller]
    C --> D[Service Layer]
    D --> E[Infrastructure Layer]
    E --> F[Database]
    F --> E
    E --> D
    D --> C
    C --> G[Response Wrapper]
    G --> H[HTTP Response]
```

### Error Handling Flow

1. **Error Occurs** → Any layer
2. **Error Propagation** → Up through layers
3. **Error Middleware** → Centralized handling
4. **Logging** → Structured error logging
5. **Response** → Standardized error format

## Security Architecture

### Authentication & Authorization

- **JWT Tokens**: Auth0 integration for user authentication
- **Role-Based Access**: User roles for company operations
- **API Keys**: Internal service authentication

### Security Middleware

- **Rate Limiting**: Prevent abuse and DoS attacks
- **CORS Configuration**: Cross-origin request handling
- **Security Headers**: XSS, CSRF, and other protections
- **Input Validation**: Joi schemas for request validation

### Data Protection

- **SQL Injection Prevention**: Parameterized queries via Knex
- **XSS Prevention**: Input sanitization and output encoding
- **CSRF Protection**: Token-based CSRF protection
- **Audit Logging**: Security event logging

## Performance Architecture

### Database Optimization

- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Indexed queries and efficient joins
- **Pagination**: Limit result sets for large datasets
- **Caching Strategy**: Redis for frequently accessed data

### Application Performance

- **Metrics Collection**: Real-time performance monitoring
- **Response Time Tracking**: Request/response time analysis
- **Memory Management**: Garbage collection optimization
- **Load Balancing**: Horizontal scaling support

## Monitoring & Observability

### Metrics Collection

- **Application Metrics**: Request rates, response times, error rates
- **System Metrics**: Memory usage, CPU utilization
- **Database Metrics**: Query performance, connection pool status
- **Business Metrics**: User activity, feature usage

### Logging Strategy

- **Structured Logging**: JSON format for machine readability
- **Log Levels**: Debug, Info, Warn, Error
- **Centralized Logging**: Papertrail integration
- **Error Tracking**: Detailed error context and stack traces

### Health Checks

- **Application Health**: Service availability and status
- **Database Health**: Connection and query performance
- **External Dependencies**: Third-party service status
- **Resource Health**: Memory, CPU, and disk usage

## Testing Architecture

### Test Pyramid

1. **Unit Tests**: Service layer and utility functions
2. **Integration Tests**: API endpoints and database operations
3. **Performance Tests**: Load testing and stress testing
4. **Security Tests**: Vulnerability scanning and penetration testing

### Test Organization

```
tests/
├── unit/
│   ├── services/        # Service layer tests
│   └── utils/          # Utility function tests
├── integration/
│   ├── controllers/    # API endpoint tests
│   └── database/      # Database integration tests
├── performance/
│   └── load-test.js   # K6 performance tests
└── setup.js          # Test configuration
```

## Deployment Architecture

### Environment Strategy

- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: Optimized for performance and reliability

### Container Strategy

- **Docker**: Containerized application deployment
- **Docker Compose**: Multi-service orchestration
- **Health Checks**: Container health monitoring
- **Resource Limits**: Memory and CPU constraints

### CI/CD Pipeline

1. **Code Quality**: Linting, formatting, security scans
2. **Testing**: Unit, integration, and performance tests
3. **Build**: Docker image creation and optimization
4. **Deploy**: Automated deployment to staging/production
5. **Monitor**: Post-deployment health checks

## Future Considerations

### Scalability

- **Microservices**: Potential service decomposition
- **Event-Driven Architecture**: Asynchronous processing
- **Caching Layer**: Redis for improved performance
- **Database Sharding**: Horizontal database scaling

### Technology Evolution

- **TypeScript Migration**: Type safety and better tooling
- **GraphQL**: Flexible API query language
- **Message Queues**: Asynchronous task processing
- **Serverless**: Function-as-a-Service deployment

### Monitoring Enhancement

- **Distributed Tracing**: Request flow across services
- **APM Integration**: Application Performance Monitoring
- **Alerting**: Proactive issue detection
- **Dashboards**: Real-time operational visibility
