// app/events/[slug]/page.tsx

import { fetchWithFallback } from "@/lib/data/utils";
import { Category, Company, Event, Subnet } from "@/lib/db/models";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import EventsClientWrapper from "./client-wrapper";

export interface Speaker {
	name: string;
	position?: string;
	company?: string;
	image?: string;
	twitter?: string;
	linkedin?: string;
	companyID?: string;
	companies?: { name: string; industry?: string; logo?: string }[];
}

export const revalidate = 600; // 10 minutes in seconds

interface Params {
	params: { slug: string };
}

// Dynamic SEO metadata per event
export async function generateMetadata({ params }: Params): Promise<Metadata> {
	const paramsData = await params;
	const slug = paramsData.slug;

	const id = Number(slug.split("-").pop());
	const eventRes = await fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`);
	const event = eventRes.data?.find((e: Event) => e.id === id);

	if (!event) {
		return {
			title: "Event Not Found | DynamicTaoMarketCap",
			description: "No details available for this event.",
		};
	}

	return generateSEOMetadata({
		title: `${event.title} | DynamicTaoMarketCap`,
		description: event.description || `Join the ${event.title} event in the TAO ecosystem.`,
		url: `${process.env.APP_BASE_URL}/events/${slug}`,
		image: event.image || `${process.env.APP_BASE_URL}/default-event-og.jpg`,
	});
}

export default async function EventPage({ params }: { params: { slug: string } }) {
	const paramsData = await params;
	const slug = paramsData.slug;

	const id = Number(slug.split("-").pop());

	// Fetch event and related resources
	const [eventRes, categoriesRes, companiesRes, subnetsRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/events`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
	]);

	// Log any errors
	if (eventRes.error) console.error("Event fetch error:", eventRes.error);
	if (categoriesRes.error) console.error("Category fetch error:", categoriesRes.error);
	if (companiesRes.error) console.error("Company fetch error:", companiesRes.error);
	if (subnetsRes.error) console.error("Subnet fetch error:", subnetsRes.error);

	const event: Event | null = eventRes.data?.find((e: Event) => e.id === id);

	// Find related data
	const relatedEvents: Event[] = eventRes.data?.filter((e: Event) => event?.event_ids?.includes(e.id)) || [];
	const relatedCategories: Category[] =
		categoriesRes.data?.filter((cat: Category) => event?.category_ids?.includes(cat.id)) || [];
	const relatedCompany: Company[] =
		companiesRes.data?.filter((comp: Company) => event?.company_ids?.includes(comp.id)) || [];
	const relatedSubnets: Subnet[] =
		subnetsRes.data?.filter((s: Subnet) => event?.subnet_ids?.includes(s.netuid)) || [];

	return (
		<EventsClientWrapper
			event={event}
			categories={relatedCategories}
			company={relatedCompany}
			subnets={relatedSubnets}
			events={relatedEvents}
		/>
	);
}
