"use client";

import type React from "react";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Pencil, Trash2, ExternalLink, Github, X } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface Project {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  link: string;
  githubLink: string;
  image: string;
}

export default function ProfileProjects() {
  const [projects, setProjects] = useState<Project[]>([
    {
      id: "1",
      title: "Neural Network Optimization Framework",
      description:
        "An open-source framework for optimizing neural networks in decentralized environments. Includes tools for distributed training and model sharing.",
      technologies: ["Python", "Tensor<PERSON>low", "PyTorch", "Blockchain"],
      link: "https://example.com/nnof",
      githubLink: "https://github.com/example/nnof",
      image: "/abstract-neural-network.png",
    },
    {
      id: "2",
      title: "Decentralized AI Marketplace",
      description:
        "A marketplace for AI models that allows developers to monetize their models and users to access them in a decentralized way.",
      technologies: ["React", "Node.js", "Solidity", "IPFS"],
      link: "https://example.com/daim",
      githubLink: "https://github.com/example/daim",
      image: "/interconnected-digital-marketplace.png",
    },
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [newTechnology, setNewTechnology] = useState("");

  const handleOpenDialog = (project?: Project) => {
    if (project) {
      setCurrentProject(project);
    } else {
      setCurrentProject({
        id: "",
        title: "",
        description: "",
        technologies: [],
        link: "",
        githubLink: "",
        image: "/team-brainstorm.png",
      });
    }
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setCurrentProject(null);
    setNewTechnology("");
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (currentProject) {
      setCurrentProject({ ...currentProject, [name]: value });
    }
  };

  const handleAddTechnology = () => {
    if (newTechnology.trim() && currentProject) {
      setCurrentProject({
        ...currentProject,
        technologies: [...currentProject.technologies, newTechnology.trim()],
      });
      setNewTechnology("");
    }
  };

  const handleRemoveTechnology = (tech: string) => {
    if (currentProject) {
      setCurrentProject({
        ...currentProject,
        technologies: currentProject.technologies.filter((t) => t !== tech),
      });
    }
  };

  const handleSave = () => {
    if (currentProject) {
      if (currentProject.id) {
        // Update existing project
        setProjects(
          projects.map((proj) =>
            proj.id === currentProject.id ? currentProject : proj
          )
        );
      } else {
        // Add new project
        const newProject = {
          ...currentProject,
          id: Date.now().toString(),
        };
        setProjects([newProject, ...projects]);
      }
      handleCloseDialog();
    }
  };

  const handleDelete = (id: string) => {
    setProjects(projects.filter((proj) => proj.id !== id));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Projects & Portfolio</h2>
        <Button onClick={() => handleOpenDialog()}>
          <Plus className="mr-2 h-4 w-4" />
          Add Project
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {projects.map((project) => (
          <Card key={project.id} className="overflow-hidden">
            <div className="h-48 bg-gray-100">
              <img
                src={project.image || "/placeholder.svg"}
                alt={project.title}
                className="w-full h-full object-cover"
              />
            </div>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium">{project.title}</h3>
                <div className="flex space-x-2">
                  {project.link && (
                    <Button variant="ghost" size="icon" asChild>
                      <a
                        href={project.link}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span className="sr-only">Visit project</span>
                      </a>
                    </Button>
                  )}
                  {project.githubLink && (
                    <Button variant="ghost" size="icon" asChild>
                      <a
                        href={project.githubLink}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Github className="h-4 w-4" />
                        <span className="sr-only">GitHub repository</span>
                      </a>
                    </Button>
                  )}
                </div>
              </div>

              <p className="mt-2 text-gray-600">{project.description}</p>

              <div className="mt-4 flex flex-wrap gap-2">
                {project.technologies.map((tech) => (
                  <Badge key={tech} variant="secondary">
                    {tech}
                  </Badge>
                ))}
              </div>

              <div className="mt-4 flex justify-end space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleOpenDialog(project)}
                >
                  <Pencil className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-red-500 hover:text-red-600 hover:bg-red-50"
                  onClick={() => handleDelete(project.id)}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {currentProject?.id ? "Edit Project" : "Add Project"}
            </DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                name="title"
                value={currentProject?.title || ""}
                onChange={handleChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                rows={4}
                value={currentProject?.description || ""}
                onChange={handleChange}
              />
            </div>

            <div className="space-y-2">
              <Label>Technologies Used</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {currentProject?.technologies.map((tech) => (
                  <Badge
                    key={tech}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tech}
                    <button
                      onClick={() => handleRemoveTechnology(tech)}
                      className="ml-1 rounded-full hover:bg-gray-200 p-0.5"
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remove {tech}</span>
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Add technology..."
                  value={newTechnology}
                  onChange={(e) => setNewTechnology(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleAddTechnology}>Add</Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="link">Project URL</Label>
                <Input
                  id="link"
                  name="link"
                  value={currentProject?.link || ""}
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="githubLink">GitHub URL</Label>
                <Input
                  id="githubLink"
                  name="githubLink"
                  value={currentProject?.githubLink || ""}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="image">Project Image URL</Label>
              <Input
                id="image"
                name="image"
                value={currentProject?.image || ""}
                onChange={handleChange}
              />
              {currentProject?.image && (
                <div className="mt-2 h-40 bg-gray-100 rounded-md overflow-hidden">
                  <img
                    src={currentProject.image || "/placeholder.svg"}
                    alt="Project preview"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCloseDialog}>
              Cancel
            </Button>
            <Button onClick={handleSave}>Save</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
