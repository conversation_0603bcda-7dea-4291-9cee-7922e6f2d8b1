"use client";

import { CategoryTag } from "@/components/category-tag";
import { JobCard } from "@/components/jobs/job-card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import type {
  Category,
  Company,
  Event,
  Job,
  News,
  Product,
  Subnet,
} from "@/lib/db/models";
import { slugify } from "@/lib/utils";
import {
  AlertCircle,
  Calendar,
  Download,
  ExternalLink,
  Home,
  Star,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ReactMarkdown from "react-markdown";

interface Props {
  product: Product | null;
  subnets: Subnet[];
  categories: Category[];
  companies: Company[];
  news: News[];
  events: Event[];
  relatedProducts?: Product[];
  jobs?: Job[];
}

export default function ProductClient({
  product,
  subnets,
  companies,
  categories,
  news,
  events,
  relatedProducts,
  jobs = [],
}: Props) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (error || !product) {
    return (
      <div className="py-8 px-6 sm:px-8 lg:px-12">
        <div className="p-8 text-center border rounded-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">Error Loading Product</h2>
          <p className="text-red-500 mb-4">
            {error || `The product could not be found.`}
          </p>
          <Link href="/products">
            <Button variant="outline" className="gap-2">
              <Home className="h-4 w-4" />
              Back to Products
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // Format launch date if available
  const formattedLaunchDate = product.launch_date
    ? new Date(product.launch_date).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : null;

  return (
    <div className="py-8 px-6 sm:px-8 lg:px-12">
      <div className="max-w-[1600px] mx-auto">
        {/* Hero Section */}
        <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-8 text-white mb-8">
          <div className="flex flex-col md:flex-row gap-6 items-center">
            <div className="relative w-24 h-24 md:w-32 md:h-32 rounded-lg overflow-hidden bg-white/20 p-3 flex items-center justify-center">
              <Image
                src={product.logo_url || "/placeholder.svg"}
                alt={product.name}
                width={128}
                height={128}
                className="object-contain"
              />
            </div>
            <div className="flex-1 text-center md:text-left">
              <div className="flex flex-col md:flex-row md:items-center gap-2 mb-2">
                <h1 className="text-3xl md:text-4xl font-bold">
                  {product.name}
                </h1>

                {/* Status Badges inline with name */}
                <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                  {product.featured && (
                    <span className="px-3 py-1 rounded-full text-xs font-bold bg-white text-purple-800 tracking-wide">
                      FEATURED
                    </span>
                  )}
                  {product.new && (
                    <span className="px-3 py-1 rounded-full text-xs font-bold bg-blue-100 text-blue-800 tracking-wide">
                      NEW
                    </span>
                  )}
                  {product.verified && (
                    <span className="px-3 py-1 rounded-full text-xs font-bold bg-emerald-100 text-emerald-800 tracking-wide">
                      VERIFIED
                    </span>
                  )}
                </div>
              </div>

              {formattedLaunchDate && (
                <div className="flex items-center gap-1 justify-center md:justify-start mb-2 text-sm text-white/80">
                  <Calendar className="h-4 w-4" />
                  <span>Launched {formattedLaunchDate}</span>
                </div>
              )}

              {/* Category Tags - now on their own line below */}
              <div className="flex flex-wrap gap-2 justify-center md:justify-start mb-4">
                {product.category_ids?.map((catId) => {
                  const category = categories.find((c) => c.id === catId);
                  return (
                    category && (
                      <span key={category.id}>
                        <CategoryTag category={category.name} />
                      </span>
                    )
                  );
                })}
              </div>

              <p className="text-lg text-white/90">
                <ReactMarkdown>{product.description}</ReactMarkdown>
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Product Details */}
            <Card>
              <CardHeader>
                <CardTitle>About {product.name}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  <ReactMarkdown>
                    {product.description_long ?? product.description}
                  </ReactMarkdown>
                </p>

                {product.key_features &&
                  Object.keys(product.key_features).length > 0 && (
                    <>
                      <h3 className="text-lg font-semibold mt-4">
                        Key Features
                      </h3>
                      <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {Object.entries(product.key_features).map(
                          ([key, value]) => (
                            <li key={key} className="flex items-start gap-3">
                              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                                <Star className="h-5 w-5 text-primary" />
                              </div>
                              <div>
                                <h4 className="font-medium">{key}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {typeof value === "string"
                                    ? value
                                    : JSON.stringify(value)}
                                </p>
                              </div>
                            </li>
                          )
                        )}
                      </ul>
                    </>
                  )}
              </CardContent>
            </Card>

            {/* Media Assets */}
            {product.assets && product.assets.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Media</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {product.assets.map((asset, index) => (
                      <div
                        key={index}
                        className="relative aspect-video rounded-lg overflow-hidden border"
                      >
                        <Image
                          src={asset || "/placeholder.svg"}
                          alt={`${product.name} media ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tabs for Features, News, Events, Jobs */}
            <Tabs defaultValue="features" className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="news">News</TabsTrigger>
                <TabsTrigger value="events">Events</TabsTrigger>
                <TabsTrigger value="jobs">Jobs ({jobs.length})</TabsTrigger>
              </TabsList>

              <TabsContent value="features" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Technical Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {product.github_repo_url && (
                      <div className="mb-4">
                        <h4 className="font-medium mb-2">GitHub Repository</h4>
                        <Button asChild variant="outline">
                          <Link
                            href={product.github_repo_url}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View on GitHub
                          </Link>
                        </Button>
                      </div>
                    )}

                    {product.team_members_git &&
                      Object.keys(product.team_members_git).length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">Development Team</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {Object.entries(product.team_members_git).map(
                              ([username, details]) => (
                                <div
                                  key={username}
                                  className="flex items-center gap-3 p-3 border rounded-lg"
                                >
                                  <div className="relative w-10 h-10 rounded-full overflow-hidden">
                                    <Image
                                      src={
                                        details?.avatar_url ||
                                        "/placeholder.svg"
                                      }
                                      alt={username}
                                      width={40}
                                      height={40}
                                      className="object-cover"
                                    />
                                  </div>
                                  <div>
                                    <h5 className="font-medium">
                                      {details?.name || username}
                                    </h5>
                                    <p className="text-sm text-muted-foreground">
                                      {details?.role || "Contributor"}
                                    </p>
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}

                    {!product.github_repo_url &&
                      (!product.team_members_git ||
                        Object.keys(product.team_members_git).length === 0) && (
                        <p className="text-muted-foreground text-center py-4">
                          No technical data available
                        </p>
                      )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="news" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Related News</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {news.length > 0 ? (
                      <div className="space-y-4">
                        {news.map((item) => (
                          <Link key={item.id} href={`/news/${item.id}`}>
                            <div className="border-b pb-4 last:border-0 hover:bg-muted/50 p-2 rounded-lg transition-colors">
                              <h4 className="font-medium">{item.title}</h4>
                              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                {item.content || item.article_part_1}
                              </p>
                              <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                                <span>
                                  {new Date(
                                    item.publication_date
                                  ).toLocaleDateString()}
                                </span>
                                {item.source && (
                                  <>
                                    <span>•</span>
                                    <span>{item.source}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </Link>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground text-center py-4">
                        No news articles found for this product
                      </p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="events" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Upcoming Events</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {events.length > 0 ? (
                      <div className="space-y-4">
                        {events.map((event) => (
                          <Link key={event.id} href={`/events/${event.id}`}>
                            <div className="border-b pb-4 last:border-0 hover:bg-muted/50 p-2 rounded-lg transition-colors">
                              <h4 className="font-medium">{event.name}</h4>
                              <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                                <Calendar className="h-4 w-4" />
                                <span>
                                  {new Date(
                                    event.start_date
                                  ).toLocaleDateString()}
                                  {event.end_date &&
                                    ` - ${new Date(
                                      event.end_date
                                    ).toLocaleDateString()}`}
                                </span>
                              </div>
                              {event.location && (
                                <p className="text-sm text-muted-foreground mt-1">
                                  {event.is_virtual
                                    ? "Online Event"
                                    : event.location}
                                </p>
                              )}
                            </div>
                          </Link>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground text-center py-4">
                        No upcoming events found for this product
                      </p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="jobs" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Available Jobs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {jobs.length > 0 ? (
                      <div className="space-y-4">
                        {jobs.map((job) => (
                          <JobCard key={job.id} job={job} />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground mb-4">
                          No job openings currently available for this product
                        </p>
                        <Button variant="outline" asChild>
                          <Link href="/jobs">Browse all jobs</Link>
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Related Products */}
            {relatedProducts && relatedProducts.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Related Products</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {relatedProducts.map((related) => (
                      <Link key={related.id} href={`/products/${related.id}`}>
                        <div className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                          <div className="relative w-12 h-12 rounded-md overflow-hidden">
                            <Image
                              src={related.logo_url || "/placeholder.svg"}
                              alt={related.name}
                              width={48}
                              height={48}
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h4 className="font-medium">{related.name}</h4>
                            <p className="text-sm text-muted-foreground line-clamp-1">
                              {related.description}
                            </p>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Developer Info */}
            {companies.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Developers</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {companies.map((company) => {
                    // Validate image URL
                    const isValidImageUrl =
                      company.logo_url &&
                      (company.logo_url.startsWith("/") ||
                        company.logo_url.startsWith("http") ||
                        company.logo_url.startsWith("data:image"));

                    return (
                      <div key={company.id} className="group">
                        <div className="flex items-center gap-3 p-2 rounded-lg transition-colors">
                          <Link
                            href={`/companies/${slugify(company.name)}`}
                            className="flex items-center gap-3 flex-1"
                          >
                            <div className="relative w-12 h-12 rounded-md overflow-hidden bg-muted flex items-center justify-center">
                              <Image
                                src={
                                  (isValidImageUrl ? company.logo_url : null) ??
                                  "/placeholder.svg"
                                }
                                alt={company.name}
                                width={48}
                                height={48}
                                className="object-cover"
                              />
                            </div>
                            <div>
                              <div className="font-medium flex items-center gap-1">
                                {company.name}
                                {company.is_verified && (
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    className="w-4 h-4 text-blue-500"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                )}
                              </div>
                            </div>
                          </Link>
                          {company.website_url && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="gap-1 h-auto"
                              asChild
                            >
                              <a
                                href={company.website_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <span className="text-sm text-muted-foreground">
                                  Website
                                </span>
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            )}

            {/* Subnet Info */}
            {subnets.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Powered By Subnets</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {subnets.map((subnet) => (
                    <Link
                      href={`/subnets/${subnet.netuid}`}
                      key={subnet.netuid}
                    >
                      <div className="flex items-center gap-3 hover:bg-muted p-2 rounded-lg transition-colors">
                        <div className="w-12 h-12 rounded-md bg-primary/10 flex items-center justify-center text-primary text-xl font-bold">
                          {subnet.subnet_symbol || subnet.name.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium">{subnet.name}</div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {subnet.category_ids?.map((catId) => {
                              const category = categories.find(
                                (c) => c.id === catId
                              );
                              return (
                                category && (
                                  <CategoryTag
                                    key={category.id}
                                    category={category.name}
                                  />
                                )
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="space-y-2">
              {product.download_url && (
                <Button className="w-full gap-2">
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              )}

              {product.website && (
                <Button
                  variant={product.download_url ? "outline" : "default"}
                  className="w-full gap-2"
                  asChild
                >
                  <Link
                    href={product.website}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="h-4 w-4" />
                    Visit Website
                  </Link>
                </Button>
              )}

              {product.github_repo_url && (
                <Button variant="outline" className="w-full gap-2" asChild>
                  <Link
                    href={product.github_repo_url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-github"
                    >
                      <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                      <path d="M9 18c-4.51 2-5-2-7-2" />
                    </svg>
                    View on GitHub
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
