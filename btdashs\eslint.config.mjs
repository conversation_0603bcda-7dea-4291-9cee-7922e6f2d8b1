// eslint.config.mjs

import { FlatCompat } from "@eslint/eslintrc";
import tsPlugin from "eslint-plugin-typescript";
import { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
	baseDirectory: __dirname,
});

const eslintConfig = [
	...compat.extends("next/core-web-vitals", "next/typescript"),
	{
		files: ["*.ts", "*.tsx"],
		languageOptions: {
			parser: "@typescript-eslint/parser",
			parserOptions: {
				project: "./tsconfig.json",
				tsconfigRootDir: __dirname,
				ecmaVersion: 12,
				sourceType: "module",
			},
		},
		plugins: {
			"@typescript-eslint": tsPlugin,
		},
		rules: {
			"@typescript-eslint/no-unused-vars": "warn",
			"@typescript-eslint/no-explicit-any": "off",
		},
	},
	{
		files: ["*.js", "*.jsx"],
		rules: {
			"no-unused-vars": "warn",
		},
	},
];

export default eslintConfig;
