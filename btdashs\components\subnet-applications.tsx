"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import type { Category, Product } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import { ArrowUpRight, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { CategoryTag } from "./category-tag";

const gradients = [
	"bg-gradient-to-br from-purple-500 to-indigo-600",
	"bg-gradient-to-br from-blue-500 to-cyan-600",
	"bg-gradient-to-br from-emerald-500 to-teal-600",
	"bg-gradient-to-br from-rose-500 to-pink-600",
	"bg-gradient-to-br from-amber-500 to-orange-600",
	"bg-gradient-to-br from-violet-500 to-purple-600",
	"bg-gradient-to-br from-green-500 to-emerald-600",
	"bg-gradient-to-br from-red-500 to-rose-600",
];

interface SubnetApplicationsProps {
	products: Product[];
	categories?: Category[];
}

export function SubnetApplications({ products, categories }: SubnetApplicationsProps) {
	const [currentIndex, setCurrentIndex] = useState(0);

	if (!products || products.length === 0) return null;

	const totalSlides = Math.ceil(products.length / 3);

	const nextSlide = () => {
		setCurrentIndex((prevIndex) => (prevIndex === totalSlides - 1 ? 0 : prevIndex + 1));
	};

	const prevSlide = () => {
		setCurrentIndex((prevIndex) => (prevIndex === 0 ? totalSlides - 1 : prevIndex - 1));
	};

	const visibleApplications = products.slice(currentIndex * 3, (currentIndex + 1) * 3);

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between p-3">
				<div>
					<CardTitle className="m-2 flex items-center gap-2 mt-2 mb-2 ml-4">
						<span>Featured Applications</span>
						<CardDescription className="text-md text-muted-foreground pt-0 m-0 inline">
							Discover top applications built on this subnet
						</CardDescription>
					</CardTitle>
				</div>
				{totalSlides > 1 && (
					<div className="flex space-x-2">
						<Button variant="outline" size="icon" onClick={prevSlide}>
							<ChevronLeft className="h-4 w-4" />
						</Button>
						<Button variant="outline" size="icon" onClick={nextSlide}>
							<ChevronRight className="h-4 w-4" />
						</Button>
					</div>
				)}
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					{visibleApplications.map((app, index) => (
						<Card
							key={app.id}
							className={cn(
								"flex flex-col justify-between overflow-hidden border-0 hover:shadow-md transition-shadow text-white",
								gradients[index % gradients.length]
							)}
						>
							<CardContent className="p-0">
								<div className="p-6">
									<div className="flex items-center gap-4 mb-4">
										<div className="relative w-12 h-12 rounded-md overflow-hidden bg-white/20 p-2 flex items-center justify-center">
											<Image
												src={app.logo_url || "/placeholder.svg"}
												alt={app.name}
												width={40}
												height={40}
												className="object-contain"
											/>
										</div>
										<div>
											<h3 className="font-semibold text-lg text-white">{app.name}</h3>
											{categories
												?.filter((category) => app.category_ids?.includes(category.id))
												.map((category) => (
													<span key={category.id}>
														<CategoryTag category={category.name} />
													</span>
												))}
											{app.category_ids?.map((catId) => {
												const category = categories?.find((c) => c.id === catId);
												return category ? (
													<span key={category.id}>
														<CategoryTag category={category.name} />
													</span>
												) : null;
											})}
										</div>
									</div>
									<div className="flex gap-2 mb-2 justify-end">
										{app.featured && (
											<Badge variant="default" className="bg-amber-500 border-0">
												Featured
											</Badge>
										)}
										{app.new && (
											<Badge variant="default" className="bg-green-500 border-0">
												New
											</Badge>
										)}
									</div>
									<p className="text-white/90 mb-4 line-clamp-2">{app.description}</p>
								</div>
							</CardContent>
							<CardFooter className="flex justify-between bg-black/10 p-4 pt-3 pb-3 border-t border-white/10">
								<Link href={`/products/${app.id}`}>
									<Button size="sm" variant="ghost" className="gap-1 text-white hover:bg-white/10">
										<span>View</span>
										<ArrowUpRight className="h-3 w-3" />
									</Button>
								</Link>
							</CardFooter>
						</Card>
					))}
				</div>
			</CardContent>
			{totalSlides > 1 && (
				<CardFooter className="flex justify-center pt-0">
					<div className="flex gap-1">
						{Array.from({ length: totalSlides }).map((_, index) => (
							<Button
								key={index}
								variant="ghost"
								size="icon"
								className={cn(
									"w-8 h-8",
									index === currentIndex && "bg-primary text-primary-foreground"
								)}
								onClick={() => setCurrentIndex(index)}
							>
								{index + 1}
							</Button>
						))}
					</div>
				</CardFooter>
			)}
		</Card>
	);
}
