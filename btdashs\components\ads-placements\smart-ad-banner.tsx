"use client";

import {
	AdVariant,
	generateSessionId,
	getCountryCode,
	getDeviceType,
	getLanguage,
	GOOGLE_AD_SLOTS,
	trackAdClick,
	trackAdImpression,
} from "@/lib/api/ad-serving";
import { ServedAd } from "@/lib/db/models";
import { useEffect, useRef, useState } from "react";

interface SmartAdBannerProps {
	variant: AdVariant;
	className?: string;
	slotId?: string; // Optional override for Google AdSense slot
	enablePaidAds?: boolean; // Allow disabling paid ads for testing
}

export function SmartAdBanner({ variant, className, slotId, enablePaidAds = true }: SmartAdBannerProps) {
	const [paidAd, setPaidAd] = useState<ServedAd | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);
	const [sessionId] = useState(() => generateSessionId());
	const [hasTrackedImpression, setHasTrackedImpression] = useState(false);

	const adContainerRef = useRef<HTMLDivElement>(null);
	const googleAdInitialized = useRef(false);
	const maxRetries = 3;

	// Standard IAB ad sizes
	const dimensions = {
		horizontal: { width: 728, height: 90 }, // Leaderboard
		vertical: { width: 300, height: 600 }, // Skyscraper
		square: { width: 300, height: 250 }, // Medium Rectangle
		billboard: { width: 970, height: 250 }, // Billboard
	};

	// Fetch paid ad on component mount with retry logic
	useEffect(() => {
		if (!enablePaidAds) {
			setIsLoading(false);
			return;
		}

		const fetchPaidAd = async (attempt = 0) => {
			try {
				setError(null);

				const userContext = {
					country_code: getCountryCode(),
					device_type: getDeviceType(navigator.userAgent),
					language: getLanguage(),
					user_agent: navigator.userAgent,
				};

				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

				const response = await fetch("/api/ads/serve", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						variant,
						...userContext,
					}),
					signal: controller.signal,
				});

				clearTimeout(timeoutId);

				if (response.ok) {
					const result = await response.json();
					if (result.success && result.data) {
						setPaidAd(result.data);
						setRetryCount(0);
						return;
					}
				}

				// If we get here, no paid ads were available (404) or other non-critical error
				if (response.status === 404) {
					// No ads available - this is expected, fall back to Google
					setRetryCount(0);
					return;
				}

				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			} catch (error) {
				console.error(`Error fetching paid ad (attempt ${attempt + 1}):`, error);

				if (attempt < maxRetries - 1) {
					// Exponential backoff: 1s, 2s, 4s
					const delay = Math.pow(2, attempt) * 1000;
					setTimeout(() => {
						setRetryCount(attempt + 1);
						fetchPaidAd(attempt + 1);
					}, delay);
				} else {
					setError("Failed to load paid ads after multiple attempts");
					setRetryCount(0);
				}
			} finally {
				if (attempt === 0 || attempt >= maxRetries - 1) {
					setIsLoading(false);
				}
			}
		};

		fetchPaidAd();
	}, [variant, enablePaidAds, maxRetries]);

	// Track impression when ad becomes visible with error handling
	useEffect(() => {
		if (!paidAd || hasTrackedImpression) return;

		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
						trackAdImpression({
							ad_id: paidAd.id,
							session_id: sessionId,
							country_code: getCountryCode(),
							device_type: getDeviceType(navigator.userAgent),
						}).catch((error) => {
							console.error("Failed to track impression:", error);
							// Don't block the user experience for tracking failures
						});
						setHasTrackedImpression(true);
						observer.disconnect();
					}
				});
			},
			{ threshold: 0.5 }
		);

		if (adContainerRef.current) {
			observer.observe(adContainerRef.current);
		}

		return () => observer.disconnect();
	}, [paidAd, sessionId, hasTrackedImpression]);

	// Initialize Google AdSense when no paid ad is available
	useEffect(() => {
		if (isLoading || paidAd || googleAdInitialized.current) return;

		const container = adContainerRef.current;
		const adEl = container?.querySelector("ins.adsbygoogle");

		if (!adEl) return;

		// Clear any previous ad content
		adEl.innerHTML = "";

		const { width, height } = dimensions[variant];

		// Apply sizing rules
		if (variant === "horizontal" || variant === "billboard") {
			const parentWidth = container.parentElement?.clientWidth || width;
			const calculatedWidth = Math.min(parentWidth, width);
			const calculatedHeight = (height / width) * calculatedWidth;

			container.style.width = `${calculatedWidth}px`;
			container.style.height = `${calculatedHeight}px`;
		} else {
			container.style.width = `${width}px`;
			container.style.height = `${height}px`;
		}

		container.style.overflow = "hidden";
		container.style.position = "relative";

		// Attempt to load Google ad with retry
		let retryCount = 0;
		const loadGoogleAd = () => {
			try {
				((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
				googleAdInitialized.current = true;
			} catch (e) {
				retryCount++;
				if (retryCount < 3) {
					setTimeout(loadGoogleAd, 1000);
				} else {
					console.error("Google AdSense load failed:", e);
				}
			}
		};

		loadGoogleAd();

		return () => {
			if (adEl) adEl.innerHTML = "";
		};
	}, [variant, isLoading, paidAd]);

	// Handle paid ad click with error handling
	const handlePaidAdClick = async () => {
		if (!paidAd) return;

		try {
			// Track the click (don't wait for it to complete)
			trackAdClick({
				ad_id: paidAd.id,
				session_id: sessionId,
				country_code: getCountryCode(),
				device_type: getDeviceType(navigator.userAgent),
			}).catch((error) => {
				console.error("Failed to track click:", error);
				// Don't block the redirect for tracking failures
			});

			// Redirect to target URL immediately
			window.open(paidAd.target_url, "_blank", "noopener,noreferrer");
		} catch (error) {
			console.error("Error handling ad click:", error);
			// Still try to redirect even if tracking fails
			window.open(paidAd.target_url, "_blank", "noopener,noreferrer");
		}
	};

	// Get the appropriate Google AdSense slot
	const googleAdSlot = slotId || GOOGLE_AD_SLOTS[variant];

	// Show loading state
	if (isLoading && enablePaidAds) {
		const { width, height } = dimensions[variant];
		return (
			<div className={`inline-block ${className}`}>
				<div
					style={{
						width: `${width}px`,
						height: `${height}px`,
						margin: "0 auto",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						backgroundColor: "#f3f4f6",
						border: "1px solid #e5e7eb",
						borderRadius: "4px",
					}}
				>
					<div className="text-sm text-gray-500">
						{retryCount > 0 ? `Loading ads... (${retryCount}/${maxRetries})` : "Loading ads..."}
					</div>
				</div>
			</div>
		);
	}

	// Show error state (only if we have an error and no fallback)
	if (error && !enablePaidAds) {
		const { width, height } = dimensions[variant];
		return (
			<div className={`inline-block ${className}`}>
				<div
					style={{
						width: `${width}px`,
						height: `${height}px`,
						margin: "0 auto",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						backgroundColor: "#fef2f2",
						border: "1px solid #fecaca",
						borderRadius: "4px",
					}}
				>
					<div className="text-sm text-red-600 text-center px-4">Ad loading failed</div>
				</div>
			</div>
		);
	}

	// Render paid ad
	if (paidAd) {
		const { width, height } = dimensions[variant];

		return (
			<div className={`inline-block ${className}`} ref={adContainerRef}>
				<div
					style={{
						width: `${width}px`,
						height: `${height}px`,
						margin: "0 auto",
						position: "relative",
						cursor: "pointer",
						overflow: "hidden",
						border: "1px solid #e5e7eb",
						borderRadius: "4px",
					}}
					onClick={handlePaidAdClick}
				>
					<img
						src={paidAd.image_url}
						alt={paidAd.title}
						style={{
							width: "100%",
							height: "100%",
							objectFit: "cover",
						}}
					/>
					<div
						style={{
							position: "absolute",
							top: "4px",
							right: "4px",
							background: "rgba(0,0,0,0.7)",
							color: "white",
							fontSize: "10px",
							padding: "2px 4px",
							borderRadius: "2px",
						}}
					>
						Sponsored
					</div>
				</div>
			</div>
		);
	}

	// Render Google AdSense fallback
	return (
		<div className={`inline-block ${className}`}>
			<div
				ref={adContainerRef}
				style={{
					minWidth: `${dimensions[variant].width}px`,
					minHeight: `${dimensions[variant].height}px`,
					margin: "0 auto",
				}}
			>
				<ins
					key={`${variant}-${googleAdSlot}`}
					className="adsbygoogle"
					style={{ display: "block" }}
					data-ad-client="ca-pub-5681407322305640"
					data-ad-slot={googleAdSlot}
					data-ad-format={
						variant === "horizontal" || variant === "billboard"
							? "horizontal"
							: variant === "square"
							? "rectangle"
							: "auto"
					}
					data-full-width-responsive="true"
				/>
			</div>
		</div>
	);
}
