"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import type { Company } from "@/lib/db/models";
import { cn, slugify } from "@/lib/utils";
import { ArrowUpRight, CheckCircle, Filter, MapPin, Plus, Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface ClientWrapperProps {
	companies: Company[];
	categories: { id: string; name: string }[];
}

const gradients = [
	"bg-gradient-to-br from-purple-500 to-indigo-600",
	"bg-gradient-to-br from-blue-500 to-cyan-600",
	"bg-gradient-to-br from-emerald-500 to-teal-600",
	"bg-gradient-to-br from-rose-500 to-pink-600",
	"bg-gradient-to-br from-amber-500 to-orange-600",
	"bg-gradient-to-br from-violet-500 to-purple-600",
	"bg-gradient-to-br from-green-500 to-emerald-600",
	"bg-gradient-to-br from-red-500 to-rose-600",
];

export default function CompaniesClientWrapper({ companies, categories }: ClientWrapperProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [locationFilter, setLocationFilter] = useState("all");
	const [categoryFilter, setCategoryFilter] = useState("all");

	const filteredCompanies = companies.filter((company) => {
		const matchesSearch =
			searchTerm === "" ||
			company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			company.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
			company.category_ids?.some((cat) =>
				categories
					.find((c) => c.id === String(cat))
					?.name.toLowerCase()
					.includes(searchTerm.toLowerCase())
			);

		const matchesLocation =
			locationFilter === "all" || company.location?.toLowerCase().includes(locationFilter.toLowerCase());

		const matchesCategory = categoryFilter === "all" || company.category_ids?.includes(categoryFilter);

		return matchesSearch && matchesLocation && matchesCategory;
	});

	const locations = Array.from(new Set(companies.map((c) => c.location).filter(Boolean))) as string[];

	// Get the display name for the selected category
	const selectedCategoryName =
		categoryFilter === "all"
			? "All Categories"
			: categories.find((c) => c.id === categoryFilter)?.name || "Category";

	return (
		<div className="container py-8">
			<div className="flex flex-col lg:flex-row gap-6">
				{/* Main content */}
				<div className="lg:w-3/4">
					<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
						<h1 className="text-3xl font-bold">Companies</h1>

						<Button variant="outline" disabled className="gap-1">
							<Plus className="h-4 w-4" />
							Add Company
						</Button>
					</div>
					<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
						<p className="text-lg text-muted-foreground mb-8">
							Explore companies building on the Bittensor network, from AI research labs to decentralized
							infrastructure providers.
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
						<div className="md:col-span-2">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
								<Input
									placeholder="Search companies, descriptions, or categories..."
									className="pl-9"
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
								/>
							</div>
						</div>
						<div className="grid grid-cols-2 gap-2">
							<Select value={categoryFilter} onValueChange={setCategoryFilter}>
								<SelectTrigger className="min-w-[150px]">
									<div className="flex items-center gap-2 truncate">
										<Filter className="h-4 w-4 flex-shrink-0" />
										<span className="truncate">{selectedCategoryName}</span>
									</div>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Categories</SelectItem>
									{categories.map((category) => (
										<SelectItem key={category.id} value={category.id}>
											{category.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select value={locationFilter} onValueChange={setLocationFilter}>
								<SelectTrigger className="min-w-[150px]">
									<div className="flex items-center gap-2 truncate">
										<MapPin className="h-4 w-4 flex-shrink-0" />
										<span className="truncate">
											{locationFilter === "all" ? "All Locations" : locationFilter}
										</span>
									</div>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Locations</SelectItem>
									{locations.map((location) => (
										<SelectItem key={location} value={location ?? ""}>
											{location}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className="mb-4">
						<p className="text-muted-foreground">
							Showing {filteredCompanies.length}{" "}
							{filteredCompanies.length === 1 ? "company" : "companies"}
						</p>
					</div>

					{filteredCompanies.length === 0 ? (
						<div className="text-center py-12 border rounded-lg">
							<p className="text-muted-foreground">No companies found</p>
						</div>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{filteredCompanies.map((company, i) => (
								<Link href={`/companies/${slugify(company.name)}`} key={company.id}>
									<div
										className={cn(
											"h-full hover:shadow-md transition-shadow overflow-hidden border-0 text-white rounded-lg",
											gradients[i % gradients.length]
										)}
									>
										<div className="p-6 pt-2">
											<div className="flex items-center gap-4 mb-4">
												<div className="relative w-16 h-16 rounded-md overflow-hidden bg-white/20 p-2 mt-0 flex items-center justify-center">
													<Image
														src={company.logo_url || "/placeholder.svg"}
														alt={company.name}
														width={48}
														height={48}
														className="object-contain"
													/>
												</div>

												<div>
													<div className="flex items-center gap-2 pt-2">
														<h2
															className={cn(
																"font-semibold text-white",
																company.name.length > 16 ? "text-sm" : "text-lg"
															)}
														>
															{company.name}
														</h2>
													</div>
													<div className="flex items-center gap-2 text-sm text-white/80">
														<span>{company.subnet_ids?.length || 0} subnets</span>
														<span>•</span>
														<span>{company.product_ids?.length || 0} apps</span>
													</div>
													{company.is_verified && (
														<Badge
															variant="default"
															className="bg-blue-500 hover:bg-blue-600 border-0 mt-2 items-center gap-1 text-xs"
														>
															<CheckCircle className="h-3 w-3" />
															<span>Verified</span>
														</Badge>
													)}
												</div>
											</div>

											<p className="text-white/90 mb-4 line-clamp-2">{company.description}</p>
										</div>
										<div className="px-6 pb-6 pt-0 flex justify-end">
											<div className="bg-white/20 rounded-full p-1">
												<ArrowUpRight className="h-4 w-4" />
											</div>
										</div>
									</div>
								</Link>
							))}
						</div>
					)}
				</div>

				{/* Sidebar */}
				<div className="lg:w-1/4">
					<div className="sticky top-24">
						<Card className="bg-gradient-to-b from-purple-500 to-indigo-600 border-0 text-white overflow-hidden mb-6">
							<CardContent className="p-6">
								<div className="font-semibold text-sm mb-2 bg-white/20 w-fit px-2 py-0.5 rounded-sm">
									SPONSORED
								</div>
								<h3 className="text-xl font-bold mb-3">Become a Validator</h3>
								<p className="text-sm mb-4 text-white/90">
									Earn rewards by running a validator node on the Bittensor network.
								</p>
								<Button variant="secondary" size="sm" className="w-full">
									Learn More
								</Button>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	);
}
