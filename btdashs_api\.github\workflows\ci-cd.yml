name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, api_refactoring ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18.x'
  POSTGRES_VERSION: '13'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint
      continue-on-error: true

    - name: Run Prettier check
      run: npx prettier --check "src/**/*.js" "tests/**/*.js"
      continue-on-error: true

    - name: Security audit
      run: npm audit --audit-level=moderate

    - name: Check for vulnerabilities
      run: npx audit-ci --moderate

    - name: SonarCloud Scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      continue-on-error: true

  # Unit and Integration Tests
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: btdashs_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Setup test environment
      run: |
        cp .env.example .env.test
        echo "NODE_ENV=test" >> .env.test
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/btdashs_test" >> .env.test
        echo "AUTH0_SYNC_SECRET=test-secret" >> .env.test
        echo "INTERNAL_API_KEY=test-key" >> .env.test

    - name: Run database migrations
      run: npm run db:migrate
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/btdashs_test

    - name: Run unit tests
      run: npm run test:unit
      env:
        NODE_ENV: test

    - name: Run integration tests
      run: npm run test:integration
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/btdashs_test

    - name: Generate test coverage
      run: npm run test:coverage
      env:
        NODE_ENV: test

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          coverage/
          test-results.xml

  # API Testing
  api-test:
    name: API Integration Tests
    runs-on: ubuntu-latest
    needs: [test]
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: btdashs_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Setup test database
      run: npm run db:migrate
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/btdashs_test

    - name: Start API server
      run: |
        npm start &
        sleep 10
      env:
        NODE_ENV: test
        PORT: 3001
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/btdashs_test

    - name: Wait for API to be ready
      run: |
        timeout 30 bash -c 'until curl -f http://localhost:3001/health; do sleep 1; done'

    - name: Run API tests
      run: npm run test:api

    - name: Stop API server
      run: pkill -f "node.*server.js" || true

  # Build and Package
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --only=production

    - name: Create production build
      run: |
        mkdir -p dist
        cp -r src dist/
        cp package*.json dist/
        cp server.js dist/
        cp -r docs dist/

    - name: Archive production build
      uses: actions/upload-artifact@v3
      with:
        name: production-build
        path: dist/

  # Docker Build and Push
  docker:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: btdash/api
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USER }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd /opt/btdash-api-staging
          docker-compose pull
          docker-compose up -d
          docker-compose exec -T api npm run db:migrate
          
    - name: Run smoke tests
      run: |
        sleep 30
        curl -f ${{ secrets.STAGING_URL }}/health
        npm run test:api
      env:
        API_BASE_URL: ${{ secrets.STAGING_URL }}

    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker, deploy-staging]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Create deployment
      uses: actions/github-script@v6
      id: deployment
      with:
        script: |
          const deployment = await github.rest.repos.createDeployment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            ref: context.sha,
            environment: 'production',
            required_contexts: [],
            auto_merge: false
          });
          return deployment.data.id;

    - name: Deploy to production server
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USER }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          cd /opt/btdash-api
          
          # Backup current version
          docker-compose exec -T postgres pg_dump -U btdash_user btdashs_production > backup_$(date +%Y%m%d_%H%M%S).sql
          
          # Deploy new version
          docker-compose pull
          docker-compose up -d
          
          # Run migrations
          docker-compose exec -T api npm run db:migrate
          
          # Health check
          sleep 30
          curl -f http://localhost:3001/health

    - name: Update deployment status (success)
      uses: actions/github-script@v6
      if: success()
      with:
        script: |
          await github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: ${{ steps.deployment.outputs.result }},
            state: 'success',
            environment_url: '${{ secrets.PRODUCTION_URL }}'
          });

    - name: Update deployment status (failure)
      uses: actions/github-script@v6
      if: failure()
      with:
        script: |
          await github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: ${{ steps.deployment.outputs.result }},
            state: 'failure'
          });

    - name: Run production smoke tests
      run: |
        sleep 60
        curl -f ${{ secrets.PRODUCTION_URL }}/health
        npm run test:api
      env:
        API_BASE_URL: ${{ secrets.PRODUCTION_URL }}

    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
      if: always()

  # Performance Testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install k6
      run: |
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6

    - name: Run performance tests
      run: k6 run tests/performance/load-test.js
      env:
        API_BASE_URL: ${{ secrets.STAGING_URL }}

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json
