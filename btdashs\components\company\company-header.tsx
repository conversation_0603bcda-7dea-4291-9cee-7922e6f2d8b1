"use client";

import { CategoryTag } from "@/components/category-tag";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Category, Company } from "@/lib/db/models";
import { CheckCircle, Globe, MessageSquare, Twitter } from "lucide-react";
import Image from "next/image";

interface CompanyHeaderProps {
  company: Company;
  categories: Category[];
}

export function CompanyHeader({ company, categories }: CompanyHeaderProps) {
  return (
    <div className="space-y-6">
      <div className="relative w-full h-48 md:h-40 lg:h-40 rounded-lg overflow-hidden">
        <Image
          src={company.header_url || "/placeholder.svg"}
          alt={`${company.name} header`}
          fill
          sizes="(max-width: 768px) 100vw, 100vw"
          className="object-cover"
          priority
        />
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="relative w-24 h-24 md:w-32 md:h-32 rounded-lg overflow-hidden border-4 border-background -mt-12 md:-mt-16 bg-white">
          <Image
            src={company.logo_url || "/placeholder.svg"}
            alt={company.name}
            fill
            sizes="(max-width: 768px) 96px, 128px"
            className="object-cover"
            priority
          />
        </div>

        <div className="flex-1">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold">{company.name}</h1>
              {company.is_verified && (
                <Badge
                  variant="default"
                  className="bg-blue-500 flex items-center gap-1"
                >
                  <CheckCircle className="h-3.5 w-3.5" />
                  Verified
                </Badge>
              )}
            </div>

            <div className="flex flex-wrap gap-2">
              {company.website_url && (
                <Button variant="outline" size="sm" className="gap-2" asChild>
                  <a
                    href={company.website_url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Globe className="h-4 w-4" />
                    Website
                  </a>
                </Button>
              )}
              {company.social_media && (
                <>
                  <Button variant="outline" size="sm" className="gap-2" asChild>
                    <a
                      href={
                        typeof company.social_media === "string"
                          ? company.social_media
                          : "#"
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Twitter className="h-4 w-4" />
                      Twitter
                    </a>
                  </Button>
                  <Button variant="outline" size="sm" className="gap-2" asChild>
                    <a
                      href={
                        typeof company.social_media === "string"
                          ? company.social_media
                          : "#"
                      }
                    >
                      <MessageSquare className="h-4 w-4" />
                      Discord
                    </a>
                  </Button>
                </>
              )}
            </div>
          </div>

          <p className="mt-4 text-muted-foreground">
            {company.description || "No description available."}
          </p>

          <div className="flex flex-wrap gap-2 pt-2">
            {categories.map((cat) => (
              <CategoryTag
                key={cat.id}
                category={cat.name.trim()}
                className="text-xs text-white/80 bg-white/20 hover:bg-white/30"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
