"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Company } from "@/lib/db/models";
import { CheckCircle, Sparkles } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface OnboardingWizardProps {
	isOpen: boolean;
	onClose: () => void;
	onComplete: () => void;
}

interface OnboardingState {
	step: number;
	hasCompany: boolean | null;
	company: Partial<Company>;
	isLoading: boolean;
	error: string | null;
}

const STEPS = [
	{ id: 1, title: "Welcome", description: "Let's get you started" },
	{ id: 2, title: "Basic Info", description: "Company name and description" },
	{ id: 3, title: "Details", description: "Additional company information" },
	{ id: 4, title: "Ready to Launch", description: "Start your first campaign" },
];

export default function OnboardingWizard({ isOpen, onClose, onComplete }: OnboardingWizardProps) {
	const router = useRouter();
	const { toast } = useToast();

	const [state, setState] = useState<OnboardingState>({
		step: 1,
		hasCompany: null,
		company: {},
		isLoading: false,
		error: null,
	});

	// Check if user has a company when wizard opens
	useEffect(() => {
		if (isOpen && state.hasCompany === null) {
			checkCompanyStatus();
		}
	}, [isOpen]);

	const checkCompanyStatus = async () => {
		setState((prev) => ({ ...prev, isLoading: true, error: null }));
		try {
			const response = await fetch("/api/user/company");
			const result = await response.json();

			if (response.ok && result.data?.id) {
				setState((prev) => ({
					...prev,
					hasCompany: true,
					isLoading: false,
				}));
			} else {
				setState((prev) => ({
					...prev,
					hasCompany: false,
					isLoading: false,
				}));
			}
		} catch (error) {
			console.error("Error checking company status:", error);
			setState((prev) => ({
				...prev,
				hasCompany: false,
				isLoading: false,
				error: "Failed to check company status",
			}));
		}
	};

	const nextStep = () => {
		setState((prev) => ({ ...prev, step: prev.step + 1 }));
	};

	const prevStep = () => {
		setState((prev) => ({ ...prev, step: prev.step - 1 }));
	};

	const handleCompanyChange = (field: keyof Company, value: any) => {
		setState((prev) => ({
			...prev,
			company: { ...prev.company, [field]: value },
		}));
	};

	const createCompany = async () => {
		setState((prev) => ({ ...prev, isLoading: true, error: null }));
		try {
			// Prepare company data with all required fields in exact order
			const companyData = {
				name: state.company.name || "",
				description: state.company.description || null,
				logo_url: state.company.logo_url || null,
				header_url: state.company.header_url || null,
				website_url: state.company.website_url || null,
				location: state.company.location || null,
				foundedyear: state.company.foundedyear || null,
				teamsize: state.company.teamsize || null,
				social_media:
					state.company.social_media && Object.keys(state.company.social_media).length > 0
						? state.company.social_media
						: null,
			};

			const response = await fetch("/api/user/company", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(companyData),
			});

			const result = await response.json();

			if (response.ok) {
				toast({
					title: "Company Created!",
					description: "Your company profile has been created successfully.",
				});
				nextStep();
			} else {
				// Handle authentication errors
				if (response.status === 401) {
					throw new Error("Please log in to create a company");
				}
				throw new Error(result.error || result.message || "Failed to create company");
			}
		} catch (error: any) {
			console.error("Error creating company:", error);
			setState((prev) => ({
				...prev,
				error: error.message,
			}));
		} finally {
			setState((prev) => ({ ...prev, isLoading: false }));
		}
	};

	const completeOnboarding = () => {
		onComplete();
		onClose();
	};

	const isCompanyBasicFormValid = () => {
		return state.company.name && state.company.name.trim().length > 0;
	};

	const isCompanyDetailsFormValid = () => {
		return true; // Details are optional
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Sparkles className="h-5 w-5 text-primary" />
						{STEPS[state.step - 1]?.title}
					</DialogTitle>
					<DialogDescription>{STEPS[state.step - 1]?.description}</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Progress */}
					<div className="space-y-2">
						<div className="flex justify-between text-sm text-muted-foreground">
							<span>
								Step {state.step} of {STEPS.length}
							</span>
						</div>
						<Progress value={(state.step / STEPS.length) * 100} className="h-2" />
					</div>

					{/* Step Content */}
					{state.step === 1 && <WelcomeStep onNext={nextStep} hasCompany={state.hasCompany} />}

					{state.step === 2 && (
						<CompanyBasicStep
							company={state.company}
							onChange={handleCompanyChange}
							onNext={nextStep}
							onPrev={prevStep}
							isValid={isCompanyBasicFormValid()}
						/>
					)}

					{state.step === 3 && (
						<CompanyDetailsStep
							company={state.company}
							onChange={handleCompanyChange}
							onNext={createCompany}
							onPrev={prevStep}
							isLoading={state.isLoading}
							error={state.error}
							isValid={isCompanyDetailsFormValid()}
						/>
					)}

					{state.step === 4 && <CompleteStep onComplete={completeOnboarding} />}
				</div>
			</DialogContent>
		</Dialog>
	);
}

// Welcome Step Component
function WelcomeStep({ onNext, hasCompany }: { onNext: () => void; hasCompany: boolean | null }) {
	return (
		<Card>
			<CardContent className="pt-6">
				<div className="text-center space-y-4">
					<div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
						<Sparkles className="h-8 w-8 text-primary" />
					</div>
					<div>
						<h3 className="text-lg font-semibold">Welcome to BTDash Ad Manager!</h3>
						<p className="text-muted-foreground mt-2">
							Let's set up your advertising account so you can start promoting your business.
						</p>
					</div>
					{hasCompany && (
						<div className="mx-auto">
							<CheckCircle className="h-3 w-3 mr-1" />
							Company profile found
						</div>
					)}
					<Button onClick={onNext} className="w-full">
						Get Started
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}

// Company Basic Info Step Component
function CompanyBasicStep({
	company,
	onChange,
	onNext,
	onPrev,
	isValid,
}: {
	company: Partial<Company>;
	onChange: (field: keyof Company, value: any) => void;
	onNext: () => void;
	onPrev: () => void;
	isValid: boolean;
}) {
	return (
		<Card>
			<CardHeader>
				<CardTitle>Basic Company Information</CardTitle>
				<CardDescription>Let's start with the essential details about your business.</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				<div className="space-y-2">
					<Label htmlFor="company-name">Company Name *</Label>
					<Input
						id="company-name"
						placeholder="Enter your company name"
						value={company.name || ""}
						onChange={(e) => onChange("name", e.target.value)}
					/>
				</div>

				<div className="space-y-2">
					<Label htmlFor="description">Description</Label>
					<Textarea
						id="description"
						placeholder="Brief description of your business"
						value={company.description || ""}
						onChange={(e) => onChange("description", e.target.value)}
						rows={4}
					/>
				</div>

				<div className="space-y-2">
					<Label htmlFor="website_url">Website URL</Label>
					<Input
						id="website_url"
						type="url"
						placeholder="https://yourcompany.com"
						value={company.website_url || ""}
						onChange={(e) => onChange("website_url", e.target.value)}
					/>
				</div>

				<div className="flex justify-between pt-4">
					<Button variant="outline" onClick={onPrev}>
						Back
					</Button>
					<Button onClick={onNext} disabled={!isValid}>
						Next
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}

// Company Details Step Component
function CompanyDetailsStep({
	company,
	onChange,
	onNext,
	onPrev,
	isLoading,
	error,
	isValid,
}: {
	company: Partial<Company>;
	onChange: (field: keyof Company, value: any) => void;
	onNext: () => void;
	onPrev: () => void;
	isLoading: boolean;
	error: string | null;
	isValid: boolean;
}) {
	const handleSocialMediaChange = (platform: string, value: string) => {
		const currentSocialMedia =
			typeof company.social_media === "object" && !Array.isArray(company.social_media)
				? company.social_media
				: {};

		onChange("social_media", {
			...currentSocialMedia,
			[platform]: value,
		});
	};

	const socialMediaPlatforms = [
		{ id: "twitter", label: "Twitter" },
		{ id: "linkedin", label: "LinkedIn" },
		{ id: "facebook", label: "Facebook" },
		{ id: "instagram", label: "Instagram" },
	];

	return (
		<Card>
			<CardHeader>
				<CardTitle>Additional Details</CardTitle>
				<CardDescription>Complete your company profile with additional information.</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{error && (
					<div className="rounded-md bg-destructive/15 p-3">
						<p className="text-sm text-destructive">{error}</p>
					</div>
				)}

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-2">
						<Label htmlFor="logo_url">Logo URL</Label>
						<Input
							id="logo_url"
							type="url"
							placeholder="https://yourcompany.com/logo.png"
							value={company.logo_url || ""}
							onChange={(e) => onChange("logo_url", e.target.value)}
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="header_url">Header Image URL</Label>
						<Input
							id="header_url"
							type="url"
							placeholder="https://yourcompany.com/header.jpg"
							value={company.header_url || ""}
							onChange={(e) => onChange("header_url", e.target.value)}
						/>
					</div>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="space-y-2">
						<Label htmlFor="location">Location</Label>
						<Input
							id="location"
							placeholder="San Francisco, CA"
							value={company.location || ""}
							onChange={(e) => onChange("location", e.target.value)}
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="foundedyear">Founded Year</Label>
						<Input
							id="foundedyear"
							type="number"
							placeholder="2020"
							min="1800"
							max={new Date().getFullYear()}
							value={company.foundedyear || ""}
							onChange={(e) => {
								const value = e.target.value;
								if (value === "") {
									onChange("foundedyear", null);
								} else {
									const year = parseInt(value);
									if (!isNaN(year)) {
										onChange("foundedyear", year);
									}
								}
							}}
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="teamsize">Team Size</Label>
						<Select value={company.teamsize || ""} onValueChange={(value) => onChange("teamsize", value)}>
							<SelectTrigger>
								<SelectValue placeholder="Select team size" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="1-10">1-10 employees</SelectItem>
								<SelectItem value="11-50">11-50 employees</SelectItem>
								<SelectItem value="51-200">51-200 employees</SelectItem>
								<SelectItem value="201-500">201-500 employees</SelectItem>
								<SelectItem value="501-1000">501-1000 employees</SelectItem>
								<SelectItem value="1000+">1000+ employees</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>

				<div className="space-y-2">
					<Label>Social Media (Optional)</Label>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{socialMediaPlatforms.map((platform) => (
							<div key={platform.id} className="space-y-2">
								<Label htmlFor={`social-${platform.id}`}>{platform.label}</Label>
								<Input
									id={`social-${platform.id}`}
									type="url"
									value={
										(typeof company.social_media === "object" &&
											!Array.isArray(company.social_media) &&
											company.social_media?.[platform.id]) ||
										""
									}
									onChange={(e) => handleSocialMediaChange(platform.id, e.target.value)}
									placeholder={`https://${platform.id}.com/yourcompany`}
								/>
							</div>
						))}
					</div>
				</div>

				<div className="flex justify-between pt-4">
					<Button variant="outline" onClick={onPrev}>
						Back
					</Button>
					<Button onClick={onNext} disabled={!isValid || isLoading}>
						{isLoading ? "Creating..." : "Create Company"}
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}

// Complete Step Component
function CompleteStep({ onComplete }: { onComplete: () => void }) {
	const router = useRouter();

	return (
		<Card>
			<CardContent className="pt-6">
				<div className="text-center space-y-4">
					<div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
						<CheckCircle className="h-8 w-8 text-green-600" />
					</div>
					<div>
						<h3 className="text-lg font-semibold">You're All Set!</h3>
						<p className="text-muted-foreground mt-2">
							Your company profile has been created. You can now start creating advertising campaigns.
						</p>
					</div>
					<div className="flex flex-col sm:flex-row gap-3 justify-center">
						<Button
							variant="outline"
							onClick={() => {
								onComplete();
								router.push("/dashboard/campaigns/create");
							}}
						>
							Create First Campaign
						</Button>
						<Button onClick={onComplete}>Complete Setup</Button>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
