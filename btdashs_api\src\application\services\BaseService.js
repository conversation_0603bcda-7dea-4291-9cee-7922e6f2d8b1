// src/application/services/BaseService.js

const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

/**
 * Base Service - Common functionality for all services
 * Provides CRUD operations and common patterns
 */
class BaseService {
	constructor(tableName, entityName) {
		this.tableName = tableName;
		this.entityName = entityName;
	}

	/**
	 * Create a new record
	 * @param {Object} data - Data to insert
	 * @returns {Promise<Object>} Created record
	 */
	async create(data) {
		try {
			const [newRecord] = await db(this.tableName)
				.insert({
					...data,
					created_at: new Date(),
				})
				.returning("*");

			logger.info(`${this.entityName} created`, { id: newRecord.id });
			return newRecord;
		} catch (error) {
			logger.error(`Error creating ${this.entityName}`, { error, data });
			throw new Error(`Failed to create ${this.entityName}: ${error.message}`);
		}
	}

	/**
	 * Get record by ID
	 * @param {number} id - Record ID
	 * @returns {Promise<Object|null>} Record or null if not found
	 */
	async getById(id) {
		try {
			const record = await db(this.tableName)
				.where({ id })
				.first();

			return record || null;
		} catch (error) {
			logger.error(`Error getting ${this.entityName} by ID`, { error, id });
			throw new Error(`Failed to get ${this.entityName}: ${error.message}`);
		}
	}

	/**
	 * Get records by user ID
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of records
	 */
	async getByUserId(userId) {
		try {
			const records = await db(this.tableName)
				.where({ user_id: userId });

			return records;
		} catch (error) {
			logger.error(`Error getting ${this.entityName} by user ID`, { error, userId });
			throw new Error(`Failed to get ${this.entityName}: ${error.message}`);
		}
	}

	/**
	 * Update record by ID
	 * @param {number} id - Record ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated record
	 */
	async updateById(id, updateData) {
		try {
			const updatePayload = {
				...updateData,
				updated_at: new Date(),
			};

			// Remove undefined values
			Object.keys(updatePayload).forEach(key => {
				if (updatePayload[key] === undefined) {
					delete updatePayload[key];
				}
			});

			const [updatedRecord] = await db(this.tableName)
				.where({ id })
				.update(updatePayload)
				.returning("*");

			if (!updatedRecord) {
				throw new Error(`${this.entityName} not found`);
			}

			logger.info(`${this.entityName} updated`, { id });
			return updatedRecord;
		} catch (error) {
			logger.error(`Error updating ${this.entityName}`, { error, id });
			throw error;
		}
	}

	/**
	 * Delete record by ID
	 * @param {number} id - Record ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteById(id) {
		try {
			const deleted = await db(this.tableName)
				.where({ id })
				.del();

			if (!deleted) {
				throw new Error(`${this.entityName} not found`);
			}

			logger.info(`${this.entityName} deleted`, { id });
			return true;
		} catch (error) {
			logger.error(`Error deleting ${this.entityName}`, { error, id });
			throw error;
		}
	}

	/**
	 * Get all records with optional filters
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options (limit, offset, orderBy)
	 * @returns {Promise<Array>} Array of records
	 */
	async getAll(filters = {}, options = {}) {
		try {
			let query = db(this.tableName);

			// Apply filters
			if (Object.keys(filters).length > 0) {
				query = query.where(filters);
			}

			// Apply ordering
			if (options.orderBy) {
				const { column, direction = 'asc' } = options.orderBy;
				query = query.orderBy(column, direction);
			}

			// Apply pagination
			if (options.limit) {
				query = query.limit(options.limit);
			}
			if (options.offset) {
				query = query.offset(options.offset);
			}

			const records = await query;
			return records;
		} catch (error) {
			logger.error(`Error getting all ${this.entityName}`, { error, filters, options });
			throw new Error(`Failed to get ${this.entityName}: ${error.message}`);
		}
	}

	/**
	 * Count records with optional filters
	 * @param {Object} filters - Optional filters
	 * @returns {Promise<number>} Count of records
	 */
	async count(filters = {}) {
		try {
			let query = db(this.tableName);

			if (Object.keys(filters).length > 0) {
				query = query.where(filters);
			}

			const result = await query.count('* as count').first();
			return parseInt(result.count);
		} catch (error) {
			logger.error(`Error counting ${this.entityName}`, { error, filters });
			throw new Error(`Failed to count ${this.entityName}: ${error.message}`);
		}
	}
}

module.exports = BaseService;
