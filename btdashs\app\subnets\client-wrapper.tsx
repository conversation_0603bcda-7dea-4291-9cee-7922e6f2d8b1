// app/subnets/client-wrapper.tsx

"use client";
import { FeaturedSubnetApplications } from "@/components/featured-subnet-applications";
import { SubnetsFilters } from "@/components/subnets/subnets-filters";
import { SubnetsStats } from "@/components/subnets/subnets-stats";
import { SubnetsTable } from "@/components/subnets/subnets-table";
import { Category, Product, Subnet } from "@/lib/db/models";
import { useState } from "react";

export default function SubnetsClientWrapper({
  subnets,
  categories,
  metrics,
  products,
  networkStats,
}: {
  subnets: Subnet[];
  categories: Category[];
  metrics: any[];
  products: Product[];
  networkStats: any;
}) {
  const [category, setCategory] = useState("all");
  const [status, setStatus] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <>
      <SubnetsStats networkStats={networkStats} />
      <SubnetsFilters
        category={category}
        setCategory={setCategory}
        status={status}
        setStatus={setStatus}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        categories={categories}
      />
      <SubnetsTable
        subnets={subnets}
        categories={categories}
        metrics={metrics}
        category={category}
        status={status}
        searchQuery={searchQuery}
      />
      <div className="mt-8">
        <FeaturedSubnetApplications apps={products} categories={categories} />
      </div>
    </>
  );
}
