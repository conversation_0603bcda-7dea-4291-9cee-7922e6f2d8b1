import { AdPlacementTable } from "@/components/ads-placements/ad-placement-table";
import { ContactForm } from "@/components/contact-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart, Globe, Users, Zap } from "lucide-react";

export default function AdvertisePage() {
	return (
		<div className="flex flex-col gap-16 py-8">
			{/* Hero Section */}
			<div className="relative -mx-6 sm:-mx-8 lg:-mx-12 px-6 sm:px-8 lg:px-12 py-24 overflow-hidden bg-gradient-to-br from-blue-600 to-blue-700">
				{/* Background Pattern */}
				<div
					className="absolute inset-0 opacity-10"
					style={{
						backgroundImage:
							"repeating-linear-gradient(45deg, #fff 0, #fff 1px, transparent 0, transparent 50%)",
						backgroundSize: "20px 20px",
					}}
				/>

				<div className="relative max-w-4xl mx-auto text-center text-white">
					<h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">Advertise on Bittensor Market</h1>
					<p className="text-xl mb-8 text-blue-100">
						Connect your brand and grow your audience with our community of AI, machine learning, and
						blockchain enthusiasts around the world
					</p>
					<div className="flex flex-col sm:flex-row gap-4 justify-center">
						<Button size="lg" variant="secondary">
							Advertise now
						</Button>
						<Button size="lg" variant="outline" className="bg-transparent text-white hover:bg-white/10">
							Contact Sales
						</Button>
					</div>
				</div>
			</div>

			{/* Why Section */}
			<div className="container mx-auto text-center">
				<h2 className="text-3xl sm:text-4xl font-bold mb-4">Why Bittensor Market?</h2>
				<p className="text-xl text-muted-foreground mb-12 max-w-3xl mx-auto">
					Founded in 2023, Bittensor Market has become the leading platform for subnet discovery and
					analytics. Work with us to connect your brand to our community of AI and blockchain enthusiasts.
				</p>

				<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
					{[
						{
							icon: Users,
							title: "Global Reach",
							value: "2M+",
							description: "Monthly active users from over 100 countries",
						},
						{
							icon: BarChart,
							title: "Market Data",
							value: "50+",
							description: "Subnets tracked with real-time analytics",
						},
						{
							icon: Globe,
							title: "Traffic",
							value: "10M+",
							description: "Monthly page views from engaged visitors",
						},
						{
							icon: Zap,
							title: "Engagement",
							value: "85%",
							description: "Average session duration over 5 minutes",
						},
					].map((stat, i) => (
						<Card key={i}>
							<CardHeader>
								<stat.icon className="w-8 h-8 mb-2 mx-auto text-blue-600" />
								<CardTitle>{stat.title}</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-3xl font-bold mb-2">{stat.value}</div>
								<p className="text-sm text-muted-foreground">{stat.description}</p>
							</CardContent>
						</Card>
					))}
				</div>
			</div>

			{/* Ad Placements Section */}
			<div className="container mx-auto">
				<h2 className="text-3xl font-bold mb-8 text-center">Advertisement Placements</h2>
				<AdPlacementTable />
			</div>

			{/* Contact Section */}
			<div className="container mx-auto">
				<Card>
					<CardHeader>
						<CardTitle className="text-2xl text-center">Get in Touch</CardTitle>
					</CardHeader>
					<CardContent>
						<ContactForm />
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
