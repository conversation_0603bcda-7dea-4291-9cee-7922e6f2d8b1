import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"

const pools = [
  { name: "TAO / Subnet 1", liquidity: "$1,234,567", volume24h: "$123,456", apy: "5.67%" },
  { name: "TAO / Subnet 2", liquidity: "$987,654", volume24h: "$98,765", apy: "4.32%" },
  { name: "Subnet 1 / Subnet 2", liquidity: "$567,890", volume24h: "$56,789", apy: "3.21%" },
]

export function LiquidityPoolOverview() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Liquidity Pools</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pools.map((pool) => (
            <div key={pool.name} className="flex justify-between items-center">
              <div>
                <p className="font-medium">{pool.name}</p>
                <p className="text-sm text-muted-foreground">Liquidity: {pool.liquidity}</p>
              </div>
              <div className="text-right">
                <p className="text-sm">24h Volume: {pool.volume24h}</p>
                <p className="text-sm text-green-600">APY: {pool.apy}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

