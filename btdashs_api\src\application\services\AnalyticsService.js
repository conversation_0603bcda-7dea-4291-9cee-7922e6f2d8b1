const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class AnalyticsService {
	/**
	 * Get campaign analytics with impressions, clicks, CTR, and spend data
	 * @param {number} campaignId - Campaign ID
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Object>} Campaign analytics data
	 */
	async getCampaignAnalytics(campaignId, startDate = null, endDate = null) {
		try {
			// Set default date range if not provided (last 30 days)
			if (!startDate || !endDate) {
				const now = new Date();
				endDate = now.toISOString().split("T")[0];
				const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				startDate = thirtyDaysAgo.toISOString().split("T")[0];
			}

			// Get campaign basic info
			const campaign = await db("dtm_ads.ad_campaigns").where({ id: campaignId }).first();

			if (!campaign) {
				throw new Error("Campaign not found");
			}

			// Get all ads for this campaign
			const ads = await db("dtm_ads.ads").where({ campaign_id: campaignId }).select("id");

			const adIds = ads.map((ad) => ad.id);

			if (adIds.length === 0) {
				return {
					campaign_id: campaignId,
					campaign_name: campaign.name,
					total_impressions: 0,
					total_clicks: 0,
					ctr: 0,
					total_spend: 0,
					avg_cpc: 0,
					avg_cpm: 0,
					daily_metrics: [],
					country_metrics: [],
					device_metrics: [],
				};
			}

			// Get total impressions and clicks
			const impressionsQuery = db("dtm_ads.ad_impressions")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"]);

			const clicksQuery = db("dtm_ads.ad_clicks")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"]);

			const [impressionsResult, clicksResult] = await Promise.all([
				impressionsQuery.count("* as count").first(),
				clicksQuery.count("* as count").first(),
			]);

			const totalImpressions = parseInt(impressionsResult.count) || 0;
			const totalClicks = parseInt(clicksResult.count) || 0;
			const ctr = totalImpressions > 0 ? totalClicks / totalImpressions : 0;

			// Calculate spend based on campaign budget model
			let totalSpend = 0;
			if (campaign.budget_cpc && totalClicks > 0) {
				totalSpend = totalClicks * campaign.budget_cpc;
			} else if (campaign.budget_cpm && totalImpressions > 0) {
				totalSpend = (totalImpressions / 1000) * campaign.budget_cpm;
			}

			const avgCpc = totalClicks > 0 ? totalSpend / totalClicks : 0;
			const avgCpm = totalImpressions > 0 ? (totalSpend / totalImpressions) * 1000 : 0;

			// Get daily metrics
			const dailyMetrics = await this.getDailyMetrics(adIds, startDate, endDate);

			// Get country metrics
			const countryMetrics = await this.getCountryMetrics(adIds, startDate, endDate);

			// Get device metrics
			const deviceMetrics = await this.getDeviceMetrics(adIds, startDate, endDate);

			return {
				campaign_id: campaignId,
				campaign_name: campaign.name,
				total_impressions: totalImpressions,
				total_clicks: totalClicks,
				ctr: parseFloat(ctr.toFixed(4)),
				total_spend: parseFloat(totalSpend.toFixed(2)),
				avg_cpc: parseFloat(avgCpc.toFixed(4)),
				avg_cpm: parseFloat(avgCpm.toFixed(2)),
				daily_metrics: dailyMetrics,
				country_metrics: countryMetrics,
				device_metrics: deviceMetrics,
			};
		} catch (error) {
			logger.error("Error getting campaign analytics", { error, campaignId, startDate, endDate });
			throw error;
		}
	}

	/**
	 * Get ad analytics for a specific ad
	 * @param {number} adId - Ad ID
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Object>} Ad analytics data
	 */
	async getAdAnalytics(adId, startDate = null, endDate = null) {
		try {
			// Set default date range if not provided (last 30 days)
			if (!startDate || !endDate) {
				const now = new Date();
				endDate = now.toISOString().split("T")[0];
				const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				startDate = thirtyDaysAgo.toISOString().split("T")[0];
			}

			// Get ad basic info
			const ad = await db("dtm_ads.ads")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.where("ads.id", adId)
				.select(
					"ads.*",
					"ad_campaigns.name as campaign_name",
					"ad_campaigns.budget_cpc",
					"ad_campaigns.budget_cpm"
				)
				.first();

			if (!ad) {
				throw new Error("Ad not found");
			}

			// Get impressions and clicks for this ad
			const impressionsQuery = db("dtm_ads.ad_impressions")
				.where("ad_id", adId)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"]);

			const clicksQuery = db("dtm_ads.ad_clicks")
				.where("ad_id", adId)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"]);

			const [impressionsResult, clicksResult] = await Promise.all([
				impressionsQuery.count("* as count").first(),
				clicksQuery.count("* as count").first(),
			]);

			const totalImpressions = parseInt(impressionsResult.count) || 0;
			const totalClicks = parseInt(clicksResult.count) || 0;
			const ctr = totalImpressions > 0 ? totalClicks / totalImpressions : 0;

			// Calculate spend
			let totalSpend = 0;
			if (ad.budget_cpc && totalClicks > 0) {
				totalSpend = totalClicks * ad.budget_cpc;
			} else if (ad.budget_cpm && totalImpressions > 0) {
				totalSpend = (totalImpressions / 1000) * ad.budget_cpm;
			}

			// Get daily metrics for this ad
			const dailyMetrics = await this.getDailyMetrics([adId], startDate, endDate);

			return {
				ad_id: adId,
				ad_title: ad.title,
				campaign_id: ad.campaign_id,
				campaign_name: ad.campaign_name,
				total_impressions: totalImpressions,
				total_clicks: totalClicks,
				ctr: parseFloat(ctr.toFixed(4)),
				total_spend: parseFloat(totalSpend.toFixed(2)),
				daily_metrics: dailyMetrics,
			};
		} catch (error) {
			logger.error("Error getting ad analytics", { error, adId, startDate, endDate });
			throw error;
		}
	}

	/**
	 * Get user analytics across all campaigns
	 * @param {number} userId - User ID
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Object>} User analytics data
	 */
	async getUserAnalytics(userId, startDate = null, endDate = null) {
		try {
			// Set default date range if not provided (last 30 days)
			if (!startDate || !endDate) {
				const now = new Date();
				endDate = now.toISOString().split("T")[0];
				const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				startDate = thirtyDaysAgo.toISOString().split("T")[0];
			}

			// Get all campaigns for this user
			const campaigns = await db("dtm_ads.ad_campaigns").where({ advertiser_id: userId }).select("id", "name");

			if (campaigns.length === 0) {
				return {
					user_id: userId,
					total_campaigns: 0,
					total_impressions: 0,
					total_clicks: 0,
					ctr: 0,
					total_spend: 0,
					campaigns: [],
				};
			}

			const campaignIds = campaigns.map((c) => c.id);

			// Get analytics for each campaign
			const campaignAnalytics = await Promise.all(
				campaignIds.map((id) => this.getCampaignAnalytics(id, startDate, endDate))
			);

			// Aggregate totals
			const totals = campaignAnalytics.reduce(
				(acc, analytics) => {
					acc.total_impressions += analytics.total_impressions;
					acc.total_clicks += analytics.total_clicks;
					acc.total_spend += analytics.total_spend;
					return acc;
				},
				{ total_impressions: 0, total_clicks: 0, total_spend: 0 }
			);

			const overallCtr = totals.total_impressions > 0 ? totals.total_clicks / totals.total_impressions : 0;

			return {
				user_id: userId,
				total_campaigns: campaigns.length,
				total_impressions: totals.total_impressions,
				total_clicks: totals.total_clicks,
				ctr: parseFloat(overallCtr.toFixed(4)),
				total_spend: parseFloat(totals.total_spend.toFixed(2)),
				campaigns: campaignAnalytics,
			};
		} catch (error) {
			logger.error("Error getting user analytics", { error, userId, startDate, endDate });
			throw error;
		}
	}

	/**
	 * Get daily metrics for ads
	 * @param {Array} adIds - Array of ad IDs
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Array>} Daily metrics data
	 */
	async getDailyMetrics(adIds, startDate, endDate) {
		try {
			// Get daily impressions
			const dailyImpressions = await db("dtm_ads.ad_impressions")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.select(db.raw("DATE(created_at) as date"))
				.count("* as impressions")
				.groupBy(db.raw("DATE(created_at)"))
				.orderBy("date");

			// Get daily clicks
			const dailyClicks = await db("dtm_ads.ad_clicks")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.select(db.raw("DATE(created_at) as date"))
				.count("* as clicks")
				.groupBy(db.raw("DATE(created_at)"))
				.orderBy("date");

			// Merge impressions and clicks data
			const clicksMap = dailyClicks.reduce((acc, row) => {
				acc[row.date] = parseInt(row.clicks);
				return acc;
			}, {});

			const dailyMetrics = dailyImpressions.map((row) => {
				const impressions = parseInt(row.impressions);
				const clicks = clicksMap[row.date] || 0;
				const ctr = impressions > 0 ? clicks / impressions : 0;

				return {
					date: row.date,
					impressions,
					clicks,
					ctr: parseFloat(ctr.toFixed(4)),
				};
			});

			return dailyMetrics;
		} catch (error) {
			logger.error("Error getting daily metrics", { error, adIds, startDate, endDate });
			throw error;
		}
	}

	/**
	 * Get country metrics for ads
	 * @param {Array} adIds - Array of ad IDs
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Array>} Country metrics data
	 */
	async getCountryMetrics(adIds, startDate, endDate) {
		try {
			// Get impressions by country
			const countryImpressions = await db("dtm_ads.ad_impressions")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.whereNotNull("country_code")
				.select("country_code")
				.count("* as impressions")
				.groupBy("country_code")
				.orderBy("impressions", "desc");

			// Get clicks by country
			const countryClicks = await db("dtm_ads.ad_clicks")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.whereNotNull("country_code")
				.select("country_code")
				.count("* as clicks")
				.groupBy("country_code");

			// Merge data
			const clicksMap = countryClicks.reduce((acc, row) => {
				acc[row.country_code] = parseInt(row.clicks);
				return acc;
			}, {});

			const countryMetrics = countryImpressions.map((row) => {
				const impressions = parseInt(row.impressions);
				const clicks = clicksMap[row.country_code] || 0;
				const ctr = impressions > 0 ? clicks / impressions : 0;

				return {
					country_code: row.country_code,
					impressions,
					clicks,
					ctr: parseFloat(ctr.toFixed(4)),
				};
			});

			return countryMetrics;
		} catch (error) {
			logger.error("Error getting country metrics", { error, adIds, startDate, endDate });
			throw error;
		}
	}

	/**
	 * Get device metrics for ads
	 * @param {Array} adIds - Array of ad IDs
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Array>} Device metrics data
	 */
	async getDeviceMetrics(adIds, startDate, endDate) {
		try {
			// Get impressions by device type
			const deviceImpressions = await db("dtm_ads.ad_impressions")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.whereNotNull("device_type")
				.select("device_type")
				.count("* as impressions")
				.groupBy("device_type")
				.orderBy("impressions", "desc");

			// Get clicks by device type
			const deviceClicks = await db("dtm_ads.ad_clicks")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.whereNotNull("device_type")
				.select("device_type")
				.count("* as clicks")
				.groupBy("device_type");

			// Merge data
			const clicksMap = deviceClicks.reduce((acc, row) => {
				acc[row.device_type] = parseInt(row.clicks);
				return acc;
			}, {});

			const deviceMetrics = deviceImpressions.map((row) => {
				const impressions = parseInt(row.impressions);
				const clicks = clicksMap[row.device_type] || 0;
				const ctr = impressions > 0 ? clicks / impressions : 0;

				return {
					device_type: row.device_type,
					impressions,
					clicks,
					ctr: parseFloat(ctr.toFixed(4)),
				};
			});

			return deviceMetrics;
		} catch (error) {
			logger.error("Error getting device metrics", { error, adIds, startDate, endDate });
			throw error;
		}
	}

	/**
	 * Get conversion tracking data
	 * @param {number} campaignId - Campaign ID
	 * @param {string} startDate - Start date (YYYY-MM-DD)
	 * @param {string} endDate - End date (YYYY-MM-DD)
	 * @returns {Promise<Object>} Conversion tracking data
	 */
	async getConversionTracking(campaignId, startDate = null, endDate = null) {
		try {
			// Set default date range if not provided (last 30 days)
			if (!startDate || !endDate) {
				const now = new Date();
				endDate = now.toISOString().split("T")[0];
				const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				startDate = thirtyDaysAgo.toISOString().split("T")[0];
			}

			// Get all ads for this campaign
			const ads = await db("dtm_ads.ads").where({ campaign_id: campaignId }).select("id");

			const adIds = ads.map((ad) => ad.id);

			if (adIds.length === 0) {
				return {
					campaign_id: campaignId,
					total_conversions: 0,
					conversion_rate: 0,
					cost_per_conversion: 0,
				};
			}

			// For now, we'll simulate conversion tracking
			// In a real implementation, you'd have a conversions table
			const totalClicks = await db("dtm_ads.ad_clicks")
				.whereIn("ad_id", adIds)
				.whereBetween("created_at", [startDate + " 00:00:00", endDate + " 23:59:59"])
				.count("* as count")
				.first();

			const clickCount = parseInt(totalClicks.count) || 0;

			// TODO: Implement actual conversion tracking with conversions table
			// For now, return 0 conversions since we don't have real conversion data
			const totalConversions = 0;
			const conversionRate = 0;

			// Get campaign analytics to calculate cost per conversion
			const analytics = await this.getCampaignAnalytics(campaignId, startDate, endDate);
			const costPerConversion = totalConversions > 0 ? analytics.total_spend / totalConversions : 0;

			return {
				campaign_id: campaignId,
				total_conversions: totalConversions,
				conversion_rate: parseFloat(conversionRate.toFixed(4)),
				cost_per_conversion: parseFloat(costPerConversion.toFixed(2)),
			};
		} catch (error) {
			logger.error("Error getting conversion tracking", { error, campaignId, startDate, endDate });
			throw error;
		}
	}
}

module.exports = AnalyticsService;
