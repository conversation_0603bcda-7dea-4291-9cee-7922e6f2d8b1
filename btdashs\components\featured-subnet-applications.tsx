"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Product, Category } from "@/lib/db/models";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRef } from "react";
import { CategoryTag } from "./category-tag";

type Props = {
  apps: Product[];
  categories: Category[]; // Optional categories prop for future use
};

export function FeaturedSubnetApplications({ apps, categories }: Props) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scroll = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const scrollAmount = direction === "left" ? -300 : 300;
      scrollContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Featured Applications</CardTitle>
        <div className="flex space-x-2">
          <Button variant="outline" size="icon" onClick={() => scroll("left")}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => scroll("right")}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div
          ref={scrollContainerRef}
          className="flex space-x-4 overflow-x-auto pb-4 hide-scrollbar"
        >
          {apps.map((app) => (
            <Card
              key={app.id}
              className="flex-shrink-0 w-[280px] overflow-hidden border hover:shadow-md transition-shadow"
            >
              <CardContent className="p-0 h-full flex flex-col">
                <div className="p-4 flex items-center gap-4">
                  <Image
                    src={app.icon_url || app.logo_url || "/placeholder.svg"}
                    alt={app.name}
                    width={60}
                    height={60}
                    className="rounded-lg shadow-sm"
                  />
                  <div>
                    <h3 className="font-semibold">{app.name}</h3>
                    {/* <div className="flex items-center text-sm mb-1">
						<Star className="h-3 w-3 text-amber-500 mr-1" />
						<span>
							4.5
						</span>
					</div> */}
                    {categories &&
                      categories
                        .filter(
                          (c) =>
                            app.category_ids &&
                            app.category_ids[0] &&
                            c.id === app.category_ids[0]
                        )
                        .map((c) => (
                          <CategoryTag
                            className="pt-2 mt-2"
                            key={c.id}
                            category={c.name}
                          />
                        ))}
                  </div>
                </div>
                <div className="px-4 pb-3">
                  {app.description?.slice(0, 150)}
                  {app.description && app.description.length > 150 ? "..." : ""}
                </div>
                <div className="mt-auto bg-muted/50 p-3 flex items-center justify-between">
                  {app.subnet_ids && app.subnet_ids.length > 0 && (
                    <Link
                      href={`/subnets/${app.subnet_ids[0]}?utm_source=dTao_market_cap&utm_medium=feature_apps&utm_campaign=featured_apps`}
                      className="text-xs text-primary hover:underline"
                    >
                      Subnet {app.subnet_ids[0]}
                    </Link>
                  )}
                  <Link href={`/products/${app.id}`}>
                    <Button size="sm" variant="secondary">
                      View
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
