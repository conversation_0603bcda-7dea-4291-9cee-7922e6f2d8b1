"use client";

import { cn } from "@/lib/utils";
import * as RadixPopover from "@radix-ui/react-popover";

export const Popover = RadixPopover.Root;
export const PopoverTrigger = RadixPopover.Trigger;

export const PopoverContent = ({ className, ...props }: RadixPopover.PopoverContentProps) => {
	return (
		<RadixPopover.Portal>
			<RadixPopover.Content
				align="end"
				sideOffset={8}
				className={cn(
					"z-50 w-72 rounded-md border bg-white p-4 text-sm shadow-md dark:border-neutral-700 dark:bg-neutral-900",
					className
				)}
				{...props}
			/>
		</RadixPopover.Portal>
	);
};
