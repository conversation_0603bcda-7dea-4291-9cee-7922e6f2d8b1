"use client";

import { <PERSON><PERSON>, Avatar<PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Subnet } from "@/lib/db/models";

interface SubnetTeamProps {
	subnet: Subnet;
}

export function SubnetTeam({ subnet }: SubnetTeamProps) {
	const teamMembers = subnet.github_team_members || [];

	if (!Array.isArray(teamMembers) || teamMembers.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Subnet Team</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-muted-foreground">No team members found for this subnet</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle>Subnet Team</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{teamMembers.map((member: any) => (
						<div key={member.login} className="flex flex-col items-center text-center">
							<a href={member.html_url} target="_blank" rel="noopener noreferrer">
								<Avatar className="w-24 h-24 mb-4 hover:ring-2 hover:ring-primary">
									<AvatarImage src={member.avatar_url} alt={member.login} />
									<AvatarFallback>
										{member.login
											.split(/[-_]/)
											.map((n: string) => n[0].toUpperCase())
											.join("")}
									</AvatarFallback>
								</Avatar>
							</a>
							<h3 className="font-semibold">{member.login}</h3>
							<p className="text-sm text-muted-foreground">{member.contributions} contributions</p>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
