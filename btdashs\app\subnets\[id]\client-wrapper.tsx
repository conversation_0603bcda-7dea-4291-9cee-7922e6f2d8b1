"use client";

import SubnetProfile from "@/components/subnets/subnet-profile";
import type { Category, Company, Event, Job, News, Product, Subnet, SubnetMetric } from "@/lib/db/models";

interface SubnetClientWrapperProps {
	subnet: Subnet;
	metrics: SubnetMetric;
	categories: Category[];
	news: News[];
	products: Product[];
	jobs: Job[];
	events: Event[];
	companies: Company[];
}

export default function SubnetClientWrapper({
	subnet,
	metrics,
	categories,
	news,
	products,
	jobs,
	events,
	companies,
}: SubnetClientWrapperProps) {
	return (
		<>
			<SubnetProfile
				subnet={subnet}
				metrics={metrics}
				categories={categories}
				news={news}
				products={products}
				jobs={jobs}
				events={events}
				companies={companies}
			/>
		</>
	);
}
