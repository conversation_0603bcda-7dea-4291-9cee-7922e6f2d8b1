// app/api/placements/[id]/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

export async function GET(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${id}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) throw new Error("Failed to fetch campaign details");

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}

// PUT /api/user/campaigns/[id] - Update campaign
export async function PUT(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;
		const body = await request.json();

		const { token } = await auth0.getAccessToken();

		// Get user info and company info
		const userResponse = await fetch(`${process.env.API_BASE_URL}/user/me`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!userResponse.ok) {
			throw new Error("Failed to get user information");
		}

		const userResult = await userResponse.json();
		const userId = userResult.success ? userResult.data.id : null;

		if (!userId) {
			throw new Error("User ID not found");
		}

		// Get user's company info for advertiser_id
		const companyResponse = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		const companyResult = await companyResponse.json();
		const companyId = companyResult.success ? companyResult.data.id : null;

		if (!companyId) {
			throw new Error(`Failed to get company information`);
		}

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${id}`, {
			method: "PUT",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${token}`,
			},
			body: JSON.stringify({
				advertiser_id: body.advertiser_id || companyId, // Use company ID as advertiser_id
				manager_id: userId, // Use user ID as manager_id
				name: body.name,
				description: body.description,
				total_budget: body.total_budget || null,
				budget_cpc: body.budget_cpc || null,
				budget_cpm: body.budget_cpm || null,
				start_date: body.start_date,
				end_date: body.end_date,
			}),
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Failed to update campaign: ${errorText}`);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}

// DELETE /api/user/campaigns/[id] - Delete campaign
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
	try {
		const paramsData = await params;
		const id = paramsData.id;

		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/campaigns/${id}`, {
			method: "DELETE",
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Failed to delete campaign: ${errorText}`);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		return NextResponse.json({ success: false, message: error.message }, { status: 500 });
	}
}
