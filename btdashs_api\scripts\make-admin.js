// scripts/make-admin.js
// <PERSON>ript to make a user an admin

const db = require("../src/infrastructure/database/knex");
const UserService = require("../src/application/services/UserService");

async function makeUserAdmin(auth0UserId) {
	try {
		console.log(`Looking for user with Auth0 ID: ${auth0UserId}`);

		// First, check if user exists
		const user = await db("dtm_base.users").where({ auth0_user_id: auth0UserId }).first();

		if (!user) {
			console.error(`❌ User with Auth0 ID ${auth0UserId} not found`);
			console.log("Make sure the user has logged in at least once to create their account.");
			process.exit(1);
		}

		console.log(`✅ Found user: ${user.email} (ID: ${user.id})`);

		// Check if admin_users table exists, if not create it
		try {
			const tableExists = await db.schema.withSchema("dtm_base").hasTable("admin_users");
			if (!tableExists) {
				console.log("Creating admin_users table...");
				await db.schema.withSchema("dtm_base").createTable("admin_users", (table) => {
					table.increments("id").primary();
					table.string("auth0_user_id", 255).notNullable().unique();
					table.integer("user_id").notNullable();
					table.string("role", 255).defaultTo("admin");
					table.timestamp("created_at").defaultTo(db.fn.now());
					table.timestamp("updated_at").defaultTo(db.fn.now());

					table.foreign("user_id").references("id").inTable("dtm_base.users").onDelete("CASCADE");
				});
				console.log("✅ Admin users table created");
			}
		} catch (error) {
			// Table might already exist, continue
			console.log("Admin users table already exists, continuing...");
		}

		// Note: is_admin column is no longer used - admin status is determined by admin_users table

		// Use UserService to make user admin
		const success = await UserService.makeUserAdmin(auth0UserId);

		if (success) {
			console.log("✅ User is now an admin");
		} else {
			console.error("❌ Failed to make user admin");
			process.exit(1);
		}

		console.log(`🎉 Successfully made ${user.email} an admin!`);
		console.log(`🔗 Admin interface: http://localhost:3000/admin`);
		console.log(`📝 Note: You may need to log out and log back in for admin privileges to take effect.`);
	} catch (error) {
		console.error("❌ Error making user admin:", error.message);
		if (error.code) {
			console.error("Error code:", error.code);
		}
		process.exit(1);
	} finally {
		await db.destroy();
	}
}

// Get Auth0 user ID from command line arguments
const auth0UserId = process.argv[2];

if (!auth0UserId) {
	console.error("Usage: node scripts/make-admin.js <auth0_user_id>");
	console.error('Example: node scripts/make-admin.js "google-oauth2|116856637670992243133"');
	console.error("");
	console.error("To find your Auth0 user ID:");
	console.error("1. Log in to the application");
	console.error("2. Check browser dev tools → Network tab");
	console.error("3. Look for API calls - your ID will be in the format: google-oauth2|123456789");
	process.exit(1);
}

makeUserAdmin(auth0UserId);
