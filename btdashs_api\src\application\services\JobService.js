// src/application/services/JobService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Job Service - Handles both user job postings and public job board operations
 *
 * This service manages job postings for both user-specific operations
 * and public job board functionality.
 *
 * Key responsibilities:
 * - User job posting management
 * - Public job board operations
 * - Job search and filtering
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class JobService extends BaseService {
	constructor() {
		super("dtm_base.jobs", "Job");
	}

	/**
	 * Create a job posting for a user
	 * @param {number} userId - User ID (owner)
	 * @param {Object} jobData - Job data
	 * @returns {Promise<Object>} Created job object
	 */
	async createUserJob(userId, jobData) {
		const {
			title,
			description,
			location,
			remote,
			type,
			currency,
			salary_time_frame,
			min_salary,
			max_salary,
			published_date,
			company_id,
			subnet_ids,
			product_ids,
			category_ids,
		} = jobData;

		try {
			return await this.create({
				owner_id: userId,
				title,
				description,
				location,
				remote,
				type,
				currency,
				salary_time_frame,
				min_salary,
				max_salary,
				published_date,
				company_id,
				subnet_ids,
				product_ids,
				category_ids,
			});
		} catch (error) {
			logger.error("Error creating user job", { error, userId });
			throw error;
		}
	}

	/**
	 * Get all jobs for a user
	 * @param {number} userId - User ID (owner)
	 * @returns {Promise<Array>} Array of job records
	 */
	async getUserJobs(userId) {
		try {
			const jobs = await this.getAll({ owner_id: userId });
			return jobs;
		} catch (error) {
			logger.error("Error getting user jobs", { error, userId });
			throw error;
		}
	}

	/**
	 * Update a job posting
	 * @param {number} jobId - Job ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated job object
	 */
	async updateUserJob(jobId, updateData) {
		const {
			title,
			description,
			location,
			remote,
			type,
			currency,
			salary_time_frame,
			min_salary,
			max_salary,
			published_date,
			company_id,
			subnet_ids,
			product_ids,
			category_ids,
		} = updateData;

		try {
			return await this.updateById(jobId, {
				title,
				description,
				location,
				remote,
				type,
				currency,
				salary_time_frame,
				min_salary,
				max_salary,
				published_date,
				company_id,
				subnet_ids,
				product_ids,
				category_ids,
			});
		} catch (error) {
			logger.error("Error updating user job", { error, jobId });
			throw error;
		}
	}

	/**
	 * Delete a job posting
	 * @param {number} jobId - Job ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteUserJob(jobId) {
		try {
			return await this.deleteById(jobId);
		} catch (error) {
			logger.error("Error deleting user job", { error, jobId });
			throw error;
		}
	}

	/**
	 * Get all public jobs with filtering and pagination
	 * @param {Object} filters - Filter options
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of job records
	 */
	async getPublicJobs(filters = {}, options = {}) {
		try {
			// Default ordering by published_date desc
			const queryOptions = {
				orderBy: { column: "published_date", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting public jobs", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get all jobs (public job board)
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of jobs
	 */
	async getAllJobs(filters = {}, options = {}) {
		return await this.getPublicJobs(filters, options);
	}

	/**
	 * Get job by ID (public access)
	 * @param {number} id - Job ID
	 * @returns {Promise<Object|null>} Job object or null if not found
	 */
	async getJobById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting job by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new job (public job board)
	 * @param {Object} jobData - Job data
	 * @returns {Promise<Object>} Created job object
	 */
	async createJob(jobData) {
		try {
			const newJob = await this.create(jobData);
			logger.info("Job created", { job_id: newJob.id });
			return newJob;
		} catch (error) {
			logger.error("Error creating job", { error, jobData });
			throw error;
		}
	}

	/**
	 * Update a job (public job board)
	 * @param {number} id - Job ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated job object
	 */
	async updateJob(id, updateData) {
		try {
			const updatedJob = await this.updateById(id, updateData);
			logger.info("Job updated", { job_id: id });
			return updatedJob;
		} catch (error) {
			logger.error("Error updating job", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a job (public job board)
	 * @param {number} id - Job ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteJob(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("Job deleted", { job_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting job", { error, id });
			throw error;
		}
	}
}

module.exports = new JobService();
