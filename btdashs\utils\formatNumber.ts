// Helper function to format numbers with commas and optional precision, handling negative numbers
export const formatNumber = (value: string | number | undefined, precision: number = 3): string => {
	if (value === undefined || value === null) return "N/A";

	const number = typeof value === "string" ? parseFloat(value) : value;
	if (isNaN(number)) return "N/A";

	// Handle negative numbers
	const isNegative = number < 0;
	const absoluteValue = Math.abs(number);

	// Handle large numbers with suffixes
	const suffixes = ["", "K", "M", "B", "T", "P", "E"];
	let suffixIndex = 0;
	let scaledValue = absoluteValue;

	while (scaledValue >= 1000 && suffixIndex < suffixes.length - 1) {
		scaledValue /= 1000;
		suffixIndex++;
	}

	// Format the number and return with appropriate sign
	const formattedValue = `${scaledValue.toFixed(precision)}${suffixes[suffixIndex]}`;
	return isNegative ? `-${formattedValue}` : formattedValue;
};
