import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';
import { stripeHelpers } from '@/lib/stripe-server';

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { amount, campaignId, customerId } = await request.json();

    // Validate input
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    if (!campaignId) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }

    // Create payment intent with campaign metadata
    const paymentIntent = await stripeHelpers.createPaymentIntent(amount, {
      campaignId: campaignId.toString(),
      userId: session.user.sub,
      customerId: customerId || '',
      type: 'campaign_payment',
    });

    // Store payment intent in database for tracking
    const response = await fetch(`${process.env.API_BASE_URL}/billing/payment-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
        'X-Internal-Key': process.env.INTERNAL_API_KEY || '',
      },
      body: JSON.stringify({
        payment_intent_id: paymentIntent.id,
        campaign_id: campaignId,
        amount: amount,
        status: 'pending',
        currency: 'usd',
      }),
    });

    if (!response.ok) {
      console.error('Failed to store payment intent in database');
      // Continue anyway, as the payment intent was created successfully
    }

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });

  } catch (error) {
    console.error('Error creating payment intent:', error);
    
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}
