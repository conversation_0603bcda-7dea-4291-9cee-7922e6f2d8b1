📂 .
┣ 📄 .gitignore
┣ 📂 app
┃ ┣ 📂 admin
┃ ┃ ┣ 📂 analytics
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📂 inventory
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📄 layout.tsx
┃ ┃ ┣ 📄 page.tsx
┃ ┃ ┣ 📂 payments
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📂 reports
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📂 requests
┃ ┃ ┃ ┣ 📂 [id]
┃ ┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┃ ┣ 📄 loading.tsx
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┗ 📂 settings
┃ ┃     ┗ 📄 page.tsx
┃ ┣ 📂 api
┃ ┃ ┗ 📂 user
┃ ┃     ┣ 📂 company
┃ ┃     ┃ ┗ 📄 route.ts
┃ ┃     ┣ 📂 me
┃ ┃     ┃ ┗ 📄 route.ts
┃ ┃     ┗ 📂 preferences
┃ ┃         ┣ 📂 [id]
┃ ┃         ┃ ┗ 📄 route.ts
┃ ┃         ┗ 📄 route.ts
┃ ┣ 📂 dashboard
┃ ┃ ┣ 📂 analytics
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📂 billing
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📂 campaigns
┃ ┃ ┃ ┣ 📂 [id]
┃ ┃ ┃ ┃ ┣ 📂 analytics
┃ ┃ ┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┃ ┣ 📄 loading.tsx
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📂 checkout
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┣ 📄 layout.tsx
┃ ┃ ┣ 📄 page.tsx
┃ ┃ ┣ 📂 placements
┃ ┃ ┃ ┣ 📂 [id]
┃ ┃ ┃ ┃ ┗ 📂 create
┃ ┃ ┃ ┃     ┗ 📄 page.tsx
┃ ┃ ┃ ┗ 📄 page.tsx
┃ ┃ ┗ 📂 settings
┃ ┃     ┣ 📄 client-wrapper.tsx
┃ ┃     ┗ 📄 page.tsx
┃ ┣ 📄 globals.css
┃ ┣ 📄 layout.tsx
┃ ┣ 📄 page.tsx
┃ ┗ 📂 profile
┃     ┗ 📄 page.tsx
┣ 📂 components
┃ ┣ 📂 analytics
┃ ┃ ┣ 📄 bar-chart.tsx
┃ ┃ ┣ 📄 campaign-performance.tsx
┃ ┃ ┣ 📄 line-chart.tsx
┃ ┃ ┗ 📄 metrics-summary.tsx
┃ ┣ 📄 badge.tsx
┃ ┣ 📄 country-selector.tsx
┃ ┣ 📄 dashboard-header.tsx
┃ ┣ 📄 dashboard-nav.tsx
┃ ┣ 📄 date-range-picker.tsx
┃ ┣ 📄 mode-toggle.tsx
┃ ┣ 📄 page-targeting-display.tsx
┃ ┣ 📄 page-type-selector.tsx
┃ ┣ 📂 profile
┃ ┃ ┣ 📄 company-info.tsx
┃ ┃ ┣ 📄 profile-basic-info.tsx
┃ ┃ ┗ 📄 profile-preferences.tsx
┃ ┣ 📄 theme-provider.tsx
┃ ┗ 📂 ui
┃     ┣ 📄 accordion.tsx
┃     ┣ 📄 alert-dialog.tsx
┃     ┣ 📄 alert.tsx
┃     ┣ 📄 aspect-ratio.tsx
┃     ┣ 📄 avatar.tsx
┃     ┣ 📄 badge.tsx
┃     ┣ 📄 breadcrumb.tsx
┃     ┣ 📄 button.tsx
┃     ┣ 📄 calendar.tsx
┃     ┣ 📄 card.tsx
┃     ┣ 📄 carousel.tsx
┃     ┣ 📄 chart.tsx
┃     ┣ 📄 checkbox.tsx
┃     ┣ 📄 collapsible.tsx
┃     ┣ 📄 command.tsx
┃     ┣ 📄 context-menu.tsx
┃     ┣ 📄 dialog.tsx
┃     ┣ 📄 drawer.tsx
┃     ┣ 📄 dropdown-menu.tsx
┃     ┣ 📄 form.tsx
┃     ┣ 📄 hover-card.tsx
┃     ┣ 📄 input-otp.tsx
┃     ┣ 📄 input.tsx
┃     ┣ 📄 label.tsx
┃     ┣ 📄 menubar.tsx
┃     ┣ 📄 navigation-menu.tsx
┃     ┣ 📄 pagination.tsx
┃     ┣ 📄 popover.tsx
┃     ┣ 📄 progress.tsx
┃     ┣ 📄 radio-group.tsx
┃     ┣ 📄 resizable.tsx
┃     ┣ 📄 scroll-area.tsx
┃     ┣ 📄 select.tsx
┃     ┣ 📄 separator.tsx
┃     ┣ 📄 sheet.tsx
┃     ┣ 📄 sidebar.tsx
┃     ┣ 📄 skeleton.tsx
┃     ┣ 📄 slider.tsx
┃     ┣ 📄 sonner.tsx
┃     ┣ 📄 switch.tsx
┃     ┣ 📄 table.tsx
┃     ┣ 📄 tabs.tsx
┃     ┣ 📄 textarea.tsx
┃     ┣ 📄 toast.tsx
┃     ┣ 📄 toaster.tsx
┃     ┣ 📄 toggle-group.tsx
┃     ┣ 📄 toggle.tsx
┃     ┣ 📄 tooltip.tsx
┃     ┣ 📄 use-mobile.tsx
┃     ┗ 📄 use-toast.ts
┣ 📄 components.json
┣ 📄 generate_structure.py
┣ 📂 hooks
┃ ┣ 📄 use-mobile.tsx
┃ ┗ 📄 use-toast.ts
┣ 📂 lib
┃ ┣ 📄 analytics.ts
┃ ┣ 📄 auth0.ts
┃ ┣ 📄 countries.ts
┃ ┣ 📂 db
┃ ┃ ┗ 📄 models.ts
┃ ┣ 📄 mock-db.ts
┃ ┣ 📄 page-categories.ts
┃ ┗ 📄 utils.ts
┣ 📄 middleware.ts
┣ 📄 next-env.d.ts
┣ 📄 next.config.mjs
┣ 📄 package-lock.json
┣ 📄 package.json
┣ 📄 pnpm-lock.yaml
┣ 📄 postcss.config.mjs
┣ 📂 public
┃ ┣ 📄 placeholder-logo.png
┃ ┣ 📄 placeholder-logo.svg
┃ ┣ 📄 placeholder-user.jpg
┃ ┣ 📄 placeholder.jpg
┃ ┗ 📄 placeholder.svg
┣ 📂 styles
┃ ┗ 📄 globals.css
┣ 📄 tailwind.config.ts
┣ 📄 tsconfig.json
┗ 📄 types.ts