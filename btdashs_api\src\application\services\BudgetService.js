const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");
const NotificationService = require("./NotificationService");

class BudgetService {
	/**
	 * Get advertiser balance
	 * @param {number} userId - User ID
	 * @returns {Promise<Object>} Balance information
	 */
	async getAdvertiserBalance(userId) {
		try {
			let balance = await db("dtm_ads.advertiser_balances").where({ user_id: userId }).first();

			if (!balance) {
				// Create initial balance record
				balance = await this.createAdvertiserBalance(userId, 0);
			}

			return balance;
		} catch (error) {
			logger.error("Error getting advertiser balance", { error, userId });
			throw error;
		}
	}

	/**
	 * Create advertiser balance record
	 * @param {number} userId - User ID
	 * @param {number} initialBalance - Initial balance amount
	 * @param {string} currency - Currency code (default: USD)
	 * @returns {Promise<Object>} Created balance record
	 */
	async createAdvertiserBalance(userId, initialBalance = 0, currency = "USD") {
		try {
			const [balance] = await db("dtm_ads.advertiser_balances")
				.insert({
					user_id: userId,
					balance: initialBalance,
					currency,
					last_updated: new Date(),
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			logger.info("Advertiser balance created", { userId, initialBalance, currency });
			return balance;
		} catch (error) {
			logger.error("Error creating advertiser balance", { error, userId, initialBalance });
			throw error;
		}
	}

	/**
	 * Add funds to advertiser balance
	 * @param {number} userId - User ID
	 * @param {number} amount - Amount to add
	 * @param {string} description - Transaction description
	 * @param {string} paymentMethod - Payment method used
	 * @returns {Promise<Object>} Updated balance and transaction record
	 */
	async addFunds(userId, amount, description = "Funds added", paymentMethod = null) {
		const trx = await db.transaction();

		try {
			// Get current balance
			let balance = await trx("dtm_ads.advertiser_balances").where({ user_id: userId }).first().forUpdate();

			if (!balance) {
				// Create balance record if it doesn't exist
				balance = await this.createAdvertiserBalance(userId, 0);
			}

			// Create billing transaction record
			const [transaction] = await trx("dtm_ads.billing_transactions")
				.insert({
					advertiser_id: userId,
					amount,
					currency: balance.currency,
					description,
					status: "completed",
					payment_method: paymentMethod,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			// Update balance
			const newBalance = parseFloat(balance.balance) + parseFloat(amount);
			const [updatedBalance] = await trx("dtm_ads.advertiser_balances")
				.where({ user_id: userId })
				.update({
					balance: newBalance,
					last_transaction_id: transaction.id,
					last_updated: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			await trx.commit();

			logger.info("Funds added to advertiser balance", {
				userId,
				amount,
				newBalance,
				transactionId: transaction.id,
			});

			return {
				balance: updatedBalance,
				transaction,
			};
		} catch (error) {
			await trx.rollback();
			logger.error("Error adding funds", { error, userId, amount });
			throw error;
		}
	}

	/**
	 * Deduct spend from advertiser balance
	 * @param {number} userId - User ID
	 * @param {number} amount - Amount to deduct
	 * @param {number} campaignId - Campaign ID for the spend
	 * @param {string} description - Transaction description
	 * @returns {Promise<Object>} Updated balance and transaction record
	 */
	async deductSpend(userId, amount, campaignId, description = "Ad spend") {
		const trx = await db.transaction();

		try {
			// Get current balance with lock
			const balance = await trx("dtm_ads.advertiser_balances").where({ user_id: userId }).first().forUpdate();

			if (!balance) {
				throw new Error("Advertiser balance not found");
			}

			const currentBalance = parseFloat(balance.balance);
			const spendAmount = parseFloat(amount);

			// Check if sufficient funds
			if (currentBalance < spendAmount) {
				throw new Error(`Insufficient funds. Current balance: ${currentBalance}, Required: ${spendAmount}`);
			}

			// Create billing transaction record
			const [transaction] = await trx("dtm_ads.billing_transactions")
				.insert({
					advertiser_id: userId,
					amount: -spendAmount, // Negative for spend
					currency: balance.currency,
					description,
					status: "completed",
					campaign_id: campaignId,
					created_at: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			// Update balance
			const newBalance = currentBalance - spendAmount;
			const [updatedBalance] = await trx("dtm_ads.advertiser_balances")
				.where({ user_id: userId })
				.update({
					balance: newBalance,
					last_transaction_id: transaction.id,
					last_updated: new Date(),
					updated_at: new Date(),
				})
				.returning("*");

			await trx.commit();

			logger.info("Spend deducted from advertiser balance", {
				userId,
				amount: spendAmount,
				newBalance,
				campaignId,
				transactionId: transaction.id,
			});

			// Check for low balance warning (less than $10)
			if (newBalance < 10) {
				await this.createLowBalanceNotification(userId, newBalance);
			}

			return {
				balance: updatedBalance,
				transaction,
			};
		} catch (error) {
			await trx.rollback();
			logger.error("Error deducting spend", { error, userId, amount, campaignId });
			throw error;
		}
	}

	/**
	 * Check if user has sufficient funds for a campaign
	 * @param {number} userId - User ID
	 * @param {number} requiredAmount - Required amount
	 * @returns {Promise<boolean>} Whether user has sufficient funds
	 */
	async hasSufficientFunds(userId, requiredAmount) {
		try {
			const balance = await this.getAdvertiserBalance(userId);
			return parseFloat(balance.balance) >= parseFloat(requiredAmount);
		} catch (error) {
			logger.error("Error checking sufficient funds", { error, userId, requiredAmount });
			throw error;
		}
	}

	/**
	 * Get spending history for a user
	 * @param {number} userId - User ID
	 * @param {number} limit - Number of transactions to return
	 * @param {number} offset - Offset for pagination
	 * @returns {Promise<Array>} Transaction history
	 */
	async getSpendingHistory(userId, limit = 50, offset = 0) {
		try {
			const transactions = await db("dtm_ads.billing_transactions")
				.where({ advertiser_id: userId })
				.orderBy("created_at", "desc")
				.limit(limit)
				.offset(offset);

			return transactions;
		} catch (error) {
			logger.error("Error getting spending history", { error, userId });
			throw error;
		}
	}

	/**
	 * Get campaign spend summary
	 * @param {number} campaignId - Campaign ID
	 * @returns {Promise<Object>} Spend summary
	 */
	async getCampaignSpendSummary(campaignId) {
		try {
			const spendSummary = await db("dtm_ads.billing_transactions")
				.where({ campaign_id: campaignId })
				.where("amount", "<", 0) // Only spend transactions (negative amounts)
				.select(
					db.raw("SUM(ABS(amount)) as total_spend"),
					db.raw("COUNT(*) as transaction_count"),
					db.raw("MIN(created_at) as first_spend"),
					db.raw("MAX(created_at) as last_spend")
				)
				.first();

			return {
				campaign_id: campaignId,
				total_spend: parseFloat(spendSummary.total_spend) || 0,
				transaction_count: parseInt(spendSummary.transaction_count) || 0,
				first_spend: spendSummary.first_spend,
				last_spend: spendSummary.last_spend,
			};
		} catch (error) {
			logger.error("Error getting campaign spend summary", { error, campaignId });
			throw error;
		}
	}

	/**
	 * Create low balance notification
	 * @param {number} userId - User ID
	 * @param {number} currentBalance - Current balance amount
	 * @returns {Promise<void>}
	 */
	async createLowBalanceNotification(userId, currentBalance) {
		try {
			const notificationService = new NotificationService();
			await notificationService.sendNotification({
				user_id: userId,
				type: "low_balance",
				title: "Low Balance Warning",
				message: `Your account balance is low ($${currentBalance.toFixed(
					2
				)}). Please add funds to continue running campaigns.`,
				metadata: { balance: currentBalance },
				priority: "high",
				email: {
					enabled: true,
					subject: "Low Balance Warning - BTDash",
					template: "low_balance",
					templateData: {
						balance: currentBalance.toFixed(2),
					},
				},
			});

			logger.info("Low balance notification sent", { userId, currentBalance });
		} catch (error) {
			logger.error("Error sending low balance notification", { error, userId, currentBalance });
			// Don't throw error for notification creation failure
		}
	}

	/**
	 * Process real-time spend tracking for impressions/clicks
	 * @param {number} adId - Ad ID
	 * @param {string} eventType - 'impression' or 'click'
	 * @returns {Promise<Object>} Spend processing result
	 */
	async processRealTimeSpend(adId, eventType) {
		try {
			// Get ad and campaign info
			const ad = await db("dtm_ads.ads")
				.leftJoin("dtm_ads.ad_campaigns", "ads.campaign_id", "ad_campaigns.id")
				.where("ads.id", adId)
				.select(
					"ads.*",
					"ad_campaigns.advertiser_id",
					"ad_campaigns.budget_cpc",
					"ad_campaigns.budget_cpm",
					"ad_campaigns.total_budget"
				)
				.first();

			if (!ad) {
				throw new Error("Ad not found");
			}

			let spendAmount = 0;
			let description = "";

			if (eventType === "click" && ad.budget_cpc) {
				spendAmount = parseFloat(ad.budget_cpc);
				description = `CPC spend for ad ${adId}`;
			} else if (eventType === "impression" && ad.budget_cpm) {
				spendAmount = parseFloat(ad.budget_cpm) / 1000; // CPM is per 1000 impressions
				description = `CPM spend for ad ${adId}`;
			}

			if (spendAmount > 0) {
				// Check if user has sufficient funds
				const hasFunds = await this.hasSufficientFunds(ad.advertiser_id, spendAmount);

				if (!hasFunds) {
					// Pause the campaign due to insufficient funds
					await db("dtm_ads.ad_campaigns")
						.where({ id: ad.campaign_id })
						.update({ status: "paused", updated_at: new Date() });

					// Ads inherit the campaign status
					await db("dtm_ads.ads")
						.where({ campaign_id: ad.campaign_id })
						.update({ status: "paused", updated_at: new Date() });

					logger.warn("Campaign paused due to insufficient funds", {
						campaignId: ad.campaign_id,
						advertiserId: ad.advertiser_id,
						requiredAmount: spendAmount,
					});

					return {
						success: false,
						reason: "insufficient_funds",
						campaign_paused: true,
					};
				}

				// Deduct spend
				const result = await this.deductSpend(ad.advertiser_id, spendAmount, ad.campaign_id, description);

				return {
					success: true,
					spend_amount: spendAmount,
					new_balance: result.balance.balance,
					transaction_id: result.transaction.id,
				};
			}

			return {
				success: true,
				spend_amount: 0,
				message: "No spend required for this event type",
			};
		} catch (error) {
			logger.error("Error processing real-time spend", { error, adId, eventType });
			throw error;
		}
	}
}

module.exports = BudgetService;
