const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

class TargetingService {
	/**
	 * Create targeting rules for an ad
	 * @param {number} adId - Ad ID
	 * @param {Object} targetingRules - Targeting rules object
	 * @returns {Promise<Array>} Created targeting rules
	 */
	async createTargetingRules(adId, targetingRules) {
		const trx = await db.transaction();

		try {
			// Delete existing targeting rules for this ad
			await trx("dtm_ads.ad_targets").where({ ad_id: adId }).del();

			const rules = [];

			// Process geographic targeting
			if (targetingRules.countries && targetingRules.countries.length > 0) {
				for (const country of targetingRules.countries) {
					rules.push({
						ad_id: adId,
						key: "country_code",
						value: country,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process region/state targeting
			if (targetingRules.regions && targetingRules.regions.length > 0) {
				for (const region of targetingRules.regions) {
					rules.push({
						ad_id: adId,
						key: "region",
						value: region,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process device type targeting
			if (targetingRules.devices && targetingRules.devices.length > 0) {
				for (const device of targetingRules.devices) {
					rules.push({
						ad_id: adId,
						key: "device_type",
						value: device,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process language targeting
			if (targetingRules.languages && targetingRules.languages.length > 0) {
				for (const language of targetingRules.languages) {
					rules.push({
						ad_id: adId,
						key: "language",
						value: language,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process age range targeting
			if (targetingRules.age_min || targetingRules.age_max) {
				if (targetingRules.age_min) {
					rules.push({
						ad_id: adId,
						key: "age",
						value: targetingRules.age_min.toString(),
						operator: "gte",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
				if (targetingRules.age_max) {
					rules.push({
						ad_id: adId,
						key: "age",
						value: targetingRules.age_max.toString(),
						operator: "lte",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process gender targeting
			if (targetingRules.genders && targetingRules.genders.length > 0) {
				for (const gender of targetingRules.genders) {
					rules.push({
						ad_id: adId,
						key: "gender",
						value: gender,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process interest targeting
			if (targetingRules.interests && targetingRules.interests.length > 0) {
				for (const interest of targetingRules.interests) {
					rules.push({
						ad_id: adId,
						key: "interest",
						value: interest,
						operator: "contains",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Process time-based targeting
			if (targetingRules.time_zones && targetingRules.time_zones.length > 0) {
				for (const timezone of targetingRules.time_zones) {
					rules.push({
						ad_id: adId,
						key: "timezone",
						value: timezone,
						operator: "equals",
						created_at: new Date(),
						updated_at: new Date(),
					});
				}
			}

			// Insert all rules
			if (rules.length > 0) {
				await trx("dtm_ads.ad_targets").insert(rules);
			}

			await trx.commit();

			logger.info("Targeting rules created", { adId, rulesCount: rules.length });
			return rules;
		} catch (error) {
			await trx.rollback();
			logger.error("Error creating targeting rules", { error, adId, targetingRules });
			throw error;
		}
	}

	/**
	 * Get targeting rules for an ad
	 * @param {number} adId - Ad ID
	 * @returns {Promise<Object>} Targeting rules object
	 */
	async getTargetingRules(adId) {
		try {
			const rules = await db("dtm_ads.ad_targets").where({ ad_id: adId }).select("key", "value", "operator");

			// Group rules by key
			const targetingRules = {
				countries: [],
				regions: [],
				devices: [],
				languages: [],
				genders: [],
				interests: [],
				time_zones: [],
				age_min: null,
				age_max: null,
			};

			for (const rule of rules) {
				switch (rule.key) {
					case "country_code":
						targetingRules.countries.push(rule.value);
						break;
					case "region":
						targetingRules.regions.push(rule.value);
						break;
					case "device_type":
						targetingRules.devices.push(rule.value);
						break;
					case "language":
						targetingRules.languages.push(rule.value);
						break;
					case "gender":
						targetingRules.genders.push(rule.value);
						break;
					case "interest":
						targetingRules.interests.push(rule.value);
						break;
					case "timezone":
						targetingRules.time_zones.push(rule.value);
						break;
					case "age":
						if (rule.operator === "gte") {
							targetingRules.age_min = parseInt(rule.value);
						} else if (rule.operator === "lte") {
							targetingRules.age_max = parseInt(rule.value);
						}
						break;
				}
			}

			return targetingRules;
		} catch (error) {
			logger.error("Error getting targeting rules", { error, adId });
			throw error;
		}
	}

	/**
	 * Check if a user/request matches targeting criteria
	 * @param {number} adId - Ad ID
	 * @param {Object} userContext - User context (country, device, etc.)
	 * @returns {Promise<boolean>} Whether user matches targeting
	 */
	async matchesTargeting(adId, userContext) {
		try {
			const rules = await db("dtm_ads.ad_targets").where({ ad_id: adId }).select("key", "value", "operator");

			// If no targeting rules, show to everyone
			if (rules.length === 0) {
				return true;
			}

			// Check each rule
			for (const rule of rules) {
				const contextValue = userContext[rule.key];

				if (!contextValue) {
					// If we don't have the context value, skip this rule
					continue;
				}

				const matches = this.evaluateRule(contextValue, rule.value, rule.operator);

				// If any rule doesn't match, exclude this user
				if (!matches) {
					return false;
				}
			}

			return true;
		} catch (error) {
			logger.error("Error checking targeting match", { error, adId, userContext });
			// On error, default to showing the ad
			return true;
		}
	}

	/**
	 * Evaluate a single targeting rule
	 * @param {string} contextValue - Value from user context
	 * @param {string} ruleValue - Value from targeting rule
	 * @param {string} operator - Comparison operator
	 * @returns {boolean} Whether rule matches
	 */
	evaluateRule(contextValue, ruleValue, operator) {
		switch (operator) {
			case "equals":
				return contextValue === ruleValue;
			case "contains":
				return contextValue.toLowerCase().includes(ruleValue.toLowerCase());
			case "startsWith":
				return contextValue.toLowerCase().startsWith(ruleValue.toLowerCase());
			case "endsWith":
				return contextValue.toLowerCase().endsWith(ruleValue.toLowerCase());
			case "in":
				const values = ruleValue.split(",").map((v) => v.trim());
				return values.includes(contextValue);
			case "notIn":
				const excludeValues = ruleValue.split(",").map((v) => v.trim());
				return !excludeValues.includes(contextValue);
			case "gte":
				return parseFloat(contextValue) >= parseFloat(ruleValue);
			case "lte":
				return parseFloat(contextValue) <= parseFloat(ruleValue);
			case "gt":
				return parseFloat(contextValue) > parseFloat(ruleValue);
			case "lt":
				return parseFloat(contextValue) < parseFloat(ruleValue);
			default:
				return true;
		}
	}

	/**
	 * Get available targeting options
	 * @returns {Promise<Object>} Available targeting options
	 */
	async getTargetingOptions() {
		try {
			// This would typically come from a database or external service
			// For now, return static options
			return {
				countries: [
					{ code: "US", name: "United States" },
					{ code: "CA", name: "Canada" },
					{ code: "GB", name: "United Kingdom" },
					{ code: "DE", name: "Germany" },
					{ code: "FR", name: "France" },
					{ code: "AU", name: "Australia" },
					{ code: "JP", name: "Japan" },
					{ code: "BR", name: "Brazil" },
					{ code: "IN", name: "India" },
					{ code: "CN", name: "China" },
				],
				devices: [
					{ value: "desktop", name: "Desktop" },
					{ value: "mobile", name: "Mobile" },
					{ value: "tablet", name: "Tablet" },
				],
				languages: [
					{ code: "en", name: "English" },
					{ code: "es", name: "Spanish" },
					{ code: "fr", name: "French" },
					{ code: "de", name: "German" },
					{ code: "it", name: "Italian" },
					{ code: "pt", name: "Portuguese" },
					{ code: "ja", name: "Japanese" },
					{ code: "zh", name: "Chinese" },
				],
				genders: [
					{ value: "male", name: "Male" },
					{ value: "female", name: "Female" },
					{ value: "other", name: "Other" },
				],
				interests: [
					"Technology",
					"Finance",
					"Health",
					"Sports",
					"Travel",
					"Food",
					"Fashion",
					"Gaming",
					"Music",
					"Movies",
					"Books",
					"Art",
					"Business",
					"Education",
				],
				age_ranges: [
					{ min: 18, max: 24, name: "18-24" },
					{ min: 25, max: 34, name: "25-34" },
					{ min: 35, max: 44, name: "35-44" },
					{ min: 45, max: 54, name: "45-54" },
					{ min: 55, max: 64, name: "55-64" },
					{ min: 65, max: null, name: "65+" },
				],
			};
		} catch (error) {
			logger.error("Error getting targeting options", { error });
			throw error;
		}
	}
}

module.exports = TargetingService;
