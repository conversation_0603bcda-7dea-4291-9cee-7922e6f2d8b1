"use client";

import type React from "react";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function SubnetSearch() {
  const [searchTerm, setSearchTerm] = useState("");
  const [category, setCategory] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    //console.log("Searching for:", searchTerm, "in category:", category)
    // Implement search logic here
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Search Subnets</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSearch} className="space-y-4">
          <Input
            type="text"
            placeholder="Search subnets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ai">AI & Machine Learning</SelectItem>
              <SelectItem value="data">Data Validation</SelectItem>
              <SelectItem value="storage">Storage</SelectItem>
              <SelectItem value="compute">Edge Computing</SelectItem>
            </SelectContent>
          </Select>
          <Button type="submit" className="w-full">
            Search
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
