// app/jobs/page.tsx

import { auth0 } from "@/lib/auth0";
import { fetchWithFallback } from "@/lib/data/utils";
import { generateSEOMetadata } from "@/lib/seo/generateMetadata";
import type { Metadata } from "next";
import { cookies } from "next/headers";
import JobsClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

// Static SEO metadata for the job listing page
export const metadata: Metadata = generateSEOMetadata({
  title: "Jobs | DynamicTaoMarketCap",
  description: "Explore career opportunities in the TAO ecosystem.",
  url: "https://dynamictaomarketcap.com/jobs",
  image: "/default-job-og.jpg",
});

export default async function JobsPage() {
  // Fetch all relevant data in parallel
  const [jobsRes, companyRes, categoriesRes, subnetsRes, productsRes] =
    await Promise.all([
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/jobs`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/companies`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/categories`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/subnets`),
      fetchWithFallback(`${process.env.APP_BASE_URL}/api/products`),
    ]);

  let authorized_job_admin = false;

  // Check if user is logged in
  const session = await auth0.getSession();
  if (session) {
    const cookieHeader = (await cookies()).toString();
    const userData = await fetchWithFallback(
      `${process.env.APP_BASE_URL}/api/user/me`,
      {
        headers: { Cookie: cookieHeader },
      }
    );

    if (userData.error) {
      console.error("User fetch error", userData.error);
    } else if (userData.data) {
      authorized_job_admin = userData.data.authorized_events_admin;
    }
  }

  // Log fetch errors
  if (jobsRes.error) console.error("Jobs fetch error", jobsRes.error);
  if (companyRes.error) console.error("Company fetch error", companyRes.error);
  if (categoriesRes.error)
    console.error("Categories fetch error", categoriesRes.error);
  if (subnetsRes.error) console.error("Subnets fetch error", subnetsRes.error);
  if (productsRes.error)
    console.error("Products fetch error", productsRes.error);

  // Enrich job data with associations
  const enrichedJobs = jobsRes.data.map((job) => ({
    ...job,
    company: companyRes.data.find((c) => c.id === job.company_id),
    categories: categoriesRes.data.filter((cat) =>
      job.category_ids?.includes(cat.id)
    ),
    subnets: subnetsRes.data.filter((s) => job.subnet_ids?.includes(s.netuid)),
    products: productsRes.data.filter((p) => job.product_ids?.includes(p.id)),
  }));

  return (
    <JobsClientWrapper
      jobs={enrichedJobs}
      authorized_job_admin={authorized_job_admin}
    />
  );
}
