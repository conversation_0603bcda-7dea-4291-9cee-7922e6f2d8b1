'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  X, 
  CreditCard, 
  RefreshCw, 
  Mail,
  Clock,
  CheckCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface PaymentErrorNotificationProps {
  campaignId: string;
  campaignName: string;
  errorType: 'payment_failed' | 'payment_error';
  errorMessage: string;
  retryUrl?: string;
  onDismiss?: () => void;
  onRetry?: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export function PaymentErrorNotification({
  campaignId,
  campaignName,
  errorType,
  errorMessage,
  retryUrl,
  onDismiss,
  onRetry,
  autoHide = false,
  autoHideDelay = 10000,
}: PaymentErrorNotificationProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);
  const [timeLeft, setTimeLeft] = useState(autoHide ? Math.floor(autoHideDelay / 1000) : 0);

  useEffect(() => {
    if (autoHide && timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (autoHide && timeLeft === 0) {
      handleDismiss();
    }
  }, [autoHide, timeLeft]);

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      if (onRetry) {
        await onRetry();
      } else if (retryUrl) {
        window.location.href = retryUrl;
      }
      toast.success('Redirecting to payment...');
    } catch (error) {
      toast.error('Failed to retry payment. Please try again.');
    } finally {
      setIsRetrying(false);
    }
  };

  const handleContactSupport = () => {
    const subject = encodeURIComponent(`Payment Issue - Campaign: ${campaignName} (${campaignId})`);
    const body = encodeURIComponent(`
Hello BTDash Support,

I'm experiencing a payment issue with my campaign:

Campaign ID: ${campaignId}
Campaign Name: ${campaignName}
Error Type: ${errorType}
Error Message: ${errorMessage}

Please help me resolve this issue.

Thank you,
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, '_blank');
  };

  if (!isVisible) {
    return null;
  }

  const isPaymentError = errorType === 'payment_error';
  const isPaymentFailed = errorType === 'payment_failed';

  return (
    <Card className={`border-l-4 ${isPaymentError ? 'border-l-orange-500' : 'border-l-red-500'} shadow-lg`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${isPaymentError ? 'bg-orange-100' : 'bg-red-100'}`}>
              <AlertTriangle className={`h-5 w-5 ${isPaymentError ? 'text-orange-600' : 'text-red-600'}`} />
            </div>
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                {isPaymentError ? 'Payment Processing Error' : 'Payment Failed'}
                <Badge variant={isPaymentError ? 'secondary' : 'destructive'}>
                  {errorType.replace('_', ' ').toUpperCase()}
                </Badge>
              </CardTitle>
              <CardDescription className="text-sm">
                Campaign: <span className="font-medium">{campaignName}</span>
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {autoHide && timeLeft > 0 && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                {timeLeft}s
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <Alert variant={isPaymentError ? 'default' : 'destructive'}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Error Details:</strong> {errorMessage}
          </AlertDescription>
        </Alert>

        {isPaymentError && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-blue-800 text-sm">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">Our team has been automatically notified</span>
            </div>
            <p className="text-blue-700 text-xs mt-1">
              We're working to resolve this issue. You can retry payment or contact support for immediate assistance.
            </p>
          </div>
        )}

        {isPaymentFailed && (
          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-yellow-800 text-sm">
              <span className="font-medium">Common solutions:</span>
              <ul className="list-disc list-inside mt-1 text-xs space-y-1">
                <li>Check your payment method details</li>
                <li>Ensure sufficient funds are available</li>
                <li>Try a different payment method</li>
                <li>Contact your bank if the issue persists</li>
              </ul>
            </div>
          </div>
        )}

        <div className="flex gap-3 pt-2">
          <Button 
            onClick={handleRetry}
            disabled={isRetrying}
            className="flex-1"
          >
            {isRetrying ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Retrying...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Retry Payment
              </>
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleContactSupport}
            className="flex-1"
          >
            <Mail className="mr-2 h-4 w-4" />
            Contact Support
          </Button>
        </div>

        <div className="text-xs text-muted-foreground text-center">
          Campaign ID: {campaignId} • Need help? Email <EMAIL>
        </div>
      </CardContent>
    </Card>
  );
}
