"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Filter, Loader2, RefreshCw } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import useSWR from "swr";

const fetcher = (url: string) =>
	fetch(url)
		.then((res) => res.json())
		.then((data) => {
			if (!data.success) {
				throw new Error(data.message || "API request failed");
			}
			return data.data;
		});

export default function AdminActivityPage() {
	const { toast } = useToast();
	const [limit, setLimit] = useState("50");

	// Fetch recent activity
	const {
		data: activities,
		error,
		isLoading,
		mutate,
	} = useSWR(`/api/admin/recent-activity?limit=${limit}`, fetcher, {
		refreshInterval: 30000,
	});

	const handleRefresh = () => {
		mutate();
		toast({
			title: "Refreshed",
			description: "Activity data has been refreshed.",
		});
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		const now = new Date();
		const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

		if (diffInHours < 1) {
			const diffInMinutes = Math.floor(diffInHours * 60);
			return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
		} else if (diffInHours < 24) {
			const hours = Math.floor(diffInHours);
			return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
		} else if (diffInHours < 48) {
			return "Yesterday";
		} else {
			return date.toLocaleDateString();
		}
	};

	const getActivityIcon = (type: string) => {
		switch (type) {
			case 'admin_action':
				return '⚡';
			case 'user_registration':
				return '👤';
			default:
				return '📝';
		}
	};

	const getActivityColor = (activityTitle: string) => {
		if (activityTitle.includes('Approved')) return 'text-green-600';
		if (activityTitle.includes('Rejected')) return 'text-red-600';
		if (activityTitle.includes('Paused')) return 'text-yellow-600';
		if (activityTitle.includes('Resumed')) return 'text-blue-600';
		return 'text-gray-600';
	};

	if (isLoading) {
		return (
			<div className="container mx-auto flex h-[70vh] items-center justify-center p-6">
				<div className="flex flex-col items-center">
					<Loader2 className="h-12 w-12 animate-spin text-primary" />
					<p className="mt-4 text-lg">Loading activity data...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6">
			{/* Header */}
			<div className="mb-6 flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Link href="/admin">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="mr-2 h-4 w-4" />
							Back to Dashboard
						</Button>
					</Link>
					<div>
						<h1 className="text-3xl font-bold">Platform Activity</h1>
						<p className="text-muted-foreground">Complete history of admin actions and platform events</p>
					</div>
				</div>
				<div className="flex items-center gap-2">
					<Select value={limit} onValueChange={setLimit}>
						<SelectTrigger className="w-32">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="25">25 items</SelectItem>
							<SelectItem value="50">50 items</SelectItem>
							<SelectItem value="100">100 items</SelectItem>
						</SelectContent>
					</Select>
					<Button variant="outline" size="sm" onClick={handleRefresh}>
						<RefreshCw className="mr-2 h-4 w-4" />
						Refresh
					</Button>
				</div>
			</div>

			{/* Activity List */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Recent Activity
					</CardTitle>
					<CardDescription>
						Showing {activities?.length || 0} most recent activities
					</CardDescription>
				</CardHeader>
				<CardContent>
					{activities && activities.length > 0 ? (
						<div className="space-y-4">
							{activities.map((activity: any, index: number) => (
								<div
									key={activity.id}
									className="flex items-start gap-4 rounded-lg border p-4 transition-colors hover:bg-muted/50"
								>
									<div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted text-lg">
										{getActivityIcon(activity.type)}
									</div>
									<div className="flex-1 space-y-1">
										<div className="flex items-center justify-between">
											<h3 className={`font-medium ${getActivityColor(activity.title)}`}>
												{activity.title}
											</h3>
											<span className="text-sm text-muted-foreground">
												{formatDate(activity.created_at)}
											</span>
										</div>
										<p className="text-sm text-muted-foreground">{activity.description}</p>
										{activity.metadata && (
											<div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
												{activity.metadata.admin_name && (
													<span className="rounded bg-muted px-2 py-1">
														Admin: {activity.metadata.admin_name}
													</span>
												)}
												{activity.metadata.advertiser_name && (
													<span className="rounded bg-muted px-2 py-1">
														Advertiser: {activity.metadata.advertiser_name}
													</span>
												)}
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="flex flex-col items-center justify-center py-12 text-center">
							<Calendar className="mb-4 h-12 w-12 text-muted-foreground" />
							<h3 className="text-lg font-medium">No activity found</h3>
							<p className="text-sm text-muted-foreground">
								Recent admin actions and platform events will appear here.
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
