# Btdashs API

This is a private RESTful API built with Node.js to manage and store subnet data fetched from [TaoStats API](https://docs.taostats.io/reference/get-subnets-1). The API processes and stores subnet data periodically for later retrieval and analysis.

## Features
- Fetch subnet data from TaoStats API at scheduled intervals.
- Store and manage subnet information in a PostgreSQL database.
- Provide RESTful endpoints to query subnet data.
- Secure access with authentication (TBD: API Key, JWT, or IP whitelisting)
- Scheduled updates at configurable intervals

## Tech Stack
- **Backend:** Node.js (Express)
- **Database:** PostgreSQL
- **External API:** TaoStats API
- **Auth:** API Keys or JWT (TBD)
- **Scheduler:** Cron jobs (for periodic updates)

### Folder Structure
```
📂 btdashs_api
 ┣ 📂 src
 ┃ ┣ 📂 application      # Business logic (use cases)
 ┃ ┃ ┣ 📂 services       # Services interacting with the domain (processing Taostats data)
 ┃ ┃ ┗ 📂 dtos           # Data Transfer Objects
 ┃ ┣ 📂 domain           # Entities & Business rules (subnets)
 ┃ ┣ 📂 infrastructure   # External services (Taostats API, DB, etc.)
 ┃ ┃ ┣ 📂 taostats       # Taostats API integration (fetching)
 ┃ ┃ ┣ 📂 database       # Database handlers
 ┃ ┃ ┗ 📂 security       # Auth & Security handlers
 ┃ ┣ 📂 presentation     # Routes & Controllers
 ┃ ┃ ┣ 📂 routes         # Express routes (e.g., `/subnets`, `/update`)
 ┃ ┃ ┗ 📂 controllers    # Controllers handling API requests
 ┃ ┣ 📂 config           # Configuration files (intervals, etc.)
 ┃ ┣ 📂 cronjobs         # Scheduled tasks
 ┃ ┗ 📂 utils            # Helper functions
 ┣ 📄 server.js          # Entry point
 ┣ 📄 .env               # Environment variables
 ┣ 📄 .gitignore         # Ignore node_modules, .env, etc.
 ┣ 📄 package.json       # Project metadata
 ┗ 📄 README.md          # Project documentation

```

## Database Schema

The API uses a **PostgreSQL** database with the following main tables:

- **categories**: Stores subnet categories.
- **subnets**: Main table storing subnet details.
- **subnet_metrics**: Stores historical subnet performance data.
- **network_prices**: Tracks TAO market prices and supply data.
- **news**: Stores news articles related to subnets.
- **validator**: Stores validator information.
- **validator_subnet_performance**: Tracks validator performance in subnets.

Indexes are used to optimize queries on timestamps, categories, and relationships.

## API Endpoints

### Categories Management
| Method  | Endpoint           | Description                         |
|---------|-------------------|-------------------------------------|
| **GET**  | `/categories`      | Get all categories                 |
| **GET**  | `/categories/:id`  | Get details of a specific category |
| **POST** | `/categories`      | Create a new category              |
| **PUT**  | `/categories/:id`  | Update an existing category        |
| **DELETE** | `/categories/:id` | Delete a category                  |

### Subnets Management
| Method  | Endpoint         | Description                         |
|---------|-----------------|-------------------------------------|
| **GET**  | `/subnets`      | Get all stored subnet data         |
| **GET**  | `/subnets/:id`  | Get details of a specific subnet   |
| **POST** | `/subnets`      | Create a new subnet                |
| **PUT**  | `/subnets/:id`  | Update an existing subnet          |
| **DELETE** | `/subnets/:id` | Delete a subnet                    |

### Subnet Metrics
| Method  | Endpoint               | Description                           |
|---------|-----------------------|---------------------------------------|
| **GET**  | `/subnet-metrics`      | Get all subnet metrics               |
| **GET**  | `/subnet-metrics/:id`  | Get details of specific subnet metrics |
| **POST** | `/subnet-metrics`      | Record new subnet metrics            |
| **PUT**  | `/subnet-metrics/:id`  | Update existing subnet metrics       |
| **DELETE** | `/subnet-metrics/:id` | Delete subnet metrics               |

### Network Prices
| Method  | Endpoint             | Description                              |
|---------|---------------------|------------------------------------------|
| **GET**  | `/network-prices`    | Get all network price data              |
| **GET**  | `/network-prices/:id` | Get details of specific network price data |
| **POST** | `/network-prices`    | Record new network price data           |
| **PUT**  | `/network-prices/:id` | Update existing network price data      |
| **DELETE** | `/network-prices/:id` | Delete network price data             |

### News Management
| Method  | Endpoint      | Description                           |
|---------|------------|---------------------------------------|
| **GET**  | `/news`     | Get all news articles               |
| **GET**  | `/news/:id` | Get details of a specific news article |
| **POST** | `/news`     | Create a new news article           |
| **PUT**  | `/news/:id` | Update an existing news article     |
| **DELETE** | `/news/:id` | Delete a news article              |

### Validator Management
| Method  | Endpoint        | Description                           |
|---------|---------------|---------------------------------------|
| **GET**  | `/validators`  | Get all validators                  |
| **GET**  | `/validators/:id` | Get details of a specific validator |
| **POST** | `/validators`  | Create a new validator              |
| **PUT**  | `/validators/:id` | Update an existing validator       |
| **DELETE** | `/validators/:id` | Delete a validator               |

### Validator Subnet Performance
| Method  | Endpoint                   | Description                                  |
|---------|---------------------------|----------------------------------------------|
| **GET**  | `/validator-performance`    | Get all validator performance data         |
| **GET**  | `/validator-performance/:id` | Get details of specific validator performance data |
| **POST** | `/validator-performance`    | Record new validator performance data      |
| **PUT**  | `/validator-performance/:id` | Update existing validator performance data |
| **DELETE** | `/validator-performance/:id` | Delete validator performance data         |

### Data Update & Cron Jobs
| Method  | Endpoint         | Description                         |
|---------|----------------|-------------------------------------|
| **POST** | `/update/subnets` | Fetch & store latest subnet data |
| **POST** | `/update/metrics`                 | Fetch & store latest subnet metrics              |
| **POST** | `/update/prices`                  | Fetch & store latest network prices              |
| **POST** | `/update/news`                    | Fetch & store latest news articles               |
| **POST** | `/update/validators`              | Fetch & store latest validator data              |
| **POST** | `/update/validator-performance`   | Fetch & store latest validator performance data  |
| **GET**  | `/update/status`  | Get last update status          |

## Setup & Installation
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <project-folder>
   ```
2. Install dependencies:
   ```bash
   npm install
   ```
3. Create a `.env` file with necessary configurations:
   ```env
   PORT=<your-port>
   TAOSTATS_API_KEY=<your-api-key>
   TAOSTATS_API_URL=<taostats-api-url>
   DATABASE_URL=<your-database-url>
   ```
4. Start the server:
   ```bash
   npm start
   ```

## License
This project is private and not intended for public use.

