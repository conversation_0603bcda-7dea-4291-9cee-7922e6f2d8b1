"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-array";
exports.ids = ["vendor-chunks/d3-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-array/src/array.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/array.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\n\nvar slice = array.slice;\nvar map = array.map;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FycmF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7O0FBRU87QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGFycmF5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBhcnJheSA9IEFycmF5LnByb3RvdHlwZTtcblxuZXhwb3J0IHZhciBzbGljZSA9IGFycmF5LnNsaWNlO1xuZXhwb3J0IHZhciBtYXAgPSBhcnJheS5tYXA7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ascending.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/ascending.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ascending)\n/* harmony export */ });\nfunction ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FzY2VuZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcYXNjZW5kaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGFzY2VuZGluZyhhLCBiKSB7XG4gIHJldHVybiBhID09IG51bGwgfHwgYiA9PSBudWxsID8gTmFOIDogYSA8IGIgPyAtMSA6IGEgPiBiID8gMSA6IGEgPj0gYiA/IDAgOiBOYU47XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bin.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/bin.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bin)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-array/src/array.js\");\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-array/src/constant.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-array/src/identity.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n\n\n\n\n\n\n\n\n\nfunction bin() {\n  var value = _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n      domain = _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n      threshold = _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) [x0, x1] = (0,_nice_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(x0, x1, tn);\n      tz = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === _extent_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n          const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_4__.tickIncrement)(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length, a = 0, b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[(0,_bisect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(Array.isArray(_) ? _array_js__WEBPACK_IMPORTED_MODULE_7__.slice.call(_) : _), histogram) : threshold;\n  };\n\n  return histogram;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisect.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/bisect.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bisectCenter: () => (/* binding */ bisectCenter),\n/* harmony export */   bisectLeft: () => (/* binding */ bisectLeft),\n/* harmony export */   bisectRight: () => (/* binding */ bisectRight),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n\n\n\n\nconst ascendingBisect = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst bisectRight = ascendingBisect.right;\nconst bisectLeft = ascendingBisect.left;\nconst bisectCenter = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_number_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).center;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bisectRight);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Jpc2VjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVDO0FBQ0Y7QUFDSjs7QUFFakMsd0JBQXdCLHdEQUFRLENBQUMscURBQVM7QUFDbkM7QUFDQTtBQUNBLHFCQUFxQix3REFBUSxDQUFDLGtEQUFNO0FBQzNDLGlFQUFlLFdBQVcsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGJpc2VjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IGJpc2VjdG9yIGZyb20gXCIuL2Jpc2VjdG9yLmpzXCI7XG5pbXBvcnQgbnVtYmVyIGZyb20gXCIuL251bWJlci5qc1wiO1xuXG5jb25zdCBhc2NlbmRpbmdCaXNlY3QgPSBiaXNlY3Rvcihhc2NlbmRpbmcpO1xuZXhwb3J0IGNvbnN0IGJpc2VjdFJpZ2h0ID0gYXNjZW5kaW5nQmlzZWN0LnJpZ2h0O1xuZXhwb3J0IGNvbnN0IGJpc2VjdExlZnQgPSBhc2NlbmRpbmdCaXNlY3QubGVmdDtcbmV4cG9ydCBjb25zdCBiaXNlY3RDZW50ZXIgPSBiaXNlY3RvcihudW1iZXIpLmNlbnRlcjtcbmV4cG9ydCBkZWZhdWx0IGJpc2VjdFJpZ2h0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisector.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/bisector.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bisector)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n\n\n\nfunction bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    compare2 = (d, x) => (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || f === _descending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"] ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/blur.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/blur.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blur: () => (/* binding */ blur),\n/* harmony export */   blur2: () => (/* binding */ blur2),\n/* harmony export */   blurImage: () => (/* binding */ blurImage)\n/* harmony export */ });\nfunction blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\n\nconst blur2 = Blur2(blurf);\n\nconst blurImage = Blur2(blurfImage);\n\nfunction Blur2(blur) {\n  return function(data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {data: values, width, height} = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || (!rx && !ry)) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\n\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\n\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\n\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/blur.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/constant.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/constant.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ constant)\n/* harmony export */ });\nfunction constant(x) {\n  return () => x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxjb25zdGFudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjb25zdGFudCh4KSB7XG4gIHJldHVybiAoKSA9PiB4O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/count.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/count.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ count)\n/* harmony export */ });\nfunction count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2NvdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGNvdW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNvdW50KHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgY291bnQgPSAwO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQ7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICArK2NvdW50O1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gY291bnQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/count.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/cross.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/cross.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cross)\n/* harmony export */ });\nfunction length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nfunction cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Nyb3NzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGNyb3NzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGxlbmd0aChhcnJheSkge1xuICByZXR1cm4gYXJyYXkubGVuZ3RoIHwgMDtcbn1cblxuZnVuY3Rpb24gZW1wdHkobGVuZ3RoKSB7XG4gIHJldHVybiAhKGxlbmd0aCA+IDApO1xufVxuXG5mdW5jdGlvbiBhcnJheWlmeSh2YWx1ZXMpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZXMgIT09IFwib2JqZWN0XCIgfHwgXCJsZW5ndGhcIiBpbiB2YWx1ZXMgPyB2YWx1ZXMgOiBBcnJheS5mcm9tKHZhbHVlcyk7XG59XG5cbmZ1bmN0aW9uIHJlZHVjZXIocmVkdWNlKSB7XG4gIHJldHVybiB2YWx1ZXMgPT4gcmVkdWNlKC4uLnZhbHVlcyk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNyb3NzKC4uLnZhbHVlcykge1xuICBjb25zdCByZWR1Y2UgPSB0eXBlb2YgdmFsdWVzW3ZhbHVlcy5sZW5ndGggLSAxXSA9PT0gXCJmdW5jdGlvblwiICYmIHJlZHVjZXIodmFsdWVzLnBvcCgpKTtcbiAgdmFsdWVzID0gdmFsdWVzLm1hcChhcnJheWlmeSk7XG4gIGNvbnN0IGxlbmd0aHMgPSB2YWx1ZXMubWFwKGxlbmd0aCk7XG4gIGNvbnN0IGogPSB2YWx1ZXMubGVuZ3RoIC0gMTtcbiAgY29uc3QgaW5kZXggPSBuZXcgQXJyYXkoaiArIDEpLmZpbGwoMCk7XG4gIGNvbnN0IHByb2R1Y3QgPSBbXTtcbiAgaWYgKGogPCAwIHx8IGxlbmd0aHMuc29tZShlbXB0eSkpIHJldHVybiBwcm9kdWN0O1xuICB3aGlsZSAodHJ1ZSkge1xuICAgIHByb2R1Y3QucHVzaChpbmRleC5tYXAoKGosIGkpID0+IHZhbHVlc1tpXVtqXSkpO1xuICAgIGxldCBpID0gajtcbiAgICB3aGlsZSAoKytpbmRleFtpXSA9PT0gbGVuZ3Roc1tpXSkge1xuICAgICAgaWYgKGkgPT09IDApIHJldHVybiByZWR1Y2UgPyBwcm9kdWN0Lm1hcChyZWR1Y2UpIDogcHJvZHVjdDtcbiAgICAgIGluZGV4W2ktLV0gPSAwO1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/cross.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/cumsum.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/cumsum.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cumsum)\n/* harmony export */ });\nfunction cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2N1bXN1bS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcY3Vtc3VtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGN1bXN1bSh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgdmFyIHN1bSA9IDAsIGluZGV4ID0gMDtcbiAgcmV0dXJuIEZsb2F0NjRBcnJheS5mcm9tKHZhbHVlcywgdmFsdWVvZiA9PT0gdW5kZWZpbmVkXG4gICAgPyB2ID0+IChzdW0gKz0gK3YgfHwgMClcbiAgICA6IHYgPT4gKHN1bSArPSArdmFsdWVvZih2LCBpbmRleCsrLCB2YWx1ZXMpIHx8IDApKTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/cumsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/descending.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/descending.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ descending)\n/* harmony export */ });\nfunction descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rlc2NlbmRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcZGVzY2VuZGluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBkZXNjZW5kaW5nKGEsIGIpIHtcbiAgcmV0dXJuIGEgPT0gbnVsbCB8fCBiID09IG51bGwgPyBOYU5cbiAgICA6IGIgPCBhID8gLTFcbiAgICA6IGIgPiBhID8gMVxuICAgIDogYiA+PSBhID8gMFxuICAgIDogTmFOO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/descending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/deviation.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/deviation.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ deviation)\n/* harmony export */ });\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/./node_modules/d3-array/src/variance.js\");\n\n\nfunction deviation(values, valueof) {\n  const v = (0,_variance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RldmlhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQzs7QUFFdEI7QUFDZixZQUFZLHdEQUFRO0FBQ3BCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxkZXZpYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHZhcmlhbmNlIGZyb20gXCIuL3ZhcmlhbmNlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRldmlhdGlvbih2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgY29uc3QgdiA9IHZhcmlhbmNlKHZhbHVlcywgdmFsdWVvZik7XG4gIHJldHVybiB2ID8gTWF0aC5zcXJ0KHYpIDogdjtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/deviation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/difference.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/difference.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ difference)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction difference(values, ...others) {\n  values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2RpZmZlcmVuY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXJCO0FBQ2YsZUFBZSxnREFBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGRpZmZlcmVuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtJbnRlcm5TZXR9IGZyb20gXCJpbnRlcm5tYXBcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGlmZmVyZW5jZSh2YWx1ZXMsIC4uLm90aGVycykge1xuICB2YWx1ZXMgPSBuZXcgSW50ZXJuU2V0KHZhbHVlcyk7XG4gIGZvciAoY29uc3Qgb3RoZXIgb2Ygb3RoZXJzKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiBvdGhlcikge1xuICAgICAgdmFsdWVzLmRlbGV0ZSh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiB2YWx1ZXM7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/difference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/disjoint.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/disjoint.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ disjoint)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rpc2pvaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DOztBQUVyQjtBQUNmLHVEQUF1RCxnREFBUztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGFBQWE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxkaXNqb2ludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0ludGVyblNldH0gZnJvbSBcImludGVybm1hcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBkaXNqb2ludCh2YWx1ZXMsIG90aGVyKSB7XG4gIGNvbnN0IGl0ZXJhdG9yID0gb3RoZXJbU3ltYm9sLml0ZXJhdG9yXSgpLCBzZXQgPSBuZXcgSW50ZXJuU2V0KCk7XG4gIGZvciAoY29uc3QgdiBvZiB2YWx1ZXMpIHtcbiAgICBpZiAoc2V0Lmhhcyh2KSkgcmV0dXJuIGZhbHNlO1xuICAgIGxldCB2YWx1ZSwgZG9uZTtcbiAgICB3aGlsZSAoKHt2YWx1ZSwgZG9uZX0gPSBpdGVyYXRvci5uZXh0KCkpKSB7XG4gICAgICBpZiAoZG9uZSkgYnJlYWs7XG4gICAgICBpZiAoT2JqZWN0LmlzKHYsIHZhbHVlKSkgcmV0dXJuIGZhbHNlO1xuICAgICAgc2V0LmFkZCh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiB0cnVlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/disjoint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/every.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/every.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ every)\n/* harmony export */ });\nfunction every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V2ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGV2ZXJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGV2ZXJ5KHZhbHVlcywgdGVzdCkge1xuICBpZiAodHlwZW9mIHRlc3QgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInRlc3QgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGxldCBpbmRleCA9IC0xO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmICghdGVzdCh2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/every.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/extent.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/extent.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ extent)\n/* harmony export */ });\nfunction extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2V4dGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGV4dGVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBleHRlbnQodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBtaW47XG4gIGxldCBtYXg7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgaWYgKG1pbiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgaWYgKHZhbHVlID49IHZhbHVlKSBtaW4gPSBtYXggPSB2YWx1ZTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpZiAobWluID4gdmFsdWUpIG1pbiA9IHZhbHVlO1xuICAgICAgICAgIGlmIChtYXggPCB2YWx1ZSkgbWF4ID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCkge1xuICAgICAgICBpZiAobWluID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICBpZiAodmFsdWUgPj0gdmFsdWUpIG1pbiA9IG1heCA9IHZhbHVlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGlmIChtaW4gPiB2YWx1ZSkgbWluID0gdmFsdWU7XG4gICAgICAgICAgaWYgKG1heCA8IHZhbHVlKSBtYXggPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gW21pbiwgbWF4XTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/extent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/filter.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/filter.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ filter)\n/* harmony export */ });\nfunction filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ZpbHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGZpbHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBmaWx0ZXIodmFsdWVzLCB0ZXN0KSB7XG4gIGlmICh0eXBlb2YgdGVzdCAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidGVzdCBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgY29uc3QgYXJyYXkgPSBbXTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgaWYgKHRlc3QodmFsdWUsICsraW5kZXgsIHZhbHVlcykpIHtcbiAgICAgIGFycmF5LnB1c2godmFsdWUpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gYXJyYXk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/fsum.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/fsum.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* binding */ Adder),\n/* harmony export */   fcumsum: () => (/* binding */ fcumsum),\n/* harmony export */   fsum: () => (/* binding */ fsum)\n/* harmony export */ });\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nfunction fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nfunction fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/fsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatest.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/greatest.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatest)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\n\nfunction greatest(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, maxValue) > 0\n          : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUV4QixvQ0FBb0MscURBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHlEQUFTO0FBQ3JCLFlBQVkseURBQVM7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcZ3JlYXRlc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ3JlYXRlc3QodmFsdWVzLCBjb21wYXJlID0gYXNjZW5kaW5nKSB7XG4gIGxldCBtYXg7XG4gIGxldCBkZWZpbmVkID0gZmFsc2U7XG4gIGlmIChjb21wYXJlLmxlbmd0aCA9PT0gMSkge1xuICAgIGxldCBtYXhWYWx1ZTtcbiAgICBmb3IgKGNvbnN0IGVsZW1lbnQgb2YgdmFsdWVzKSB7XG4gICAgICBjb25zdCB2YWx1ZSA9IGNvbXBhcmUoZWxlbWVudCk7XG4gICAgICBpZiAoZGVmaW5lZFxuICAgICAgICAgID8gYXNjZW5kaW5nKHZhbHVlLCBtYXhWYWx1ZSkgPiAwXG4gICAgICAgICAgOiBhc2NlbmRpbmcodmFsdWUsIHZhbHVlKSA9PT0gMCkge1xuICAgICAgICBtYXggPSBlbGVtZW50O1xuICAgICAgICBtYXhWYWx1ZSA9IHZhbHVlO1xuICAgICAgICBkZWZpbmVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmIChkZWZpbmVkXG4gICAgICAgICAgPyBjb21wYXJlKHZhbHVlLCBtYXgpID4gMFxuICAgICAgICAgIDogY29tcGFyZSh2YWx1ZSwgdmFsdWUpID09PSAwKSB7XG4gICAgICAgIG1heCA9IHZhbHVlO1xuICAgICAgICBkZWZpbmVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatestIndex.js":
/*!****************************************************!*\
  !*** ./node_modules/d3-array/src/greatestIndex.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatestIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n\n\n\nfunction greatestIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (compare.length === 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyZWF0ZXN0SW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ0Y7O0FBRXRCLHlDQUF5QyxxREFBUztBQUNqRSxtQ0FBbUMsd0RBQVE7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGdyZWF0ZXN0SW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCBtYXhJbmRleCBmcm9tIFwiLi9tYXhJbmRleC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBncmVhdGVzdEluZGV4KHZhbHVlcywgY29tcGFyZSA9IGFzY2VuZGluZykge1xuICBpZiAoY29tcGFyZS5sZW5ndGggPT09IDEpIHJldHVybiBtYXhJbmRleCh2YWx1ZXMsIGNvbXBhcmUpO1xuICBsZXQgbWF4VmFsdWU7XG4gIGxldCBtYXggPSAtMTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgKytpbmRleDtcbiAgICBpZiAobWF4IDwgMFxuICAgICAgICA/IGNvbXBhcmUodmFsdWUsIHZhbHVlKSA9PT0gMFxuICAgICAgICA6IGNvbXBhcmUodmFsdWUsIG1heFZhbHVlKSA+IDApIHtcbiAgICAgIG1heFZhbHVlID0gdmFsdWU7XG4gICAgICBtYXggPSBpbmRleDtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatestIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/group.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/group.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ group),\n/* harmony export */   flatGroup: () => (/* binding */ flatGroup),\n/* harmony export */   flatRollup: () => (/* binding */ flatRollup),\n/* harmony export */   groups: () => (/* binding */ groups),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   indexes: () => (/* binding */ indexes),\n/* harmony export */   rollup: () => (/* binding */ rollup),\n/* harmony export */   rollups: () => (/* binding */ rollups)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-array/src/identity.js\");\n\n\n\nfunction group(values, ...keys) {\n  return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\n\nfunction groups(values, ...keys) {\n  return nest(values, Array.from, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nfunction flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nfunction flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nfunction rollup(values, reduce, ...keys) {\n  return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], reduce, keys);\n}\n\nfunction rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nfunction index(values, ...keys) {\n  return nest(values, _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], unique, keys);\n}\n\nfunction indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new internmap__WEBPACK_IMPORTED_MODULE_1__.InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/group.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/groupSort.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/groupSort.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ groupSort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./group.js */ \"(ssr)/./node_modules/d3-array/src/group.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n\n\nfunction groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__.rollup)(values, reduce, key), (([ak, av], [bk, bv]) => (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk)))\n    : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_group_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || (0,_ascending_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ak, bk))))\n    .map(([key]) => key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2dyb3VwU29ydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVDO0FBQ0U7QUFDWjs7QUFFZDtBQUNmO0FBQ0EsTUFBTSxvREFBSSxDQUFDLGlEQUFNLGdEQUFnRCx5REFBUyxZQUFZLHlEQUFTO0FBQy9GLE1BQU0sb0RBQUksQ0FBQyxxREFBSywwREFBMEQseURBQVM7QUFDbkY7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGdyb3VwU29ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXNjZW5kaW5nIGZyb20gXCIuL2FzY2VuZGluZy5qc1wiO1xuaW1wb3J0IGdyb3VwLCB7cm9sbHVwfSBmcm9tIFwiLi9ncm91cC5qc1wiO1xuaW1wb3J0IHNvcnQgZnJvbSBcIi4vc29ydC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBncm91cFNvcnQodmFsdWVzLCByZWR1Y2UsIGtleSkge1xuICByZXR1cm4gKHJlZHVjZS5sZW5ndGggIT09IDJcbiAgICA/IHNvcnQocm9sbHVwKHZhbHVlcywgcmVkdWNlLCBrZXkpLCAoKFthaywgYXZdLCBbYmssIGJ2XSkgPT4gYXNjZW5kaW5nKGF2LCBidikgfHwgYXNjZW5kaW5nKGFrLCBiaykpKVxuICAgIDogc29ydChncm91cCh2YWx1ZXMsIGtleSksICgoW2FrLCBhdl0sIFtiaywgYnZdKSA9PiByZWR1Y2UoYXYsIGJ2KSB8fCBhc2NlbmRpbmcoYWssIGJrKSkpKVxuICAgIC5tYXAoKFtrZXldKSA9PiBrZXkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/groupSort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/identity.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/identity.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ identity)\n/* harmony export */ });\nfunction identity(x) {\n  return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxpZGVudGl0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBpZGVudGl0eSh4KSB7XG4gIHJldHVybiB4O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.Adder),\n/* harmony export */   InternMap: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternMap),\n/* harmony export */   InternSet: () => (/* reexport safe */ internmap__WEBPACK_IMPORTED_MODULE_56__.InternSet),\n/* harmony export */   ascending: () => (/* reexport safe */ _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   bin: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   bisect: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   bisectCenter: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectCenter),\n/* harmony export */   bisectLeft: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectLeft),\n/* harmony export */   bisectRight: () => (/* reexport safe */ _bisect_js__WEBPACK_IMPORTED_MODULE_0__.bisectRight),\n/* harmony export */   bisector: () => (/* reexport safe */ _bisector_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   blur: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur),\n/* harmony export */   blur2: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blur2),\n/* harmony export */   blurImage: () => (/* reexport safe */ _blur_js__WEBPACK_IMPORTED_MODULE_3__.blurImage),\n/* harmony export */   count: () => (/* reexport safe */ _count_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   cross: () => (/* reexport safe */ _cross_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   cumsum: () => (/* reexport safe */ _cumsum_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   descending: () => (/* reexport safe */ _descending_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   deviation: () => (/* reexport safe */ _deviation_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   difference: () => (/* reexport safe */ _difference_js__WEBPACK_IMPORTED_MODULE_50__[\"default\"]),\n/* harmony export */   disjoint: () => (/* reexport safe */ _disjoint_js__WEBPACK_IMPORTED_MODULE_51__[\"default\"]),\n/* harmony export */   every: () => (/* reexport safe */ _every_js__WEBPACK_IMPORTED_MODULE_43__[\"default\"]),\n/* harmony export */   extent: () => (/* reexport safe */ _extent_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   fcumsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fcumsum),\n/* harmony export */   filter: () => (/* reexport safe */ _filter_js__WEBPACK_IMPORTED_MODULE_45__[\"default\"]),\n/* harmony export */   flatGroup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatGroup),\n/* harmony export */   flatRollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.flatRollup),\n/* harmony export */   fsum: () => (/* reexport safe */ _fsum_js__WEBPACK_IMPORTED_MODULE_10__.fsum),\n/* harmony export */   greatest: () => (/* reexport safe */ _greatest_js__WEBPACK_IMPORTED_MODULE_34__[\"default\"]),\n/* harmony export */   greatestIndex: () => (/* reexport safe */ _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__[\"default\"]),\n/* harmony export */   group: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   groupSort: () => (/* reexport safe */ _groupSort_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   groups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.groups),\n/* harmony export */   histogram: () => (/* reexport safe */ _bin_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   index: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.index),\n/* harmony export */   indexes: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.indexes),\n/* harmony export */   intersection: () => (/* reexport safe */ _intersection_js__WEBPACK_IMPORTED_MODULE_52__[\"default\"]),\n/* harmony export */   least: () => (/* reexport safe */ _least_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   leastIndex: () => (/* reexport safe */ _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   map: () => (/* reexport safe */ _map_js__WEBPACK_IMPORTED_MODULE_46__[\"default\"]),\n/* harmony export */   max: () => (/* reexport safe */ _max_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   maxIndex: () => (/* reexport safe */ _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   mean: () => (/* reexport safe */ _mean_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   median: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   medianIndex: () => (/* reexport safe */ _median_js__WEBPACK_IMPORTED_MODULE_20__.medianIndex),\n/* harmony export */   merge: () => (/* reexport safe */ _merge_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   min: () => (/* reexport safe */ _min_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   minIndex: () => (/* reexport safe */ _minIndex_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   mode: () => (/* reexport safe */ _mode_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   nice: () => (/* reexport safe */ _nice_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   pairs: () => (/* reexport safe */ _pairs_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   permute: () => (/* reexport safe */ _permute_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   quantile: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   quantileIndex: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileIndex),\n/* harmony export */   quantileSorted: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_28__.quantileSorted),\n/* harmony export */   quickselect: () => (/* reexport safe */ _quickselect_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   range: () => (/* reexport safe */ _range_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   rank: () => (/* reexport safe */ _rank_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   reduce: () => (/* reexport safe */ _reduce_js__WEBPACK_IMPORTED_MODULE_47__[\"default\"]),\n/* harmony export */   reverse: () => (/* reexport safe */ _reverse_js__WEBPACK_IMPORTED_MODULE_48__[\"default\"]),\n/* harmony export */   rollup: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollup),\n/* harmony export */   rollups: () => (/* reexport safe */ _group_js__WEBPACK_IMPORTED_MODULE_11__.rollups),\n/* harmony export */   scan: () => (/* reexport safe */ _scan_js__WEBPACK_IMPORTED_MODULE_36__[\"default\"]),\n/* harmony export */   shuffle: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__[\"default\"]),\n/* harmony export */   shuffler: () => (/* reexport safe */ _shuffle_js__WEBPACK_IMPORTED_MODULE_37__.shuffler),\n/* harmony export */   some: () => (/* reexport safe */ _some_js__WEBPACK_IMPORTED_MODULE_44__[\"default\"]),\n/* harmony export */   sort: () => (/* reexport safe */ _sort_js__WEBPACK_IMPORTED_MODULE_49__[\"default\"]),\n/* harmony export */   subset: () => (/* reexport safe */ _subset_js__WEBPACK_IMPORTED_MODULE_53__[\"default\"]),\n/* harmony export */   sum: () => (/* reexport safe */ _sum_js__WEBPACK_IMPORTED_MODULE_38__[\"default\"]),\n/* harmony export */   superset: () => (/* reexport safe */ _superset_js__WEBPACK_IMPORTED_MODULE_54__[\"default\"]),\n/* harmony export */   thresholdFreedmanDiaconis: () => (/* reexport safe */ _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   thresholdScott: () => (/* reexport safe */ _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   thresholdSturges: () => (/* reexport safe */ _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   tickIncrement: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickIncrement),\n/* harmony export */   tickStep: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__.tickStep),\n/* harmony export */   ticks: () => (/* reexport safe */ _ticks_js__WEBPACK_IMPORTED_MODULE_39__[\"default\"]),\n/* harmony export */   transpose: () => (/* reexport safe */ _transpose_js__WEBPACK_IMPORTED_MODULE_40__[\"default\"]),\n/* harmony export */   union: () => (/* reexport safe */ _union_js__WEBPACK_IMPORTED_MODULE_55__[\"default\"]),\n/* harmony export */   variance: () => (/* reexport safe */ _variance_js__WEBPACK_IMPORTED_MODULE_41__[\"default\"]),\n/* harmony export */   zip: () => (/* reexport safe */ _zip_js__WEBPACK_IMPORTED_MODULE_42__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _bisect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisect.js */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _blur_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blur.js */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _cross_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cross.js */ \"(ssr)/./node_modules/d3-array/src/cross.js\");\n/* harmony import */ var _cumsum_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./cumsum.js */ \"(ssr)/./node_modules/d3-array/src/cumsum.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./deviation.js */ \"(ssr)/./node_modules/d3-array/src/deviation.js\");\n/* harmony import */ var _extent_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./extent.js */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var _fsum_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./fsum.js */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _group_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./group.js */ \"(ssr)/./node_modules/d3-array/src/group.js\");\n/* harmony import */ var _groupSort_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./groupSort.js */ \"(ssr)/./node_modules/d3-array/src/groupSort.js\");\n/* harmony import */ var _bin_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./bin.js */ \"(ssr)/./node_modules/d3-array/src/bin.js\");\n/* harmony import */ var _threshold_freedmanDiaconis_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./threshold/freedmanDiaconis.js */ \"(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js\");\n/* harmony import */ var _threshold_scott_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./threshold/scott.js */ \"(ssr)/./node_modules/d3-array/src/threshold/scott.js\");\n/* harmony import */ var _threshold_sturges_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./threshold/sturges.js */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _mean_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./mean.js */ \"(ssr)/./node_modules/d3-array/src/mean.js\");\n/* harmony import */ var _median_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./median.js */ \"(ssr)/./node_modules/d3-array/src/median.js\");\n/* harmony import */ var _merge_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./merge.js */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _mode_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./mode.js */ \"(ssr)/./node_modules/d3-array/src/mode.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _pairs_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./pairs.js */ \"(ssr)/./node_modules/d3-array/src/pairs.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _range_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./range.js */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _rank_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rank.js */ \"(ssr)/./node_modules/d3-array/src/rank.js\");\n/* harmony import */ var _least_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./least.js */ \"(ssr)/./node_modules/d3-array/src/least.js\");\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/./node_modules/d3-array/src/leastIndex.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n/* harmony import */ var _greatestIndex_js__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! ./greatestIndex.js */ \"(ssr)/./node_modules/d3-array/src/greatestIndex.js\");\n/* harmony import */ var _scan_js__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ./scan.js */ \"(ssr)/./node_modules/d3-array/src/scan.js\");\n/* harmony import */ var _shuffle_js__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! ./shuffle.js */ \"(ssr)/./node_modules/d3-array/src/shuffle.js\");\n/* harmony import */ var _sum_js__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! ./sum.js */ \"(ssr)/./node_modules/d3-array/src/sum.js\");\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/./node_modules/d3-array/src/transpose.js\");\n/* harmony import */ var _variance_js__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! ./variance.js */ \"(ssr)/./node_modules/d3-array/src/variance.js\");\n/* harmony import */ var _zip_js__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ./zip.js */ \"(ssr)/./node_modules/d3-array/src/zip.js\");\n/* harmony import */ var _every_js__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! ./every.js */ \"(ssr)/./node_modules/d3-array/src/every.js\");\n/* harmony import */ var _some_js__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! ./some.js */ \"(ssr)/./node_modules/d3-array/src/some.js\");\n/* harmony import */ var _filter_js__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! ./filter.js */ \"(ssr)/./node_modules/d3-array/src/filter.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/d3-array/src/map.js\");\n/* harmony import */ var _reduce_js__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ./reduce.js */ \"(ssr)/./node_modules/d3-array/src/reduce.js\");\n/* harmony import */ var _reverse_js__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ./reverse.js */ \"(ssr)/./node_modules/d3-array/src/reverse.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _difference_js__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ./difference.js */ \"(ssr)/./node_modules/d3-array/src/difference.js\");\n/* harmony import */ var _disjoint_js__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! ./disjoint.js */ \"(ssr)/./node_modules/d3-array/src/disjoint.js\");\n/* harmony import */ var _intersection_js__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./intersection.js */ \"(ssr)/./node_modules/d3-array/src/intersection.js\");\n/* harmony import */ var _subset_js__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! ./subset.js */ \"(ssr)/./node_modules/d3-array/src/subset.js\");\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/./node_modules/d3-array/src/superset.js\");\n/* harmony import */ var _union_js__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! ./union.js */ \"(ssr)/./node_modules/d3-array/src/union.js\");\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use bin.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // Deprecated; use leastIndex.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/intersection.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-array/src/intersection.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ intersection)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction intersection(values, ...others) {\n  values = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet ? values : new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet(values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2ludGVyc2VjdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQzs7QUFFckI7QUFDZixlQUFlLGdEQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSwyQkFBMkIsZ0RBQVMsZ0JBQWdCLGdEQUFTO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcaW50ZXJzZWN0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuU2V0fSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGludGVyc2VjdGlvbih2YWx1ZXMsIC4uLm90aGVycykge1xuICB2YWx1ZXMgPSBuZXcgSW50ZXJuU2V0KHZhbHVlcyk7XG4gIG90aGVycyA9IG90aGVycy5tYXAoc2V0KTtcbiAgb3V0OiBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGZvciAoY29uc3Qgb3RoZXIgb2Ygb3RoZXJzKSB7XG4gICAgICBpZiAoIW90aGVyLmhhcyh2YWx1ZSkpIHtcbiAgICAgICAgdmFsdWVzLmRlbGV0ZSh2YWx1ZSk7XG4gICAgICAgIGNvbnRpbnVlIG91dDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlcztcbn1cblxuZnVuY3Rpb24gc2V0KHZhbHVlcykge1xuICByZXR1cm4gdmFsdWVzIGluc3RhbmNlb2YgSW50ZXJuU2V0ID8gdmFsdWVzIDogbmV3IEludGVyblNldCh2YWx1ZXMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/intersection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/least.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/least.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ least)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\n\nfunction least(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, minValue) < 0\n          : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUV4QixpQ0FBaUMscURBQVM7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHlEQUFTO0FBQ3JCLFlBQVkseURBQVM7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbGVhc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbGVhc3QodmFsdWVzLCBjb21wYXJlID0gYXNjZW5kaW5nKSB7XG4gIGxldCBtaW47XG4gIGxldCBkZWZpbmVkID0gZmFsc2U7XG4gIGlmIChjb21wYXJlLmxlbmd0aCA9PT0gMSkge1xuICAgIGxldCBtaW5WYWx1ZTtcbiAgICBmb3IgKGNvbnN0IGVsZW1lbnQgb2YgdmFsdWVzKSB7XG4gICAgICBjb25zdCB2YWx1ZSA9IGNvbXBhcmUoZWxlbWVudCk7XG4gICAgICBpZiAoZGVmaW5lZFxuICAgICAgICAgID8gYXNjZW5kaW5nKHZhbHVlLCBtaW5WYWx1ZSkgPCAwXG4gICAgICAgICAgOiBhc2NlbmRpbmcodmFsdWUsIHZhbHVlKSA9PT0gMCkge1xuICAgICAgICBtaW4gPSBlbGVtZW50O1xuICAgICAgICBtaW5WYWx1ZSA9IHZhbHVlO1xuICAgICAgICBkZWZpbmVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmIChkZWZpbmVkXG4gICAgICAgICAgPyBjb21wYXJlKHZhbHVlLCBtaW4pIDwgMFxuICAgICAgICAgIDogY29tcGFyZSh2YWx1ZSwgdmFsdWUpID09PSAwKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlO1xuICAgICAgICBkZWZpbmVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbjtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/least.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/leastIndex.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/leastIndex.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leastIndex)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n\n\n\nfunction leastIndex(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (compare.length === 1) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2xlYXN0SW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ0Y7O0FBRXRCLHNDQUFzQyxxREFBUztBQUM5RCxtQ0FBbUMsd0RBQVE7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXGxlYXN0SW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCBtaW5JbmRleCBmcm9tIFwiLi9taW5JbmRleC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBsZWFzdEluZGV4KHZhbHVlcywgY29tcGFyZSA9IGFzY2VuZGluZykge1xuICBpZiAoY29tcGFyZS5sZW5ndGggPT09IDEpIHJldHVybiBtaW5JbmRleCh2YWx1ZXMsIGNvbXBhcmUpO1xuICBsZXQgbWluVmFsdWU7XG4gIGxldCBtaW4gPSAtMTtcbiAgbGV0IGluZGV4ID0gLTE7XG4gIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgKytpbmRleDtcbiAgICBpZiAobWluIDwgMFxuICAgICAgICA/IGNvbXBhcmUodmFsdWUsIHZhbHVlKSA9PT0gMFxuICAgICAgICA6IGNvbXBhcmUodmFsdWUsIG1pblZhbHVlKSA8IDApIHtcbiAgICAgIG1pblZhbHVlID0gdmFsdWU7XG4gICAgICBtaW4gPSBpbmRleDtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbjtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/leastIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/map.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/map.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ map)\n/* harmony export */ });\nfunction map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21hcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXG1hcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtYXAodmFsdWVzLCBtYXBwZXIpIHtcbiAgaWYgKHR5cGVvZiB2YWx1ZXNbU3ltYm9sLml0ZXJhdG9yXSAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwidmFsdWVzIGlzIG5vdCBpdGVyYWJsZVwiKTtcbiAgaWYgKHR5cGVvZiBtYXBwZXIgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIm1hcHBlciBpcyBub3QgYSBmdW5jdGlvblwiKTtcbiAgcmV0dXJuIEFycmF5LmZyb20odmFsdWVzLCAodmFsdWUsIGluZGV4KSA9PiBtYXBwZXIodmFsdWUsIGluZGV4LCB2YWx1ZXMpKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/max.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/max.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ max)\n/* harmony export */ });\nfunction max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbWF4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1heCh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1heDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtYXggPCB2YWx1ZSB8fCAobWF4ID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtYXggPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1heDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/max.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/maxIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/maxIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ maxIndex)\n/* harmony export */ });\nfunction maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heEluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbWF4SW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWF4SW5kZXgodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBtYXg7XG4gIGxldCBtYXhJbmRleCA9IC0xO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICArK2luZGV4O1xuICAgICAgaWYgKHZhbHVlICE9IG51bGxcbiAgICAgICAgICAmJiAobWF4IDwgdmFsdWUgfHwgKG1heCA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWF4ID0gdmFsdWUsIG1heEluZGV4ID0gaW5kZXg7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGxcbiAgICAgICAgICAmJiAobWF4IDwgdmFsdWUgfHwgKG1heCA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWF4ID0gdmFsdWUsIG1heEluZGV4ID0gaW5kZXg7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtYXhJbmRleDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/maxIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/mean.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/mean.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mean)\n/* harmony export */ });\nfunction mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lYW4uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxtZWFuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1lYW4odmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBjb3VudCA9IDA7XG4gIGxldCBzdW0gPSAwO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQsIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgICsrY291bnQsIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgaWYgKGNvdW50KSByZXR1cm4gc3VtIC8gY291bnQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/mean.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/median.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/median.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ median),\n/* harmony export */   medianIndex: () => (/* binding */ medianIndex)\n/* harmony export */ });\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n\n\nfunction median(values, valueof) {\n  return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, 0.5, valueof);\n}\n\nfunction medianIndex(values, valueof) {\n  return (0,_quantile_js__WEBPACK_IMPORTED_MODULE_0__.quantileIndex)(values, 0.5, valueof);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lZGlhbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0Q7O0FBRXZDO0FBQ2YsU0FBUyx3REFBUTtBQUNqQjs7QUFFTztBQUNQLFNBQVMsMkRBQWE7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxtZWRpYW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHF1YW50aWxlLCB7cXVhbnRpbGVJbmRleH0gZnJvbSBcIi4vcXVhbnRpbGUuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWVkaWFuKHZhbHVlcywgdmFsdWVvZikge1xuICByZXR1cm4gcXVhbnRpbGUodmFsdWVzLCAwLjUsIHZhbHVlb2YpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gbWVkaWFuSW5kZXgodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIHJldHVybiBxdWFudGlsZUluZGV4KHZhbHVlcywgMC41LCB2YWx1ZW9mKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/median.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/merge.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/merge.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ merge)\n/* harmony export */ });\nfunction* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nfunction merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXG1lcmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uKiBmbGF0dGVuKGFycmF5cykge1xuICBmb3IgKGNvbnN0IGFycmF5IG9mIGFycmF5cykge1xuICAgIHlpZWxkKiBhcnJheTtcbiAgfVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtZXJnZShhcnJheXMpIHtcbiAgcmV0dXJuIEFycmF5LmZyb20oZmxhdHRlbihhcnJheXMpKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/min.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/min.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ min)\n/* harmony export */ });\nfunction min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbWluLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1pbih2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgbGV0IG1pbjtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbFxuICAgICAgICAgICYmIChtaW4gPiB2YWx1ZSB8fCAobWluID09PSB1bmRlZmluZWQgJiYgdmFsdWUgPj0gdmFsdWUpKSkge1xuICAgICAgICBtaW4gPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1pbjtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/min.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/minIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/minIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ minIndex)\n/* harmony export */ });\nfunction minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbkluZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbWluSW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWluSW5kZXgodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBtaW47XG4gIGxldCBtaW5JbmRleCA9IC0xO1xuICBsZXQgaW5kZXggPSAtMTtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICArK2luZGV4O1xuICAgICAgaWYgKHZhbHVlICE9IG51bGxcbiAgICAgICAgICAmJiAobWluID4gdmFsdWUgfHwgKG1pbiA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWluID0gdmFsdWUsIG1pbkluZGV4ID0gaW5kZXg7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGxcbiAgICAgICAgICAmJiAobWluID4gdmFsdWUgfHwgKG1pbiA9PT0gdW5kZWZpbmVkICYmIHZhbHVlID49IHZhbHVlKSkpIHtcbiAgICAgICAgbWluID0gdmFsdWUsIG1pbkluZGV4ID0gaW5kZXg7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtaW5JbmRleDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/minIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/mode.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/mode.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mode)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction mode(values, valueof) {\n  const counts = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7O0FBRXJCO0FBQ2YscUJBQXFCLGdEQUFTO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbW9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0ludGVybk1hcH0gZnJvbSBcImludGVybm1hcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBtb2RlKHZhbHVlcywgdmFsdWVvZikge1xuICBjb25zdCBjb3VudHMgPSBuZXcgSW50ZXJuTWFwKCk7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmIHZhbHVlID49IHZhbHVlKSB7XG4gICAgICAgIGNvdW50cy5zZXQodmFsdWUsIChjb3VudHMuZ2V0KHZhbHVlKSB8fCAwKSArIDEpO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsICYmIHZhbHVlID49IHZhbHVlKSB7XG4gICAgICAgIGNvdW50cy5zZXQodmFsdWUsIChjb3VudHMuZ2V0KHZhbHVlKSB8fCAwKSArIDEpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBsZXQgbW9kZVZhbHVlO1xuICBsZXQgbW9kZUNvdW50ID0gMDtcbiAgZm9yIChjb25zdCBbdmFsdWUsIGNvdW50XSBvZiBjb3VudHMpIHtcbiAgICBpZiAoY291bnQgPiBtb2RlQ291bnQpIHtcbiAgICAgIG1vZGVDb3VudCA9IGNvdW50O1xuICAgICAgbW9kZVZhbHVlID0gdmFsdWU7XG4gICAgfVxuICB9XG4gIHJldHVybiBtb2RlVmFsdWU7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/mode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/nice.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/nice.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nice)\n/* harmony export */ });\n/* harmony import */ var _ticks_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ticks.js */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n\n\nfunction nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = (0,_ticks_js__WEBPACK_IMPORTED_MODULE_0__.tickIncrement)(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL25pY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7O0FBRTFCO0FBQ2Y7QUFDQTtBQUNBLGlCQUFpQix3REFBYTtBQUM5QjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbmljZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3RpY2tJbmNyZW1lbnR9IGZyb20gXCIuL3RpY2tzLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG5pY2Uoc3RhcnQsIHN0b3AsIGNvdW50KSB7XG4gIGxldCBwcmVzdGVwO1xuICB3aGlsZSAodHJ1ZSkge1xuICAgIGNvbnN0IHN0ZXAgPSB0aWNrSW5jcmVtZW50KHN0YXJ0LCBzdG9wLCBjb3VudCk7XG4gICAgaWYgKHN0ZXAgPT09IHByZXN0ZXAgfHwgc3RlcCA9PT0gMCB8fCAhaXNGaW5pdGUoc3RlcCkpIHtcbiAgICAgIHJldHVybiBbc3RhcnQsIHN0b3BdO1xuICAgIH0gZWxzZSBpZiAoc3RlcCA+IDApIHtcbiAgICAgIHN0YXJ0ID0gTWF0aC5mbG9vcihzdGFydCAvIHN0ZXApICogc3RlcDtcbiAgICAgIHN0b3AgPSBNYXRoLmNlaWwoc3RvcCAvIHN0ZXApICogc3RlcDtcbiAgICB9IGVsc2UgaWYgKHN0ZXAgPCAwKSB7XG4gICAgICBzdGFydCA9IE1hdGguY2VpbChzdGFydCAqIHN0ZXApIC8gc3RlcDtcbiAgICAgIHN0b3AgPSBNYXRoLmZsb29yKHN0b3AgKiBzdGVwKSAvIHN0ZXA7XG4gICAgfVxuICAgIHByZXN0ZXAgPSBzdGVwO1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/nice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/number.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/number.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number),\n/* harmony export */   numbers: () => (/* binding */ numbers)\n/* harmony export */ });\nfunction number(x) {\n  return x === null ? NaN : +x;\n}\n\nfunction* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL251bWJlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFlO0FBQ2Y7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcbnVtYmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG51bWJlcih4KSB7XG4gIHJldHVybiB4ID09PSBudWxsID8gTmFOIDogK3g7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiogbnVtYmVycyh2YWx1ZXMsIHZhbHVlb2YpIHtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICB5aWVsZCB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAoKHZhbHVlID0gdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkgIT0gbnVsbCAmJiAodmFsdWUgPSArdmFsdWUpID49IHZhbHVlKSB7XG4gICAgICAgIHlpZWxkIHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/pairs.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/pairs.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pairs),\n/* harmony export */   pair: () => (/* binding */ pair)\n/* harmony export */ });\nfunction pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nfunction pair(a, b) {\n  return [a, b];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3BhaXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxwYWlycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBwYWlycyh2YWx1ZXMsIHBhaXJvZiA9IHBhaXIpIHtcbiAgY29uc3QgcGFpcnMgPSBbXTtcbiAgbGV0IHByZXZpb3VzO1xuICBsZXQgZmlyc3QgPSBmYWxzZTtcbiAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICBpZiAoZmlyc3QpIHBhaXJzLnB1c2gocGFpcm9mKHByZXZpb3VzLCB2YWx1ZSkpO1xuICAgIHByZXZpb3VzID0gdmFsdWU7XG4gICAgZmlyc3QgPSB0cnVlO1xuICB9XG4gIHJldHVybiBwYWlycztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHBhaXIoYSwgYikge1xuICByZXR1cm4gW2EsIGJdO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/pairs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/permute.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/permute.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ permute)\n/* harmony export */ });\nfunction permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3Blcm11dGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHBlcm11dGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcGVybXV0ZShzb3VyY2UsIGtleXMpIHtcbiAgcmV0dXJuIEFycmF5LmZyb20oa2V5cywga2V5ID0+IHNvdXJjZVtrZXldKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/permute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quantile.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/quantile.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile),\n/* harmony export */   quantileIndex: () => (/* binding */ quantileIndex),\n/* harmony export */   quantileSorted: () => (/* binding */ quantileSorted)\n/* harmony export */ });\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n\n\n\n\n\n\n\n\n\nfunction quantile(values, p, valueof) {\n  values = Float64Array.from((0,_number_js__WEBPACK_IMPORTED_MODULE_0__.numbers)(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n  if (p >= 1) return (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values, i0).subarray(0, i0 + 1)),\n      value1 = (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction quantileSorted(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction quantileIndex(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => (0,_number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(valueof(values[i], i, values)));\n  if (p <= 0) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(numbers);\n  if (p >= 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(numbers);\n  var numbers,\n      index = Uint32Array.from(values, (_, i) => i),\n      j = numbers.length - 1,\n      i = Math.floor(j * p);\n  (0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(index, i, 0, j, (i, j) => (0,_sort_js__WEBPACK_IMPORTED_MODULE_6__.ascendingDefined)(numbers[i], numbers[j]));\n  i = (0,_greatest_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(index.subarray(0, i + 1), (i) => numbers[i]);\n  return i >= 0 ? i : -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quickselect.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-array/src/quickselect.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quickselect)\n/* harmony export */ });\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? _sort_js__WEBPACK_IMPORTED_MODULE_0__.ascendingDefined : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__.compareDefined)(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quickselect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/range.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/range.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ range)\n/* harmony export */ });\nfunction range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHJhbmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJhbmdlKHN0YXJ0LCBzdG9wLCBzdGVwKSB7XG4gIHN0YXJ0ID0gK3N0YXJ0LCBzdG9wID0gK3N0b3AsIHN0ZXAgPSAobiA9IGFyZ3VtZW50cy5sZW5ndGgpIDwgMiA/IChzdG9wID0gc3RhcnQsIHN0YXJ0ID0gMCwgMSkgOiBuIDwgMyA/IDEgOiArc3RlcDtcblxuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IE1hdGgubWF4KDAsIE1hdGguY2VpbCgoc3RvcCAtIHN0YXJ0KSAvIHN0ZXApKSB8IDAsXG4gICAgICByYW5nZSA9IG5ldyBBcnJheShuKTtcblxuICB3aGlsZSAoKytpIDwgbikge1xuICAgIHJhbmdlW2ldID0gc3RhcnQgKyBpICogc3RlcDtcbiAgfVxuXG4gIHJldHVybiByYW5nZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/range.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/rank.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/rank.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rank)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n\nfunction rank(values, valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] ? (i, j) => (0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.ascendingDefined)(V[i], V[j]) : (0,_sort_js__WEBPACK_IMPORTED_MODULE_1__.compareDefined)(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVDO0FBQ29COztBQUU1QyxnQ0FBZ0MscURBQVM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0EsMERBQTBELHFEQUFTO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHFEQUFTLGFBQWEsMERBQWdCLGVBQWUsd0RBQWM7QUFDN0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxyYW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhc2NlbmRpbmcgZnJvbSBcIi4vYXNjZW5kaW5nLmpzXCI7XG5pbXBvcnQge2FzY2VuZGluZ0RlZmluZWQsIGNvbXBhcmVEZWZpbmVkfSBmcm9tIFwiLi9zb3J0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJhbmsodmFsdWVzLCB2YWx1ZW9mID0gYXNjZW5kaW5nKSB7XG4gIGlmICh0eXBlb2YgdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0gIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInZhbHVlcyBpcyBub3QgaXRlcmFibGVcIik7XG4gIGxldCBWID0gQXJyYXkuZnJvbSh2YWx1ZXMpO1xuICBjb25zdCBSID0gbmV3IEZsb2F0NjRBcnJheShWLmxlbmd0aCk7XG4gIGlmICh2YWx1ZW9mLmxlbmd0aCAhPT0gMikgViA9IFYubWFwKHZhbHVlb2YpLCB2YWx1ZW9mID0gYXNjZW5kaW5nO1xuICBjb25zdCBjb21wYXJlSW5kZXggPSAoaSwgaikgPT4gdmFsdWVvZihWW2ldLCBWW2pdKTtcbiAgbGV0IGssIHI7XG4gIHZhbHVlcyA9IFVpbnQzMkFycmF5LmZyb20oViwgKF8sIGkpID0+IGkpO1xuICAvLyBSaXNreSBjaGFpbmluZyBkdWUgdG8gU2FmYXJpIDE0IGh0dHBzOi8vZ2l0aHViLmNvbS9kMy9kMy1hcnJheS9pc3N1ZXMvMTIzXG4gIHZhbHVlcy5zb3J0KHZhbHVlb2YgPT09IGFzY2VuZGluZyA/IChpLCBqKSA9PiBhc2NlbmRpbmdEZWZpbmVkKFZbaV0sIFZbal0pIDogY29tcGFyZURlZmluZWQoY29tcGFyZUluZGV4KSk7XG4gIHZhbHVlcy5mb3JFYWNoKChqLCBpKSA9PiB7XG4gICAgICBjb25zdCBjID0gY29tcGFyZUluZGV4KGosIGsgPT09IHVuZGVmaW5lZCA/IGogOiBrKTtcbiAgICAgIGlmIChjID49IDApIHtcbiAgICAgICAgaWYgKGsgPT09IHVuZGVmaW5lZCB8fCBjID4gMCkgayA9IGosIHIgPSBpO1xuICAgICAgICBSW2pdID0gcjtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIFJbal0gPSBOYU47XG4gICAgICB9XG4gICAgfSk7XG4gIHJldHVybiBSO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/rank.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/reduce.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/reduce.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JlZHVjZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sYUFBYTtBQUNuQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHJlZHVjZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByZWR1Y2UodmFsdWVzLCByZWR1Y2VyLCB2YWx1ZSkge1xuICBpZiAodHlwZW9mIHJlZHVjZXIgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInJlZHVjZXIgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGNvbnN0IGl0ZXJhdG9yID0gdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0oKTtcbiAgbGV0IGRvbmUsIG5leHQsIGluZGV4ID0gLTE7XG4gIGlmIChhcmd1bWVudHMubGVuZ3RoIDwgMykge1xuICAgICh7ZG9uZSwgdmFsdWV9ID0gaXRlcmF0b3IubmV4dCgpKTtcbiAgICBpZiAoZG9uZSkgcmV0dXJuO1xuICAgICsraW5kZXg7XG4gIH1cbiAgd2hpbGUgKCh7ZG9uZSwgdmFsdWU6IG5leHR9ID0gaXRlcmF0b3IubmV4dCgpKSwgIWRvbmUpIHtcbiAgICB2YWx1ZSA9IHJlZHVjZXIodmFsdWUsIG5leHQsICsraW5kZXgsIHZhbHVlcyk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/reduce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/reverse.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/reverse.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ reverse)\n/* harmony export */ });\nfunction reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JldmVyc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xccmV2ZXJzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiByZXZlcnNlKHZhbHVlcykge1xuICBpZiAodHlwZW9mIHZhbHVlc1tTeW1ib2wuaXRlcmF0b3JdICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJ2YWx1ZXMgaXMgbm90IGl0ZXJhYmxlXCIpO1xuICByZXR1cm4gQXJyYXkuZnJvbSh2YWx1ZXMpLnJldmVyc2UoKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/reverse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/scan.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/scan.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ scan)\n/* harmony export */ });\n/* harmony import */ var _leastIndex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./leastIndex.js */ \"(ssr)/./node_modules/d3-array/src/leastIndex.js\");\n\n\nfunction scan(values, compare) {\n  const index = (0,_leastIndex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, compare);\n  return index < 0 ? undefined : index;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NjYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7O0FBRTFCO0FBQ2YsZ0JBQWdCLDBEQUFVO0FBQzFCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxzY2FuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBsZWFzdEluZGV4IGZyb20gXCIuL2xlYXN0SW5kZXguanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc2Nhbih2YWx1ZXMsIGNvbXBhcmUpIHtcbiAgY29uc3QgaW5kZXggPSBsZWFzdEluZGV4KHZhbHVlcywgY29tcGFyZSk7XG4gIHJldHVybiBpbmRleCA8IDAgPyB1bmRlZmluZWQgOiBpbmRleDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/scan.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/shuffle.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/shuffle.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   shuffler: () => (/* binding */ shuffler)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (shuffler(Math.random));\n\nfunction shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NodWZmbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxpRUFBZSxxQkFBcUIsRUFBQzs7QUFFOUI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHNodWZmbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgc2h1ZmZsZXIoTWF0aC5yYW5kb20pO1xuXG5leHBvcnQgZnVuY3Rpb24gc2h1ZmZsZXIocmFuZG9tKSB7XG4gIHJldHVybiBmdW5jdGlvbiBzaHVmZmxlKGFycmF5LCBpMCA9IDAsIGkxID0gYXJyYXkubGVuZ3RoKSB7XG4gICAgbGV0IG0gPSBpMSAtIChpMCA9ICtpMCk7XG4gICAgd2hpbGUgKG0pIHtcbiAgICAgIGNvbnN0IGkgPSByYW5kb20oKSAqIG0tLSB8IDAsIHQgPSBhcnJheVttICsgaTBdO1xuICAgICAgYXJyYXlbbSArIGkwXSA9IGFycmF5W2kgKyBpMF07XG4gICAgICBhcnJheVtpICsgaTBdID0gdDtcbiAgICB9XG4gICAgcmV0dXJuIGFycmF5O1xuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/shuffle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/some.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/some.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ some)\n/* harmony export */ });\nfunction some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3NvbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcc29tZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzb21lKHZhbHVlcywgdGVzdCkge1xuICBpZiAodHlwZW9mIHRlc3QgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcInRlc3QgaXMgbm90IGEgZnVuY3Rpb25cIik7XG4gIGxldCBpbmRleCA9IC0xO1xuICBmb3IgKGNvbnN0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgIGlmICh0ZXN0KHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGZhbHNlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/some.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sort.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/sort.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ascendingDefined: () => (/* binding */ ascendingDefined),\n/* harmony export */   compareDefined: () => (/* binding */ compareDefined),\n/* harmony export */   \"default\": () => (/* binding */ sort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n\n\n\nfunction sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if ((f && f.length !== 2) || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return (0,_permute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\n\nfunction compareDefined(compare = _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n  if (compare === _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nfunction ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/subset.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/subset.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ subset)\n/* harmony export */ });\n/* harmony import */ var _superset_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./superset.js */ \"(ssr)/./node_modules/d3-array/src/superset.js\");\n\n\nfunction subset(values, other) {\n  return (0,_superset_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(other, values);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1YnNldC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQzs7QUFFdEI7QUFDZixTQUFTLHdEQUFRO0FBQ2pCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcc3Vic2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBzdXBlcnNldCBmcm9tIFwiLi9zdXBlcnNldC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzdWJzZXQodmFsdWVzLCBvdGhlcikge1xuICByZXR1cm4gc3VwZXJzZXQob3RoZXIsIHZhbHVlcyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/subset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sum.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/sum.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ sum)\n/* harmony export */ });\nfunction sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1bS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxzdW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc3VtKHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgc3VtID0gMDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlID0gK3ZhbHVlKSB7XG4gICAgICAgIHN1bSArPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgZm9yIChsZXQgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICBpZiAodmFsdWUgPSArdmFsdWVvZih2YWx1ZSwgKytpbmRleCwgdmFsdWVzKSkge1xuICAgICAgICBzdW0gKz0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBzdW07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/superset.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/superset.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ superset)\n/* harmony export */ });\nfunction superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3N1cGVyc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGFBQWE7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxzdXBlcnNldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzdXBlcnNldCh2YWx1ZXMsIG90aGVyKSB7XG4gIGNvbnN0IGl0ZXJhdG9yID0gdmFsdWVzW1N5bWJvbC5pdGVyYXRvcl0oKSwgc2V0ID0gbmV3IFNldCgpO1xuICBmb3IgKGNvbnN0IG8gb2Ygb3RoZXIpIHtcbiAgICBjb25zdCBpbyA9IGludGVybihvKTtcbiAgICBpZiAoc2V0LmhhcyhpbykpIGNvbnRpbnVlO1xuICAgIGxldCB2YWx1ZSwgZG9uZTtcbiAgICB3aGlsZSAoKHt2YWx1ZSwgZG9uZX0gPSBpdGVyYXRvci5uZXh0KCkpKSB7XG4gICAgICBpZiAoZG9uZSkgcmV0dXJuIGZhbHNlO1xuICAgICAgY29uc3QgaXZhbHVlID0gaW50ZXJuKHZhbHVlKTtcbiAgICAgIHNldC5hZGQoaXZhbHVlKTtcbiAgICAgIGlmIChPYmplY3QuaXMoaW8sIGl2YWx1ZSkpIGJyZWFrO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cblxuZnVuY3Rpb24gaW50ZXJuKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZSAhPT0gbnVsbCAmJiB0eXBlb2YgdmFsdWUgPT09IFwib2JqZWN0XCIgPyB2YWx1ZS52YWx1ZU9mKCkgOiB2YWx1ZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/superset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js":
/*!*****************************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/freedmanDiaconis.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdFreedmanDiaconis)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../quantile.js */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n\n\n\nfunction thresholdFreedmanDiaconis(values, min, max) {\n  const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.75) - (0,_quantile_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9mcmVlZG1hbkRpYWNvbmlzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUNNOztBQUV2QjtBQUNmLFlBQVkscURBQUssY0FBYyx3REFBUSxpQkFBaUIsd0RBQVE7QUFDaEU7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHRocmVzaG9sZFxcZnJlZWRtYW5EaWFjb25pcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY291bnQgZnJvbSBcIi4uL2NvdW50LmpzXCI7XG5pbXBvcnQgcXVhbnRpbGUgZnJvbSBcIi4uL3F1YW50aWxlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZEZyZWVkbWFuRGlhY29uaXModmFsdWVzLCBtaW4sIG1heCkge1xuICBjb25zdCBjID0gY291bnQodmFsdWVzKSwgZCA9IHF1YW50aWxlKHZhbHVlcywgMC43NSkgLSBxdWFudGlsZSh2YWx1ZXMsIDAuMjUpO1xuICByZXR1cm4gYyAmJiBkID8gTWF0aC5jZWlsKChtYXggLSBtaW4pIC8gKDIgKiBkICogTWF0aC5wb3coYywgLTEgLyAzKSkpIDogMTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/freedmanDiaconis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/scott.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/scott.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdScott)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n/* harmony import */ var _deviation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../deviation.js */ \"(ssr)/./node_modules/d3-array/src/deviation.js\");\n\n\n\nfunction thresholdScott(values, min, max) {\n  const c = (0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values), d = (0,_deviation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zY290dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDUTs7QUFFekI7QUFDZixZQUFZLHFEQUFLLGNBQWMseURBQVM7QUFDeEM7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHRocmVzaG9sZFxcc2NvdHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvdW50IGZyb20gXCIuLi9jb3VudC5qc1wiO1xuaW1wb3J0IGRldmlhdGlvbiBmcm9tIFwiLi4vZGV2aWF0aW9uLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZFNjb3R0KHZhbHVlcywgbWluLCBtYXgpIHtcbiAgY29uc3QgYyA9IGNvdW50KHZhbHVlcyksIGQgPSBkZXZpYXRpb24odmFsdWVzKTtcbiAgcmV0dXJuIGMgJiYgZCA/IE1hdGguY2VpbCgobWF4IC0gbWluKSAqIE1hdGguY2JydChjKSAvICgzLjQ5ICogZCkpIDogMTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/scott.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/threshold/sturges.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-array/src/threshold/sturges.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ thresholdSturges)\n/* harmony export */ });\n/* harmony import */ var _count_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../count.js */ \"(ssr)/./node_modules/d3-array/src/count.js\");\n\n\nfunction thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log((0,_count_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values)) / Math.LN2) + 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RocmVzaG9sZC9zdHVyZ2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdDOztBQUVqQjtBQUNmLHdDQUF3QyxxREFBSztBQUM3QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHRocmVzaG9sZFxcc3R1cmdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY291bnQgZnJvbSBcIi4uL2NvdW50LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZFN0dXJnZXModmFsdWVzKSB7XG4gIHJldHVybiBNYXRoLm1heCgxLCBNYXRoLmNlaWwoTWF0aC5sb2coY291bnQodmFsdWVzKSkgLyBNYXRoLkxOMikgKyAxKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/threshold/sturges.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ticks.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/ticks.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ticks),\n/* harmony export */   tickIncrement: () => (/* binding */ tickIncrement),\n/* harmony export */   tickStep: () => (/* binding */ tickStep)\n/* harmony export */ });\nconst e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nfunction ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nfunction tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nfunction tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ticks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/transpose.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/transpose.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n\n\nfunction transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = (0,_min_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3RyYW5zcG9zZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQjs7QUFFWjtBQUNmO0FBQ0EsdUJBQXVCLG1EQUFHLDRDQUE0QyxRQUFRO0FBQzlFLDJEQUEyRCxRQUFRO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcdHJhbnNwb3NlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtaW4gZnJvbSBcIi4vbWluLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRyYW5zcG9zZShtYXRyaXgpIHtcbiAgaWYgKCEobiA9IG1hdHJpeC5sZW5ndGgpKSByZXR1cm4gW107XG4gIGZvciAodmFyIGkgPSAtMSwgbSA9IG1pbihtYXRyaXgsIGxlbmd0aCksIHRyYW5zcG9zZSA9IG5ldyBBcnJheShtKTsgKytpIDwgbTspIHtcbiAgICBmb3IgKHZhciBqID0gLTEsIG4sIHJvdyA9IHRyYW5zcG9zZVtpXSA9IG5ldyBBcnJheShuKTsgKytqIDwgbjspIHtcbiAgICAgIHJvd1tqXSA9IG1hdHJpeFtqXVtpXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRyYW5zcG9zZTtcbn1cblxuZnVuY3Rpb24gbGVuZ3RoKGQpIHtcbiAgcmV0dXJuIGQubGVuZ3RoO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/transpose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/union.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/union.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ union)\n/* harmony export */ });\n/* harmony import */ var internmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! internmap */ \"(ssr)/./node_modules/internmap/src/index.js\");\n\n\nfunction union(...others) {\n  const set = new internmap__WEBPACK_IMPORTED_MODULE_0__.InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3VuaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DOztBQUVyQjtBQUNmLGtCQUFrQixnREFBUztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2hzXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHVuaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7SW50ZXJuU2V0fSBmcm9tIFwiaW50ZXJubWFwXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVuaW9uKC4uLm90aGVycykge1xuICBjb25zdCBzZXQgPSBuZXcgSW50ZXJuU2V0KCk7XG4gIGZvciAoY29uc3Qgb3RoZXIgb2Ygb3RoZXJzKSB7XG4gICAgZm9yIChjb25zdCBvIG9mIG90aGVyKSB7XG4gICAgICBzZXQuYWRkKG8pO1xuICAgIH1cbiAgfVxuICByZXR1cm4gc2V0O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/union.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/variance.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/variance.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ variance)\n/* harmony export */ });\nfunction variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3ZhcmlhbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcdmFyaWFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdmFyaWFuY2UodmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGxldCBjb3VudCA9IDA7XG4gIGxldCBkZWx0YTtcbiAgbGV0IG1lYW4gPSAwO1xuICBsZXQgc3VtID0gMDtcbiAgaWYgKHZhbHVlb2YgPT09IHVuZGVmaW5lZCkge1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICBkZWx0YSA9IHZhbHVlIC0gbWVhbjtcbiAgICAgICAgbWVhbiArPSBkZWx0YSAvICsrY291bnQ7XG4gICAgICAgIHN1bSArPSBkZWx0YSAqICh2YWx1ZSAtIG1lYW4pO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgZGVsdGEgPSB2YWx1ZSAtIG1lYW47XG4gICAgICAgIG1lYW4gKz0gZGVsdGEgLyArK2NvdW50O1xuICAgICAgICBzdW0gKz0gZGVsdGEgKiAodmFsdWUgLSBtZWFuKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgaWYgKGNvdW50ID4gMSkgcmV0dXJuIHN1bSAvIChjb3VudCAtIDEpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/variance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/zip.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/zip.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ zip)\n/* harmony export */ });\n/* harmony import */ var _transpose_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transpose.js */ \"(ssr)/./node_modules/d3-array/src/transpose.js\");\n\n\nfunction zip() {\n  return (0,_transpose_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3ppcC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFeEI7QUFDZixTQUFTLHlEQUFTO0FBQ2xCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlbmppXFxEZXNrdG9wXFxHX1BST0dcXE5pY29sYXNcXGJ0ZGFzaC1lY29zeXN0ZW1cXGJ0ZGFzaHNcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcemlwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0cmFuc3Bvc2UgZnJvbSBcIi4vdHJhbnNwb3NlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHppcCgpIHtcbiAgcmV0dXJuIHRyYW5zcG9zZShhcmd1bWVudHMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/zip.js\n");

/***/ })

};
;