// lib/db/models.ts

// Base interface for common fields (e.g., timestamps)
interface BaseModel {
	createdAt?: Date;
	updatedAt?: Date;
}

// User table
export interface User extends BaseModel {
	id: number;
	email: string;
	username: string;
	first_name?: string;
	last_name?: string;
	image_url?: string;
	headline?: string;
	email_verified?: boolean;
	location?: string;
	bio?: string;
	phone?: string;
	expert_job_title?: string;
	authorized_company_admin?: boolean;
	authorized_job_admin?: boolean;
	authorized_events_admin?: boolean;
	website?: string;
	skill_ids?: number[];
	created_at: Date;
	updated_at: Date;
}

// Company table
export interface Company extends BaseModel {
	id: number;
	name: string;
	description?: string | null;
	logo_url?: string | null;
	header_url?: string | null;
	created_at: Date | string;
	updated_at: Date | string;
	locale?: string | null;
	website_url?: string | null;
	is_verified: boolean;
	is_featured: boolean;
	social_media?: { [key: string]: string } | string[] | null;
	netuids: (number | string)[];
	category_ids: (number | string)[];
	news_ids: (number | string)[];
	product_ids: (number | string)[];
	subnet_ids: (number | string)[];
	job_ids: (number | string)[];
	event_ids: (number | string)[];
	foundedyear?: number | string | null;
	teamsize?: string | null;
	location?: string | null;
}

// User Preferences table
export interface UserPreferences extends BaseModel {
	id: number;
	user_id: number;
	job_types?: string[];
	industries?: string[];
	travel_availability?: string;
	open_to_relocation?: boolean;
	locations?: string[];
	compensation_range?: string;
	additional_notes?: string;
	created_at?: Date;
	updated_at?: Date;
}

// Ad Slot table
export interface AdSlot extends BaseModel {
	id: number;
	name: string;
	width: number;
	height: number;
	is_active: boolean;
	description?: string;
	page: "all" | "home" | "subnets" | "companies" | "newsletter";
	price_cpm?: number;
	price_cpc?: number;
	estimated_views?: number;
	max_file_size?: string;
	allowed_ad_types?: string[];
}

// Ad Campaign table
export interface AdCampaign extends BaseModel {
	id: number;
	advertiser_id: number;
	manager_id?: number | null;
	name: string;
	total_budget?: number | null;
	budget_cpc?: number | null;
	budget_cpm?: number | null;
	start_date: Date | string;
	end_date: Date | string;
	status: "pending" | "approved" | "active" | "paused" | "rejected" | "completed";
	targeting?: Record<string, any> | null; // JSON object for targeting criteria
	created_at: Date | string;
	updated_at: Date | string;
}

// Ad table
export interface Ad extends BaseModel {
	id: number;
	campaign_id: number;
	slot_id: number;
	title: string;
	image_url: string;
	target_url: string;
	max_impressions?: number | null;
	max_clicks?: number | null;
	weight: number;
	status: "pending" | "active" | "paused" | "rejected";
	rejection_reason?: string | null;
	created_at: Date | string;
	updated_at: Date | string;
}

// Ad Impression table
export interface AdImpression extends BaseModel {
	id: number;
	ad_id: number;
	user_id?: number | null;
	session_id: string;
	ip_address: string;
	user_agent: string;
	country_code?: string | null;
	device_type?: string | null;
	viewed_time?: number | null; // Seconds ad was visible
}

// Ad Click table
export interface AdClick extends BaseModel {
	id: number;
	ad_id: number;
	user_id?: number | null;
	session_id: string;
	ip_address: string;
	user_agent: string;
	country_code?: string | null;
	device_type?: string | null;
	referrer_url?: string | null;
}

// Ad Target table
export interface AdTarget extends BaseModel {
	ad_id: number;
	key: string;
	value: string;
	operator: "equals" | "contains" | "startsWith" | "endsWith" | "in" | "notIn";
}

// Daily Ad Report table
export interface DailyAdReport extends BaseModel {
	ad_id: number;
	date: Date | string;
	impressions: number;
	clicks: number;
	spend: number;
	avg_view_time?: number | null;
	unique_users: number;
}

// Campaign Daily Report table
export interface CampaignDailyReport extends BaseModel {
	campaign_id: number;
	date: Date | string;
	impressions: number;
	clicks: number;
	spend: number;
	ctr: number;
	avg_cpc: number;
	avg_cpm: number;
}

// Billing Transaction table
export interface BillingTransaction extends BaseModel {
	advertiser_id: number;
	amount: number;
	currency: string;
	description: string;
	status: "pending" | "completed" | "failed" | "refunded";
	payment_method?: string | null;
	invoice_id?: string | null;
	campaign_id?: number | null;
}

// Notification table
export interface AdNotification extends BaseModel {
	user_id: number;
	type:
		| "campaign_approval"
		| "campaign_rejection"
		| "ad_approval"
		| "ad_rejection"
		| "payment_receipt"
		| "low_balance";
	title: string;
	message: string;
	is_read: boolean;
	related_id?: number | null; // Could be campaign_id, ad_id, etc.
	metadata?: Record<string, unknown> | null;
}

// Advertiser Balance table
export interface AdvertiserBalance extends BaseModel {
	user_id: number;
	balance: number;
	currency: string;
	last_transaction_id?: number | null;
	last_updated: Date | string;
}

// Rejection Reason table
export interface RejectionReason extends BaseModel {
	entity_type: "campaign" | "ad";
	code: string;
	description: string;
	is_active: boolean;
}
