"use client";

import { FeaturedCompanies } from "@/components/jobs/featured-companies";
import { JobsGrid } from "@/components/jobs/jobs-grid";
import { PopularCategories } from "@/components/jobs/popular-categories";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import { Category, Company, Job, Product, Subnet } from "@/lib/db/models";
import { Briefcase, ExternalLink, Filter, MapPin, Plus, Search } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface JobsClientProps {
	jobs: (Job & {
		company?: Company;
		category?: Category[];
		subnet?: Subnet[];
		product?: Product[];
	})[];
	authorized_job_admin?: boolean;
}

export default function JobsClientWrapper({ jobs, authorized_job_admin = false }: JobsClientProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [locationFilter, setLocationFilter] = useState("all");
	const [typeFilter, setTypeFilter] = useState("all");

	const filteredJobs = jobs.filter((job) => {
		const matchesSearch =
			searchTerm === "" ||
			job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
			job.company?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			job.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
			job.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
			job.type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
			job.category?.some((cat) => cat.name.toLowerCase().includes(searchTerm.toLowerCase()));

		const matchesLocation = locationFilter === "all" || job.location?.includes(locationFilter);

		const matchesType = typeFilter === "all" || job.type === typeFilter;

		return matchesSearch && matchesLocation && matchesType;
	});

	const locations = Array.from(new Set(jobs.map((job) => job.location)));

	return (
		<div className="container py-8">
			<div className="flex flex-col lg:flex-row gap-6">
				{/* Main content */}
				<div className="lg:w-3/4">
					<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
						<h1 className="text-3xl font-bold flex items-center gap-2">
							<Briefcase className="h-7 w-7" />
							Jobs
						</h1>
						{authorized_job_admin ? (
							<Button asChild>
								<Link href="/profile#jobs" className="gap-1">
									<Plus className="h-4 w-4" />
									Post a Job
								</Link>
							</Button>
						) : (
							<Button variant="outline" disabled className="gap-1">
								<Plus className="h-4 w-4" />
								Post a Job
							</Button>
						)}
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
						<div className="md:col-span-2">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
								<Input
									placeholder="Search jobs, companies, or keywords..."
									className="pl-9"
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
								/>
							</div>
						</div>
						<div className="grid grid-cols-2 gap-2">
							<Select value={locationFilter} onValueChange={setLocationFilter}>
								<SelectTrigger>
									<div className="flex items-center gap-2">
										<MapPin className="h-4 w-4" />
										<span className="truncate">
											{locationFilter === "all" ? "All Locations" : locationFilter}
										</span>
									</div>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Locations</SelectItem>
									{locations.map((location) => (
										<SelectItem key={location} value={location ?? ""}>
											{location}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select value={typeFilter} onValueChange={setTypeFilter}>
								<SelectTrigger>
									<div className="flex items-center gap-2">
										<Filter className="h-4 w-4" />
										<span className="truncate">
											{typeFilter === "all" ? "All Types" : typeFilter}
										</span>
									</div>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Types</SelectItem>
									<SelectItem value="FULL_TIME">Full-time</SelectItem>
									<SelectItem value="PART_TIME">Part-time</SelectItem>
									<SelectItem value="CONTRACT">Contract</SelectItem>
									<SelectItem value="FREELANCE">Freelance</SelectItem>
									<SelectItem value="INTERNSHIP">Internship</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className="mb-4">
						<p className="text-muted-foreground">
							Showing {filteredJobs.length} {filteredJobs.length === 1 ? "job" : "jobs"}
						</p>
					</div>

					<JobsGrid jobs={filteredJobs} loading={false} />
				</div>

				{/* Sidebar */}
				<div className="lg:w-1/4">
					<div className="sticky top-24">
						<Card className="bg-gradient-to-b from-purple-500 to-indigo-600 border-0 text-white overflow-hidden mb-6">
							<CardContent className="p-6">
								<div className="font-semibold text-sm mb-2 bg-white/20 w-fit px-2 py-0.5 rounded-sm">
									SPONSORED
								</div>
								<h3 className="text-xl font-bold mb-3">Become a Validator</h3>
								<p className="text-sm mb-4 text-white/90">
									Earn rewards by running a validator node on the Bittensor network.
								</p>
								<Button variant="secondary" size="sm" asChild className="w-full">
									<a href="#" className="gap-1">
										Learn More
										<ExternalLink className="h-3 w-3" />
									</a>
								</Button>
							</CardContent>
						</Card>

						<PopularCategories jobs={jobs} title="Popular Job Categories" maxCategories={5} />

						<FeaturedCompanies jobs={jobs} title="Featured Companies" maxCompanies={5} />
					</div>
				</div>
			</div>
		</div>
	);
}
