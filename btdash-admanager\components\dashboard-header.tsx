// components/dashboard-header.tsx
import { ModeToggle } from "@/components/mode-toggle";
import { auth0 } from "@/lib/auth0";
import { getInitials } from "@/lib/utils";
import { Layout } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface DashboardHeaderProps {
	isAdmin?: boolean;
}

export async function DashboardHeader({ isAdmin = false }: DashboardHeaderProps) {
	const session = await auth0.getSession();
	const user = session?.user;

	if (!user) return null;

	const displayName = user.name || user.nickname || user.email || "";

	return (
		<header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="flex h-16 w-full items-center px-4 sm:px-6 lg:px-8">
				<div className="flex items-center">
					<Link href={isAdmin ? "/admin" : "/dashboard"} className="flex items-center gap-2 font-bold">
						<Layout className="h-5 w-5" />
						<span className="text-lg">AdManager {isAdmin && "Admin"}</span>
					</Link>
				</div>

				<div className="ml-auto flex items-center gap-4">
					<ModeToggle />

					<div className="flex items-center gap-3">
						<Link
							href={isAdmin ? "/admin/settings?tab=account" : "/dashboard/settings?tab=account"}
							className="hidden sm:flex sm:items-center sm:gap-2"
						>
							{user.picture ? (
								<div className="relative h-8 w-8 rounded-full overflow-hidden">
									<Image
										src={user.picture}
										alt={displayName || "User Avatar"}
										fill
										sizes="32px"
										className="object-cover"
									/>
								</div>
							) : (
								<div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-bold">
									{getInitials(displayName)}
								</div>
							)}
							<span className="text-sm font-medium">{displayName}</span>
						</Link>
					</div>
				</div>
			</div>
		</header>
	);
}
