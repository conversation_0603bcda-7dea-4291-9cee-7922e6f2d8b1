/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/track/impression/route";
exports.ids = ["app/api/ads/track/impression/route"];
exports.modules = {

/***/ "(rsc)/./app/api/ads/track/impression/route.ts":
/*!***********************************************!*\
  !*** ./app/api/ads/track/impression/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        // Validate request body\n        let body;\n        try {\n            body = await request.json();\n        } catch (parseError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        const { ad_id, session_id, user_id, country_code, device_type, viewed_time } = body;\n        // Validate required fields\n        if (!ad_id || !session_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Missing required fields: ad_id and session_id\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate field types\n        if (typeof ad_id !== \"number\" || typeof session_id !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Invalid field types: ad_id must be number, session_id must be string\"\n            }, {\n                status: 400\n            });\n        }\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        if (!process.env.INTERNAL_API_KEY) {\n            console.error(\"INTERNAL_API_KEY not configured\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Forward to backend API with timeout\n        const headers = new Headers();\n        headers.set(\"Content-Type\", \"application/json\");\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 5000);\n        try {\n            const response = await fetch(`${API_BASE}/track/impression`, {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify({\n                    ad_id,\n                    session_id,\n                    user_id,\n                    ip_address: request.ip || \"unknown\",\n                    user_agent: request.headers.get(\"user-agent\") || \"unknown\",\n                    country_code,\n                    device_type,\n                    viewed_time\n                }),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const result = await response.json();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n            } else {\n                const errorText = await response.text().catch(()=>\"Unknown error\");\n                console.error(`Backend API error ${response.status}: ${errorText}`);\n                // Don't expose backend errors to client\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: \"Failed to track impression\"\n                }, {\n                    status: 500\n                });\n            }\n        } finally{\n            clearTimeout(timeoutId);\n        }\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        if (error instanceof Error) {\n            if (error.name === \"AbortError\") {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: \"Request timeout\"\n                }, {\n                    status: 504\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Failed to track impression\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ads/track/impression/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_track_impression_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ads/track/impression/route.ts */ \"(rsc)/./app/api/ads/track/impression/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/track/impression/route\",\n        pathname: \"/api/ads/track/impression\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/track/impression/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\ads\\\\track\\\\impression\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_track_impression_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&page=%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Ftrack%2Fimpression%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();