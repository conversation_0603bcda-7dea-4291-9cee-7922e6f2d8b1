import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const newsItems = [
  {
    title: "Bittensor Launches New AI Subnet",
    date: "2025-02-15",
    category: "New Subnet",
    snippet:
      "Bittensor expands its ecosystem with a cutting-edge AI subnet focused on...",
  },
  {
    title: "Record-breaking Transaction Volume on Data Storage Subnet",
    date: "2025-02-14",
    category: "Milestone",
    snippet:
      "The Bittensor Data Storage subnet achieves unprecedented transaction volumes...",
  },
  {
    title: "Upcoming Bittensor Protocol Upgrade",
    date: "2025-02-13",
    category: "Protocol Update",
    snippet:
      "Bittensor announces a major protocol upgrade scheduled for next month...",
  },
];

export function NewsSection() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Latest News</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {newsItems.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <h3 className="font-medium">{item.title}</h3>
                <Badge>{item.category}</Badge>
              </div>
              <p className="text-sm text-muted-foreground">{item.snippet}</p>
              <p className="text-xs text-muted-foreground">{item.date}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
