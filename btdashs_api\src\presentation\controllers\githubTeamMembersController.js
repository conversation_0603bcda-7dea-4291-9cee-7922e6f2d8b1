const GitHubService = require("../../application/services/GitHubService");
const db = require("../../infrastructure/database/knex");
const githubService = require("../../infrastructure/github/githubService");
const axios = require("axios");
const { updateEndpointStatus } = require("../../infrastructure/statusMonitor");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

/* --- DATABASE INTERACTIONS --- */

const getSubnetTeamMembers = asyncHandler(async (req, res) => {
	const { id } = req.params;

	const subnet = await db("subnets").select("github_team_members").where({ netuid: id }).first();

	if (!subnet) {
		return sendNotFound(res, "Subnet not found");
	}

	return sendSuccess(res, subnet.github_team_members || [], "Subnet team members retrieved successfully");
});

const updateSubnetTeamMembers = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const { teamMembers } = req.body;

	if (!Array.isArray(teamMembers)) {
		return sendError(res, "teamMembers must be an array", 400);
	}

	const [updatedSubnet] = await db("subnets")
		.where({ netuid: id })
		.update({ github_team_members: teamMembers })
		.returning("*");

	if (!updatedSubnet) {
		return sendNotFound(res, "Subnet not found");
	}

	return sendSuccess(res, updatedSubnet.github_team_members || [], "Subnet team members updated successfully");
});

const removeSubnetTeamMember = async (req, res) => {
	try {
		const { id, memberLogin } = req.params;

		const subnet = await db("subnets").where({ netuid: id }).first();

		if (!subnet) {
			return res.status(404).json({ message: "Subnet not found" });
		}

		const updatedMembers = (subnet.github_team_members || []).filter((m) => m.login !== memberLogin);

		await db("subnets").where({ netuid: id }).update({ github_team_members: updatedMembers });

		res.status(200).json({
			data: updatedMembers,
			removed: memberLogin,
		});
	} catch (error) {
		console.error("Error removing team member:", error);
		res.status(500).json({ message: "Error removing team member" });
	}
};

/* --- GITHUB API INTERACTIONS --- */

const fetchContributorsFromGitHub = async (repoUrl) => {
	const [owner, repo] = repoUrl.split("/").filter(Boolean).slice(-2);
	const contributors = await githubService.fetchRepoContributors(owner, repo);

	return contributors.map((c) => ({
		login: c.login,
		id: c.id,
		avatar_url: c.avatar_url,
		html_url: c.html_url,
		type: c.type,
		contributions: c.contributions,
	}));
};

const syncAllTeamsFromGitHub = async (req, res) => {
	try {
		const subnets = await db("subnets").select("netuid", "name", "github_repo").whereNotNull("github_repo");

		const results = await Promise.all(
			subnets.map(async (subnet) => {
				try {
					const contributors = await fetchContributorsFromGitHub(subnet.github_repo);
					await db("subnets").where({ netuid: subnet.netuid }).update({ github_team_members: contributors });

					return {
						netuid: subnet.netuid,
						name: subnet.name,
						success: true,
						contributors: contributors.length,
					};
				} catch (error) {
					console.error(`Failed to sync ${subnet.name}:`, error.message);
					return {
						netuid: subnet.netuid,
						name: subnet.name,
						success: false,
						error: error.message,
					};
				}
			})
		);

		const successCount = results.filter((r) => r.success).length;

		updateEndpointStatus(
			"/update/github-team-members",
			successCount === results.length,
			`Synced ${successCount}/${results.length} subnets`
		);

		res.status(200).json({
			message: `Synced ${successCount}/${subnets.length} subnets from GitHub`,
			rateLimit: await githubService.getRateLimitStatus(),
		});
	} catch (error) {
		console.error("Bulk sync failed:", error);

		updateEndpointStatus("/update/github-team-members", false, error.message);

		res.status(500).json({
			message: "Bulk sync operation failed",
			error: error.message,
			rateLimit: await githubService.getRateLimitStatus(),
		});
	}
};

module.exports = {
	getSubnetTeamMembers,
	updateSubnetTeamMembers,
	removeSubnetTeamMember,
	fetchContributorsFromGitHub,
	syncAllTeamsFromGitHub,
};
