// src/application/services/SkillsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview Skills Service - Handles skill management operations
 * 
 * This service manages skills in the platform, providing CRUD operations
 * and skill-related business logic for both public skills directory
 * and user skill associations.
 * 
 * Key responsibilities:
 * - Public skills directory management
 * - Skill CRUD operations
 * - Skill search and filtering
 * - Data validation and sanitization
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class SkillsService extends BaseService {
  constructor() {
    super("dtm_base.skills", "Skill");
  }

  /**
   * Get all skills with default sorting by name
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of skills
   */
  async getAllSkills(filters = {}, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'name', direction: 'asc' },
        ...options
      };

      return await this.getAll(filters, queryOptions);
    } catch (error) {
      logger.error("Error getting all skills", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get skill by ID
   * @param {number} id - Skill ID
   * @returns {Promise<Object|null>} Skill object or null if not found
   */
  async getSkillById(id) {
    try {
      return await this.getById(id);
    } catch (error) {
      logger.error("Error getting skill by ID", { error, id });
      throw error;
    }
  }

  /**
   * Create a new skill
   * @param {Object} skillData - Skill data
   * @returns {Promise<Object>} Created skill object
   */
  async createSkill(skillData) {
    try {
      const newSkill = await this.create(skillData);
      logger.info("Skill created", { skill_id: newSkill.id });
      return newSkill;
    } catch (error) {
      logger.error("Error creating skill", { error, skillData });
      throw error;
    }
  }

  /**
   * Update a skill
   * @param {number} id - Skill ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated skill object
   */
  async updateSkill(id, updateData) {
    try {
      const updatedSkill = await this.updateById(id, updateData);
      logger.info("Skill updated", { skill_id: id });
      return updatedSkill;
    } catch (error) {
      logger.error("Error updating skill", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete a skill
   * @param {number} id - Skill ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteSkill(id) {
    try {
      const result = await this.deleteById(id);
      logger.info("Skill deleted", { skill_id: id });
      return result;
    } catch (error) {
      logger.error("Error deleting skill", { error, id });
      throw error;
    }
  }

  /**
   * Search skills by name or description
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of matching skills
   */
  async searchSkills(searchTerm, options = {}) {
    try {
      const db = require("../../infrastructure/database/knex");
      
      let query = db(this.tableName)
        .where('name', 'ilike', `%${searchTerm}%`)
        .orWhere('description', 'ilike', `%${searchTerm}%`);

      // Apply ordering
      if (options.orderBy) {
        const { column, direction = 'asc' } = options.orderBy;
        query = query.orderBy(column, direction);
      } else {
        query = query.orderBy('name', 'asc');
      }

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.offset(options.offset);
      }

      const skills = await query;
      
      logger.info("Skills searched", { 
        searchTerm, 
        resultCount: skills.length,
        options 
      });
      
      return skills;
    } catch (error) {
      logger.error("Error searching skills", { error, searchTerm, options });
      throw new Error(`Failed to search skills: ${error.message}`);
    }
  }

  /**
   * Get popular skills (most used by users)
   * @param {number} limit - Number of skills to return
   * @returns {Promise<Array>} Array of popular skills
   */
  async getPopularSkills(limit = 10) {
    try {
      const db = require("../../infrastructure/database/knex");
      
      const skills = await db(this.tableName)
        .leftJoin('user_skills', `${this.tableName}.id`, 'user_skills.skill_id')
        .select(
          `${this.tableName}.*`,
          db.raw('COUNT(user_skills.skill_id) as usage_count')
        )
        .groupBy(`${this.tableName}.id`)
        .orderBy('usage_count', 'desc')
        .limit(limit);

      logger.info("Popular skills retrieved", { 
        count: skills.length,
        limit 
      });
      
      return skills;
    } catch (error) {
      logger.error("Error getting popular skills", { error, limit });
      throw new Error(`Failed to get popular skills: ${error.message}`);
    }
  }

  /**
   * Get skills by category
   * @param {string} category - Skill category
   * @returns {Promise<Array>} Array of skills in the category
   */
  async getSkillsByCategory(category) {
    try {
      const skills = await this.getAll({ category }, {
        orderBy: { column: 'name', direction: 'asc' }
      });

      logger.info("Skills retrieved by category", { 
        category,
        count: skills.length 
      });
      
      return skills;
    } catch (error) {
      logger.error("Error getting skills by category", { error, category });
      throw new Error(`Failed to get skills by category: ${error.message}`);
    }
  }
}

module.exports = new SkillsService();
