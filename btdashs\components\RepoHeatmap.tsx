"use client";

import { useEffect, useState } from "react";
import { format, subDays, eachDayOfInterval } from "date-fns";

interface Props {
  owner: string;
  repo: string;
}

interface DayData {
  date: string;
  count: number;
}

const RepoHeatmap = ({ owner, repo }: Props) => {
  const [data, setData] = useState<DayData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await fetch(`/api/commits?owner=${owner}&repo=${repo}`);
        const json = await res.json();

        if (json.error) throw new Error(json.error);

        const dateMap: Record<string, number> = {};
        json.commits.forEach((c: any) => {
          const date = c.commit.author.date.slice(0, 10);
          dateMap[date] = (dateMap[date] || 0) + 1;
        });

        const days = eachDayOfInterval({
          start: subDays(new Date(), 365),
          end: new Date(),
        });

        const mapped = days.map((date) => {
          const key = format(date, "yyyy-MM-dd");
          return { date: key, count: dateMap[key] || 0 };
        });

        setData(mapped);
      } catch (err: any) {
        console.error(err);
        setError(err.message || "Failed to load heatmap");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [owner, repo]);

  const getLevel = (count: number) => {
    if (count === 0) return "level-0";
    if (count < 2) return "level-1";
    if (count < 5) return "level-2";
    if (count < 10) return "level-3";
    return "level-4";
  };

  return (
    <div>
      <h2 className="text-xl mb-4">
        Commit Activity:{" "}
        <code>
          {owner}/{repo}
        </code>
      </h2>

      {loading && <div className="text-gray-500">Loading heatmap...</div>}
      {error && <div className="text-red-600">❌ {error}</div>}

      {!loading && !error && (
        <div className="flex">
          <div className="flex flex-col text-xs mr-2 text-gray-500">
            {["M", "W", "F"].map((d, i) => (
              <div key={i} style={{ height: 14 }}>
                {d}
              </div>
            ))}
          </div>

          <div>
            <div className="grid grid-cols-53 gap-1">
              {data.map((day, i) => (
                <div
                  key={i}
                  className={`w-3 h-3 rounded-sm ${getLevel(day.count)}`}
                  title={`${day.date}: ${day.count} commit${
                    day.count !== 1 ? "s" : ""
                  }`}
                />
              ))}
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              {[0, 60, 120, 180, 240, 300].map((offset) => {
                const labelDate = subDays(new Date(), 365 - offset);
                return <span key={offset}>{format(labelDate, "MMM")}</span>;
              })}
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .level-0 {
          background-color: #ebedf0;
        }
        .level-1 {
          background-color: #c6e48b;
        }
        .level-2 {
          background-color: #7bc96f;
        }
        .level-3 {
          background-color: #239a3b;
        }
        .level-4 {
          background-color: #196127;
        }
      `}</style>
    </div>
  );
};

export default RepoHeatmap;
