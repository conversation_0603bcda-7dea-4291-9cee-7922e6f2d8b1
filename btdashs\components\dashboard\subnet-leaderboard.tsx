import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Trophy, Zap, Clock } from "lucide-react"

const leaderboardData = [
  { id: 1, name: "AI Inference Subnet", score: 95, profitability: "High", efficiency: "95%", popularity: 10000 },
  { id: 2, name: "Data Validation Subnet", score: 92, profitability: "Medium", efficiency: "98%", popularity: 8500 },
  { id: 3, name: "Storage Subnet", score: 88, profitability: "Medium", efficiency: "92%", popularity: 7500 },
  { id: 4, name: "Edge Computing Subnet", score: 85, profitability: "High", efficiency: "90%", popularity: 9000 },
  { id: 5, name: "NLP Subnet", score: 82, profitability: "Low", efficiency: "94%", popularity: 6500 },
]

export function SubnetLeaderboard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Subnet Leaderboard</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Rank</TableHead>
              <TableHead>Subnet</TableHead>
              <TableHead>Score</TableHead>
              <TableHead>Profitability</TableHead>
              <TableHead>Efficiency</TableHead>
              <TableHead>Popularity</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {leaderboardData.map((subnet, index) => (
              <TableRow key={subnet.id}>
                <TableCell>
                  {index === 0 ? (
                    <Trophy className="h-6 w-6 text-yellow-500" />
                  ) : (
                    <span className="font-bold">{index + 1}</span>
                  )}
                </TableCell>
                <TableCell className="font-medium">{subnet.name}</TableCell>
                <TableCell>{subnet.score}</TableCell>
                <TableCell>
                  <Badge variant={subnet.profitability === "High" ? "default" : "secondary"}>
                    <Zap className="h-3 w-3 mr-1" />
                    {subnet.profitability}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="gap-1">
                    <Clock className="h-3 w-3" />
                    {subnet.efficiency}
                  </Badge>
                </TableCell>
                <TableCell>{subnet.popularity.toLocaleString()} users</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

