-- ============================================================================
-- BTDash API - Phase 2 Seed Data
-- ============================================================================
-- This script inserts initial reference data for Phase 2 features
-- ============================================================================

-- ============================================================================
-- REJECTION REASONS SEED DATA
-- ============================================================================

-- Campaign rejection reasons
INSERT INTO dtm_ads.rejection_reasons (entity_type, code, description, is_active) VALUES
('campaign', 'INAPPROPRIATE_CONTENT', 'Campaign contains inappropriate or offensive content', true),
('campaign', 'MISLEADING_CLAIMS', 'Campaign makes false or misleading claims', true),
('campaign', 'POOR_QUALITY', 'Campaign materials are of poor quality or unprofessional', true),
('campaign', 'TRADEMARK_VIOLATION', 'Campaign violates trademark or copyright laws', true),
('campaign', 'PROHIBITED_PRODUCT', 'Campaign promotes prohibited products or services', true),
('campaign', 'INCOMPLETE_INFORMATION', 'Campaign lacks required information or documentation', true),
('campaign', 'BUDGET_INSUFFICIENT', 'Campaign budget is insufficient for the requested duration', true),
('campaign', 'TARGET_MISMATCH', 'Campaign targeting does not align with platform audience', true),
('campaign', 'TECHNICAL_ISSUES', 'Campaign has technical issues that prevent proper display', true),
('campaign', 'POLICY_VIOLATION', 'Campaign violates platform advertising policies', true)
ON CONFLICT (entity_type, code) DO NOTHING;

-- Ad rejection reasons
INSERT INTO dtm_ads.rejection_reasons (entity_type, code, description, is_active) VALUES
('ad', 'IMAGE_QUALITY', 'Ad image is of poor quality, blurry, or pixelated', true),
('ad', 'WRONG_DIMENSIONS', 'Ad image does not meet required dimensions for the slot', true),
('ad', 'INAPPROPRIATE_IMAGE', 'Ad image contains inappropriate or offensive content', true),
('ad', 'MISLEADING_TEXT', 'Ad text is misleading or makes false claims', true),
('ad', 'BROKEN_LINK', 'Ad target URL is broken or leads to inappropriate content', true),
('ad', 'TRADEMARK_ISSUE', 'Ad uses unauthorized trademarks or copyrighted material', true),
('ad', 'EXCESSIVE_TEXT', 'Ad contains too much text relative to image content', true),
('ad', 'POOR_DESIGN', 'Ad design is unprofessional or does not meet quality standards', true),
('ad', 'MALWARE_DETECTED', 'Ad or target URL contains malware or suspicious content', true),
('ad', 'POLICY_VIOLATION', 'Ad violates platform advertising policies', true),
('ad', 'DUPLICATE_CONTENT', 'Ad is a duplicate of existing approved content', true),
('ad', 'INVALID_FORMAT', 'Ad file format is not supported or corrupted', true)
ON CONFLICT (entity_type, code) DO NOTHING;

-- ============================================================================
-- SAMPLE ADMIN USER (Optional - for testing)
-- ============================================================================

-- Note: This assumes you have a user with ID 1 that you want to make an admin
-- Adjust the user_id as needed for your environment
-- UPDATE dtm_base.users SET is_admin = TRUE WHERE id = 1;

-- ============================================================================
-- SAMPLE ADVERTISER BALANCE (Optional - for testing)
-- ============================================================================

-- Create sample advertiser balances for testing
-- Note: Adjust user_ids as needed for your environment
/*
INSERT INTO dtm_ads.advertiser_balances (user_id, balance, currency) VALUES
(1, 1000.00, 'USD'),
(2, 500.00, 'USD'),
(3, 250.00, 'USD')
ON CONFLICT (user_id) DO NOTHING;
*/

-- ============================================================================
-- SAMPLE BILLING TRANSACTIONS (Optional - for testing)
-- ============================================================================

-- Create sample billing transactions for testing
-- Note: Adjust advertiser_ids as needed for your environment
/*
INSERT INTO dtm_ads.billing_transactions (advertiser_id, amount, currency, description, status) VALUES
(1, 1000.00, 'USD', 'Initial deposit via credit card', 'completed'),
(1, -25.50, 'USD', 'Campaign spend for Campaign #1', 'completed'),
(1, -15.75, 'USD', 'Campaign spend for Campaign #1', 'completed'),
(2, 500.00, 'USD', 'Initial deposit via PayPal', 'completed'),
(2, -45.25, 'USD', 'Campaign spend for Campaign #2', 'completed'),
(3, 250.00, 'USD', 'Initial deposit via bank transfer', 'completed')
ON CONFLICT DO NOTHING;
*/

-- ============================================================================
-- SAMPLE NOTIFICATIONS (Optional - for testing)
-- ============================================================================

-- Create sample notifications for testing
-- Note: Adjust user_ids as needed for your environment
/*
INSERT INTO dtm_ads.ad_notifications (user_id, type, title, message, is_read) VALUES
(1, 'campaign_approval', 'Campaign Approved', 'Your campaign "Summer Sale 2024" has been approved and is now active.', false),
(1, 'low_balance', 'Low Balance Warning', 'Your account balance is low ($25.50). Please add funds to continue running campaigns.', false),
(2, 'ad_rejection', 'Ad Rejected', 'Your ad "Product Banner" has been rejected due to poor image quality.', true),
(2, 'campaign_approval', 'Campaign Approved', 'Your campaign "Holiday Special" has been approved and is now active.', true),
(3, 'payment_receipt', 'Payment Received', 'We have received your payment of $250.00. Your balance has been updated.', false)
ON CONFLICT DO NOTHING;
*/

-- ============================================================================
-- SAMPLE AD TARGETS (Optional - for testing)
-- ============================================================================

-- Create sample targeting rules for testing
-- Note: Adjust ad_ids as needed for your environment
/*
INSERT INTO dtm_ads.ad_targets (ad_id, key, value, operator) VALUES
(1, 'country_code', 'US', 'equals'),
(1, 'device_type', 'desktop', 'equals'),
(1, 'age', '25', 'gte'),
(1, 'age', '54', 'lte'),
(2, 'country_code', 'CA', 'equals'),
(2, 'country_code', 'US', 'equals'),
(2, 'device_type', 'mobile', 'equals'),
(3, 'language', 'en', 'equals'),
(3, 'interest', 'technology', 'contains')
ON CONFLICT DO NOTHING;
*/

-- ============================================================================
-- UTILITY FUNCTIONS FOR TESTING
-- ============================================================================

-- Function to create a test advertiser with balance
CREATE OR REPLACE FUNCTION create_test_advertiser(
    p_email VARCHAR(255),
    p_username VARCHAR(255),
    p_first_name VARCHAR(255),
    p_last_name VARCHAR(255),
    p_initial_balance DECIMAL(10,2) DEFAULT 1000.00
) RETURNS INTEGER AS $$
DECLARE
    v_user_id INTEGER;
BEGIN
    -- Insert user
    INSERT INTO dtm_base.users (email, username, first_name, last_name, created_at, updated_at)
    VALUES (p_email, p_username, p_first_name, p_last_name, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO v_user_id;
    
    -- Create advertiser balance
    INSERT INTO dtm_ads.advertiser_balances (user_id, balance, currency)
    VALUES (v_user_id, p_initial_balance, 'USD');
    
    -- Create initial deposit transaction
    INSERT INTO dtm_ads.billing_transactions (advertiser_id, amount, currency, description, status)
    VALUES (v_user_id, p_initial_balance, 'USD', 'Initial deposit for testing', 'completed');
    
    RETURN v_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create test admin user
CREATE OR REPLACE FUNCTION create_test_admin(
    p_email VARCHAR(255),
    p_username VARCHAR(255),
    p_first_name VARCHAR(255),
    p_last_name VARCHAR(255)
) RETURNS INTEGER AS $$
DECLARE
    v_user_id INTEGER;
BEGIN
    -- Insert user
    INSERT INTO dtm_base.users (email, username, first_name, last_name, is_admin, created_at, updated_at)
    VALUES (p_email, p_username, p_first_name, p_last_name, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id INTO v_user_id;
    
    RETURN v_user_id;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- EXAMPLE USAGE OF UTILITY FUNCTIONS
-- ============================================================================

-- Uncomment these lines to create test users:
/*
SELECT create_test_advertiser('<EMAIL>', 'testadvertiser', 'Test', 'Advertiser', 1500.00);
SELECT create_test_admin('<EMAIL>', 'testadmin', 'Test', 'Admin');
*/

-- ============================================================================
-- DATA VALIDATION QUERIES
-- ============================================================================

-- Query to verify rejection reasons were inserted
-- SELECT entity_type, code, description FROM dtm_ads.rejection_reasons ORDER BY entity_type, code;

-- Query to check table structures
-- SELECT table_name, column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_schema = 'dtm_ads' 
-- ORDER BY table_name, ordinal_position;

-- Query to verify indexes were created
-- SELECT schemaname, tablename, indexname, indexdef 
-- FROM pg_indexes 
-- WHERE schemaname = 'dtm_ads' 
-- ORDER BY tablename, indexname;
