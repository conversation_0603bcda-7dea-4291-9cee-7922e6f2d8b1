const ProductsService = require("../../application/services/ProductsService");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

const getAllProducts = asyncHandler(async (req, res) => {
	const products = await ProductsService.getAllProducts();
	return sendSuccess(res, products, "Products retrieved successfully");
});

const getProductById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const product = await ProductsService.getProductById(id);
	if (!product) {
		return sendNotFound(res, "Product not found");
	}
	return sendSuccess(res, product, "Product retrieved successfully");
});

const getProductsBySubnetId = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const products = await ProductsService.getProductsBySubnetId(id);
	return sendSuccess(res, products, "Products retrieved successfully");
});

const getFeaturedProducts = asyncHandler(async (req, res) => {
	const { flag } = req.params;
	const products = await ProductsService.getAllProducts({ featured: flag });
	return sendSuccess(res, products, "Featured products retrieved successfully");
});

module.exports = {
	getAllProducts,
	getProductById,
	getProductsBySubnetId,
	getFeaturedProducts,
};
