"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/delaunator";
exports.ids = ["vendor-chunks/delaunator"];
exports.modules = {

/***/ "(ssr)/./node_modules/delaunator/index.js":
/*!******************************************!*\
  !*** ./node_modules/delaunator/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunator)\n/* harmony export */ });\n/* harmony import */ var robust_predicates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! robust-predicates */ \"(ssr)/./node_modules/robust-predicates/index.js\");\n\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\n\n\nclass Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if ((0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], (0,robust_predicates__WEBPACK_IMPORTED_MODULE_0__.orient2d)(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/delaunator/index.js\n");

/***/ })

};
;