"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const subnets = [
  {
    id: "subnet-1",
    name: "AI Inference Subnet",
    tps: 1000,
    responseTime: "100ms",
    uptime: "99.99%",
    rewards: "50K TAO/day",
  },
  {
    id: "subnet-2",
    name: "Data Validation Subnet",
    tps: 5000,
    responseTime: "50ms",
    uptime: "99.95%",
    rewards: "30K TAO/day",
  },
  {
    id: "subnet-3",
    name: "Storage Subnet",
    tps: 2000,
    responseTime: "200ms",
    uptime: "99.90%",
    rewards: "40K TAO/day",
  },
  {
    id: "subnet-4",
    name: "Edge Computing Subnet",
    tps: 3000,
    responseTime: "75ms",
    uptime: "99.98%",
    rewards: "45K TAO/day",
  },
]

export function SubnetComparison() {
  const [subnet1, setSubnet1] = useState(subnets[0])
  const [subnet2, setSubnet2] = useState(subnets[1])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Compare Subnets</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-4">
          <Select value={subnet1.id} onValueChange={(value) => setSubnet1(subnets.find((s) => s.id === value)!)}>
            <SelectTrigger>
              <SelectValue placeholder="Select subnet 1" />
            </SelectTrigger>
            <SelectContent>
              {subnets.map((subnet) => (
                <SelectItem key={subnet.id} value={subnet.id}>
                  {subnet.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={subnet2.id} onValueChange={(value) => setSubnet2(subnets.find((s) => s.id === value)!)}>
            <SelectTrigger>
              <SelectValue placeholder="Select subnet 2" />
            </SelectTrigger>
            <SelectContent>
              {subnets.map((subnet) => (
                <SelectItem key={subnet.id} value={subnet.id}>
                  {subnet.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Metric</TableHead>
              <TableHead>{subnet1.name}</TableHead>
              <TableHead>{subnet2.name}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>TPS</TableCell>
              <TableCell>{subnet1.tps}</TableCell>
              <TableCell>{subnet2.tps}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Response Time</TableCell>
              <TableCell>{subnet1.responseTime}</TableCell>
              <TableCell>{subnet2.responseTime}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Uptime</TableCell>
              <TableCell>{subnet1.uptime}</TableCell>
              <TableCell>{subnet2.uptime}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Rewards</TableCell>
              <TableCell>{subnet1.rewards}</TableCell>
              <TableCell>{subnet2.rewards}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

