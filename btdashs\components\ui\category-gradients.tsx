export function getCategoryGradient(category: string): string {
  const lowerCategory = category.toLowerCase();

  if (lowerCategory.includes("text")) return "from-blue-400 to-cyan-500";
  if (lowerCategory.includes("image")) return "from-orange-400 to-amber-500";
  if (lowerCategory.includes("text processing and nlp"))
    return "from-green-400 to-emerald-500";
  if (lowerCategory.includes("image generation"))
    return "from-yellow-400 to-amber-500";
  if (lowerCategory.includes("general ai and multimodal"))
    return "from-purple-400 to-indigo-500";
  if (lowerCategory.includes("decentralized compute"))
    return "from-teal-400 to-cyan-500";
  if (lowerCategory.includes("data collection"))
    return "from-rose-400 to-pink-500";
  if (lowerCategory.includes("pretraining"))
    return "from-indigo-400 to-violet-500";
  if (lowerCategory.includes("text to speech"))
    return "from-cyan-400 to-blue-500";
  if (lowerCategory.includes("blockchain analytics"))
    return "from-blue-500 to-indigo-600";
  if (lowerCategory.includes("decentralized marketing"))
    return "from-teal-500 to-emerald-600";
  if (lowerCategory.includes("financial prediction"))
    return "from-green-500 to-teal-600";
  if (lowerCategory.includes("3d asset creation"))
    return "from-emerald-400 to-teal-600";
  if (lowerCategory.includes("security and verification"))
    return "from-red-400 to-rose-500";
  if (lowerCategory.includes("machine translation"))
    return "from-cyan-500 to-emerald-600";
  if (lowerCategory.includes("governance"))
    return "from-blue-500 to-violet-600";
  if (lowerCategory.includes("ai data networks"))
    return "from-fuchsia-400 to-purple-500";
  if (lowerCategory.includes("ai-detection"))
    return "from-red-500 to-orange-600";
  if (lowerCategory.includes("defi-lending"))
    return "from-yellow-500 to-orange-600";
  if (lowerCategory.includes("decentralized training"))
    return "from-violet-500 to-indigo-600";
  if (lowerCategory.includes("ai drug discovery"))
    return "from-emerald-500 to-lime-600";
  if (lowerCategory.includes("agent")) return "from-purple-500 to-violet-600";
  if (lowerCategory.includes("time-series prediction"))
    return "from-blue-500 to-teal-600";
  if (lowerCategory.includes("inference"))
    return "from-indigo-500 to-purple-600";
  if (lowerCategory.includes("ai model development"))
    return "from-cyan-500 to-indigo-600";
  if (lowerCategory.includes("ai data pipeline"))
    return "from-rose-500 to-fuchsia-600";
  if (lowerCategory.includes("distributed training"))
    return "from-teal-500 to-blue-600";
  if (lowerCategory.includes("generative ai"))
    return "from-pink-500 to-purple-600";
  if (lowerCategory.includes("components & tooling"))
    return "from-gray-400 to-slate-500";
  if (lowerCategory.includes("scientific research"))
    return "from-violet-500 to-blue-600";
  if (lowerCategory.includes("general infrastructure"))
    return "from-slate-500 to-gray-600";
  if (lowerCategory.includes("ai powered tools"))
    return "from-orange-500 to-yellow-600";
  if (lowerCategory.includes("model hosting"))
    return "from-blue-400 to-indigo-600";
  if (lowerCategory.includes("predictive system"))
    return "from-teal-500 to-green-600";
  if (lowerCategory.includes("agentic ai"))
    return "from-fuchsia-500 to-violet-600";
  if (lowerCategory.includes("for sale")) return "from-emerald-500 to-lime-600";
  if (lowerCategory.includes("bitcoin mining"))
    return "from-rose-500 to-pink-600";
  if (lowerCategory.includes("ai-fi")) return "from-yellow-500 to-amber-600";
  if (lowerCategory.includes("video processing"))
    return "from-emerald-500 to-teal-600";

  // Default gradient for other categories
  return "from-gray-500 to-slate-600";
}
