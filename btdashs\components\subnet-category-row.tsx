"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Subnet, SubnetMetric } from "@/lib/db/models";
import { cn } from "@/lib/utils";
import { formatNumber } from "@/utils/formatNumber";
import throttle from "lodash.throttle";
import { ChevronLeft, ChevronRight, Circle, Github, Share } from "lucide-react";
import Link from "next/link";
import React, { useCallback, useMemo, useRef, useState } from "react";
import { Line, LineChart, ResponsiveContainer } from "recharts";

const gradients = [
	"bg-gradient-to-br from-purple-500 to-indigo-600",
	"bg-gradient-to-br from-blue-500 to-cyan-600",
	"bg-gradient-to-br from-emerald-500 to-teal-600",
	"bg-gradient-to-br from-rose-500 to-pink-600",
	"bg-gradient-to-br from-amber-500 to-orange-600",
];

interface SubnetCategoryRowProps {
	categoryName: string;
	categoryId: number;
	isScrollable?: boolean;
	subnets: Subnet[];
	metrics: SubnetMetric[];
	isLoading?: boolean;
	error?: string | null;
}

export function SubnetCategoryRow({
	categoryName,
	categoryId,
	isScrollable = true,
	subnets = [],
	metrics = [],
	isLoading = false,
	error = null,
}: SubnetCategoryRowProps) {
	const scrollContainerRef = useRef<HTMLDivElement>(null);
	const [imageErrors, setImageErrors] = useState<{ [key: number]: boolean }>({});

	const scroll = useCallback((direction: "left" | "right") => {
		if (!scrollContainerRef.current) return;
		const container = scrollContainerRef.current;
		const cardWidth = 350;
		const scrollAmount = cardWidth * 3;

		container.scrollBy({
			left: direction === "left" ? -scrollAmount : scrollAmount,
			behavior: "smooth",
		});
	}, []);

	const throttledScroll = useMemo(() => throttle(scroll, 500, { leading: true, trailing: false }), [scroll]);

	const memoizedCards = useMemo(
		() =>
			subnets.map((subnet: Subnet, index: number) => (
				<div key={subnet.netuid} className="w-[350px] flex-shrink-0">
					<MemoizedSubnetCard
						subnet={subnet}
						index={index}
						metrics={metrics}
						imageErrors={imageErrors}
						setImageErrors={setImageErrors}
					/>
				</div>
			)),
		[subnets, metrics, imageErrors]
	);

	if (isLoading) {
		return (
			<div className="mb-8 overflow-hidden">
				<div className="flex justify-between items-center mb-4">
					<h2 className="text-2xl font-bold">{categoryName}</h2>
					{isScrollable && (
						<div className="flex space-x-2">
							<Button variant="outline" size="icon" aria-label="Scroll left" disabled>
								<ChevronLeft className="h-4 w-4" />
							</Button>
							<Button variant="outline" size="icon" aria-label="Scroll right" disabled>
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>
					)}
				</div>
				<div className="flex space-x-4">
					{Array.from({ length: 6 }).map((_, index) => (
						<div
							key={index}
							className="w-[350px] h-[220px] bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
						></div>
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="mb-8">
				<h2 className="text-2xl font-bold mb-4">{categoryName}</h2>
				<div className="text-red-500">Error loading subnets: {error}</div>
			</div>
		);
	}

	if (subnets.length === 0) {
		return (
			<div className="mb-8">
				<h2 className="text-2xl font-bold mb-4">{categoryName}</h2>
				<div className="text-muted-foreground">No subnets found in this category</div>
			</div>
		);
	}

	return (
		<div className="mb-8 overflow-hidden">
			<div className="flex justify-between items-center mb-4">
				<h2 className="text-2xl font-bold">{categoryName}</h2>
				{isScrollable && (
					<div className="flex space-x-2">
						<Button
							variant="outline"
							size="icon"
							onClick={() => throttledScroll("left")}
							aria-label="Scroll left"
						>
							<ChevronLeft className="h-4 w-4" />
						</Button>
						<Button
							variant="outline"
							size="icon"
							onClick={() => throttledScroll("right")}
							aria-label="Scroll right"
						>
							<ChevronRight className="h-4 w-4" />
						</Button>
					</div>
				)}
			</div>
			<div className="relative">
				{isScrollable ? (
					<div
						ref={scrollContainerRef}
						className="overflow-x-auto pb-4 hide-scrollbar"
						style={{
							scrollbarWidth: "none",
							msOverflowStyle: "none",
							transform: "translateZ(0)",
							willChange: "transform",
						}}
					>
						<div className="flex space-x-4 w-max">{memoizedCards}</div>
					</div>
				) : (
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
						{memoizedCards}
					</div>
				)}
			</div>
		</div>
	);
}

interface SubnetCardProps {
	subnet: Subnet;
	index: number;
	metrics: SubnetMetric[];
	imageErrors: { [key: number]: boolean };
	setImageErrors: React.Dispatch<React.SetStateAction<{ [key: number]: boolean }>>;
}

const SubnetCard = React.memo(function SubnetCard({
	subnet,
	index,
	metrics,
	imageErrors,
	setImageErrors,
}: SubnetCardProps) {
	const subnetMetric = metrics.find((metric) => metric.netuid === subnet.netuid);

	const chartData = useMemo(() => {
		if (!subnetMetric?.seven_day_prices) {
			return Array.from({ length: 7 }, (_, i) => ({
				day: i + 1,
				value: 0,
			}));
		}

		try {
			const prices =
				typeof subnetMetric.seven_day_prices === "string"
					? JSON.parse(subnetMetric.seven_day_prices)
					: subnetMetric.seven_day_prices;

			if (prices.length > 0) {
				const baseValue = parseFloat(prices[0]) || 1;
				return prices.map((price: string | number, index: number) => ({
					day: index + 1,
					value: ((parseFloat(price as string) - baseValue) / baseValue) * 100,
				}));
			}
		} catch (e) {
			console.error("Error processing seven_day_prices:", e);
		}

		return Array.from({ length: 7 }, (_, i) => ({
			day: i + 1,
			value: 0,
		}));
	}, [subnetMetric]);

	const handleImageError = useCallback(() => {
		setImageErrors((prev) => ({
			...prev,
			[subnet.netuid]: true,
		}));
	}, [subnet.netuid, setImageErrors]);

	const handleShareClick = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();
			e.stopPropagation();
			window.open(
				`https://twitter.com/intent/tweet?text=Check%20out%20${encodeURIComponent(
					subnet.name
				)}%20&url=https%3A%2F%2Ftao.xyz`,
				"_blank"
			);
		},
		[subnet.name]
	);

	const handleGithubClick = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();
			e.stopPropagation();
			if (subnet.github_repo) {
				window.open(subnet.github_repo, "_blank");
			}
		},
		[subnet.github_repo]
	);

	return (
		<div className="relative">
			<Link href={`/subnets/${subnet.netuid}`} prefetch={false} className="block">
				<Card
					className={cn(
						"w-full h-[220px] text-white hover:opacity-90 transition-opacity overflow-hidden",
						gradients[index % gradients.length]
					)}
				>
					<div className="p-4 h-full flex flex-col">
						<div className="flex items-start justify-between mb-2">
							<div className="flex items-center gap-2">
								{subnet.is_active && (
									<div className="flex items-center gap-1.5 bg-black/20 py-1 px-2 rounded-full text-xs">
										<Circle className="h-2 w-2 fill-green-400 text-green-400 animate-pulse" />
										Running
									</div>
								)}
								<div className="bg-black/20 py-1 px-2 rounded-full text-xs">SN {subnet.netuid}</div>
								{subnet.github_repo && (
									<button
										onClick={handleGithubClick}
										className="flex items-center gap-1 bg-black/20 p-1 rounded-full text-xs hover:underline"
									>
										<Github className="h-4 w-4" />
									</button>
								)}
							</div>
							<button onClick={handleShareClick} className="flex items-center gap-1 text-sm">
								<Share className="h-4 w-4" />
							</button>
						</div>
						<div className="flex items-center gap-2 mb-1">
							{subnet.image_url && !imageErrors[subnet.netuid] ? (
								<img
									src={subnet.image_url}
									alt={subnet.name}
									className="h-5 w-5 rounded-full"
									onError={handleImageError}
									loading="lazy"
									decoding="async"
								/>
							) : (
								<div className="h-7 w-7 rounded-full bg-white/20 flex items-center justify-center text-md">
									{subnet.subnet_symbol || subnet.name.charAt(0)}
								</div>
							)}
							<h3 className="font-semibold text-lg">{subnet.name}</h3>
						</div>
						<p className="text-sm text-white/90 mb-2 line-clamp-2">{subnet.description_short}</p>
						<div className="mt-auto flex items-end justify-between">
							<div>
								<div className="text-2xl font-bold">
									{formatNumber(subnetMetric?.alpha_price_tao, 5) || "N/A"} τ
								</div>
								<div className="text-xs text-white/75">
									{formatNumber(subnetMetric?.market_cap_tao) || "N/A"} MCap
								</div>
							</div>
							<div className="w-24 h-12">
								<ResponsiveContainer width="100%" height="100%">
									<LineChart data={chartData} margin={{ top: 2, right: 0, bottom: 2, left: 0 }}>
										<Line
											type="monotone"
											dataKey="value"
											stroke="#ffffff"
											strokeWidth={2}
											dot={false}
											isAnimationActive={false}
										/>
									</LineChart>
								</ResponsiveContainer>
							</div>
						</div>
					</div>
				</Card>
			</Link>
		</div>
	);
});

const MemoizedSubnetCard = React.memo(SubnetCard);
