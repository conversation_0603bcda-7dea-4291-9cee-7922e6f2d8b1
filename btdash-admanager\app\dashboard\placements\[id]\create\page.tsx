// app/dashboard/placements/[id]/create/page.tsx
import { fetchWithFallback } from "@/lib/utils";
import { cookies } from "next/headers";
import CreateCampaignClientWrapper from "./client-wrapper";

export const revalidate = 3600; // 1 hour

export default async function CreateCampaignPage({ params }: { params: { id: string } }) {
	const paramsData = await params;
	const id = paramsData.id;
	const cookieHeader = (await cookies()).toString();

	const [slotRes, compRes] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/placements/${id}`, {
			headers: { Cookie: cookieHeader },
			next: { tags: ["placement"] },
		}),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/user/company`, { headers: { Cookie: cookieHeader } }),
	]);

	if (!slotRes.data) {
		return <div>Placement not found</div>;
	}

	if (!compRes.data) {
		return <div>Company not found</div>;
	}

	return <CreateCampaignClientWrapper slot={slotRes.data} company={compRes.data} />;
}
