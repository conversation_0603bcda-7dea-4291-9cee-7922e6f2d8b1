// src/application/services/ExperienceService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * Experience Service - Handles user experience records business logic
 */
class ExperienceService extends BaseService {
	constructor() {
		super("dtm_base.user_experiences", "Experience");
	}

	/**
	 * Create an experience record for a user
	 * @param {number} userId - User ID
	 * @param {Object} experienceData - Experience data
	 * @returns {Promise<Object>} Created experience object
	 */
	async createUserExperience(userId, experienceData) {
		const {
			company_name,
			role,
			location,
			start_date,
			end_date,
			description,
			current_job
		} = experienceData;

		try {
			return await this.create({
				user_id: userId,
				company_name,
				role,
				location,
				start_date,
				end_date,
				description,
				current_job,
			});
		} catch (error) {
			logger.error("Error creating user experience", { error, userId });
			throw error;
		}
	}

	/**
	 * Get all experience records for a user
	 * @param {number} userId - User ID
	 * @returns {Promise<Array>} Array of experience records
	 */
	async getUserExperiences(userId) {
		try {
			return await this.getByUserId(userId);
		} catch (error) {
			logger.error("Error getting user experiences", { error, userId });
			throw error;
		}
	}

	/**
	 * Update an experience record
	 * @param {number} experienceId - Experience ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated experience object
	 */
	async updateUserExperience(experienceId, updateData) {
		const {
			company_name,
			role,
			location,
			start_date,
			end_date,
			description,
			current_job
		} = updateData;

		try {
			return await this.updateById(experienceId, {
				company_name,
				role,
				location,
				start_date,
				end_date,
				description,
				current_job,
			});
		} catch (error) {
			logger.error("Error updating user experience", { error, experienceId });
			throw error;
		}
	}

	/**
	 * Delete an experience record
	 * @param {number} experienceId - Experience ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteUserExperience(experienceId) {
		try {
			return await this.deleteById(experienceId);
		} catch (error) {
			logger.error("Error deleting user experience", { error, experienceId });
			throw error;
		}
	}
}

module.exports = new ExperienceService();
