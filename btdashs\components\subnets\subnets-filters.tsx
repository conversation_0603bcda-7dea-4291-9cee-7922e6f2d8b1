// components/subnets/subnets-filters.tsx
"use client";

import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search } from "lucide-react";

export function SubnetsFilters({
	category,
	setCategory,
	status,
	setStatus,
	searchQuery,
	setSearchQuery,
	categories,
}: {
	category: string;
	setCategory: (category: string) => void;
	status: string;
	setStatus: (status: string) => void;
	searchQuery: string;
	setSearchQuery: (query: string) => void;
	categories: Array<{ id: number; name: string }>;
}) {
	return (
		<div className="flex flex-col sm:flex-row gap-4 mb-6" suppressHydrationWarning>
			{/* Search Input */}
			<div className="relative flex-1">
				<Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
				<Input
					className="pl-8"
					placeholder="Search subnets..."
					value={searchQuery}
					onChange={(e) => setSearchQuery(e.target.value)}
				/>
			</div>

			{/* Category Filter */}
			<Select value={category} onValueChange={setCategory}>
				<SelectTrigger className="w-[180px]">
					<SelectValue placeholder="All Categories" />
				</SelectTrigger>
				<SelectContent>
					<SelectItem value="all">All Categories</SelectItem>
					{categories.map((cat) => (
						<SelectItem key={cat.id} value={cat.id.toString()}>
							{cat.name}
						</SelectItem>
					))}
				</SelectContent>
			</Select>

			{/* Status Filter */}
			<Select value={status} onValueChange={setStatus}>
				<SelectTrigger className="w-[180px]">
					<SelectValue placeholder="All Status" />
				</SelectTrigger>
				<SelectContent>
					<SelectItem value="all">All Status</SelectItem>
					<SelectItem value="active">Active</SelectItem>
					<SelectItem value="inactive">Inactive</SelectItem>
				</SelectContent>
			</Select>
		</div>
	);
}
