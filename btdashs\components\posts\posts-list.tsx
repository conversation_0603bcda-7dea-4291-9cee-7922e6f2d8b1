"use client"

import { useState } from "react"
import { <PERSON><PERSON>, Avatar<PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, MessageCircle, Rocket, Share2, MoreHorizontal } from "lucide-react"
import Image from "next/image"

interface Post {
  id: string
  author: {
    name: string
    username: string
    avatar: string
  }
  content: string
  title: string
  timestamp: string
  images?: string[]
  reactions: {
    likes: number
    comments: number
    shares: number
    rockets: number
  }
}

const posts: Post[] = [
  {
    id: "1",
    author: {
      name: "<PERSON>",
      username: "alice_ai",
      avatar: "/placeholder.svg",
    },
    title: "Introducing New Subnet Architecture",
    content: `We're excited to announce a new subnet architecture that improves scalability and performance. 
    This update includes:
    - Enhanced validator coordination
    - Improved consensus mechanism
    - Better resource allocation
    
    Check out the full documentation for more details!`,
    timestamp: "2 hours ago",
    images: [
      "https://sjc.microlink.io/2Yge5Ovykv5fA2-8o_EGnJD2AggH39fGwTeLL0siCDPrFQxhVFjmFFxIlVVoo3GvVx68TwHhEZeeSkAhxXEg4g.jpeg",
      "https://sjc.microlink.io/2Yge5Ovykv5fA2-8o_EGnJD2AggH39fGwTeLL0siCDPrFQxhVFjmFFxIlVVoo3GvVx68TwHhEZeeSkAhxXEg4g.jpeg",
    ],
    reactions: {
      likes: 156,
      comments: 32,
      shares: 18,
      rockets: 45,
    },
  },
  {
    id: "2",
    author: {
      name: "Bob Smith",
      username: "bob_validator",
      avatar: "/placeholder.svg",
    },
    title: "Performance Benchmarks: February 2025",
    content:
      "Just completed our monthly performance benchmarks. We're seeing a 40% improvement in transaction processing speed compared to last month. Here's a detailed breakdown of the results...",
    timestamp: "5 hours ago",
    reactions: {
      likes: 89,
      comments: 15,
      shares: 7,
      rockets: 23,
    },
  },
]

interface PostsListProps {
  sortBy: string
}

export function PostsList({ sortBy }: PostsListProps) {
  const [likedPosts, setLikedPosts] = useState<Set<string>>(new Set())

  const toggleLike = (postId: string) => {
    setLikedPosts((prev) => {
      const next = new Set(prev)
      if (next.has(postId)) {
        next.delete(postId)
      } else {
        next.add(postId)
      }
      return next
    })
  }

  return (
    <div className="space-y-6">
      {posts.map((post) => (
        <Card key={post.id}>
          <CardHeader className="flex flex-row items-start justify-between space-y-0">
            <div className="flex items-start gap-3">
              <Avatar>
                <AvatarImage src={post.author.avatar} alt={post.author.name} />
                <AvatarFallback>{post.author.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-semibold">{post.author.name}</div>
                <div className="text-sm text-muted-foreground">
                  @{post.author.username} · {post.timestamp}
                </div>
              </div>
            </div>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <h3 className="text-xl font-semibold">{post.title}</h3>
            <p className="whitespace-pre-line">{post.content}</p>

            {post.images && (
              <div className="grid grid-cols-2 gap-2 mt-4">
                {post.images.map((image, index) => (
                  <div key={index} className="relative aspect-video rounded-lg overflow-hidden">
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`Post image ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
            )}

            <div className="flex items-center justify-between pt-4">
              <Button variant="ghost" size="sm" className="flex items-center gap-1" onClick={() => toggleLike(post.id)}>
                <Heart className={`h-4 w-4 ${likedPosts.has(post.id) ? "fill-red-500 text-red-500" : ""}`} />
                <span>{post.reactions.likes}</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                <span>{post.reactions.comments}</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <Share2 className="h-4 w-4" />
                <span>{post.reactions.shares}</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex items-center gap-1">
                <Rocket className="h-4 w-4" />
                <span>{post.reactions.rockets}</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

