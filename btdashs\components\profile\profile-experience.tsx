"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { UserExperience } from "@/lib/db/models";
import { Building, Calendar, Pencil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface ProfileExperienceClientProps {
	initialExperience: UserExperience[];
}

export default function ProfileExperienceClient({ initialExperience }: ProfileExperienceClientProps) {
	const [experiences, setExperiences] = useState<UserExperience[]>(initialExperience);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [currentExperience, setCurrentExperience] = useState<Partial<UserExperience> | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleOpenDialog = (experience?: UserExperience) => {
		setCurrentExperience(
			experience || {
				company_name: "",
				role: "",
				start_date: undefined,
				end_date: undefined,
				current_job: false,
				description: "",
				location: "",
			}
		);
		setError(null);
		setIsDialogOpen(true);
	};

	const handleCloseDialog = () => {
		setIsDialogOpen(false);
		setCurrentExperience(null);
		setError(null);
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value, type } = e.target;
		const checked = type === "checkbox" ? (e.target as HTMLInputElement).checked : undefined;

		setCurrentExperience((prev) => ({
			...prev!,
			[name]: checked !== undefined ? checked : value,
			...(name === "current_job" && checked ? { end_date: undefined } : {}),
		}));
	};

	const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>, field: "start_date" | "end_date") => {
		const value = e.target.value;
		const date = value ? new Date(value) : undefined;

		setCurrentExperience((prev) => ({
			...prev!,
			[field]: date,
		}));
	};

	const formatDateForInput = (date?: Date | string | null) => {
		if (!date) return "";
		const dateObj = date instanceof Date ? date : new Date(date);
		return dateObj.toISOString().split("T")[0];
	};

	const handleSave = async () => {
		if (!currentExperience) return;

		setIsLoading(true);
		setError(null);

		try {
			const method = currentExperience.id ? "PUT" : "POST";
			const url = currentExperience.id
				? `/api/user/experiences/${currentExperience.id}`
				: "/api/user/experiences";

			const response = await fetch(url, {
				method,
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					...currentExperience,
					start_date: currentExperience.start_date
						? new Date(currentExperience.start_date).toISOString()
						: null,
					end_date: currentExperience.current_job
						? null
						: currentExperience.end_date
						? new Date(currentExperience.end_date).toISOString()
						: null,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to save experience");
			}

			const { data } = await response.json();
			const savedExperience = (method === "PUT" ? data : data.data) as UserExperience;

			setExperiences((prev) =>
				currentExperience.id
					? prev.map((exp) => (exp.id === savedExperience.id ? savedExperience : exp))
					: [savedExperience, ...prev]
			);

			handleCloseDialog();
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async (id: number) => {
		setIsLoading(true);
		try {
			const response = await fetch(`/api/user/experiences/${id}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to delete experience");
			}

			setExperiences((prev) => prev.filter((exp) => exp.id !== id));
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const formatDateDisplay = (date?: Date | string) => {
		if (!date) return "";
		const dateObj = new Date(date);
		return dateObj.toLocaleDateString("en-US", { month: "short", year: "numeric" });
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Work Experience</h2>
				<Button onClick={() => handleOpenDialog()}>
					<Plus className="mr-2 h-4 w-4" />
					Add Experience
				</Button>
			</div>

			{error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>}

			<div className="space-y-4">
				{experiences.length === 0 ? (
					<Card>
						<CardContent className="p-6 text-center text-gray-500">
							No work experiences added yet
						</CardContent>
					</Card>
				) : (
					experiences.map((exp) => (
						<Card key={exp.id}>
							<CardContent className="p-6">
								<div className="flex justify-between">
									<div>
										<h3 className="text-lg font-medium">{exp.role}</h3>
										<div className="flex items-center text-gray-500 mt-1">
											<Building className="h-4 w-4 mr-1" />
											<span>{exp.company_name}</span>
											{exp.location && <span className="mx-2">•</span>}
											{exp.location && <span>{exp.location}</span>}
										</div>
										<div className="flex items-center text-gray-500 mt-1">
											<Calendar className="h-4 w-4 mr-1" />
											<span>
												{formatDateDisplay(exp.start_date)} -{" "}
												{exp.current_job ? "Present" : formatDateDisplay(exp.end_date)}
											</span>
										</div>
										{exp.description && (
											<p className="mt-4 whitespace-pre-line">{exp.description}</p>
										)}
									</div>
									<div className="flex flex-col space-y-2">
										<Button
											variant="ghost"
											size="icon"
											onClick={() => handleOpenDialog(exp)}
											disabled={isLoading}
										>
											<Pencil className="h-4 w-4" />
										</Button>
										<Button
											variant="ghost"
											size="icon"
											className="text-red-500 hover:text-red-600 hover:bg-red-50"
											onClick={() => handleDelete(exp.id)}
											disabled={isLoading}
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))
				)}
			</div>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[600px]">
					<DialogHeader>
						<DialogTitle>{currentExperience?.id ? "Edit Experience" : "Add Experience"}</DialogTitle>
					</DialogHeader>

					<div className="grid gap-4 py-4">
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="role">Job Title*</Label>
								<Input
									id="role"
									name="role"
									value={currentExperience?.role || ""}
									onChange={handleChange}
									required
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="company_name">Company*</Label>
								<Input
									id="company_name"
									name="company_name"
									value={currentExperience?.company_name || ""}
									onChange={handleChange}
									required
								/>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="location">Location</Label>
							<Input
								id="location"
								name="location"
								value={currentExperience?.location || ""}
								onChange={handleChange}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="start_date">Start Date*</Label>
								<Input
									id="start_date"
									name="start_date"
									type="date"
									value={formatDateForInput(currentExperience?.start_date)}
									onChange={(e) => handleDateChange(e, "start_date")}
									required
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="end_date">End Date</Label>
								<Input
									id="end_date"
									name="end_date"
									type="date"
									value={formatDateForInput(currentExperience?.end_date)}
									onChange={(e) => handleDateChange(e, "end_date")}
									disabled={currentExperience?.current_job}
								/>
							</div>
						</div>

						<div className="flex items-center space-x-2">
							<input
								type="checkbox"
								id="current_job"
								name="current_job"
								checked={currentExperience?.current_job || false}
								onChange={handleChange}
								className="rounded border-gray-300"
							/>
							<Label htmlFor="current_job">I currently work here</Label>
						</div>

						<div className="space-y-2">
							<Label htmlFor="description">Description</Label>
							<Textarea
								id="description"
								name="description"
								rows={4}
								value={currentExperience?.description || ""}
								onChange={handleChange}
							/>
						</div>
					</div>

					{error && <div className="text-red-500 text-sm">{error}</div>}

					<div className="flex justify-end space-x-2">
						<Button variant="outline" onClick={handleCloseDialog} disabled={isLoading}>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={isLoading}>
							{isLoading ? "Saving..." : "Save"}
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
