// controllers/usersController.js

const logger = require("@/../../logger");
const {
	sendSuccess,
	sendError,
	sendNotFound,
	sendInternalError,
	sendUnauthorized,
} = require("../../utils/responseWrapper");

// Import services
const UserService = require("../../application/services/UserService");
const CompanyService = require("../../application/services/CompanyService");
const PreferencesService = require("../../application/services/PreferencesService");
const SkillService = require("../../application/services/SkillService");
const EducationService = require("../../application/services/EducationService");
const ExperienceService = require("../../application/services/ExperienceService");
const JobService = require("../../application/services/JobService");
const EventService = require("../../application/services/EventService");

// ─── USER SYNC ────────────────────────────────────────────────────────────────

// Sync a user from Auth0 into our DB and create default preferences
const syncUser = async (req, res) => {
	try {
		const authHeader = req.headers.authorization;
		if (authHeader !== `Bearer ${process.env.AUTH0_SYNC_SECRET}`) {
			logger.warn("syncUser: unauthorized");
			return sendUnauthorized(res, "Invalid sync secret");
		}

		const { user_id, email, name, picture } = req.body;
		const user = await UserService.syncUser({ user_id, email, name, picture });

		logger.info("syncUser: completed", { id: user.id });
		return sendSuccess(res, { id: user.id }, "User synced successfully");
	} catch (error) {
		logger.error("syncUser: error", { error });
		return sendInternalError(res, "Failed to sync user");
	}
};

// ─── USER PROFILE ───────────────────────────────────────────────────────────────

// Get current authenticated user's data
const getUserData = async (req, res) => {
	try {
		if (!req.auth) {
			logger.warn("getUserData: unauthorized");
			return sendUnauthorized(res, "Authentication required: No valid token provided");
		}

		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);

		return sendSuccess(res, user, "User data retrieved successfully");
	} catch (error) {
		logger.error("getUserData: error", { error });
		return sendInternalError(res, "Internal server error");
	}
};

// Update current authenticated user's profile
const updateUserData = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const {
			first_name,
			last_name,
			image_url,
			headline,
			location,
			bio,
			phone,
			expert_job_title,
			website,
			skill_ids,
		} = req.body;

		const updatePayload = {
			first_name,
			last_name,
			image_url,
			headline,
			location,
			bio,
			phone,
			expert_job_title,
			website,
			skill_ids,
		};

		logger.info("updateUserData: payload", { updatePayload });
		await UserService.updateUserProfile(auth0_id, updatePayload);

		return sendSuccess(res, null, "User profile updated successfully");
	} catch (error) {
		logger.error("updateUserData: error", { error });
		return sendInternalError(res, "Failed to update user profile");
	}
};

// ─── USER COMPANY ───────────────────────────────────────────────────────────

// Create company and link user as owner
const createCompanyWithOwner = async (req, res) => {
	try {
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendNotFound(res, "User not found");
		}

		const { name, description, logo_url, header_url, website_url, location, foundedyear, teamsize, social_media } =
			req.body;

		const companyData = {
			name,
			description,
			logo_url,
			header_url,
			website_url,
			location,
			foundedyear,
			teamsize,
			social_media,
		};

		const newCompany = await CompanyService.createCompanyWithOwner(user.id, companyData);

		return sendSuccess(res, newCompany, "Company created successfully", 201);
	} catch (error) {
		logger.error("Error creating company with owner", { error, auth0_id: req.auth?.sub });
		return sendInternalError(res, "Failed to create company");
	}
};

// Get all company by ID with user authentication
const getUserCompany = async (req, res) => {
	try {
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendNotFound(res, "User not found");
		}

		const companyWithRole = await CompanyService.getUserCompany(user.id);

		// return empty if no link
		if (!companyWithRole) {
			return sendSuccess(res, null, "No company associated with user");
		}

		const { role, ...company } = companyWithRole;
		return sendSuccess(res, { ...company, role }, "User company retrieved successfully");
	} catch (error) {
		logger.error("Error getting user company", { error, auth0_id: req.auth?.sub });
		return sendInternalError(res, "Failed to retrieve user company");
	}
};

// Update company by ID with user authentication
const updateCompanyWithAuth = async (req, res) => {
	try {
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		const { id } = req.params;
		const { name, description, logo_url, header_url, website_url, location, foundedyear, teamsize, social_media } =
			req.body;

		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendNotFound(res, "User not found");
		}

		const updateData = {
			name,
			description,
			logo_url,
			header_url,
			website_url,
			location,
			foundedyear,
			teamsize,
			social_media,
		};

		const updatedCompany = await CompanyService.updateCompanyWithAuth(user.id, id, updateData);

		return sendSuccess(res, updatedCompany, "Company updated successfully");
	} catch (error) {
		if (error.message === "Forbidden: Insufficient permissions") {
			return sendError(res, "Insufficient permissions to update this company", null, 403);
		}
		if (error.message === "Company not found") {
			return sendNotFound(res, "Company not found");
		}
		logger.error("Error updating company", { error, auth0_id: req.auth?.sub, companyId: req.params.id });
		return sendInternalError(res, "Failed to update company");
	}
};

// Delete company by ID with user authentication
const deleteCompanyWithAuth = async (req, res) => {
	try {
		const auth0_id = req.auth?.sub;
		if (!auth0_id) {
			return sendUnauthorized(res, "Authentication required");
		}

		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return sendNotFound(res, "User not found");
		}

		const { id } = req.params;

		await CompanyService.deleteCompanyWithAuth(user.id, id);

		return sendSuccess(res, { deleted: true }, "Company deleted successfully");
	} catch (error) {
		if (error.message === "Forbidden: Only owner can delete the company") {
			return sendError(res, "Only company owner can delete the company", null, 403);
		}
		if (error.message === "Company not found") {
			return sendNotFound(res, "Company not found");
		}
		logger.error("Error deleting company", { error, auth0_id: req.auth?.sub, companyId: req.params.id });
		return sendInternalError(res, "Failed to delete company");
	}
};

// ─── USER PREFERENCES ───────────────────────────────────────────────────────────

// Create preferences for current user
const createUserPreferences = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const { job_types, industries, travel_availability, locations, compensation_range, additional_notes } =
			req.body;

		const preferencesData = {
			job_types,
			industries,
			travel_availability,
			locations,
			compensation_range,
			additional_notes,
		};

		const newPreferences = await PreferencesService.createUserPreferences(user.id, preferencesData);

		res.status(201).json({ data: newPreferences });
	} catch (error) {
		logger.error("createUserPreferences: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get preferences for current user
const getUserPreferences = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const prefs = await PreferencesService.getUserPreferences(user.id);

		res.json({ data: prefs });
	} catch (error) {
		logger.error("getUserPreferences: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get a specific preference by record ID
const getUserPreferencesById = async (req, res) => {
	try {
		const { id } = req.params;
		const pref = await PreferencesService.getPreferencesById(id);
		if (!pref) return res.status(404).json({ message: "Preferences not found" });

		res.json({ data: pref });
	} catch (error) {
		logger.error("getUserPreferencesById: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Update preferences for current user
const updateUserPreferences = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}

		const {
			job_types,
			industries,
			travel_availability,
			locations,
			open_to_relocation,
			compensation_range,
			additional_notes,
		} = req.body;

		const updateData = {
			job_types,
			industries,
			travel_availability,
			locations,
			open_to_relocation,
			compensation_range,
			additional_notes,
		};

		const updatedPrefs = await PreferencesService.updateUserPreferences(user.id, updateData);

		return res.status(200).json({ data: updatedPrefs });
	} catch (error) {
		// log message + stack explicitly
		logger.error({
			msg: "updateUserPreferences failed",
			error: error.message,
			stack: error.stack,
		});
		return res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Delete preferences for current user
const deleteUserPreferences = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		await PreferencesService.deleteUserPreferences(user.id);

		res.status(200).json({ success: true });
	} catch (error) {
		if (error.message === "Preferences not found") {
			return res.status(404).json({ message: "Preferences not found" });
		}
		logger.error("deleteUserPreferences: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// ─── USER SKILLS ────────────────────────────────────────────────────────────────

// Create a skill for current user
const createUserSkill = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const { skill_id, endorsements, level } = req.body;
		const skillData = { skill_id, endorsements, level };

		const newSkill = await SkillService.createUserSkill(user.id, skillData);

		res.status(201).json({ data: newSkill });
	} catch (error) {
		logger.error("createUserSkill: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get all skills for current user
const getUserSkills = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const skills = await SkillService.getUserSkills(user.id);
		res.json({ data: skills });
	} catch (error) {
		logger.error("getUserSkills: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get a specific skill by ID
const getUserSkillById = async (req, res) => {
	try {
		const { id } = req.params;
		const skill = await SkillService.getById(id);
		if (!skill) return res.status(404).json({ message: "Skill not found" });

		res.json({ data: skill });
	} catch (error) {
		logger.error("getUserSkillById: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Update a specific skill by ID
const updateUserSkill = async (req, res) => {
	try {
		const { id } = req.params;
		const { endorsements, level } = req.body;

		const updateData = { endorsements, level };
		const updatedSkill = await SkillService.updateUserSkill(id, updateData);

		res.status(200).json({ data: updatedSkill });
	} catch (error) {
		if (error.message === "Skill not found") {
			return res.status(404).json({ message: "Skill not found" });
		}
		logger.error("updateUserSkill: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Delete a specific skill by ID
const deleteUserSkill = async (req, res) => {
	try {
		const { id } = req.params;
		await SkillService.deleteUserSkill(id);

		res.status(200).json({ success: true });
	} catch (error) {
		if (error.message === "Skill not found") {
			return res.status(404).json({ message: "Skill not found" });
		}
		logger.error("deleteUserSkill: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// ─── USER EDUCATIONS ────────────────────────────────────────────────────────────

// Create an education record for current user
const createUserEducation = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const { institution_name, start_date, end_date, degree, description, field_of_study } = req.body;

		const educationData = {
			institution_name,
			start_date,
			end_date,
			degree,
			description,
			field_of_study,
		};

		const newEducation = await EducationService.createUserEducation(user.id, educationData);

		res.status(201).json({ data: newEducation });
	} catch (error) {
		logger.error("createUserEducation: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get all education records for current user
const getUserEducations = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const edus = await EducationService.getUserEducations(user.id);
		res.json({ data: edus });
	} catch (error) {
		logger.error("getUserEducations: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get a specific education by ID
const getUserEducationById = async (req, res) => {
	try {
		const { id } = req.params;
		const edu = await EducationService.getById(id);
		if (!edu) return res.status(404).json({ message: "Education not found" });

		res.json({ data: edu });
	} catch (error) {
		logger.error("getUserEducationById: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Update a specific education by ID
const updateUserEducation = async (req, res) => {
	try {
		const { id } = req.params;
		const { institution_name, start_date, end_date, degree, description, field_of_study } = req.body;

		const updateData = {
			institution_name,
			start_date,
			end_date,
			degree,
			description,
			field_of_study,
		};

		const updatedEducation = await EducationService.updateUserEducation(id, updateData);

		res.status(200).json({ data: updatedEducation });
	} catch (error) {
		if (error.message === "Education not found") {
			return res.status(404).json({ message: "Education not found" });
		}
		logger.error("updateUserEducation: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Delete a specific education by ID
const deleteUserEducation = async (req, res) => {
	try {
		const { id } = req.params;
		await EducationService.deleteUserEducation(id);

		res.status(200).json({ success: true });
	} catch (error) {
		if (error.message === "Education not found") {
			return res.status(404).json({ message: "Education not found" });
		}
		logger.error("deleteUserEducation: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// ─── USER EXPERIENCES ───────────────────────────────────────────────────────────

// Create an experience record for current user
const createUserExperience = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const { company_name, role, location, start_date, end_date, description, current_job } = req.body;

		const experienceData = {
			company_name,
			role,
			location,
			start_date,
			end_date,
			description,
			current_job,
		};

		const newExperience = await ExperienceService.createUserExperience(user.id, experienceData);

		res.status(201).json({ data: newExperience });
	} catch (error) {
		logger.error("createUserExperience: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get all experience records for current user
const getUserExperiences = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const exps = await ExperienceService.getUserExperiences(user.id);
		res.json({ data: exps });
	} catch (error) {
		logger.error("getUserExperiences: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get a specific experience by ID
const getUserExperienceById = async (req, res) => {
	try {
		const { id } = req.params;
		const exp = await db("dtm_base.user_experiences").where({ id }).first();
		if (!exp) return res.status(404).json({ message: "Experience not found" });

		res.json({ data: exp });
	} catch (error) {
		logger.error("getUserExperienceById: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Update a specific experience by ID
const updateUserExperience = async (req, res) => {
	try {
		const { id } = req.params;
		const { company_name, role, location, start_date, end_date, description, current_job } = req.body;

		const updateData = {
			company_name,
			role,
			location,
			start_date,
			end_date,
			description,
			current_job,
		};

		const updatedExperience = await ExperienceService.updateUserExperience(id, updateData);

		res.status(200).json({ data: updatedExperience });
	} catch (error) {
		if (error.message === "Experience not found") {
			return res.status(404).json({ message: "Experience not found" });
		}
		logger.error("updateUserExperience: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Delete a specific experience by ID
const deleteUserExperience = async (req, res) => {
	logger.info("deleteUserExperience: start");
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.user_experiences").where({ id }).del();
		if (!deleted) return res.status(404).json({ message: "Experience not found" });

		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("deleteUserExperience: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// ─── USER JOBS ───────────────────────────────────────────────────────────

// Create a job for current user
const createUserJob = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const {
			title,
			description,
			location,
			remote,
			type,
			currency,
			salary_time_frame,
			min_salary,
			max_salary,
			published_date,
			company_id,
			subnet_ids,
			product_ids,
			category_ids,
		} = req.body;

		const jobData = {
			owner_id: user.id,
			title,
			description,
			location,
			remote,
			type,
			currency,
			salary_time_frame,
			min_salary,
			max_salary,
			published_date,
			company_id,
			subnet_ids,
			product_ids,
			category_ids,
		};

		const newJob = await JobService.createUserJob(user.id, jobData);
		res.status(201).json({ data: newJob });
	} catch (error) {
		logger.error("createUserJob: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get all jobs for current user
const getUserJobs = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const jobs = await JobService.getUserJobs(user.id);
		res.json({ data: jobs });
	} catch (error) {
		logger.error("getUserJobs: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get a specific job by ID
const getUserJobById = async (req, res) => {
	try {
		const { id } = req.params;
		const job = await db("dtm_base.jobs").where({ id }).first();
		if (!job) return res.status(404).json({ message: "Job not found" });

		res.json({ data: job });
	} catch (error) {
		logger.error("getUserJobById: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Update a specific job by ID
const updateUserJob = async (req, res) => {
	try {
		const { id } = req.params;
		const {
			title,
			description,
			location,
			remote,
			type,
			currency,
			salary_time_frame,
			min_salary,
			max_salary,
			published_date,
			company_id,
			subnet_ids,
			product_ids,
			category_ids,
		} = req.body;

		const updateData = {
			title,
			description,
			location,
			remote,
			type,
			currency,
			salary_time_frame,
			min_salary,
			max_salary,
			published_date,
			company_id,
			subnet_ids,
			product_ids,
			category_ids,
		};

		const updatedJob = await JobService.updateUserJob(id, updateData);

		res.status(200).json({ data: updatedJob });
	} catch (error) {
		if (error.message === "Job not found") {
			return res.status(404).json({ message: "Job not found" });
		}
		logger.error("updateUserJob: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Delete a specific job by ID
const deleteUserJob = async (req, res) => {
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.jobs").where({ id }).del();
		if (!deleted) return res.status(404).json({ message: "Job not found" });

		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("deleteUserJob: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// ─── USER EVENTS ───────────────────────────────────────────────────────────

// Create a new event
const createUserEvent = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const eventData = {
			...req.body,
			created_by_id: user.id,
		};

		const newEvent = await EventService.createUserEvent(user.id, eventData);

		res.status(201).json({ data: newEvent });
	} catch (error) {
		logger.error("createEvent: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get all events for current user
const getUserEvents = async (req, res) => {
	try {
		const auth0_id = req.auth.sub;
		const user = await UserService.getUserByAuth0Id(auth0_id);
		if (!user) return res.status(404).json({ message: "User not found" });

		const events = await EventService.getUserEvents(user.id);
		res.json({ data: events });
	} catch (error) {
		logger.error("getUserEvents: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Get a specific event by ID
const getUserEventById = async (req, res) => {
	try {
		const { id } = req.params;
		const event = await db("dtm_base.events").where({ id }).first();
		if (!event) return res.status(404).json({ message: "Event not found" });

		res.json({ data: event });
	} catch (error) {
		logger.error("getUserEventById: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Update a specific event by ID
const updateUserEvent = async (req, res) => {
	try {
		const { id } = req.params;
		const {
			name,
			description,
			start_date,
			end_date,
			location,
			is_virtual,
			event_type,
			website_url,
			registration_url,
			image_url,
			is_published,
			published_at,
			is_featured,
			subnet_ids,
			product_ids,
			company_ids,
			category_ids,
			event_ids,
			organizer_ids,
			desc_about_this_event,
			desc_what_u_will_learn,
			desc_who_should_attend,
			image_url_banner,
			speakers,
		} = req.body;

		const updateData = {
			name,
			description,
			start_date,
			end_date,
			location,
			is_virtual,
			event_type,
			website_url,
			registration_url,
			image_url,
			is_published,
			published_at,
			is_featured,
			subnet_ids,
			product_ids,
			company_ids,
			category_ids,
			event_ids,
			organizer_ids,
			desc_about_this_event,
			desc_what_u_will_learn,
			desc_who_should_attend,
			image_url_banner,
			speakers,
		};

		const updatedEvent = await EventService.updateUserEvent(id, updateData);

		res.status(200).json({ data: updatedEvent });
	} catch (error) {
		if (error.message === "Event not found") {
			return res.status(404).json({ message: "Event not found" });
		}
		logger.error("updateUserEvent: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

// Delete a specific event by ID
const deleteUserEvent = async (req, res) => {
	try {
		const { id } = req.params;
		const deleted = await db("dtm_base.events").where({ id }).del();
		if (!deleted) return res.status(404).json({ message: "Event not found" });

		res.status(200).json({ success: true });
	} catch (error) {
		logger.error("deleteUserEvent: error", { error });
		res.status(500).json({ message: "Internal server error", error: error.message });
	}
};

module.exports = {
	syncUser,
	getUserData,
	updateUserData,
	createCompanyWithOwner,
	getUserCompany,
	updateCompanyWithAuth,
	deleteCompanyWithAuth,
	createUserPreferences,
	getUserPreferences,
	getUserPreferencesById,
	updateUserPreferences,
	deleteUserPreferences,
	createUserSkill,
	getUserSkills,
	getUserSkillById,
	updateUserSkill,
	deleteUserSkill,
	createUserEducation,
	getUserEducations,
	getUserEducationById,
	updateUserEducation,
	deleteUserEducation,
	createUserExperience,
	getUserExperiences,
	getUserExperienceById,
	updateUserExperience,
	deleteUserExperience,
	createUserJob,
	getUserJobs,
	getUserJobById,
	updateUserJob,
	deleteUserJob,
	createUserEvent,
	getUserEvents,
	getUserEventById,
	updateUserEvent,
	deleteUserEvent,
};
