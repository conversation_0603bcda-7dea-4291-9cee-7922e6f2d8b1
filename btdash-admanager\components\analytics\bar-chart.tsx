"use client";

import { countries } from "@/components/country-selector";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import type { CountryMetric, DeviceMetric } from "@/lib/analytics";
import { useEffect, useRef } from "react";

interface BarChartProps {
	data: (CountryMetric | DeviceMetric)[];
	title: string;
	description?: string;
	metric: "impressions" | "clicks" | "ctr";
	height?: number;
	type: "country" | "device";
}

export function BarChart({ data, title, description, metric, height = 300, type }: BarChartProps) {
	const canvasRef = useRef<HTMLCanvasElement>(null);

	useEffect(() => {
		if (!canvasRef.current) return;

		const canvas = canvasRef.current;
		const ctx = canvas.getContext("2d");
		if (!ctx) return;

		// Clear canvas
		ctx.clearRect(0, 0, canvas.width, canvas.height);

		// Set dimensions
		const width = canvas.width;
		const chartHeight = height - 40; // Leave space for labels

		// Find max value
		const values = data.map((d) => d[metric]);
		const maxValue = Math.max(...values) * 1.1; // Add 10% padding

		// Draw axes
		ctx.beginPath();
		ctx.strokeStyle = "#e2e8f0";
		ctx.moveTo(60, 20);
		ctx.lineTo(60, chartHeight);
		ctx.lineTo(width - 20, chartHeight);
		ctx.stroke();

		// Draw bars
		const barWidth = (width - 80) / data.length - 10;

		data.forEach((item, i) => {
			const x = 60 + i * ((width - 80) / data.length) + 5;
			const barHeight = (item[metric] / maxValue) * (chartHeight - 40);
			const y = chartHeight - barHeight;

			// Draw bar
			ctx.fillStyle = "#3b82f6";
			ctx.fillRect(x, y, barWidth, barHeight);

			// Draw label
			ctx.fillStyle = "#64748b";
			ctx.font = "10px sans-serif";
			ctx.textAlign = "center";

			let label = "";
			if (type === "country") {
				const countryItem = item as CountryMetric;
				const country = countries.find((c) => c.value === countryItem.country_code);
				label = country ? country.label : countryItem.country_code;
			} else {
				label = (item as DeviceMetric).device_type;
			}

			// Truncate long labels
			if (label.length > 10) {
				label = label.substring(0, 8) + "...";
			}

			ctx.fillText(label, x + barWidth / 2, chartHeight + 15);

			// Draw value on top of bar
			ctx.fillStyle = "#3b82f6";
			ctx.textAlign = "center";
			let valueText = "";

			if (metric === "impressions" || metric === "clicks") {
				valueText = item[metric] >= 1000 ? `${(item[metric] / 1000).toFixed(1)}k` : item[metric].toString();
			} else if (metric === "ctr") {
				valueText = `${(item[metric] * 100).toFixed(1)}%`;
			}

			ctx.fillText(valueText, x + barWidth / 2, y - 5);
		});

		// Draw y-axis labels
		ctx.textAlign = "right";
		ctx.textBaseline = "middle";
		ctx.fillStyle = "#64748b";

		const yStep = (chartHeight - 40) / 5;
		for (let i = 0; i <= 5; i++) {
			const y = chartHeight - i * yStep;
			const value = (i / 5) * maxValue;
			let label = "";

			if (metric === "impressions" || metric === "clicks") {
				label = value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value.toFixed(0);
			} else if (metric === "ctr") {
				label = `${(value * 100).toFixed(1)}%`;
			}

			ctx.fillText(label, 55, y);
		}
	}, [data, metric, height, type]);

	return (
		<Card>
			<CardHeader>
				<CardTitle>{title}</CardTitle>
				{description && <CardDescription>{description}</CardDescription>}
			</CardHeader>
			<CardContent>
				<div className="w-full" style={{ height: `${height}px` }}>
					<canvas
						ref={canvasRef}
						width={800}
						height={height}
						style={{ width: "100%", height: `${height}px` }}
					/>
				</div>
			</CardContent>
		</Card>
	);
}
