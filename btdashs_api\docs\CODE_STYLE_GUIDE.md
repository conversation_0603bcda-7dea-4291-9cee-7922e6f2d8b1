# BTDash API Code Style Guide

## Overview

This document outlines the coding standards and best practices for the BTDash API project. Consistent code style improves readability, maintainability, and team collaboration.

## General Principles

1. **Clarity over Cleverness**: Write code that is easy to understand
2. **Consistency**: Follow established patterns throughout the codebase
3. **Documentation**: Document complex logic and business rules
4. **Testing**: Write testable code with clear dependencies
5. **Error Handling**: Handle errors gracefully with proper logging

## JavaScript Style Guidelines

### Naming Conventions

#### Variables and Functions
- Use **camelCase** for variables and functions
- Use descriptive names that explain the purpose

```javascript
// ✅ Good
const userProfile = await getUserProfile(userId);
const isValidEmail = validateEmail(email);

// ❌ Bad
const up = await getUP(uid);
const valid = validate(e);
```

#### Constants
- Use **SCREAMING_SNAKE_CASE** for constants

```javascript
// ✅ Good
const MAX_RETRY_ATTEMPTS = 3;
const API_BASE_URL = 'https://api.example.com';

// ❌ Bad
const maxRetryAttempts = 3;
const apiBaseUrl = 'https://api.example.com';
```

#### Classes and Constructors
- Use **PascalCase** for classes and constructors

```javascript
// ✅ Good
class UserService {
  constructor() {}
}

// ❌ Bad
class userService {
  constructor() {}
}
```

#### Files and Directories
- Use **camelCase** for file names
- Use **kebab-case** for directory names when multiple words

```
// ✅ Good
userService.js
user-management/
api-documentation/

// ❌ Bad
UserService.js
user_service.js
usermanagement/
```

### Function Guidelines

#### Function Declaration vs Expression
- Use **function declarations** for main functions
- Use **arrow functions** for callbacks and short functions

```javascript
// ✅ Good - Function declaration
function calculateTotal(items) {
  return items.reduce((sum, item) => sum + item.price, 0);
}

// ✅ Good - Arrow function for callbacks
const processedItems = items.map(item => ({
  ...item,
  total: item.price * item.quantity
}));

// ❌ Bad - Inconsistent usage
const calculateTotal = function(items) {
  return items.reduce(function(sum, item) {
    return sum + item.price;
  }, 0);
};
```

#### Function Length
- Keep functions small and focused (max 20-30 lines)
- Extract complex logic into separate functions

```javascript
// ✅ Good
async function createUser(userData) {
  validateUserData(userData);
  const hashedPassword = await hashPassword(userData.password);
  const user = await saveUser({ ...userData, password: hashedPassword });
  await sendWelcomeEmail(user.email);
  return user;
}

function validateUserData(userData) {
  if (!userData.email) throw new Error('Email is required');
  if (!userData.password) throw new Error('Password is required');
}

// ❌ Bad - Too long and complex
async function createUser(userData) {
  if (!userData.email) throw new Error('Email is required');
  if (!userData.password) throw new Error('Password is required');
  if (userData.password.length < 8) throw new Error('Password too short');
  // ... 20 more lines of validation and processing
}
```

### Error Handling

#### Use Proper Error Types
- Create specific error classes for different error types
- Include relevant context in error messages

```javascript
// ✅ Good
class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

function validateEmail(email) {
  if (!email) {
    throw new ValidationError('Email is required', 'email');
  }
  if (!email.includes('@')) {
    throw new ValidationError('Invalid email format', 'email');
  }
}

// ❌ Bad
function validateEmail(email) {
  if (!email || !email.includes('@')) {
    throw new Error('Bad email');
  }
}
```

#### Async/Await Error Handling
- Always handle errors in async functions
- Use try-catch blocks appropriately

```javascript
// ✅ Good
async function getUserData(userId) {
  try {
    const user = await UserService.getById(userId);
    if (!user) {
      throw new NotFoundError(`User not found: ${userId}`);
    }
    return user;
  } catch (error) {
    logger.error('Error fetching user data', { userId, error: error.message });
    throw error;
  }
}

// ❌ Bad
async function getUserData(userId) {
  const user = await UserService.getById(userId); // No error handling
  return user;
}
```

## Documentation Standards

### JSDoc Comments
- Use JSDoc for all public functions and classes
- Include parameter types, return types, and descriptions

```javascript
/**
 * Creates a new user account with the provided data
 * 
 * @param {Object} userData - The user data object
 * @param {string} userData.email - User's email address
 * @param {string} userData.password - User's password (will be hashed)
 * @param {string} [userData.name] - User's display name (optional)
 * @returns {Promise<Object>} The created user object
 * @throws {ValidationError} When user data is invalid
 * @throws {ConflictError} When email already exists
 * 
 * @example
 * const user = await createUser({
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   name: 'John Doe'
 * });
 */
async function createUser(userData) {
  // Implementation
}
```

### Inline Comments
- Use comments to explain **why**, not **what**
- Comment complex business logic and algorithms

```javascript
// ✅ Good - Explains why
// We need to hash the password before storing it for security
const hashedPassword = await bcrypt.hash(password, 10);

// Cache the result for 5 minutes to reduce database load
const cacheKey = `user:${userId}`;
await redis.setex(cacheKey, 300, JSON.stringify(user));

// ❌ Bad - Explains what (obvious from code)
// Set the user's email
user.email = email;

// Call the save function
await user.save();
```

## Code Organization

### File Structure
- One class per file
- Group related functions together
- Use barrel exports for clean imports

```javascript
// ✅ Good - userService.js
class UserService {
  async createUser(userData) { /* ... */ }
  async getUserById(id) { /* ... */ }
  async updateUser(id, data) { /* ... */ }
}

module.exports = UserService;

// ✅ Good - services/index.js (barrel export)
module.exports = {
  UserService: require('./UserService'),
  CompanyService: require('./CompanyService'),
  EmailService: require('./EmailService')
};
```

### Import Organization
- Group imports by type: external, internal, relative
- Use destructuring for multiple imports from same module

```javascript
// ✅ Good
// External dependencies
const express = require('express');
const bcrypt = require('bcrypt');

// Internal modules
const { UserService, EmailService } = require('../services');
const { validateEmail } = require('../utils/validation');

// Relative imports
const config = require('./config');

// ❌ Bad - Mixed order
const config = require('./config');
const express = require('express');
const { UserService } = require('../services');
const bcrypt = require('bcrypt');
```

## Database and API Patterns

### Database Queries
- Use parameterized queries to prevent SQL injection
- Use transactions for multi-step operations
- Handle database errors appropriately

```javascript
// ✅ Good
async function transferFunds(fromUserId, toUserId, amount) {
  return await db.transaction(async (trx) => {
    await trx('accounts')
      .where({ user_id: fromUserId })
      .decrement('balance', amount);
    
    await trx('accounts')
      .where({ user_id: toUserId })
      .increment('balance', amount);
    
    await trx('transactions').insert({
      from_user_id: fromUserId,
      to_user_id: toUserId,
      amount,
      created_at: new Date()
    });
  });
}

// ❌ Bad - No transaction, SQL injection risk
async function transferFunds(fromUserId, toUserId, amount) {
  await db.raw(`UPDATE accounts SET balance = balance - ${amount} WHERE user_id = ${fromUserId}`);
  await db.raw(`UPDATE accounts SET balance = balance + ${amount} WHERE user_id = ${toUserId}`);
}
```

### API Response Format
- Use consistent response structure
- Include appropriate HTTP status codes
- Provide meaningful error messages

```javascript
// ✅ Good
const sendSuccess = (res, data, message = 'Success') => {
  res.status(200).json({
    success: true,
    data,
    message
  });
};

const sendError = (res, statusCode, message, error = null) => {
  res.status(statusCode).json({
    success: false,
    message,
    error: error?.message || error
  });
};

// ❌ Bad - Inconsistent responses
res.json({ user }); // Sometimes just data
res.json({ success: true, user }); // Sometimes with success flag
res.json({ error: 'Something went wrong' }); // Different error format
```

## Testing Guidelines

### Test Structure
- Use descriptive test names
- Follow Arrange-Act-Assert pattern
- Group related tests with describe blocks

```javascript
// ✅ Good
describe('UserService', () => {
  describe('createUser', () => {
    it('should create a user with valid data', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      // Act
      const user = await UserService.createUser(userData);

      // Assert
      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.password).not.toBe(userData.password); // Should be hashed
    });

    it('should throw ValidationError for invalid email', async () => {
      // Arrange
      const userData = {
        email: 'invalid-email',
        password: 'password123'
      };

      // Act & Assert
      await expect(UserService.createUser(userData))
        .rejects
        .toThrow(ValidationError);
    });
  });
});
```

### Mock Usage
- Mock external dependencies
- Use meaningful mock data
- Reset mocks between tests

```javascript
// ✅ Good
jest.mock('../database/knex');
const mockDb = require('../database/knex');

beforeEach(() => {
  jest.clearAllMocks();
});

it('should handle database errors gracefully', async () => {
  // Arrange
  mockDb.mockRejectedValue(new Error('Database connection failed'));

  // Act & Assert
  await expect(UserService.getUsers())
    .rejects
    .toThrow('Failed to fetch users');
});
```

## Performance Guidelines

### Async Operations
- Use Promise.all for parallel operations
- Avoid blocking operations in loops
- Use streaming for large datasets

```javascript
// ✅ Good - Parallel execution
const [user, company, preferences] = await Promise.all([
  UserService.getById(userId),
  CompanyService.getByUserId(userId),
  PreferencesService.getByUserId(userId)
]);

// ❌ Bad - Sequential execution
const user = await UserService.getById(userId);
const company = await CompanyService.getByUserId(userId);
const preferences = await PreferencesService.getByUserId(userId);
```

### Memory Management
- Avoid memory leaks with proper cleanup
- Use pagination for large datasets
- Clean up event listeners and timers

```javascript
// ✅ Good
async function getUsers(page = 1, limit = 10) {
  const offset = (page - 1) * limit;
  return await db('users')
    .limit(limit)
    .offset(offset)
    .orderBy('created_at', 'desc');
}

// ❌ Bad - Loading all users at once
async function getUsers() {
  return await db('users').select('*'); // Could be millions of records
}
```

## Security Guidelines

### Input Validation
- Validate all user inputs
- Sanitize data before database operations
- Use parameterized queries

```javascript
// ✅ Good
const userSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  name: Joi.string().max(100).optional()
});

function validateUserInput(userData) {
  const { error, value } = userSchema.validate(userData);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }
  return value;
}
```

### Authentication & Authorization
- Always verify user permissions
- Use secure session management
- Log security events

```javascript
// ✅ Good
async function updateCompany(userId, companyId, updateData) {
  const userCompany = await db('user_company')
    .where({ user_id: userId, company_id: companyId })
    .first();

  if (!userCompany || !['owner', 'admin'].includes(userCompany.role)) {
    logger.warn('Unauthorized company update attempt', { userId, companyId });
    throw new ForbiddenError('Insufficient permissions');
  }

  // Proceed with update
}
```

## Tools and Automation

### ESLint Configuration
- Use consistent linting rules
- Fix linting errors before committing
- Use Prettier for code formatting

### Git Commit Messages
- Use conventional commit format
- Include scope and description
- Reference issue numbers when applicable

```
feat(auth): add JWT token refresh functionality

- Implement automatic token refresh
- Add refresh token rotation
- Update authentication middleware

Closes #123
```

## Code Review Guidelines

### What to Look For
1. **Functionality**: Does the code work as intended?
2. **Readability**: Is the code easy to understand?
3. **Performance**: Are there any performance issues?
4. **Security**: Are there any security vulnerabilities?
5. **Testing**: Are there adequate tests?
6. **Documentation**: Is the code properly documented?

### Review Checklist
- [ ] Code follows style guidelines
- [ ] Functions are small and focused
- [ ] Error handling is appropriate
- [ ] Tests cover new functionality
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed
