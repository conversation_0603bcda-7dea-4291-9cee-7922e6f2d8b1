import { getItemById } from "@/lib/data/utils";
import { NextResponse } from "next/server";

export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
	const paramsData = await params;
	const id = paramsData.id;

	try {
		const data = await getItemById("categories", id);
		return NextResponse.json({
			success: true,
			data,
			message: "Category fetched successfully",
		});
	} catch (err) {
		return NextResponse.json(
			{
				success: false,
				message: "Category not found",
				errors: err instanceof Error ? err.message : "Unknown error",
			},
			{ status: 404 }
		);
	}
}
