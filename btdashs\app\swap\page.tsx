import { SwapInterface } from "@/components/swap-interface"
import { LiquidityPoolOverview } from "@/components/liquidity-pool-overview"
import { TransactionHistory } from "@/components/transaction-history"

export default function SwapPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Subnet Token Swap</h1>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <SwapInterface />
        </div>
        <div className="space-y-8">
          <LiquidityPoolOverview />
          <TransactionHistory />
        </div>
      </div>
    </div>
  )
}

