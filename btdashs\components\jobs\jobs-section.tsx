import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Job } from "@/lib/db/models";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { JobsGrid } from "./jobs-grid";

interface JobsSectionProps {
	title: string;
	jobs: Job[];
	limit?: number;
	entityType?: "subnet" | "company" | "application";
	entityId?: string | number;
}

export function JobsSection({ title, jobs, limit = 4, entityType, entityId }: JobsSectionProps) {
	// Limit the number of jobs to display
	const displayedJobs = limit ? jobs.slice(0, limit) : jobs;
	const hasMoreJobs = jobs.length > limit;

	// Build the view all URL based on entity type
	let viewAllUrl = "/jobs";
	if (entityType && entityId) {
		viewAllUrl = `/jobs?${entityType}=${entityId}`;
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle className="flex items-center gap-2">{title}</CardTitle>
				{hasMoreJobs && (
					<Link href={viewAllUrl}>
						<Button variant="ghost" size="sm" className="gap-1">
							View All
							<ArrowRight className="h-4 w-4" />
						</Button>
					</Link>
				)}
			</CardHeader>
			<CardContent>
				{jobs.length > 0 ? (
					<JobsGrid jobs={displayedJobs} />
				) : (
					<div className="text-center py-12 border rounded-lg bg-muted/20">
						<p className="text-muted-foreground">No job listings available at this time.</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
