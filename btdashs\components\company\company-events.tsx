"use client";

import { EventsSection } from "@/components/events/events-section";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { Event } from "@/lib/db/models";
import { Calendar, PlusCircle } from "lucide-react";
import Link from "next/link";

interface CompanyEventsProps {
	events: Event[];
	companyId: string | number;
	authorized_events_admin?: boolean;
	limit?: number;
}

export function CompanyEvents({
	events = [],
	companyId,
	authorized_events_admin = false,
	limit = 4,
}: CompanyEventsProps) {
	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold flex items-center gap-2">
					<Calendar className="h-5 w-5 text-emerald-500" />
					Upcoming Events
				</h2>
				{authorized_events_admin ? (
					<Link href="/profile#events">
						<Button size="sm" className="flex items-center gap-1 bg-emerald-500 hover:bg-emerald-600">
							<PlusCircle className="h-4 w-4" />
							<span>Create Event</span>
						</Button>
					</Link>
				) : (
					<Button
						size="sm"
						className="flex items-center gap-1 bg-gray-300 text-gray-500 cursor-not-allowed"
						disabled
					>
						<PlusCircle className="h-4 w-4" />
						<span>Create Event</span>
					</Button>
				)}
			</div>

			{events.length === 0 ? (
				<Card className="border border-dashed">
					<CardContent className="p-6 text-center">
						<Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
						<h3 className="text-lg font-medium mb-2">No Upcoming Events</h3>
						<p className="text-muted-foreground mb-4">
							This company doesn't have any scheduled events at the moment.
						</p>
						{authorized_events_admin ? (
							<Link href={`/events/create?company=${companyId}`}>
								<Button size="sm" variant="outline">
									<PlusCircle className="h-4 w-4 mr-2" />
									Post an Event
								</Button>
							</Link>
						) : (
							<Button size="sm" variant="outline" disabled className="cursor-not-allowed">
								<PlusCircle className="h-4 w-4 mr-2" />
								Post an Event
							</Button>
						)}
					</CardContent>
				</Card>
			) : (
				<EventsSection title="" events={events} limit={limit} entityType="company" entityId={companyId} />
			)}
		</div>
	);
}
