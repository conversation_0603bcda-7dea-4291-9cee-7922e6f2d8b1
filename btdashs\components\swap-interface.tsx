"use client";

import { useState } from "react";
import { ArrowDownUp } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const tokens = [
  { id: "tao", name: "TAO", balance: 1000 },
  { id: "subnet1", name: "Subnet 1 Token", balance: 500 },
  { id: "subnet2", name: "Subnet 2 Token", balance: 750 },
  { id: "subnet3", name: "Subnet 3 Token", balance: 250 },
];

export function SwapInterface() {
  const [fromToken, setFromToken] = useState(tokens[0]);
  const [toToken, setToToken] = useState(tokens[1]);
  const [fromAmount, setFromAmount] = useState("");
  const [toAmount, setToAmount] = useState("");

  const handleSwap = () => {
    // In a real application, this would trigger the actual swap process
    //console.log(`Swapping ${fromAmount} ${fromToken.name} to ${toAmount} ${toToken.name}`)
  };

  const handleFromAmountChange = (value: string) => {
    setFromAmount(value);
    // Simulate exchange rate calculation
    setToAmount((Number.parseFloat(value) * 0.95).toFixed(2));
  };

  const switchTokens = () => {
    setFromToken(toToken);
    setToToken(fromToken);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Swap Tokens</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">From</label>
          <div className="flex space-x-2">
            <Select
              value={fromToken.id}
              onValueChange={(value) =>
                setFromToken(tokens.find((t) => t.id === value) || tokens[0])
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select token" />
              </SelectTrigger>
              <SelectContent>
                {tokens.map((token) => (
                  <SelectItem key={token.id} value={token.id}>
                    {token.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input
              type="number"
              placeholder="0.00"
              value={fromAmount}
              onChange={(e) => handleFromAmountChange(e.target.value)}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            Balance: {fromToken.balance.toFixed(2)} {fromToken.name}
          </p>
        </div>

        <div className="flex justify-center">
          <Button variant="ghost" size="icon" onClick={switchTokens}>
            <ArrowDownUp className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">To</label>
          <div className="flex space-x-2">
            <Select
              value={toToken.id}
              onValueChange={(value) =>
                setToToken(tokens.find((t) => t.id === value) || tokens[1])
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select token" />
              </SelectTrigger>
              <SelectContent>
                {tokens.map((token) => (
                  <SelectItem key={token.id} value={token.id}>
                    {token.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Input type="number" placeholder="0.00" value={toAmount} readOnly />
          </div>
          <p className="text-sm text-muted-foreground">
            Balance: {toToken.balance.toFixed(2)} {toToken.name}
          </p>
        </div>

        <div className="bg-muted p-3 rounded-md">
          <p className="text-sm">
            1 {fromToken.name} = {(1 / 0.95).toFixed(4)} {toToken.name}
          </p>
          <p className="text-sm text-muted-foreground">
            Slippage tolerance: 0.5%
          </p>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full" onClick={handleSwap}>
          Swap
        </Button>
      </CardFooter>
    </Card>
  );
}
