import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Check } from "lucide-react"

const pricingPlans = [
  {
    name: "Basic",
    price: "₮ 100 / month",
    features: ["Up to 100,000 API calls", "24/7 support", "Basic analytics"],
  },
  {
    name: "Pro",
    price: "₮ 500 / month",
    features: ["Up to 1,000,000 API calls", "Priority support", "Advanced analytics", "Custom model fine-tuning"],
  },
  {
    name: "Enterprise",
    price: "Custom pricing",
    features: [
      "Unlimited API calls",
      "Dedicated support team",
      "Full analytics suite",
      "Custom model development",
      "SLA guarantees",
    ],
  },
]

export function SubnetPricing() {
  return (
    <div className="grid md:grid-cols-3 gap-6">
      {pricingPlans.map((plan) => (
        <Card key={plan.name}>
          <CardHeader>
            <CardTitle>{plan.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-4">{plan.price}</div>
            <ul className="space-y-2 mb-4">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-2" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
            <Button className="w-full">{plan.name === "Enterprise" ? "Contact Sales" : "Get Started"}</Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

