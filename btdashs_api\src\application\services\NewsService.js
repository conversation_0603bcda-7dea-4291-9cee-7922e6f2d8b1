// src/application/services/NewsService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview News Service - Handles news article operations
 *
 * This service manages news articles and content, providing
 * CRUD operations and content management functionality.
 *
 * Key responsibilities:
 * - News article CRUD operations
 * - Content publishing and management
 * - Article search and filtering
 * - Data validation and sanitization
 *
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class NewsService extends BaseService {
	constructor() {
		super("dtm_base.news", "News");
	}

	/**
	 * Get all news articles with default sorting by published date
	 * @param {Object} filters - Optional filters
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of news articles
	 */
	async getAllNews(filters = {}, options = {}) {
		try {
			const queryOptions = {
				orderBy: { column: "publication_date", direction: "desc" },
				...options,
			};

			return await this.getAll(filters, queryOptions);
		} catch (error) {
			logger.error("Error getting all news", { error, filters, options });
			throw error;
		}
	}

	/**
	 * Get news article by ID
	 * @param {number} id - News article ID
	 * @returns {Promise<Object|null>} News article object or null if not found
	 */
	async getNewsById(id) {
		try {
			return await this.getById(id);
		} catch (error) {
			logger.error("Error getting news by ID", { error, id });
			throw error;
		}
	}

	/**
	 * Create a new news article
	 * @param {Object} newsData - News article data
	 * @returns {Promise<Object>} Created news article object
	 */
	async createNews(newsData) {
		try {
			const newNews = await this.create({
				...newsData,
				publication_date: newsData.publication_date || new Date(),
			});
			logger.info("News article created", { news_id: newNews.id });
			return newNews;
		} catch (error) {
			logger.error("Error creating news", { error, newsData });
			throw error;
		}
	}

	/**
	 * Update a news article
	 * @param {number} id - News article ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated news article object
	 */
	async updateNews(id, updateData) {
		try {
			const updatedNews = await this.updateById(id, updateData);
			logger.info("News article updated", { news_id: id });
			return updatedNews;
		} catch (error) {
			logger.error("Error updating news", { error, id, updateData });
			throw error;
		}
	}

	/**
	 * Delete a news article
	 * @param {number} id - News article ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteNews(id) {
		try {
			const result = await this.deleteById(id);
			logger.info("News article deleted", { news_id: id });
			return result;
		} catch (error) {
			logger.error("Error deleting news", { error, id });
			throw error;
		}
	}

	/**
	 * Get published news articles
	 * @param {number} limit - Number of articles to return
	 * @returns {Promise<Array>} Array of published news articles
	 */
	async getPublishedNews(limit = 10) {
		try {
			const news = await this.getAll(
				{ is_published: true },
				{
					limit,
					orderBy: { column: "publication_date", direction: "desc" },
				}
			);

			logger.info("Published news retrieved", { count: news.length, limit });
			return news;
		} catch (error) {
			logger.error("Error getting published news", { error, limit });
			throw new Error(`Failed to get published news: ${error.message}`);
		}
	}

	/**
	 * Search news articles
	 * @param {string} searchTerm - Search term
	 * @param {Object} options - Query options
	 * @returns {Promise<Array>} Array of matching news articles
	 */
	async searchNews(searchTerm, options = {}) {
		try {
			const db = require("../../infrastructure/database/knex");

			let query = db(this.tableName)
				.where("title", "ilike", `%${searchTerm}%`)
				.orWhere("content", "ilike", `%${searchTerm}%`)
				.orWhere("summary", "ilike", `%${searchTerm}%`);

			// Apply ordering
			if (options.orderBy) {
				const { column, direction = "desc" } = options.orderBy;
				query = query.orderBy(column, direction);
			} else {
				query = query.orderBy("publication_date", "desc");
			}

			// Apply pagination
			if (options.limit) {
				query = query.limit(options.limit);
			}
			if (options.offset) {
				query = query.offset(options.offset);
			}

			const news = await query;

			logger.info("News searched", {
				searchTerm,
				resultCount: news.length,
				options,
			});

			return news;
		} catch (error) {
			logger.error("Error searching news", { error, searchTerm, options });
			throw new Error(`Failed to search news: ${error.message}`);
		}
	}
}

module.exports = new NewsService();
