"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { UserEducation } from "@/lib/db/models";
import { Calendar, GraduationCap, Pencil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface ProfileEducationClientProps {
	initialEducation: UserEducation[];
}

export default function ProfileEducationClient({ initialEducation }: ProfileEducationClientProps) {
	const [educations, setEducations] = useState<UserEducation[]>(initialEducation);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [currentEducation, setCurrentEducation] = useState<Partial<UserEducation> | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleOpenDialog = (education?: UserEducation) => {
		setCurrentEducation(
			education || {
				institution_name: "",
				degree: "",
				field_of_study: "",
				start_date: undefined,
				end_date: undefined,
				description: "",
			}
		);
		setError(null);
		setIsDialogOpen(true);
	};

	const handleCloseDialog = () => {
		setIsDialogOpen(false);
		setCurrentEducation(null);
		setError(null);
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value, type } = e.target;
		const checked = type === "checkbox" ? (e.target as HTMLInputElement).checked : undefined;

		setCurrentEducation((prev) => ({
			...prev!,
			[name]: checked !== undefined ? checked : value,
			...(name === "current_study" && checked ? { end_date: undefined } : {}),
		}));
	};

	const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>, field: "start_date" | "end_date") => {
		const value = e.target.value;
		const date = value ? new Date(value) : undefined;

		setCurrentEducation((prev) => ({
			...prev!,
			[field]: date,
		}));
	};

	const formatDateForInput = (date?: Date | string | null) => {
		if (!date) return "";
		const dateObj = date instanceof Date ? date : new Date(date);
		return dateObj.toISOString().split("T")[0];
	};

	const handleSave = async () => {
		if (!currentEducation) return;

		setIsLoading(true);
		setError(null);

		try {
			const method = currentEducation.id ? "PUT" : "POST";
			const url = currentEducation.id ? `/api/user/educations/${currentEducation.id}` : "/api/user/educations";

			const response = await fetch(url, {
				method,
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					...currentEducation,
					start_date: currentEducation.start_date
						? new Date(currentEducation.start_date).toISOString()
						: null,
					end_date: currentEducation.end_date ? new Date(currentEducation.end_date).toISOString() : null,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to save education");
			}

			const { data } = await response.json();
			const savedEducation = (method === "PUT" ? data : data.data) as UserEducation;

			setEducations((prev) =>
				currentEducation.id
					? prev.map((edu) => (edu.id === savedEducation.id ? savedEducation : edu))
					: [savedEducation, ...prev]
			);

			handleCloseDialog();
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async (id: number) => {
		setIsLoading(true);
		try {
			const response = await fetch(`/api/user/educations/${id}`, {
				method: "DELETE",
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to delete education");
			}

			setEducations((prev) => prev.filter((edu) => edu.id !== id));
		} catch (err) {
			setError(err instanceof Error ? err.message : "An unknown error occurred");
		} finally {
			setIsLoading(false);
		}
	};

	const formatDateDisplay = (date?: Date | string) => {
		if (!date) return "";
		const dateObj = new Date(date);
		return dateObj.toLocaleDateString("en-US", { month: "short", year: "numeric" });
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Education</h2>
				<Button onClick={() => handleOpenDialog()}>
					<Plus className="mr-2 h-4 w-4" />
					Add Education
				</Button>
			</div>

			{error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">{error}</div>}

			<div className="space-y-4">
				{educations.length === 0 ? (
					<Card>
						<CardContent className="p-6 text-center text-gray-500">
							No education entries added yet
						</CardContent>
					</Card>
				) : (
					educations.map((edu) => (
						<Card key={edu.id}>
							<CardContent className="p-6">
								<div className="flex justify-between">
									<div>
										<h3 className="text-lg font-medium">{edu.institution_name}</h3>
										<div className="flex items-center text-gray-500 mt-1">
											<GraduationCap className="h-4 w-4 mr-1" />
											<span>
												{edu.degree} {edu.field_of_study && `in ${edu.field_of_study}`}
											</span>
										</div>
										<div className="flex items-center text-gray-500 mt-1">
											<Calendar className="h-4 w-4 mr-1" />
											<span>
												{formatDateDisplay(edu.start_date)} -{" "}
												{edu.end_date ? formatDateDisplay(edu.end_date) : "Present"}
											</span>
										</div>
										{edu.description && (
											<p className="mt-4 whitespace-pre-line">{edu.description}</p>
										)}
									</div>
									<div className="flex flex-col space-y-2">
										<Button
											variant="ghost"
											size="icon"
											onClick={() => handleOpenDialog(edu)}
											disabled={isLoading}
										>
											<Pencil className="h-4 w-4" />
										</Button>
										<Button
											variant="ghost"
											size="icon"
											className="text-red-500 hover:text-red-600 hover:bg-red-50"
											onClick={() => handleDelete(edu.id)}
											disabled={isLoading}
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))
				)}
			</div>

			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent className="sm:max-w-[600px]">
					<DialogHeader>
						<DialogTitle>{currentEducation?.id ? "Edit Education" : "Add Education"}</DialogTitle>
					</DialogHeader>

					<div className="grid gap-4 py-4">
						<div className="space-y-2">
							<Label htmlFor="institution_name">Institution*</Label>
							<Input
								id="institution_name"
								name="institution_name"
								value={currentEducation?.institution_name || ""}
								onChange={handleChange}
								required
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="degree">Degree*</Label>
								<Input
									id="degree"
									name="degree"
									value={currentEducation?.degree || ""}
									onChange={handleChange}
									required
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="field_of_study">Field of Study</Label>
								<Input
									id="field_of_study"
									name="field_of_study"
									value={currentEducation?.field_of_study || ""}
									onChange={handleChange}
								/>
							</div>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-2">
								<Label htmlFor="start_date">Start Date*</Label>
								<Input
									id="start_date"
									name="start_date"
									type="date"
									value={formatDateForInput(currentEducation?.start_date)}
									onChange={(e) => handleDateChange(e, "start_date")}
									required
								/>
							</div>
							<div className="space-y-2">
								<Label htmlFor="end_date">End Date</Label>
								<Input
									id="end_date"
									name="end_date"
									type="date"
									value={formatDateForInput(currentEducation?.end_date)}
									onChange={(e) => handleDateChange(e, "end_date")}
								/>
							</div>
						</div>

						<div className="space-y-2">
							<Label htmlFor="description">Description</Label>
							<Textarea
								id="description"
								name="description"
								rows={4}
								value={currentEducation?.description || ""}
								onChange={handleChange}
							/>
						</div>
					</div>

					{error && <div className="text-red-500 text-sm">{error}</div>}

					<div className="flex justify-end space-x-2">
						<Button variant="outline" onClick={handleCloseDialog} disabled={isLoading}>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={isLoading}>
							{isLoading ? "Saving..." : "Save"}
						</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
