"use client";

import { useEffect, useRef } from "react";

interface AdBannerProps {
	variant?: "horizontal" | "vertical" | "square";
	className?: string;
	adSlot: string;
}

export function AdBanner({ variant = "horizontal", className, adSlot }: AdBannerProps) {
	const adContainerRef = useRef<HTMLDivElement>(null);
	const initialized = useRef(false);

	// Standard IAB ad sizes
	const dimensions = {
		horizontal: { width: 728, height: 90 }, // Leaderboard
		vertical: { width: 300, height: 600 }, // Skyscraper
		square: { width: 300, height: 250 }, // Medium Rectangle
	};

	useEffect(() => {
		if (typeof window === "undefined" || !adContainerRef.current) return;

		const container = adContainerRef.current;
		const adEl = container.querySelector("ins.adsbygoogle");

		if (!adEl) return;

		// Clear any previous ad content
		adEl.innerHTML = "";

		const { width, height } = dimensions[variant];

		// Apply sizing rules
		if (variant === "horizontal") {
			const parentWidth = container.parentElement?.clientWidth || width;
			const calculatedWidth = Math.min(parentWidth, width);
			const calculatedHeight = (height / width) * calculatedWidth;

			container.style.width = `${calculatedWidth}px`;
			container.style.height = `${calculatedHeight}px`;
		} else {
			container.style.width = `${width}px`;
			container.style.height = `${height}px`;
		}

		container.style.overflow = "hidden";
		container.style.position = "relative";

		// Attempt to load ad with retry
		let retryCount = 0;
		const loadAd = () => {
			try {
				((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
				initialized.current = true;
			} catch (e) {
				retryCount++;
				if (retryCount < 3) {
					setTimeout(loadAd, 1000);
				} else {
					console.error("AdSense load failed:", e);
				}
			}
		};

		loadAd();

		return () => {
			if (adEl) adEl.innerHTML = "";
		};
	}, [variant, adSlot]);

	return (
		<div className={`inline-block ${className}`}>
			<div
				ref={adContainerRef}
				style={{
					minWidth: `${dimensions[variant].width}px`,
					minHeight: `${dimensions[variant].height}px`,
					margin: "0 auto",
				}}
			>
				<ins
					key={`${variant}-${adSlot}`}
					className="adsbygoogle"
					style={{ display: "block" }}
					data-ad-client="ca-pub-5681407322305640"
					data-ad-slot={adSlot}
					data-ad-format={
						variant === "horizontal" ? "horizontal" : variant === "square" ? "rectangle" : "auto"
					}
					data-full-width-responsive="true"
				/>
			</div>
		</div>
	);
}
