import { NextResponse } from "next/server";
import { auth0 } from "../../../../lib/auth0";

export const GET = async function getUserJobs(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/jobs`, {
			headers: {
				Authorization: `Bearer ${accessToken}`,
			},
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const experiences = await response.json();
		return NextResponse.json(experiences);
	} catch (error) {
		console.error("Error fetching user jobs:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};

export const POST = async function addUserJob(request: Request) {
	try {
		const { token: accessToken } = await auth0.getAccessToken();

		if (!accessToken) {
			return NextResponse.redirect(new URL("/api/auth/login", request.url));
		}

		const body = await request.json();

		// Validate required fields
		if (!body.title) {
			return NextResponse.json({ error: "Job title is required" }, { status: 400 });
		}

		const response = await fetch(`${process.env.API_BASE_URL}/user/jobs`, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${accessToken}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				...body,
			}),
		});

		if (!response.ok) {
			const error = await response.json();
			return NextResponse.json({ error: error.message }, { status: response.status });
		}

		const experience = await response.json();
		return NextResponse.json({ data: experience }, { status: 201 });
	} catch (error) {
		console.error("Error adding job:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
};
