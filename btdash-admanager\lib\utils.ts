import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export async function fetchWithFallback(
	url: string,
	options: RequestInit = {},
	retries: number = 3,
	retryDelay: number = 1000
): Promise<any> {
	let lastError: Error | null = null;

	for (let i = 0; i < retries; i++) {
		try {
			const res = await fetch(url, {
				cache: "no-store",
				...options,
			});

			// Handle 401 specifically
			if (res.status === 401) {
				if (typeof window !== "undefined") {
					window.location.href = `/auth/logout?returnTo=${encodeURIComponent(window.location.pathname)}`;
				}
				// Return immediately without retrying
				return { success: false, data: null, message: "Unauthorized", error: new Error("Unauthorized") };
			}

			if (!res.ok) {
				throw new Error(`Failed to fetch ${url}: HTTP ${res.status}`);
			}

			const result = await res.json();

			// Handle standardized response format
			if (result.success !== undefined) {
				return result; // Return the full response with success, data, message
			} else {
				// Legacy format - wrap in new format for backward compatibility
				return { success: true, data: result.data || result, message: "Success" };
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));

			// Skip retries for 401 errors
			if (lastError.message.includes("401")) {
				return { success: false, data: null, message: "Unauthorized", error: lastError };
			}

			console.error(`Attempt ${i + 1} failed for ${url}:`, error);

			// Don't wait on the last attempt
			if (i < retries - 1) {
				await new Promise((resolve) => setTimeout(resolve, retryDelay));
				retryDelay *= 2; // Exponential backoff
			}
		}
	}

	console.error(`All ${retries} attempts failed for ${url}:`, lastError);

	return { success: false, data: null, message: lastError?.message || "Unknown error", error: lastError };
}

/**
 * SERVER-SIDE ONLY
 * Secure fetch that injects INTERNAL_API_KEY.
 * Returns full fetch Response (not JSON parsed).
 */
export async function fetchInternal(url: string, options: RequestInit = {}): Promise<Response> {
	const headers = new Headers(options.headers);
	headers.set("x-internal-api-key", process.env.INTERNAL_API_KEY!);

	const finalOptions: RequestInit = {
		...options,
		headers,
		cache: "no-store",
	};

	return fetch(url, finalOptions);
}

/**
 * Turn any string into a URL-friendly slug:
 * - lower-cases
 * - trims
 * - replaces spaces & non-word chars with single hyphens
 */
export function slugify(text: string): string {
	return text
		.toLowerCase()
		.trim()
		.replace(/[\s\W-]+/g, "-") // collapse spaces & non-word into hyphen
		.replace(/^-+|-+$/g, ""); // trim leading/trailing hyphens
}

/**
 * Generate initials from a name string.
 * If no name is provided, returns "U" for "Unknown".
 */
export function getInitials(name: string | undefined) {
	if (!name) return "U";
	const names = name.split(" ");
	return names
		.map((n) => n[0])
		.join("")
		.toUpperCase();
}
