"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const tokens = [
  { id: "tao", name: "TA<PERSON>" },
  { id: "subnet", name: "Subnet Token" },
]

export function SubnetTokenSwap() {
  const [fromToken, setFromToken] = useState(tokens[0])
  const [toToken, setToToken] = useState(tokens[1])
  const [amount, setAmount] = useState("")

  const handleSwap = () => {
    // Implement swap logic here
    console.log(`Swapping ${amount} ${fromToken.name} to ${toToken.name}`)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Swap Tokens</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">From</label>
            <div className="flex space-x-2">
              <Select
                value={fromToken.id}
                onValueChange={(value) => setFromToken(tokens.find((t) => t.id === value) || tokens[0])}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select token" />
                </SelectTrigger>
                <SelectContent>
                  {tokens.map((token) => (
                    <SelectItem key={token.id} value={token.id}>
                      {token.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input type="number" placeholder="0.00" value={amount} onChange={(e) => setAmount(e.target.value)} />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">To</label>
            <div className="flex space-x-2">
              <Select
                value={toToken.id}
                onValueChange={(value) => setToToken(tokens.find((t) => t.id === value) || tokens[1])}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select token" />
                </SelectTrigger>
                <SelectContent>
                  {tokens.map((token) => (
                    <SelectItem key={token.id} value={token.id}>
                      {token.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input type="number" placeholder="0.00" value={(Number.parseFloat(amount) * 0.95).toFixed(2)} readOnly />
            </div>
          </div>

          <Button className="w-full" onClick={handleSwap}>
            Swap
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

