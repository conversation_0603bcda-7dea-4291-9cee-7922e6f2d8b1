// src/application/services/GitHubService.js

const BaseService = require("./BaseService");
const logger = require("../../../logger");

/**
 * @fileoverview GitHub Service - Handles GitHub data operations
 * 
 * This service manages GitHub-related data including contributions,
 * team members, and repository information.
 * 
 * Key responsibilities:
 * - GitHub contributions CRUD operations
 * - Team members management
 * - Repository data handling
 * - Data validation and sanitization
 * 
 * <AUTHOR> Development Team
 * @since 1.0.0
 */
class GitHubService extends BaseService {
  constructor() {
    super("dtm_base.github_contributions", "GitHubContribution");
  }

  /**
   * Get all GitHub contributions with default sorting by date
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of GitHub contributions
   */
  async getAllContributions(filters = {}, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'date', direction: 'desc' },
        ...options
      };

      return await this.getAll(filters, queryOptions);
    } catch (error) {
      logger.error("Error getting all GitHub contributions", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get GitHub contribution by ID
   * @param {number} id - Contribution ID
   * @returns {Promise<Object|null>} Contribution object or null if not found
   */
  async getContributionById(id) {
    try {
      return await this.getById(id);
    } catch (error) {
      logger.error("Error getting GitHub contribution by ID", { error, id });
      throw error;
    }
  }

  /**
   * Get contributions by username
   * @param {string} username - GitHub username
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of contributions for the user
   */
  async getContributionsByUsername(username, options = {}) {
    try {
      const queryOptions = {
        orderBy: { column: 'date', direction: 'desc' },
        ...options
      };

      const contributions = await this.getAll({ username }, queryOptions);
      logger.info("GitHub contributions retrieved by username", { 
        username, 
        count: contributions.length 
      });
      return contributions;
    } catch (error) {
      logger.error("Error getting contributions by username", { error, username });
      throw new Error(`Failed to get contributions by username: ${error.message}`);
    }
  }

  /**
   * Create a new GitHub contribution record
   * @param {Object} contributionData - Contribution data
   * @returns {Promise<Object>} Created contribution object
   */
  async createContribution(contributionData) {
    try {
      const newContribution = await this.create(contributionData);
      logger.info("GitHub contribution created", { contribution_id: newContribution.id });
      return newContribution;
    } catch (error) {
      logger.error("Error creating GitHub contribution", { error, contributionData });
      throw error;
    }
  }

  /**
   * Update a GitHub contribution
   * @param {number} id - Contribution ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated contribution object
   */
  async updateContribution(id, updateData) {
    try {
      const updatedContribution = await this.updateById(id, updateData);
      logger.info("GitHub contribution updated", { contribution_id: id });
      return updatedContribution;
    } catch (error) {
      logger.error("Error updating GitHub contribution", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete a GitHub contribution
   * @param {number} id - Contribution ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteContribution(id) {
    try {
      const result = await this.deleteById(id);
      logger.info("GitHub contribution deleted", { contribution_id: id });
      return result;
    } catch (error) {
      logger.error("Error deleting GitHub contribution", { error, id });
      throw error;
    }
  }

  /**
   * Get all team members
   * @param {Object} filters - Optional filters
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of team members
   */
  async getAllTeamMembers(filters = {}, options = {}) {
    try {
      const db = require("../../infrastructure/database/knex");
      
      const queryOptions = {
        orderBy: { column: 'name', direction: 'asc' },
        ...options
      };

      let query = db("dtm_base.github_team_members");

      // Apply filters
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined) {
          query = query.where(key, filters[key]);
        }
      });

      // Apply ordering
      if (queryOptions.orderBy) {
        const { column, direction } = queryOptions.orderBy;
        query = query.orderBy(column, direction);
      }

      // Apply pagination
      if (queryOptions.limit) {
        query = query.limit(queryOptions.limit);
      }
      if (queryOptions.offset) {
        query = query.offset(queryOptions.offset);
      }

      const teamMembers = await query;
      
      logger.info("GitHub team members retrieved", { 
        count: teamMembers.length,
        filters,
        options 
      });
      
      return teamMembers;
    } catch (error) {
      logger.error("Error getting all team members", { error, filters, options });
      throw error;
    }
  }

  /**
   * Get team member by ID
   * @param {number} id - Team member ID
   * @returns {Promise<Object|null>} Team member object or null if not found
   */
  async getTeamMemberById(id) {
    try {
      const db = require("../../infrastructure/database/knex");
      const teamMember = await db("dtm_base.github_team_members").where({ id }).first();
      
      if (teamMember) {
        logger.info("GitHub team member retrieved by ID", { id });
      }
      
      return teamMember;
    } catch (error) {
      logger.error("Error getting team member by ID", { error, id });
      throw error;
    }
  }

  /**
   * Create a new team member
   * @param {Object} memberData - Team member data
   * @returns {Promise<Object>} Created team member object
   */
  async createTeamMember(memberData) {
    try {
      const db = require("../../infrastructure/database/knex");
      const [newMember] = await db("dtm_base.github_team_members")
        .insert(memberData)
        .returning("*");
        
      logger.info("GitHub team member created", { member_id: newMember.id });
      return newMember;
    } catch (error) {
      logger.error("Error creating team member", { error, memberData });
      throw error;
    }
  }

  /**
   * Update a team member
   * @param {number} id - Team member ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated team member object
   */
  async updateTeamMember(id, updateData) {
    try {
      const db = require("../../infrastructure/database/knex");
      const [updatedMember] = await db("dtm_base.github_team_members")
        .where({ id })
        .update(updateData)
        .returning("*");
        
      if (updatedMember) {
        logger.info("GitHub team member updated", { member_id: id });
      }
      
      return updatedMember;
    } catch (error) {
      logger.error("Error updating team member", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete a team member
   * @param {number} id - Team member ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteTeamMember(id) {
    try {
      const db = require("../../infrastructure/database/knex");
      const deleted = await db("dtm_base.github_team_members").where({ id }).del();
      
      if (deleted) {
        logger.info("GitHub team member deleted", { member_id: id });
      }
      
      return deleted > 0;
    } catch (error) {
      logger.error("Error deleting team member", { error, id });
      throw error;
    }
  }

  /**
   * Get contribution statistics for a user
   * @param {string} username - GitHub username
   * @param {number} days - Number of days to analyze
   * @returns {Promise<Object>} Contribution statistics
   */
  async getContributionStats(username, days = 30) {
    try {
      const db = require("../../infrastructure/database/knex");
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const stats = await db(this.tableName)
        .where('username', username)
        .where('date', '>=', startDate)
        .select(
          db.raw('COUNT(*) as total_days'),
          db.raw('SUM(contributions) as total_contributions'),
          db.raw('AVG(contributions) as avg_contributions'),
          db.raw('MAX(contributions) as max_contributions')
        )
        .first();

      logger.info("GitHub contribution stats retrieved", { 
        username,
        days,
        stats 
      });
      
      return stats;
    } catch (error) {
      logger.error("Error getting contribution stats", { error, username, days });
      throw new Error(`Failed to get contribution stats: ${error.message}`);
    }
  }

  /**
   * Get top contributors
   * @param {number} days - Number of days to analyze
   * @param {number} limit - Number of top contributors to return
   * @returns {Promise<Array>} Array of top contributors
   */
  async getTopContributors(days = 30, limit = 10) {
    try {
      const db = require("../../infrastructure/database/knex");
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const topContributors = await db(this.tableName)
        .where('date', '>=', startDate)
        .select(
          'username',
          db.raw('SUM(contributions) as total_contributions'),
          db.raw('COUNT(*) as active_days')
        )
        .groupBy('username')
        .orderBy('total_contributions', 'desc')
        .limit(limit);

      logger.info("Top GitHub contributors retrieved", { 
        days,
        limit,
        count: topContributors.length 
      });
      
      return topContributors;
    } catch (error) {
      logger.error("Error getting top contributors", { error, days, limit });
      throw new Error(`Failed to get top contributors: ${error.message}`);
    }
  }
}

module.exports = new GitHubService();
