import { getItemById } from "@/lib/data/utils";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function GET(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const data = await getItemById("jobs", id);
		return NextResponse.json({
			success: true,
			data,
			message: "Job fetched successfully",
		});
	} catch (err) {
		return NextResponse.json(
			{
				success: false,
				message: "Job not found",
				errors: err instanceof Error ? err.message : "Unknown error",
			},
			{ status: 404 }
		);
	}
}
