"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { Company } from "@/lib/db/models";
import { PlusCircle, Save } from "lucide-react";
import { useEffect, useState } from "react";

interface CompanyInfoProps {
	initialCompany?: Partial<Company>;
}

export default function CompanyInfo({ initialCompany }: CompanyInfoProps) {
	const [company, setCompany] = useState<Partial<Company>>(initialCompany || {});
	const [editMode, setEditMode] = useState(!initialCompany?.id);
	const [saving, setSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		setCompany(initialCompany || {});
		if (!initialCompany?.id) setEditMode(true);
	}, [initialCompany]);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = e.target;
		setCompany((prev) => ({ ...prev, [name]: value }));
	};

	const handleSocialMediaChange = (platform: string, value: string) => {
		setCompany((prev) => ({
			...prev,
			social_media: {
				...(typeof prev.social_media === "object" && !Array.isArray(prev.social_media)
					? prev.social_media
					: {}),
				[platform]: value,
			},
		}));
	};

	const handleSave = async () => {
		setSaving(true);
		setError(null);
		try {
			const method = company.id ? "PUT" : "POST";

			const res = await fetch("/api/user/company", {
				method,
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(company),
			});

			if (!res.ok) {
				const err = await res.json();
				throw new Error(err.error || "Failed to save company");
			}

			const saved = await res.json();
			setCompany(saved.data);
			setEditMode(false);
		} catch (err: any) {
			console.error("Save failed:", err);
			setError(err.message);
		} finally {
			setSaving(false);
		}
	};

	const socialMediaPlatforms = [
		{ id: "twitter", label: "Twitter" },
		{ id: "linkedin", label: "LinkedIn" },
		{ id: "facebook", label: "Facebook" },
		{ id: "instagram", label: "Instagram" },
	];

	return (
		<div className="space-y-6">
			{editMode ? (
				<div className="grid gap-6">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="name">Company Name *</Label>
							<Input id="name" name="name" value={company.name || ""} onChange={handleChange} required />
						</div>
						<div className="space-y-2">
							<Label htmlFor="website_url">Website URL</Label>
							<Input
								id="website_url"
								name="website_url"
								type="url"
								value={company.website_url || ""}
								onChange={handleChange}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<Label htmlFor="description">Description</Label>
						<Textarea
							id="description"
							name="description"
							rows={4}
							value={company.description || ""}
							onChange={handleChange}
							placeholder="Describe your company..."
						/>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="logo_url">Logo URL</Label>
							<Input
								id="logo_url"
								name="logo_url"
								type="url"
								value={company.logo_url || ""}
								onChange={handleChange}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="header_url">Header Image URL</Label>
							<Input
								id="header_url"
								name="header_url"
								type="url"
								value={company.header_url || ""}
								onChange={handleChange}
							/>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="space-y-2">
							<Label htmlFor="location">Location</Label>
							<Input
								id="location"
								name="location"
								value={company.location || ""}
								onChange={handleChange}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="foundedyear">Founded Year</Label>
							<Input
								id="foundedyear"
								name="foundedyear"
								type="number"
								value={company.foundedyear || ""}
								onChange={handleChange}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="teamsize">Team Size</Label>
							<Input
								id="teamsize"
								name="teamsize"
								type="text"
								value={company.teamsize || ""}
								onChange={handleChange}
							/>
						</div>
					</div>

					<div className="space-y-2">
						<Label>Social Media</Label>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{socialMediaPlatforms.map((platform) => (
								<div key={platform.id} className="space-y-2">
									<Label htmlFor={`social-${platform.id}`}>{platform.label}</Label>
									<Input
										id={`social-${platform.id}`}
										type="url"
										value={
											(typeof company.social_media === "object" &&
												!Array.isArray(company.social_media) &&
												company.social_media?.[
													platform.id as keyof typeof company.social_media
												]) ||
											""
										}
										onChange={(e) => handleSocialMediaChange(platform.id, e.target.value)}
										placeholder={`https://${platform.id}.com/yourcompany`}
									/>
								</div>
							))}
						</div>
					</div>

					{error && <p className="text-red-500">{error}</p>}

					<div className="flex justify-end gap-2">
						{company.id && (
							<Button variant="outline" onClick={() => setEditMode(false)}>
								Cancel
							</Button>
						)}
						<Button onClick={handleSave} disabled={saving}>
							{company.id ? <Save className="mr-2 h-4 w-4" /> : <PlusCircle className="mr-2 h-4 w-4" />}
							{saving ? "Saving..." : company.id ? "Save Changes" : "Create Company"}
						</Button>
					</div>
				</div>
			) : (
				<div className="space-y-6">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Company Name</p>
							<p className="text-foreground">{company.name || "Not specified"}</p>
						</div>
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Website</p>
							<p className="text-foreground">
								{company.website_url ? (
									<a
										href={company.website_url}
										target="_blank"
										rel="noopener noreferrer"
										className="text-blue-600 hover:underline"
									>
										{company.website_url}
									</a>
								) : (
									<span className="text-gray-400 italic">Not specified</span>
								)}
							</p>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Logo</p>
							<p className="text-foreground">{company.logo_url || "Not specified"}</p>
						</div>
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Header Image</p>
							<p className="text-foreground">{company.header_url || "Not specified"}</p>
						</div>
					</div>

					<div className="space-y-1">
						<p className="text-sm font-medium text-muted-foreground">Description</p>
						<p className="whitespace-pre-wrap text-foreground">
							{company.description || (
								<span className="text-gray-400 italic">No description provided</span>
							)}
						</p>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Location</p>
							<p className="text-foreground">{company.location || "Not specified"}</p>
						</div>
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Founded Year</p>
							<p className="text-foreground">{company.foundedyear || "Not specified"}</p>
						</div>
						<div className="space-y-1">
							<p className="text-sm font-medium text-muted-foreground">Team Size</p>
							<p className="text-foreground">{company.teamsize || "Not specified"}</p>
						</div>
					</div>

					<div className="space-y-2">
						<p className="text-sm font-medium text-muted-foreground">Social Media</p>
						<div className="flex flex-wrap gap-2">
							{socialMediaPlatforms.map((platform) => {
								const url =
									typeof company.social_media === "object" && !Array.isArray(company.social_media)
										? company.social_media?.[platform.id as keyof typeof company.social_media]
										: null;

								return url ? (
									<Badge key={platform.id} variant="secondary" className="flex items-center gap-1">
										<a
											href={url}
											target="_blank"
											rel="noopener noreferrer"
											className="hover:underline"
										>
											{platform.label}
										</a>
									</Badge>
								) : null;
							})}
							{!socialMediaPlatforms.some(
								(p) =>
									typeof company.social_media === "object" &&
									!Array.isArray(company.social_media) &&
									company.social_media?.[p.id as keyof typeof company.social_media]
							) && <span className="text-gray-400 italic">No social media links provided</span>}
						</div>
					</div>

					<div className="flex justify-end">
						<Button onClick={() => setEditMode(true)}>Edit Company Info</Button>
					</div>
				</div>
			)}
		</div>
	);
}
