import { getCategoryById, pageTypesWithCategories } from "@/lib/page-categories";
import { Building2, FileText, Laptop, Newspaper } from "lucide-react";

interface PageTargetingDisplayProps {
	pageTypes: string[];
	categories: { [pageType: string]: string[] | "all" };
	detailed?: boolean;
}

export function PageTargetingDisplay({ pageTypes, categories, detailed = false }: PageTargetingDisplayProps) {
	if (pageTypes.length === 0) {
		return <p className="text-sm">No page targeting configured</p>;
	}

	// Get icon for page type
	const getPageTypeIcon = (type: string) => {
		switch (type) {
			case "subnet":
				return <Laptop className="h-4 w-4 text-muted-foreground" />;
			case "companies":
				return <Building2 className="h-4 w-4 text-muted-foreground" />;
			case "products":
				return <FileText className="h-4 w-4 text-muted-foreground" />;
			case "news":
				return <Newspaper className="h-4 w-4 text-muted-foreground" />;
			default:
				return <FileText className="h-4 w-4 text-muted-foreground" />;
		}
	};

	// Get label for page type
	const getPageTypeLabel = (type: string) => {
		const pageType = pageTypesWithCategories.find((pt) => pt.type === type);
		return pageType ? pageType.label : type;
	};

	// Get category names for a page type
	const getCategoryNames = (pageType: string, categoryIds: string[] | "all") => {
		if (categoryIds === "all") return "All categories";

		const pageTypeObj = pageTypesWithCategories.find((pt) => pt.type === pageType);
		if (!pageTypeObj) return "";

		if (categoryIds.length === 0) return "No categories";
		if (categoryIds.length === pageTypeObj.categories.length) return "All categories";

		if (detailed) {
			return categoryIds
				.map((id) => {
					const category = getCategoryById(id);
					return category ? category.name : id;
				})
				.join(", ");
		} else {
			return `${categoryIds.length} ${categoryIds.length === 1 ? "category" : "categories"}`;
		}
	};

	if (detailed) {
		return (
			<div className="space-y-4">
				{pageTypes.map((type) => (
					<div key={type} className="space-y-2">
						<div className="flex items-center gap-2">
							{getPageTypeIcon(type)}
							<h4 className="font-medium">{getPageTypeLabel(type)}</h4>
						</div>
						<p className="text-sm pl-6">{getCategoryNames(type, categories[type] || "all")}</p>
					</div>
				))}
			</div>
		);
	}

	// For simple display, just show the number of page types
	if (pageTypes.length === pageTypesWithCategories.length) {
		const allCategoriesSelected = pageTypes.every((type) => categories[type] === "all");

		if (allCategoriesSelected) {
			return <p className="text-sm">All pages</p>;
		}
	}

	return (
		<p className="text-sm">
			{pageTypes.length} page {pageTypes.length === 1 ? "type" : "types"} selected
		</p>
	);
}
