// test-api.js - Simple test script to verify API is working
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

async function testAPI() {
  console.log('🧪 Testing BTDashs API...\n');

  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    // Test standardized response format
    if (healthResponse.data.success !== undefined) {
      console.log('✅ Standardized response format detected');
    } else {
      console.log('❌ Response format not standardized');
    }
    console.log('');

    // Test 404 handling
    console.log('2. Testing 404 error handling...');
    try {
      await axios.get(`${API_BASE_URL}/api/nonexistent-endpoint`);
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ 404 handling working:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.response?.data);
      }
    }
    console.log('');

    // Test validation on campaigns endpoint (should fail without auth)
    console.log('3. Testing authentication on protected endpoint...');
    try {
      await axios.get(`${API_BASE_URL}/api/campaigns`);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Authentication protection working:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.response?.data);
      }
    }
    console.log('');

    // Test validation on campaign creation (should fail with validation error)
    console.log('4. Testing validation on campaign creation...');
    try {
      await axios.post(`${API_BASE_URL}/api/campaigns`, {
        name: '', // Invalid: empty name
        start_date: 'invalid-date', // Invalid: bad date format
      });
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Authentication required for campaign creation');
      } else if (error.response?.status === 422) {
        console.log('✅ Validation working:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.response?.data);
      }
    }
    console.log('');

    console.log('🎉 API tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the API server is running on port 3001');
    }
  }
}

// Run tests
testAPI();
