"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useState } from "react"

interface User {
  id: string
  name: string
  username: string
  avatar: string
  role: string
}

const activeUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    username: "alice_ai",
    avatar: "/placeholder.svg",
    role: "Core Developer",
  },
  {
    id: "2",
    name: "<PERSON>",
    username: "bob_validator",
    avatar: "/placeholder.svg",
    role: "Validator",
  },
  {
    id: "3",
    name: "<PERSON>",
    username: "carol_research",
    avatar: "/placeholder.svg",
    role: "AI Researcher",
  },
  {
    id: "4",
    name: "<PERSON>",
    username: "david_dev",
    avatar: "/placeholder.svg",
    role: "Develo<PERSON>",
  },
  {
    id: "5",
    name: "<PERSON>",
    username: "eva_ml",
    avatar: "/placeholder.svg",
    role: "ML Engineer",
  },
]

export function ActiveUsers() {
  const [following, setFollowing] = useState<Set<string>>(new Set())

  const toggleFollow = (userId: string) => {
    setFollowing((prev) => {
      const next = new Set(prev)
      if (next.has(userId)) {
        next.delete(userId)
      } else {
        next.add(userId)
      }
      return next
    })
  }

  return (
    <div className="space-y-4">
      <h2 className="font-semibold text-lg">Recently active users</h2>
      <div className="space-y-4">
        {activeUsers.map((user) => (
          <div key={user.id} className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback>{user.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">@{user.username}</div>
              </div>
            </div>
            <Button
              variant={following.has(user.id) ? "default" : "outline"}
              size="sm"
              onClick={() => toggleFollow(user.id)}
            >
              {following.has(user.id) ? "Following" : "Follow"}
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}

