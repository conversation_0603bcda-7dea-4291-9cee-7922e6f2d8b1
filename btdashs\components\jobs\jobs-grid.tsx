import { JobCard } from "@/components/jobs/job-card";
import type { Category, Company, Job, Product, Subnet } from "@/lib/db/models";

interface JobsGridProps {
	jobs: (Job & {
		company?: Company;
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	editMode?: boolean;
	onEdit?: (job: Job) => void;
	onDelete?: (jobId: number) => void;
	loading?: boolean;
}

export function JobsGrid({ jobs, editMode = false, onEdit, onDelete, loading = false }: JobsGridProps) {
	if (loading) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				{Array.from({ length: 6 }).map((_, i) => (
					<div key={i} className="h-[220px] bg-muted animate-pulse rounded-lg"></div>
				))}
			</div>
		);
	}

	if (jobs.length === 0) {
		return (
			<div className="text-center py-12">
				<h3 className="text-lg font-medium mb-2">No jobs found</h3>
				<p className="text-muted-foreground">Try adjusting your search or filters</p>
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
			{jobs.map((job) => (
				<JobCard key={job.id} job={job} editMode={editMode} onEdit={onEdit} onDelete={onDelete} />
			))}
		</div>
	);
}
