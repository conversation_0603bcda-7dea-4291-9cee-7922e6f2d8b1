"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { News } from "@/lib/db/models";
import { CalendarIcon, Newspaper } from "lucide-react";

interface SubnetNewsProps {
	news: News[];
}

export function SubnetNews({ news }: SubnetNewsProps) {
	const newsItems = Array.isArray(news) ? news : [];

	if (newsItems.length === 0) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>Latest News</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex flex-col items-center justify-center py-8 text-center">
						<Newspaper className="h-12 w-12 text-muted-foreground mb-4" />
						<p className="text-muted-foreground">No news available for this subnet at the moment.</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card>
			<CardHeader>
				<CardTitle>Latest News</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-6">
					{newsItems.map((item) => (
						<div key={item.id} className="space-y-2">
							<div className="flex items-center justify-between">
								<h3 className="text-lg font-semibold">{item.title}</h3>
								<Badge variant="outline">{item.category_ids?.join(", ") || "Uncategorized"}</Badge>
							</div>
							<p className="text-sm text-muted-foreground">
								{item.content?.slice(0, 200) ?? "No details available."}
							</p>
							<div className="flex items-center text-sm text-muted-foreground">
								<CalendarIcon className="mr-1 h-4 w-4" />
								{new Date(item.publication_date).toLocaleDateString()}
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
