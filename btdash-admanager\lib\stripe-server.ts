import Stripe from 'stripe';
import { stripeConfig, validateStripeConfig } from './stripe';

// Validate configuration on import
validateStripeConfig();

// Create Stripe instance for server-side operations
export const stripe = new Stripe(stripeConfig.secretKey!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

// Helper functions for common Stripe operations
export const stripeHelpers = {
  // Create a payment intent for campaign billing
  async createPaymentIntent(amount: number, metadata: Record<string, string>) {
    return await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: stripeConfig.currency,
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    });
  },

  // Create a customer
  async createCustomer(email: string, name?: string, metadata?: Record<string, string>) {
    return await stripe.customers.create({
      email,
      name,
      metadata,
    });
  },

  // Retrieve a customer
  async getCustomer(customerId: string) {
    return await stripe.customers.retrieve(customerId);
  },

  // Create a setup intent for saving payment methods
  async createSetupIntent(customerId: string) {
    return await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
    });
  },

  // List customer payment methods
  async listPaymentMethods(customerId: string) {
    return await stripe.paymentMethods.list({
      customer: customerId,
      type: 'card',
    });
  },

  // Detach a payment method
  async detachPaymentMethod(paymentMethodId: string) {
    return await stripe.paymentMethods.detach(paymentMethodId);
  },

  // Confirm a payment intent
  async confirmPaymentIntent(paymentIntentId: string, paymentMethodId?: string) {
    const updateData: Stripe.PaymentIntentConfirmParams = {};
    
    if (paymentMethodId) {
      updateData.payment_method = paymentMethodId;
    }
    
    return await stripe.paymentIntents.confirm(paymentIntentId, updateData);
  },

  // Retrieve payment intent
  async getPaymentIntent(paymentIntentId: string) {
    return await stripe.paymentIntents.retrieve(paymentIntentId);
  },

  // Create an invoice
  async createInvoice(customerId: string, metadata?: Record<string, string>) {
    return await stripe.invoices.create({
      customer: customerId,
      metadata,
    });
  },

  // Verify webhook signature
  verifyWebhookSignature(payload: string | Buffer, signature: string) {
    return stripe.webhooks.constructEvent(
      payload,
      signature,
      stripeConfig.webhookSecret!
    );
  },
};

export default stripe;
