// app/api/user/skills/[id]/route.ts
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { auth0 } from "../../../../../lib/auth0";

export async function GET(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const res = await fetch(`${process.env.API_BASE_URL}/user/skills/${id}`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		return handleResponse(res);
	} catch (error) {
		console.error("Skill fetch error:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
}

export async function PUT(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const { level } = await req.json();

		const res = await fetch(`${process.env.API_BASE_URL}/user/skills/${id}`, {
			method: "PUT",
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-Type": "application/json",
			},
			body: JSON.stringify({ level }),
		});

		return handleResponse(res);
	} catch (error) {
		console.error("Skill update error:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
}

export async function DELETE(req: NextRequest, context: { params: { id: string } }) {
	const paramsData = await context.params;
	const id = paramsData.id;

	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			return NextResponse.redirect(new URL("/api/auth/login", req.url));
		}

		const res = await fetch(`${process.env.API_BASE_URL}/user/skills/${id}`, {
			method: "DELETE",
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		return handleResponse(res);
	} catch (error) {
		console.error("Skill delete error:", error);
		return NextResponse.json({ error: "Server error" }, { status: 500 });
	}
}

// Shared response handler
function handleResponse(response: Response) {
	const contentType = response.headers.get("content-type");

	if (contentType?.includes("application/json")) {
		return response.json().then(
			(data) =>
				new NextResponse(JSON.stringify(data), {
					status: response.status,
					headers: { "Content-Type": "application/json" },
				})
		);
	}

	return response.text().then(
		(text) =>
			new NextResponse(text, {
				status: response.status,
				headers: { "Content-Type": "text/plain" },
			})
	);
}
