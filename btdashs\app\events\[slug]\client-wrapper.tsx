"use client";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { event_banner_placeholder } from "@/constants";
import type { Category, Company, Event, Subnet } from "@/lib/db/models";
import {
  ArrowLeft,
  Calendar,
  ExternalLink,
  MapPin,
  Share2,
  Users,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ReactMarkdown from "react-markdown";

export interface Speaker {
  name: string;
  position?: string;
  company?: string;
  image?: string;
  twitter?: string;
  linkedin?: string;
  companyID?: string;
}

interface EventsClientProps {
  event: Event | null;
  categories: Category[];
  company: Company[] | null | undefined;
  subnets: Subnet[];
  events: Event[] | null;
}

export default function EventsClientWrapper({
  event,
  categories,
  company,
  subnets,
  events,
}: EventsClientProps) {
  const [copied, setCopied] = useState(false);

  const formatDate = (date: Date | string | null) => {
    if (!date) return "";
    const d = new Date(date);
    return d.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (date: Date | string | null) => {
    if (!date) return "";
    const d = new Date(date);
    return d.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: event?.name || "Event",
        text: event?.description || "Check out this event",
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  let speakers: Speaker[] = [];
  if (event && event.speakers && event.speakers.length > 0) {
    try {
      const speakersData = event.speakers[0];
      speakers =
        typeof speakersData === "string"
          ? JSON.parse(speakersData)
          : speakersData;

      // Ensure we always have an array of speakers
      if (!Array.isArray(speakers)) {
        speakers = [];
      }
    } catch (error) {
      console.error("Error parsing speakers:", error);
      speakers = [];
    }
  }

  return (
    <>
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <Link
            href="/events"
            className="text-sm flex items-center hover:underline"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Events
          </Link>
        </div>

        <div className="relative rounded-xl overflow-hidden mb-8">
          <div className="relative h-[150px] md:h-[200px] w-full">
            <Image
              src={event?.image_url_banner || event_banner_placeholder}
              alt={event?.name || "Event banner"}
              fill
              className="object-cover"
              priority
            />

            <div className="absolute inset-0 flex flex-col justify-end p-6 pb-1 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
              <div className="mb-6">
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {event?.name}
                </h1>
                {/* <p className="text-white/80 text-lg mb-4 max-w-3xl">{event?.description}</p> */}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-1 text-white">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  <span>
                    {event?.start_date &&
                    event?.end_date &&
                    event?.start_date !== event.end_date
                      ? `${formatDate(event.start_date)} - ${formatDate(
                          event?.end_date
                        )}`
                      : event
                      ? formatDate(event.start_date)
                      : ""}
                  </span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span>{event?.location || "Location not available"}</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  <span className="mr-2">Featured:</span>
                  <div className="flex -space-x-2">
                    {Array.isArray(company) &&
                      company.map((company, index) => (
                        <Link
                          key={company.id || index}
                          href={company.website_url || "#"}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="relative h-8 w-8 rounded-full border-2 border-white bg-background overflow-hidden"
                          title={company.name}
                        >
                          <Image
                            src={company.logo_url || "/placeholder.svg"}
                            alt={company.name || "Company logo"}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover"
                          />
                        </Link>
                      ))}
                    {Array.isArray(subnets) &&
                      Array.isArray(event?.subnet_ids) &&
                      subnets
                        .filter((subnet) =>
                          event.subnet_ids?.includes(subnet.netuid)
                        )
                        .map((subnet, index) => (
                          <Link
                            key={subnet.id || index}
                            href={`/subnets/${subnet.netuid}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="relative h-8 w-8 rounded-full border-2 border-white bg-background overflow-hidden"
                            title={subnet.name}
                          >
                            <div className="w-7 h-7 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold">
                              {subnet.subnet_symbol || subnet.name.charAt(0)}
                            </div>
                          </Link>
                        ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-2xl font-bold mb-4">About This Event</h2>
                <p className="text-muted-foreground mb-6">
                  <ReactMarkdown>
                    {event?.desc_about_this_event ||
                      "Join us for an immersive experience in the world of decentralized AI."}
                  </ReactMarkdown>
                </p>

                {event && event.desc_what_u_will_learn && (
                  <>
                    <h3 className="text-xl font-bold mb-3">
                      What You&apos;ll Learn
                    </h3>
                    <p className="text-muted-foreground mb-6">
                      <ReactMarkdown>
                        {event.desc_what_u_will_learn}
                      </ReactMarkdown>
                    </p>
                  </>
                )}

                {event && event.desc_who_should_attend && (
                  <>
                    <h3 className="text-xl font-bold mb-3">
                      Who Should Attend
                    </h3>
                    <p className="text-muted-foreground">
                      <ReactMarkdown>
                        {event.desc_who_should_attend}
                      </ReactMarkdown>
                    </p>
                  </>
                )}
              </CardContent>
            </Card>
            <Card className="mt-6">
              <CardContent className="p-6">
                <h2 className="text-2xl font-bold mb-4">Event Location</h2>
                {event?.location && !event?.is_virtual ? (
                  <div className="w-full h-64 relative">
                    <iframe
                      src={`/api/maps?location=${encodeURIComponent(
                        event.location
                      )}`}
                      width="100%"
                      height="100%"
                      style={{ border: 0, borderRadius: "0.5rem" }}
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      className="rounded-lg"
                    />
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    In person location not available.
                  </p>
                )}
              </CardContent>
            </Card>
            {/* <Card className="mt-6">
						<CardContent className="p-6">
							<h2 className="text-2xl font-bold mb-4">Event Schedule</h2>
							<div className="space-y-4">
								<div className="border-l-4 border-primary pl-4">
									<h3 className="font-bold">Day 1 - December 15</h3>
									<div className="mt-3 space-y-3">
										<div>
											<p className="text-sm text-muted-foreground">9:00 AM - 10:00 AM</p>
											<p className="font-medium">Registration & Welcome Coffee</p>
										</div>
										<div>
											<p className="text-sm text-muted-foreground">10:00 AM - 11:30 AM</p>
											<p className="font-medium">Keynote: The Future of Decentralized AI</p>
											<p className="text-sm">Speaker: Alex Johnson, Bittensor Foundation</p>
										</div>
									</div>
								</div>

								<div className="border-l-4 border-primary pl-4">
									<h3 className="font-bold">Day 2 - December 16</h3>
									<div className="mt-3 space-y-3">
										<div>
											<p className="text-sm text-muted-foreground">9:30 AM - 11:00 AM</p>
											<p className="font-medium">Workshop: Building Your First Subnet</p>
										</div>
									</div>
								</div>
							</div>
						</CardContent>
					</Card> */}
          </div>

          <div>
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-4">Event Details</h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 mr-3 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="font-medium">
                        {event ? formatDate(event.start_date) : ""}
                        {event?.end_date && ` - ${formatDate(event.end_date)}`}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {event && event.start_date !== event.end_date
                          ? "All Day"
                          : `${event ? formatTime(event.start_date) : ""}${
                              event?.end_date
                                ? ` - ${formatTime(event.end_date)}`
                                : ""
                            }`}
                      </p>
                    </div>
                  </div>

                  {event && event.location && (
                    <div className="flex items-start">
                      <MapPin className="h-5 w-5 mr-3 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Location</p>
                        <p className="text-sm text-muted-foreground">
                          {event.location}
                        </p>
                        {!event.is_virtual && (
                          <p className="text-sm text-muted-foreground">
                            In-person event
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {event && Array.isArray(speakers) && speakers.length > 0 && (
              <Card className="mt-6">
                <CardContent className="p-6">
                  <h2 className="text-xl font-bold mb-4">Speakers</h2>
                  <div className="space-y-4">
                    {speakers.map((speaker, index) => (
                      <div
                        key={`${
                          typeof speaker === "string"
                            ? speaker
                            : (speaker as Speaker).name
                        }-${index}`}
                        className="flex items-start gap-3"
                      >
                        <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                          <Image
                            src={
                              typeof speaker !== "string" && speaker.image
                                ? speaker.image
                                : "/placeholder.svg"
                            }
                            alt={speaker.name}
                            width={48}
                            height={48}
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <p className="font-medium">
                            {typeof speaker === "string"
                              ? speaker
                              : speaker.name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {[speaker.position, speaker.company]
                              .filter(Boolean)
                              .join(", ")}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="mt-6 flex gap-4">
              {event && event.website_url && (
                <Link
                  href={event.website_url}
                  className="flex-1"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button className="w-full">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Register Now
                  </Button>
                </Link>
              )}
              <Button variant="outline" size="lg" onClick={handleShare}>
                <Share2 className="h-4 w-4" />
                {copied ? "Copied!" : "Share"}
              </Button>
            </div>

            {events && events.length > 0 && (
              <Card className="mt-6">
                <CardContent className="p-6">
                  <h2 className="text-xl font-bold mb-4">Related Events</h2>
                  <div className="space-y-3">
                    {events.map((event) => (
                      <Link
                        href={`/events/${event.id}`}
                        key={event.id}
                        className="block group"
                      >
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded bg-purple-100 flex items-center justify-center text-lg mr-3">
                            📅
                          </div>
                          <div>
                            <p className="font-medium group-hover:text-primary transition-colors">
                              {event.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatDate(event.start_date)}
                              {event.end_date &&
                              event.start_date !== event.end_date
                                ? ` - ${formatDate(event.end_date)}`
                                : ""}
                            </p>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
