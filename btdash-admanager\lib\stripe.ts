import { loadStripe, Stripe } from '@stripe/stripe-js';

// Client-side Stripe instance
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    
    if (!publishableKey) {
      throw new Error('Missing Stripe publishable key');
    }
    
    stripePromise = loadStripe(publishableKey);
  }
  
  return stripePromise;
};

// Server-side Stripe configuration
export const stripeConfig = {
  secretKey: process.env.STRIPE_SECRET_KEY,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  currency: process.env.STRIPE_CURRENCY || 'usd',
  country: process.env.STRIPE_COUNTRY || 'US',
};

// Validate server-side Stripe configuration
export const validateStripeConfig = () => {
  if (!stripeConfig.secretKey) {
    throw new Error('Missing Stripe secret key');
  }
  
  if (!stripeConfig.webhookSecret) {
    throw new Error('Missing Stripe webhook secret');
  }
  
  return true;
};
