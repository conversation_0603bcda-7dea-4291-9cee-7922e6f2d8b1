"use client"

import * as React from "react"
import { Check, ChevronsUpDown, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// List of countries with their codes
export const countries = [
  { value: "US", label: "United States" },
  { value: "CA", label: "Canada" },
  { value: "UK", label: "United Kingdom" },
  { value: "AU", label: "Australia" },
  { value: "DE", label: "Germany" },
  { value: "FR", label: "France" },
  { value: "JP", label: "Japan" },
  { value: "CN", label: "China" },
  { value: "IN", label: "India" },
  { value: "BR", label: "Brazil" },
  { value: "MX", label: "Mexico" },
  { value: "IT", label: "Italy" },
  { value: "ES", label: "Spain" },
  { value: "RU", label: "Russia" },
  { value: "ZA", label: "South Africa" },
  { value: "AR", label: "Argentina" },
  { value: "NL", label: "Netherlands" },
  { value: "SE", label: "Sweden" },
  { value: "NO", label: "Norway" },
  { value: "SG", label: "Singapore" },
]

interface CountrySelectorProps {
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
}

export function CountrySelector({ selected, onChange, placeholder = "Select countries..." }: CountrySelectorProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (country: string) => {
    if (selected.includes(country)) {
      onChange(selected.filter((item) => item !== country))
    } else {
      onChange([...selected, country])
    }
  }

  const handleRemove = (country: string) => {
    onChange(selected.filter((item) => item !== country))
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
            {selected.length > 0 ? `${selected.length} selected` : placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <Command>
            <CommandInput placeholder="Search countries..." />
            <CommandList>
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup className="max-h-[300px] overflow-auto">
                {countries.map((country) => (
                  <CommandItem key={country.value} value={country.value} onSelect={() => handleSelect(country.value)}>
                    <Check
                      className={cn("mr-2 h-4 w-4", selected.includes(country.value) ? "opacity-100" : "opacity-0")}
                    />
                    {country.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selected.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selected.map((value) => {
            const country = countries.find((c) => c.value === value)
            return (
              <Badge key={value} variant="secondary" className="flex items-center gap-1">
                {country?.label}
                <button
                  type="button"
                  className="rounded-full outline-none focus:ring-2 focus:ring-ring"
                  onClick={() => handleRemove(value)}
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">Remove {country?.label}</span>
                </button>
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}
