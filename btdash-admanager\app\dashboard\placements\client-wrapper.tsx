// app/dashboard/placements/client-wrapper.tsx
"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import type { AdSlot } from "@/lib/db/models";
import Image from "next/image";
import Link from "next/link";

export default function PlacementsClientWrapper({ placements }: { placements: AdSlot[] }) {
	const homePlacements = placements.filter((p) => p.page === "home");
	const newsletterPlacements = placements.filter((p) => p.page === "newsletter");
	const subnetsPlacements = placements.filter((p) => p.page === "subnets");
	const companiesPlacements = placements.filter((p) => p.page === "companies");

	const renderPlacementCard = (placement: AdSlot) => (
		<Card key={placement.id}>
			<CardHeader className="pb-4">
				<div className="flex items-center justify-between">
					<Badge>
						{placement.page === "home"
							? "home"
							: placement.page === "subnets"
							? "Subnets"
							: placement.page === "companies"
							? "Companies"
							: "Newsletter"}
					</Badge>
					<Badge variant="outline">
						{placement.width}x{placement.height}
					</Badge>
				</div>
				<CardTitle className="mt-2">{placement.name}</CardTitle>
				<CardDescription>{placement.description}</CardDescription>
			</CardHeader>
			<CardContent className="pb-2">
				<div className="aspect-video overflow-hidden rounded-lg bg-muted">
					<Image
						src={`/placeholder.svg?text=${placement.width}x${placement.height}`}
						alt={placement.name}
						width={placement.width}
						height={placement.height}
						className="object-contain w-full h-full"
					/>
				</div>
				<div className="grid grid-cols-2 gap-4 mt-4">
					<div>
						<p className="text-sm font-medium">Dimensions</p>
						<p className="text-sm text-muted-foreground">
							{placement.width}x{placement.height}px
						</p>
					</div>
					<div className="justify-self-end text-right">
						<p className="text-sm font-medium">Format</p>
						<p className="text-sm text-muted-foreground">
							{placement.allowed_ad_types?.join(", ") || "Image/HTML"}
						</p>
					</div>
				</div>
			</CardContent>
			<CardFooter>
				<Link href={`/dashboard/ads/create?placement=${placement.id}`} className="w-full">
					<Button className="w-full">Select Placement</Button>
				</Link>
			</CardFooter>
		</Card>
	);

	return (
		<Tabs defaultValue="all" className="space-y-4">
			<TabsList>
				<TabsTrigger value="all">All</TabsTrigger>
				<TabsTrigger value="home">Home</TabsTrigger>
				<TabsTrigger value="subnets">Subnets</TabsTrigger>
				<TabsTrigger value="companies">Companies</TabsTrigger>
			</TabsList>

			<TabsContent value="all" className="space-y-4">
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">{placements.map(renderPlacementCard)}</div>
			</TabsContent>

			<TabsContent value="home" className="space-y-4">
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
					{homePlacements.map(renderPlacementCard)}
				</div>
			</TabsContent>

			<TabsContent value="subnets" className="space-y-4">
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
					{subnetsPlacements.map(renderPlacementCard)}
				</div>
			</TabsContent>

			<TabsContent value="companies" className="space-y-4">
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
					{companiesPlacements.map(renderPlacementCard)}
				</div>
			</TabsContent>
		</Tabs>
	);
}
