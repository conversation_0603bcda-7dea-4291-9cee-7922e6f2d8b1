// components/Jobs/popular-categories.tsx
"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Category, Company, Job, Product, Subnet } from "@/lib/db/models";

interface PopularCategoriesProps {
	jobs: (Job & {
		company?: Company;
		categories?: Category[];
		subnets?: Subnet[];
		products?: Product[];
	})[];
	title?: string;
	maxCategories?: number;
}

export function PopularCategories({
	jobs,
	title = "Popular Job Categories",
	maxCategories = 5,
}: PopularCategoriesProps) {
	// Count jobs per category
	const categoryCounts = jobs.reduce((acc, job) => {
		if (job.categories) {
			job.categories.forEach((category) => {
				acc[category.id] = {
					count: (acc[category.id]?.count || 0) + 1,
					name: category.name,
				};
			});
		}
		return acc;
	}, {} as Record<number, { count: number; name: string }>);

	// Sort categories by job count and take top ones
	const popularCategories = Object.entries(categoryCounts)
		.sort((a, b) => b[1].count - a[1].count)
		.slice(0, maxCategories);

	// If no categories found
	if (popularCategories.length === 0) {
		return (
			<Card className="mb-6">
				<CardContent className="p-4">
					<h3 className="font-medium mb-3">{title}</h3>
					<div className="space-y-2 text-sm">No categories found.</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="mb-6">
			<CardContent className="p-4">
				<h3 className="font-medium mb-3">{title}</h3>
				<div className="space-y-2 text-sm">
					{popularCategories.map(([categoryId, { count, name }]) => (
						<div key={categoryId} className="flex justify-between">
							<span>{name}</span>
							<span className="text-muted-foreground">{count} jobs</span>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
