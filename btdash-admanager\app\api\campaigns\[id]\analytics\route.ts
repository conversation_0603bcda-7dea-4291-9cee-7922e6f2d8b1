// app/api/campaigns/[id]/analytics/route.ts
import { auth0 } from "@/lib/auth0";
import { NextResponse } from "next/server";

export async function GET(req: Request, { params }: { params: { id: string } }) {
	try {
		const { token } = await auth0.getAccessToken();
		const { searchParams } = new URL(req.url);
		
		const startDate = searchParams.get('start_date');
		const endDate = searchParams.get('end_date');

		const urlParams = new URLSearchParams();
		if (startDate) urlParams.append('start_date', startDate);
		if (endDate) urlParams.append('end_date', endDate);

		const queryString = urlParams.toString();
		const url = `${process.env.API_BASE_URL}/campaigns/${params.id}/analytics${queryString ? `?${queryString}` : ''}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error("API Error:", response.status, errorText);
			return NextResponse.json(
				{
					success: false,
					message: `API Error: ${response.status} - ${errorText}`,
				},
				{ status: response.status }
			);
		}

		const result = await response.json();

		// Handle standardized response format
		if (result.success) {
			return NextResponse.json({ success: true, data: result.data, message: result.message });
		} else {
			return NextResponse.json(
				{ success: false, message: result.message, errors: result.errors },
				{ status: 400 }
			);
		}
	} catch (error: any) {
		console.error("Campaign analytics fetch error:", error);

		return NextResponse.json(
			{
				success: false,
				message: error.message || "Internal server error",
				error: error.code || "unknown_error",
			},
			{ status: 500 }
		);
	}
}
