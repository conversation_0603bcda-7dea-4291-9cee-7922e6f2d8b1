"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, Calendar, Check, Loader2, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import type { Campaign } from "@/types";

export default function AdRequestReviewPage({ params }: { params: Promise<{ id: string }> }) {
	const { id } = use(params) as { id: string };
	const router = useRouter();
	const { toast } = useToast();
	const [campaign, setCampaign] = useState<Campaign | null>(null);
	const [placement, setPlacement] = useState<any>(null);
	const [advertiser, setAdvertiser] = useState<any>(null);
	const [manager, setManager] = useState<any>(null);
	const [loading, setLoading] = useState(true);
	const [feedback, setFeedback] = useState("");
	const [selectedReason, setSelectedReason] = useState("");
	const [rejectionReasons, setRejectionReasons] = useState<Array<{ code: string; description: string }>>([]);
	const [submitting, setSubmitting] = useState(false);
	const [overlappingCampaigns, setOverlappingCampaigns] = useState<Campaign[]>([]);

	useEffect(() => {
		const fetchCampaignDetails = async () => {
			try {
				setLoading(true);

				// Fetch campaign details from API
				const response = await fetch(`/api/user/campaigns/${id}`);
				const result = await response.json();

				if (!result.success || !result.data) {
					toast({
						title: "Campaign not found",
						description: "The requested campaign could not be found.",
						variant: "destructive",
					});
					router.push("/admin/requests");
					return;
				}

				const campaignData = result.data;
				setCampaign(campaignData);

				// For now, we'll skip overlapping campaigns check since it requires complex logic
				// This can be implemented later with a dedicated admin endpoint
				setOverlappingCampaigns([]);

				// Fetch placement data if available
				if (campaignData.slot_id) {
					try {
						const placementResponse = await fetch(`/api/placements/${campaignData.slot_id}`);
						const placementResult = await placementResponse.json();
						if (placementResult.success) {
							setPlacement(placementResult.data);
						}
					} catch (error) {
						console.log("Could not fetch placement data:", error);
					}
				}

				// Set advertiser info from campaign data (this is the company)
				if (campaignData.advertiser_name) {
					setAdvertiser({
						id: campaignData.advertiser_id,
						name: campaignData.advertiser_name,
						email: campaignData.advertiser_email || "",
						role: "company", // This is actually a company, not a user
					});
				}

				// Fetch rejection reasons
				try {
					const reasonsResponse = await fetch("/api/admin/rejection-reasons?entity_type=campaign");
					const reasonsResult = await reasonsResponse.json();
					if (reasonsResult.success) {
						setRejectionReasons(reasonsResult.data);
					}
				} catch (error) {
					console.log("Could not fetch rejection reasons:", error);
				}

				// Set manager info from campaign data (this is the user managing the campaign)
				if (campaignData.manager_name) {
					setManager({
						id: campaignData.manager_id,
						name: campaignData.manager_name,
						email: campaignData.manager_email || "",
						role: "user", // This is the actual user
					});
				}
			} catch (error) {
				console.error("Error fetching campaign details:", error);
				toast({
					title: "Error",
					description: "Failed to load campaign details. Please try again.",
					variant: "destructive",
				});
			} finally {
				setLoading(false);
			}
		};

		fetchCampaignDetails();
	}, [id, router, toast]);

	const handleApprove = async () => {
		try {
			setSubmitting(true);

			const response = await fetch(`/api/admin/campaigns/${id}/approve`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					notes: feedback || "Campaign approved",
				}),
			});

			const result = await response.json();

			if (!result.success) {
				throw new Error(result.message || "Failed to approve campaign");
			}

			toast({
				title: "Campaign approved",
				description: "The ad campaign has been approved and is now active.",
			});

			router.push("/admin/requests");
		} catch (error: any) {
			console.error("Error approving campaign:", error);
			toast({
				title: "Error",
				description: error.message || "Failed to approve the campaign. Please try again.",
				variant: "destructive",
			});
		} finally {
			setSubmitting(false);
		}
	};

	const handleReject = async () => {
		if (!selectedReason) {
			toast({
				title: "Rejection reason required",
				description: "Please select a rejection reason before rejecting the campaign.",
				variant: "destructive",
			});
			return;
		}

		try {
			setSubmitting(true);

			const response = await fetch(`/api/admin/campaigns/${id}/reject`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					reason: selectedReason,
					notes: feedback.trim() || null,
				}),
			});

			const result = await response.json();

			if (!result.success) {
				throw new Error(result.message || "Failed to reject campaign");
			}

			toast({
				title: "Campaign rejected",
				description: "The ad campaign has been rejected with feedback.",
			});

			router.push("/admin/requests");
		} catch (error: any) {
			console.error("Error rejecting campaign:", error);
			toast({
				title: "Error",
				description: error.message || "Failed to reject the campaign. Please try again.",
				variant: "destructive",
			});
		} finally {
			setSubmitting(false);
		}
	};

	// Format date for display
	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	if (loading) {
		return (
			<div className="container mx-auto flex h-[70vh] items-center justify-center p-6">
				<div className="flex flex-col items-center">
					<Loader2 className="h-12 w-12 animate-spin text-primary" />
					<p className="mt-4 text-lg">Loading campaign details...</p>
				</div>
			</div>
		);
	}

	if (!campaign) {
		return (
			<div className="container mx-auto p-6">
				<div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
					<h3 className="text-lg font-medium">Campaign not found</h3>
					<p className="text-sm text-muted-foreground">The requested campaign could not be found.</p>
					<Button variant="outline" className="mt-4" onClick={() => router.push("/admin/requests")}>
						<ArrowLeft className="mr-2 h-4 w-4" />
						Back to Requests
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6">
			<div className="mb-6">
				<Button variant="outline" className="mb-4" onClick={() => router.push("/admin/requests")}>
					<ArrowLeft className="mr-2 h-4 w-4" />
					Back to Requests
				</Button>
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">{campaign.name}</h1>
						<div className="mt-1 flex items-center gap-2">
							<Badge variant="outline">{placement?.type || "Unknown"}</Badge>
							<Badge variant="outline" className="capitalize">
								{campaign.status}
							</Badge>
						</div>
					</div>
				</div>
			</div>

			{/* Show overlapping campaigns alert if any exist */}
			{overlappingCampaigns.length > 0 && (
				<Alert variant="destructive" className="mb-6">
					<AlertCircle className="h-4 w-4" />
					<AlertTitle>Scheduling Conflict Detected</AlertTitle>
					<AlertDescription>
						This campaign overlaps with {overlappingCampaigns.length} existing approved campaign
						{overlappingCampaigns.length > 1 ? "s" : ""} on the same placement.
					</AlertDescription>
				</Alert>
			)}

			<div className="grid gap-6 lg:grid-cols-3">
				<div className="lg:col-span-2">
					<Tabs defaultValue="overview" className="w-full">
						<TabsList className="mb-4">
							<TabsTrigger value="overview">Overview</TabsTrigger>
							<TabsTrigger value="creative">Creative</TabsTrigger>
							<TabsTrigger value="targeting">Targeting</TabsTrigger>
							{overlappingCampaigns.length > 0 && (
								<TabsTrigger value="conflicts" className="relative">
									Conflicts
									<span className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-white">
										{overlappingCampaigns.length}
									</span>
								</TabsTrigger>
							)}
						</TabsList>

						<TabsContent value="overview">
							<Card>
								<CardHeader>
									<CardTitle>Campaign Overview</CardTitle>
									<CardDescription>Review the campaign details and settings</CardDescription>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="grid gap-4 md:grid-cols-2">
										<div>
											<h3 className="mb-2 font-medium">Campaign Details</h3>
											<div className="space-y-2 rounded-lg border p-3">
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">Name:</span>
													<span className="col-span-2 text-sm font-medium">
														{campaign.name}
													</span>
												</div>
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">URL:</span>
													<span className="col-span-2 text-sm font-medium">
														{campaign.url}
													</span>
												</div>
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">Duration:</span>
													<span className="col-span-2 text-sm font-medium">
														{formatDate(campaign.startDate)} -{" "}
														{formatDate(campaign.endDate)}
													</span>
												</div>
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">Submitted:</span>
													<span className="col-span-2 text-sm font-medium">
														{formatDate(campaign.createdAt)}
													</span>
												</div>
											</div>
										</div>

										<div>
											<h3 className="mb-2 font-medium">Placement Information</h3>
											<div className="space-y-2 rounded-lg border p-3">
												{placement ? (
													<>
														<div className="grid grid-cols-3 gap-1">
															<span className="text-sm text-muted-foreground">Name:</span>
															<span className="col-span-2 text-sm font-medium">
																{placement.name}
															</span>
														</div>
														<div className="grid grid-cols-3 gap-1">
															<span className="text-sm text-muted-foreground">Type:</span>
															<span className="col-span-2 text-sm font-medium">
																{placement.type}
															</span>
														</div>
														<div className="grid grid-cols-3 gap-1">
															<span className="text-sm text-muted-foreground">
																Dimensions:
															</span>
															<span className="col-span-2 text-sm font-medium">
																{placement.dimensions}
															</span>
														</div>
														<div className="grid grid-cols-3 gap-1">
															<span className="text-sm text-muted-foreground">
																Price:
															</span>
															<span className="col-span-2 text-sm font-medium">
																{placement.priceDisplay}
															</span>
														</div>
													</>
												) : (
													<p className="text-sm text-muted-foreground">
														Placement information not available
													</p>
												)}
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						</TabsContent>

						<TabsContent value="creative">
							<Card>
								<CardHeader>
									<CardTitle>Ad Creative</CardTitle>
									<CardDescription>Review the advertisement creative and content</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										<div className="overflow-hidden rounded-lg border">
											<div className="aspect-video w-full overflow-hidden bg-muted">
												<img
													src={campaign.imageUrl || "/placeholder.svg"}
													alt={campaign.name}
													className="h-full w-full object-contain"
												/>
											</div>
											<div className="p-4">
												<h3 className="font-medium">{campaign.name}</h3>
												<p className="text-sm text-muted-foreground">{campaign.url}</p>
											</div>
										</div>

										<div>
											<h3 className="mb-2 font-medium">Creative Specifications</h3>
											<div className="space-y-2 rounded-lg border p-3">
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">Format:</span>
													<span className="col-span-2 text-sm font-medium">
														{placement?.format || "Unknown"}
													</span>
												</div>
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">Dimensions:</span>
													<span className="col-span-2 text-sm font-medium">
														{placement?.dimensions || "Unknown"}
													</span>
												</div>
												<div className="grid grid-cols-3 gap-1">
													<span className="text-sm text-muted-foreground">
														Max File Size:
													</span>
													<span className="col-span-2 text-sm font-medium">
														{placement?.maxFileSize || "Unknown"}
													</span>
												</div>
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						</TabsContent>

						<TabsContent value="targeting">
							<Card>
								<CardHeader>
									<CardTitle>Targeting Settings</CardTitle>
									<CardDescription>Review the campaign targeting configuration</CardDescription>
								</CardHeader>
								<CardContent className="space-y-4">
									<div>
										<h3 className="mb-2 font-medium">Geographic Targeting</h3>
										<div className="rounded-lg border p-3">
											{campaign.targeting?.countries ? (
												<>
													<div className="mb-2">
														<span className="text-sm font-medium">Mode: </span>
														<Badge variant="outline" className="capitalize">
															{campaign.targeting.countries.mode}
														</Badge>
													</div>

													{campaign.targeting.countries.mode === "include" && (
														<div>
															<span className="text-sm text-muted-foreground">
																Included Countries:
															</span>
															<div className="mt-1 flex flex-wrap gap-1">
																{campaign.targeting.countries.include?.length > 0 ? (
																	campaign.targeting.countries.include.map(
																		(country) => (
																			<Badge key={country} variant="secondary">
																				{country}
																			</Badge>
																		)
																	)
																) : (
																	<span className="text-sm italic text-muted-foreground">
																		No countries specified
																	</span>
																)}
															</div>
														</div>
													)}

													{campaign.targeting.countries.mode === "exclude" && (
														<div>
															<span className="text-sm text-muted-foreground">
																Excluded Countries:
															</span>
															<div className="mt-1 flex flex-wrap gap-1">
																{campaign.targeting.countries.exclude?.length > 0 ? (
																	campaign.targeting.countries.exclude.map(
																		(country) => (
																			<Badge key={country} variant="secondary">
																				{country}
																			</Badge>
																		)
																	)
																) : (
																	<span className="text-sm italic text-muted-foreground">
																		No countries specified
																	</span>
																)}
															</div>
														</div>
													)}

													{campaign.targeting.countries.mode === "all" && (
														<span className="text-sm text-muted-foreground">
															Targeting all countries
														</span>
													)}
												</>
											) : (
												<span className="text-sm text-muted-foreground">
													No geographic targeting configured
												</span>
											)}
										</div>
									</div>

									<div>
										<h3 className="mb-2 font-medium">Page Type Targeting</h3>
										<div className="rounded-lg border p-3">
											{campaign.targeting?.pageTypes ? (
												<>
													<div className="mb-2">
														<span className="text-sm text-muted-foreground">
															Selected Page Types:
														</span>
														<div className="mt-1 flex flex-wrap gap-1">
															{campaign.targeting.pageTypes.types?.map((type) => (
																<Badge key={type} variant="secondary">
																	{type}
																</Badge>
															)) || (
																<span className="text-sm italic text-muted-foreground">
																	No page types specified
																</span>
															)}
														</div>
													</div>

													{campaign.targeting.pageTypes.categories && (
														<div className="mt-3">
															<span className="text-sm text-muted-foreground">
																Category Targeting:
															</span>
															<div className="mt-2 space-y-2">
																{Object.entries(
																	campaign.targeting.pageTypes.categories
																).map(([pageType, categories]) => (
																	<div key={pageType} className="rounded border p-2">
																		<span className="text-sm font-medium capitalize">
																			{pageType}:
																		</span>
																		<div className="mt-1 flex flex-wrap gap-1">
																			{categories === "all" ? (
																				<Badge variant="outline">
																					All Categories
																				</Badge>
																			) : (
																				(categories as string[]).map(
																					(category) => (
																						<Badge
																							key={category}
																							variant="secondary"
																						>
																							{category}
																						</Badge>
																					)
																				)
																			)}
																		</div>
																	</div>
																))}
															</div>
														</div>
													)}
												</>
											) : (
												<span className="text-sm text-muted-foreground">
													No page type targeting configured
												</span>
											)}
										</div>
									</div>

									<div>
										<h3 className="mb-2 font-medium">Device Targeting</h3>
										<div className="rounded-lg border p-3">
											{campaign.targeting?.devices && campaign.targeting.devices.length > 0 ? (
												<div className="flex flex-wrap gap-1">
													{campaign.targeting.devices.map((device) => (
														<Badge key={device} variant="secondary">
															{device}
														</Badge>
													))}
												</div>
											) : (
												<span className="text-sm text-muted-foreground">
													No device targeting configured
												</span>
											)}
										</div>
									</div>

									<div>
										<h3 className="mb-2 font-medium">Language Targeting</h3>
										<div className="rounded-lg border p-3">
											{campaign.targeting?.languages &&
											campaign.targeting.languages.length > 0 ? (
												<div className="flex flex-wrap gap-1">
													{campaign.targeting.languages.map((language) => (
														<Badge key={language} variant="secondary">
															{language.toUpperCase()}
														</Badge>
													))}
												</div>
											) : (
												<span className="text-sm text-muted-foreground">
													No language targeting configured
												</span>
											)}
										</div>
									</div>

									<div>
										<h3 className="mb-2 font-medium">Interest Targeting</h3>
										<div className="rounded-lg border p-3">
											{campaign.targeting?.interests &&
											campaign.targeting.interests.length > 0 ? (
												<div className="flex flex-wrap gap-1">
													{campaign.targeting.interests.map((interest) => (
														<Badge
															key={interest}
															variant="secondary"
															className="capitalize"
														>
															{interest}
														</Badge>
													))}
												</div>
											) : (
												<span className="text-sm text-muted-foreground">
													No interest targeting configured
												</span>
											)}
										</div>
									</div>

									<div>
										<h3 className="mb-2 font-medium">Age Targeting</h3>
										<div className="rounded-lg border p-3">
											{campaign.targeting?.age && campaign.targeting.age.length > 0 ? (
												<div className="flex flex-wrap gap-1">
													{campaign.targeting.age.map((ageRange) => (
														<Badge key={ageRange} variant="secondary">
															{ageRange}
														</Badge>
													))}
												</div>
											) : (
												<span className="text-sm text-muted-foreground">
													No age targeting configured
												</span>
											)}
										</div>
									</div>
								</CardContent>
							</Card>
						</TabsContent>

						{/* New tab for conflicts */}
						<TabsContent value="conflicts">
							<Card>
								<CardHeader>
									<CardTitle>Scheduling Conflicts</CardTitle>
									<CardDescription>
										This campaign overlaps with {overlappingCampaigns.length} existing approved
										campaign
										{overlappingCampaigns.length > 1 ? "s" : ""} on the same placement
									</CardDescription>
								</CardHeader>
								<CardContent>
									<div className="space-y-4">
										{overlappingCampaigns.map((overlapCampaign) => (
											<div key={overlapCampaign.id} className="rounded-lg border p-4">
												<div className="mb-2 flex items-center justify-between">
													<h3 className="font-medium">{overlapCampaign.name}</h3>
													<Badge variant="outline" className="capitalize">
														{overlapCampaign.status}
													</Badge>
												</div>
												<div className="mb-3 flex items-center gap-2 text-sm text-muted-foreground">
													<Calendar className="h-4 w-4" />
													<span>
														{formatDate(overlapCampaign.startDate)} -{" "}
														{formatDate(overlapCampaign.endDate)}
													</span>
												</div>
												<div className="grid grid-cols-2 gap-4">
													<div className="space-y-1">
														<p className="text-xs font-medium">Placement</p>
														<p className="text-sm">{placement?.name || "Unknown"}</p>
													</div>
													<div className="space-y-1">
														<p className="text-xs font-medium">Advertiser</p>
														<p className="text-sm">{advertiser?.name || "Unknown"}</p>
													</div>
												</div>
											</div>
										))}

										<div className="mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950">
											<h3 className="mb-2 font-medium">Conflict Resolution Options</h3>
											<ul className="ml-5 list-disc space-y-1 text-sm">
												<li>Adjust the campaign dates to avoid overlap</li>
												<li>Reject this campaign with feedback about the conflict</li>
												<li>Approve anyway (multiple ads will rotate in the same placement)</li>
												<li>Cancel one of the existing campaigns to make room</li>
											</ul>
										</div>
									</div>
								</CardContent>
							</Card>
						</TabsContent>
					</Tabs>
				</div>

				<div>
					<Card>
						<CardHeader>
							<CardTitle>Advertiser Information (Company)</CardTitle>
						</CardHeader>
						<CardContent>
							{advertiser ? (
								<div className="space-y-4">
									<div className="flex items-center gap-3">
										<div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
											{advertiser.name
												.split(" ")
												.map((n: string) => n[0])
												.join("")}
										</div>
										<div>
											<p className="font-medium">{advertiser.name}</p>
											<p className="text-sm text-muted-foreground">{advertiser.email}</p>
										</div>
									</div>
									<div className="rounded-lg border p-3">
										<div className="mb-2 text-sm font-medium">Company Details</div>
										<div className="grid grid-cols-3 gap-1">
											<span className="text-sm text-muted-foreground">ID:</span>
											<span className="col-span-2 text-sm">{advertiser.id}</span>
										</div>
										<div className="grid grid-cols-3 gap-1">
											<span className="text-sm text-muted-foreground">Type:</span>
											<span className="col-span-2 text-sm capitalize">{advertiser.role}</span>
										</div>
									</div>
								</div>
							) : (
								<p className="text-sm text-muted-foreground">Advertiser information not available</p>
							)}
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle>Campaign Manager (User)</CardTitle>
						</CardHeader>
						<CardContent>
							{manager ? (
								<div className="space-y-4">
									<div className="flex items-center gap-3">
										<div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-600">
											{manager.name
												.split(" ")
												.map((n: string) => n[0])
												.join("")}
										</div>
										<div>
											<p className="font-medium">{manager.name}</p>
											<p className="text-sm text-muted-foreground">{manager.email}</p>
										</div>
									</div>
									<div className="rounded-lg border p-3">
										<div className="mb-2 text-sm font-medium">User Details</div>
										<div className="grid grid-cols-3 gap-1">
											<span className="text-sm text-muted-foreground">ID:</span>
											<span className="col-span-2 text-sm">{manager.id}</span>
										</div>
										<div className="grid grid-cols-3 gap-1">
											<span className="text-sm text-muted-foreground">Role:</span>
											<span className="col-span-2 text-sm capitalize">{manager.role}</span>
										</div>
									</div>
								</div>
							) : (
								<p className="text-sm text-muted-foreground">Manager information not available</p>
							)}
						</CardContent>
					</Card>

					{/* Show scheduling information with conflict warning */}
					<Card className="mt-6">
						<CardHeader>
							<CardTitle>Campaign Schedule</CardTitle>
							<CardDescription>Review the campaign timeline</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div className="rounded-lg border p-3">
									<div className="mb-2 flex items-center gap-2">
										<Calendar className="h-4 w-4 text-muted-foreground" />
										<span className="font-medium">Campaign Duration</span>
									</div>
									<div className="grid grid-cols-3 gap-1">
										<span className="text-sm text-muted-foreground">Start Date:</span>
										<span className="col-span-2 text-sm">{formatDate(campaign.startDate)}</span>
									</div>
									<div className="grid grid-cols-3 gap-1">
										<span className="text-sm text-muted-foreground">End Date:</span>
										<span className="col-span-2 text-sm">{formatDate(campaign.endDate)}</span>
									</div>
									<div className="grid grid-cols-3 gap-1">
										<span className="text-sm text-muted-foreground">Duration:</span>
										<span className="col-span-2 text-sm">
											{Math.ceil(
												(new Date(campaign.endDate).getTime() -
													new Date(campaign.startDate).getTime()) /
													(1000 * 60 * 60 * 24)
											)}{" "}
											days
										</span>
									</div>
								</div>

								{overlappingCampaigns.length > 0 && (
									<div className="rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950">
										<div className="mb-2 flex items-center gap-2">
											<AlertCircle className="h-4 w-4 text-red-500" />
											<span className="font-medium text-red-600 dark:text-red-400">
												Scheduling Conflict
											</span>
										</div>
										<p className="text-sm text-red-600 dark:text-red-400">
											This campaign overlaps with {overlappingCampaigns.length} existing approved
											campaign
											{overlappingCampaigns.length > 1 ? "s" : ""} on the same placement.
										</p>
									</div>
								)}
							</div>
						</CardContent>
					</Card>

					<Card className="mt-6">
						<CardHeader>
							<CardTitle>Review Decision</CardTitle>
							<CardDescription>Approve or reject this ad campaign request</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								<div>
									<label htmlFor="rejection-reason" className="mb-2 block text-sm font-medium">
										Rejection Reason
									</label>
									<Select value={selectedReason} onValueChange={setSelectedReason}>
										<SelectTrigger>
											<SelectValue placeholder="Select a rejection reason..." />
										</SelectTrigger>
										<SelectContent>
											{rejectionReasons.map((reason) => (
												<SelectItem key={reason.code} value={reason.code}>
													{reason.description}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<p className="mt-1 text-xs text-muted-foreground">Required for rejection.</p>
								</div>
								<div>
									<label htmlFor="feedback" className="mb-2 block text-sm font-medium">
										Additional Notes (Optional)
									</label>
									<Textarea
										id="feedback"
										placeholder="Provide additional feedback to the advertiser..."
										value={feedback}
										onChange={(e) => setFeedback(e.target.value)}
										className="min-h-[100px]"
									/>
									<p className="mt-1 text-xs text-muted-foreground">
										Optional additional notes for the advertiser.
									</p>
								</div>
							</div>
						</CardContent>
						<CardFooter className="flex justify-between">
							<Button
								variant="destructive"
								onClick={handleReject}
								disabled={submitting || !selectedReason}
							>
								<X className="mr-2 h-4 w-4" />
								{submitting ? "Rejecting..." : "Reject"}
							</Button>
							<Button variant="default" onClick={handleApprove} disabled={submitting}>
								<Check className="mr-2 h-4 w-4" />
								{submitting ? "Approving..." : "Approve"}
							</Button>
						</CardFooter>
					</Card>
				</div>
			</div>
		</div>
	);
}
