const CategoriesService = require("../../application/services/CategoriesService");
const logger = require("../../../logger");
const { sendSuccess, sendError, sendNotFound, sendInternalError } = require("../../utils/responseWrapper");
const { asyncHandler } = require("../../middleware/errorHandler");

// Get all categories sorted by id
const getAllCategories = asyncHandler(async (req, res) => {
	// Fetch categories and order them by id in ascending order
	const categories = await CategoriesService.getAllCategories({ active: true });
	return sendSuccess(res, categories, "Categories retrieved successfully");
});

// Get a category by ID
const getCategoryById = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const category = await CategoriesService.getCategoryById(id);
	if (!category || !category.active) {
		return sendNotFound(res, "Category not found");
	}
	return sendSuccess(res, category, "Category retrieved successfully");
});

// Create a new category
const createCategory = asyncHandler(async (req, res) => {
	const { category_name, description } = req.body;
	const newCategory = await CategoriesService.createCategory({ category_name, description });
	return sendSuccess(res, newCategory, "Category created successfully", 201);
});

// Update a category
const updateCategory = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const { category_name, description } = req.body;
	const updatedCategory = await CategoriesService.updateCategory(id, { category_name, description });
	if (!updatedCategory) {
		return sendNotFound(res, "Category not found");
	}
	return sendSuccess(res, updatedCategory, "Category updated successfully");
});

// Delete a category
const deleteCategory = asyncHandler(async (req, res) => {
	const { id } = req.params;
	const deleted = await CategoriesService.deleteCategory(id);
	if (!deleted) {
		return sendNotFound(res, "Category not found");
	}
	return sendSuccess(res, null, "Category deleted successfully");
});

module.exports = {
	getAllCategories,
	getCategoryById,
	createCategory,
	updateCategory,
	deleteCategory,
};
