/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/serve/route";
exports.ids = ["app/api/ads/serve/route"];
exports.modules = {

/***/ "(rsc)/./app/api/ads/serve/route.ts":
/*!************************************!*\
  !*** ./app/api/ads/serve/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(rsc)/./lib/api/ad-serving.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\nasync function POST(request) {\n    try {\n        // Validate request body\n        let body;\n        try {\n            body = await request.json();\n        } catch (parseError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        const { variant, country_code, device_type, language, user_agent } = body;\n        // Validate required fields\n        if (!variant) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Missing required field: variant\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate variant\n        if (!(variant in _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__.AD_SLOT_MAPPING)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `Invalid ad variant: ${variant}. Valid variants: ${Object.keys(_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__.AD_SLOT_MAPPING).join(\", \")}`\n            }, {\n                status: 400\n            });\n        }\n        // Validate environment configuration\n        if (!process.env.API_BASE_URL && !process.env.APP_BASE_URL) {\n            console.error(\"Missing API_BASE_URL or APP_BASE_URL environment variable\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        if (!process.env.INTERNAL_API_KEY) {\n            console.error(\"Missing INTERNAL_API_KEY environment variable\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch paid ad from backend with timeout\n        const paidAd = await Promise.race([\n            (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__.fetchPaidAd)(variant, {\n                country_code,\n                device_type,\n                language,\n                user_agent\n            }),\n            new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 10000))\n        ]);\n        if (paidAd) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: paidAd,\n                message: \"Paid ad served successfully\"\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"No paid ads available\"\n            }, {\n                status: 404\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in ad serving route:\", error);\n        // Provide more specific error messages\n        if (error instanceof Error) {\n            if (error.message.includes(\"timeout\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Request timeout - backend service unavailable\"\n                }, {\n                    status: 504\n                });\n            }\n            if (error.message.includes(\"ECONNREFUSED\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Backend service unavailable\"\n                }, {\n                    status: 503\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ads/serve/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/ad-serving.ts":
/*!*******************************!*\
  !*** ./lib/api/ad-serving.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   fetchPaidAd: () => (/* binding */ fetchPaidAd),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense fallback slots mapped by slot ID\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"7230250579\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.SUBNET_BANNER]: \"8814794983\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"8814794983\"\n};\n/**\n * SERVER-SIDE ONLY\n * Fetch a paid ad from the backend API\n */ async function fetchPaidAd(slotId, userContext) {\n    try {\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return null;\n        }\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...userContext?.country_code && {\n                country_code: userContext.country_code\n            },\n            ...userContext?.device_type && {\n                device_type: userContext.device_type\n            },\n            ...userContext?.language && {\n                language: userContext.language\n            },\n            ...userContext?.user_agent && {\n                user_agent: userContext.user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const response = await fetch(`${API_BASE}/api/serve-ad?${params}`, {\n            headers,\n            cache: \"no-store\"\n        });\n        if (!response.ok) {\n            if (response.status === 404) {\n                // No ads available for this slot\n                return null;\n            }\n            throw new Error(`Ad serving failed: ${response.status}`);\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return result.data;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching paid ad:\", error);\n        return null;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (false) {}\n    return \"en-US\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/ad-serving.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_serve_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ads/serve/route.ts */ \"(rsc)/./app/api/ads/serve/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/serve/route\",\n        pathname: \"/api/ads/serve\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/serve/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\ads\\\\serve\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_serve_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();