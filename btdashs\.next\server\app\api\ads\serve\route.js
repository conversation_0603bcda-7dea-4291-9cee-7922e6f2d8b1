/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/serve/route";
exports.ids = ["app/api/ads/serve/route"];
exports.modules = {

/***/ "(rsc)/./app/api/ads/serve/route.ts":
/*!************************************!*\
  !*** ./app/api/ads/serve/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(rsc)/./lib/api/ad-serving.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\nasync function POST(request) {\n    try {\n        // Validate request body\n        let body;\n        try {\n            body = await request.json();\n        } catch (parseError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        const { slotId, country_code, device_type, language, user_agent } = body;\n        // Validate required fields\n        if (!slotId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Missing required field: slotId\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate slot ID\n        const validSlotIds = Object.values(_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__.AD_SLOTS);\n        if (!validSlotIds.includes(slotId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `Invalid slot ID: ${slotId}. Valid slot IDs: ${validSlotIds.join(\", \")}`\n            }, {\n                status: 400\n            });\n        }\n        // Validate environment configuration\n        if (!process.env.API_BASE_URL && !process.env.APP_BASE_URL) {\n            console.error(\"Missing API_BASE_URL or APP_BASE_URL environment variable\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        if (!process.env.INTERNAL_API_KEY) {\n            console.error(\"Missing INTERNAL_API_KEY environment variable\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch paid ad from backend with timeout\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...country_code && {\n                country_code\n            },\n            ...device_type && {\n                device_type\n            },\n            ...language && {\n                language\n            },\n            ...user_agent && {\n                user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const paidAd = await Promise.race([\n            fetch(`${API_BASE}/serve-ad?${params}`, {\n                headers,\n                cache: \"no-store\"\n            }).then(async (response)=>{\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        return null; // No ads available\n                    }\n                    throw new Error(`Ad serving failed: ${response.status}`);\n                }\n                const data = await response.json();\n                return data.success ? data.data : null;\n            }),\n            new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 10000))\n        ]);\n        if (paidAd) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: paidAd,\n                message: \"Paid ad served successfully\"\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"No paid ads available\"\n            }, {\n                status: 404\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in ad serving route:\", error);\n        // Provide more specific error messages\n        if (error instanceof Error) {\n            if (error.message.includes(\"timeout\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Request timeout - backend service unavailable\"\n                }, {\n                    status: 504\n                });\n            }\n            if (error.message.includes(\"ECONNREFUSED\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Backend service unavailable\"\n                }, {\n                    status: 503\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ads/serve/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/ad-serving.ts":
/*!*******************************!*\
  !*** ./lib/api/ad-serving.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   fetchPaidAd: () => (/* binding */ fetchPaidAd),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense fallback slots mapped by slot ID\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"7230250579\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.SUBNET_BANNER]: \"8814794983\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"7964747747\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"7964747747\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"8814794983\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"7844510005\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"8814794983\"\n};\n/**\n * SERVER-SIDE ONLY\n * Fetch a paid ad from the backend API\n */ async function fetchPaidAd(slotId, userContext) {\n    try {\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return null;\n        }\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...userContext?.country_code && {\n                country_code: userContext.country_code\n            },\n            ...userContext?.device_type && {\n                device_type: userContext.device_type\n            },\n            ...userContext?.language && {\n                language: userContext.language\n            },\n            ...userContext?.user_agent && {\n                user_agent: userContext.user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const response = await fetch(`${API_BASE}/serve-ad?${params}`, {\n            headers,\n            cache: \"no-store\"\n        });\n        if (!response.ok) {\n            if (response.status === 404) {\n                // No ads available for this slot\n                return null;\n            }\n            throw new Error(`Ad serving failed: ${response.status}`);\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return result.data;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching paid ad:\", error);\n        return null;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    // Generate a UUID v4 compatible string\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c == \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (false) {}\n    return \"en-US\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/ad-serving.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_serve_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ads/serve/route.ts */ \"(rsc)/./app/api/ads/serve/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/serve/route\",\n        pathname: \"/api/ads/serve\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/serve/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\ads\\\\serve\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_serve_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhZHMlMkZzZXJ2ZSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYWRzJTJGc2VydmUlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZhZHMlMkZzZXJ2ZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiZW5qaSU1Q0Rlc2t0b3AlNUNHX1BST0clNUNOaWNvbGFzJTVDYnRkYXNoLWVjb3N5c3RlbSU1Q2J0ZGFzaHMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2JlbmppJTVDRGVza3RvcCU1Q0dfUFJPRyU1Q05pY29sYXMlNUNidGRhc2gtZWNvc3lzdGVtJTVDYnRkYXNocyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDcUQ7QUFDbEk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGJlbmppXFxcXERlc2t0b3BcXFxcR19QUk9HXFxcXE5pY29sYXNcXFxcYnRkYXNoLWVjb3N5c3RlbVxcXFxidGRhc2hzXFxcXGFwcFxcXFxhcGlcXFxcYWRzXFxcXHNlcnZlXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hZHMvc2VydmUvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hZHMvc2VydmVcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2Fkcy9zZXJ2ZS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGJlbmppXFxcXERlc2t0b3BcXFxcR19QUk9HXFxcXE5pY29sYXNcXFxcYnRkYXNoLWVjb3N5c3RlbVxcXFxidGRhc2hzXFxcXGFwcFxcXFxhcGlcXFxcYWRzXFxcXHNlcnZlXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();