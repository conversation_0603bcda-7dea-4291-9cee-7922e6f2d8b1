// src/application/services/CompanyService.js

const db = require("../../infrastructure/database/knex");
const logger = require("../../../logger");

/**
 * Company Service - Handles all company-related business logic
 */
class CompanyService {
	/**
	 * Create a new company with user as owner
	 * @param {number} userId - User ID who will be the owner
	 * @param {Object} companyData - Company data
	 * @returns {Promise<Object>} Created company object
	 */
	async createCompanyWithOwner(userId, companyData) {
		const {
			name,
			description,
			logo_url,
			header_url,
			website_url,
			location,
			foundedyear,
			teamsize,
			social_media
		} = companyData;

		try {
			// Start transaction
			return await db.transaction(async (trx) => {
				// 1. Insert company
				const [newCompany] = await trx("dtm_base.companies")
					.insert({
						name,
						description,
						logo_url,
						header_url,
						website_url,
						location,
						foundedyear,
						teamsize,
						social_media,
						created_at: new Date(),
					})
					.returning("*");

				// 2. Link user to company as owner
				await trx("dtm_base.user_company").insert({
					user_id: userId,
					company_id: newCompany.id,
					role: "owner",
					created_at: new Date(),
				});

				logger.info("Company created with owner", { 
					company_id: newCompany.id, 
					user_id: userId 
				});

				return newCompany;
			});
		} catch (error) {
			logger.error("Error creating company with owner", { error, userId });
			throw new Error(`Failed to create company: ${error.message}`);
		}
	}

	/**
	 * Get user's company
	 * @param {number} userId - User ID
	 * @returns {Promise<Object|null>} Company object with role or null
	 */
	async getUserCompany(userId) {
		try {
			const userCompany = await db("dtm_base.user_company")
				.where({ user_id: userId })
				.orderBy("created_at", "asc")
				.first();

			if (!userCompany) {
				return null;
			}

			const company = await db("dtm_base.companies")
				.where({ id: userCompany.company_id })
				.first();

			if (!company) {
				return null;
			}

			return {
				...company,
				role: userCompany.role
			};
		} catch (error) {
			logger.error("Error getting user company", { error, userId });
			throw new Error(`Failed to get user company: ${error.message}`);
		}
	}

	/**
	 * Update company with authorization check
	 * @param {number} userId - User ID
	 * @param {number} companyId - Company ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated company object
	 */
	async updateCompanyWithAuth(userId, companyId, updateData) {
		try {
			// Check user permissions
			const userCompany = await db("dtm_base.user_company")
				.where({ user_id: userId, company_id: companyId })
				.first();

			if (!userCompany || !["owner", "admin"].includes(userCompany.role)) {
				throw new Error("Forbidden: Insufficient permissions");
			}

			const {
				name,
				description,
				logo_url,
				header_url,
				website_url,
				location,
				foundedyear,
				teamsize,
				social_media
			} = updateData;

			const updatePayload = {
				name,
				description,
				logo_url,
				header_url,
				website_url,
				location,
				foundedyear,
				teamsize,
				social_media,
				updated_at: new Date(),
			};

			// Remove undefined values
			Object.keys(updatePayload).forEach(key => {
				if (updatePayload[key] === undefined) {
					delete updatePayload[key];
				}
			});

			const [updatedCompany] = await db("dtm_base.companies")
				.where({ id: companyId })
				.update(updatePayload)
				.returning("*");

			if (!updatedCompany) {
				throw new Error("Company not found");
			}

			logger.info("Company updated", { company_id: companyId, user_id: userId });
			return updatedCompany;
		} catch (error) {
			logger.error("Error updating company", { error, userId, companyId });
			throw error;
		}
	}

	/**
	 * Delete company with authorization check
	 * @param {number} userId - User ID
	 * @param {number} companyId - Company ID
	 * @returns {Promise<boolean>} Success status
	 */
	async deleteCompanyWithAuth(userId, companyId) {
		try {
			// Check ownership
			const userCompany = await db("dtm_base.user_company")
				.where({ user_id: userId, company_id: companyId })
				.first();

			if (!userCompany || userCompany.role !== "owner") {
				throw new Error("Forbidden: Only owner can delete the company");
			}

			const deleted = await db("dtm_base.companies")
				.where({ id: companyId })
				.del();

			if (!deleted) {
				throw new Error("Company not found");
			}

			logger.info("Company deleted", { company_id: companyId, user_id: userId });
			return true;
		} catch (error) {
			logger.error("Error deleting company", { error, userId, companyId });
			throw error;
		}
	}
}

module.exports = new CompanyService();
