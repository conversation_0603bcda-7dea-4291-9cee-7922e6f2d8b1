"use client";

import { Card, CardContent } from "@/components/ui/card";
import * as am5 from "@amcharts/amcharts5";
import * as am5flow from "@amcharts/amcharts5/flow";
import am5themes_Animated from "@amcharts/amcharts5/themes/Animated";
import am5themes_Responsive from "@amcharts/amcharts5/themes/Responsive";
import { useEffect, useRef } from "react";

interface SubnetRelationshipChartProps {
  subnetId: string;
  data: {
    companyCount?: number;
    productCount?: number;
    eventCount?: number;
    jobCount?: number;
    categoryCount?: number;
    validatorCount?: number;
    newsCount?: number;
    subnetCount?: number; // Optional, if not used
  };
  className?: string;
}

export function SubnetRelationshipChart({
  subnetId,
  data,
  className,
}: SubnetRelationshipChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const rootRef = useRef<am5.Root | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // Dispose of the root if it already exists
    if (rootRef.current) {
      rootRef.current.dispose();
    }

    // Create root and chart
    const root = am5.Root.new(chartRef.current);
    rootRef.current = root;

    root.setThemes([
      am5themes_Animated.new(root),
      am5themes_Responsive.new(root),
    ]);

    const series = root.container.children.push(
      am5flow.ChordDirected.new(root, {
        sourceIdField: "from",
        targetIdField: "to",
        valueField: "value",
        sort: "ascending",
      })
    );

    series.links.template.setAll({
      fillStyle: "source",
      strokeWidth: 0,
      opacity: 0.6,
      tooltipText: "{from} → {to}",
    });

    const colors = series?.nodes?.get("colors");
    if (colors) {
      colors.set("step", 2);
    }

    // Add bullets to links (the animated flow)
    series.bullets.push(function (_root, _series, dataItem) {
      const bullet = am5.Bullet.new(root, {
        locationY: Math.random(),
        sprite: am5.Circle.new(root, {
          radius: 5,
          fill: dataItem.get("source").get("fill"),
        }),
      });

      bullet.animate({
        key: "locationY",
        to: 1,
        from: 0,
        duration: Math.random() * 1000 + 2000,
        loops: Infinity,
      });

      return bullet;
    });

    // Add bullets to nodes
    series.nodes.bullets.push(function (_root, _series, dataItem) {
      return am5.Bullet.new(root, {
        sprite: am5.Circle.new(root, {
          radius: 12,
          fill: dataItem.get("fill"),
          tooltipText: "{name}",
          interactive: true,
          cursorOverStyle: "pointer",
          tooltipY: 7,
          tooltipX: 7,
        }),
      });
    });

    // Move bullets container to the front
    series.children.moveValue(series.bulletsContainer, 0);

    series.nodes.labels.template.setAll({
      textType: "adjusted",
      fill: am5.color(0xffffff),
      fontSize: 11,
      fontWeight: "500",
      /* paddingTop: 5,
      paddingBottom: 5, */
      paddingLeft: 5,
      paddingRight: 5,
    });

    const categoriesId = `Categories\n(${data.categoryCount})`;
    const validatorsId = `Validators (${data.validatorCount})`;
    const companiesId = `Companies\n(${data.companyCount})`;
    const productsId = `Products\n(${data.productCount})`;
    const eventsId = `Events\n(${data.eventCount})`;
    const jobsId = `Jobs\n(${data.jobCount})`;
    const newsId = `News\n(${data.newsCount})`;
    const subnetCount = `Subnet\n(${data.subnetCount})`;

    const nodes = [
      { id: subnetId, name: subnetId },
      ...((data.categoryCount ?? 0) > 0
        ? [{ id: categoriesId, name: "Categories" }]
        : []),
      ...((data.validatorCount ?? 0) > 0
        ? [{ id: validatorsId, name: "Validators" }]
        : []),
      ...((data.companyCount ?? 0) > 0
        ? [{ id: companiesId, name: "Companies" }]
        : []),
      ...((data.productCount ?? 0) > 0
        ? [{ id: productsId, name: "Products" }]
        : []),
      ...(data.subnetCount ? [{ id: subnetCount, name: "Subnets" }] : []),
      ...((data.eventCount ?? 0) > 0 ? [{ id: eventsId, name: "Events" }] : []),
      ...((data.jobCount ?? 0) > 0 ? [{ id: jobsId, name: "Jobs" }] : []),
      ...((data.newsCount ?? 0) > 0 ? [{ id: newsId, name: "News" }] : []),
    ];

    const links = [
      (data.validatorCount ?? 0) > 0 && {
        from: subnetId,
        to: validatorsId,
        value: data.validatorCount ?? 0,
      },
      (data.companyCount ?? 0) > 0 && {
        from: subnetId,
        to: companiesId,
        value: data.companyCount ?? 0,
      },
      (data.productCount ?? 0) > 0 && {
        from: subnetId,
        to: productsId,
        value: data.productCount ?? 0,
      },
      (data.eventCount ?? 0) > 0 && {
        from: subnetId,
        to: eventsId,
        value: data.eventCount ?? 0,
      },
      (data.jobCount ?? 0) > 0 && {
        from: subnetId,
        to: jobsId,
        value: data.jobCount ?? 0,
      },
      (data.categoryCount ?? 0) > 0 && {
        from: subnetId,
        to: categoriesId,
        value: data.categoryCount ?? 0,
      },
      (data.newsCount ?? 0) > 0 && {
        from: subnetId,
        to: newsId,
        value: data.newsCount ?? 0,
      },
      (data.subnetCount ?? 0) > 0 && {
        from: subnetId,
        to: subnetCount,
        value: data.subnetCount ?? 0,
      },
    ].filter(Boolean) as { from: string; to: string; value: number }[];

    series.nodes.data.setAll(nodes);
    series.data.setAll(links);
    series.appear(1000, 100);

    return () => {
      root.dispose();
    };
  }, [subnetId, data]);

  return (
    <Card className={className}>
      <CardContent className="h-[340px] pt-4 pl-0 ml-0 overflow-visible">
        <h1 className="text-lg pl-2">Yuma Pulse™</h1>
        <div ref={chartRef} className="w-full h-full" />
      </CardContent>
    </Card>
  );
}
