import { fetchWithFallback } from "@/lib/data/utils";
import ValidatorsClientWrapper from "./client-wrapper";

export const revalidate = 600; // 10 minutes in seconds

export default async function ValidatorsPage() {
	const [validators, performances] = await Promise.all([
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/validators`),
		fetchWithFallback(`${process.env.APP_BASE_URL}/api/validator-performance`),
	]);

	return (
		<div className="py-8 px-6 sm:px-8 lg:px-12">
			<div className="max-w-[2000px] mx-auto">
				<div className="mb-8">
					<h1 className="text-3xl font-bold mb-4">Validators</h1>
					<p className="text-lg text-muted-foreground">
						A comprehensive list of Bittensor Validators. The data is a realtime summary of validation
						activity across the network. Data is divided into root and alpha activity and summed across all
						subnets the validator is active.
					</p>
				</div>
				<ValidatorsClientWrapper validators={validators.data || []} performances={performances.data || []} />
			</div>
		</div>
	);
}
