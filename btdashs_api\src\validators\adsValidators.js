// src/validators/adsValidators.js
const Joi = require("joi");
const { sendValidationError } = require("../utils/responseWrapper");

// Campaign validation schema
const campaignSchema = Joi.object({
	advertiser_id: Joi.number().integer().positive().required().messages({
		"number.base": "Advertiser ID must be a number",
		"number.positive": "Advertiser ID must be positive",
		"any.required": "Advertiser ID is required",
	}),

	manager_id: Joi.number().integer().positive().allow(null).messages({
		"number.base": "Manager ID must be a number",
		"number.positive": "Manager ID must be positive",
		"any.required": "Manager ID is required",
	}),

	name: Joi.string().min(3).max(100).required().messages({
		"string.min": "Campaign name must be at least 3 characters",
		"string.max": "Campaign name cannot exceed 100 characters",
		"any.required": "Campaign name is required",
	}),

	total_budget: Joi.number().positive().allow(null).messages({
		"number.positive": "Total budget must be positive",
	}),

	budget_cpc: Joi.number().positive().allow(null).messages({
		"number.positive": "CPC budget must be positive",
	}),

	budget_cpm: Joi.number().positive().allow(null).messages({
		"number.positive": "CPM budget must be positive",
	}),

	start_date: Joi.date().iso().required().messages({
		"date.base": "Start date must be a valid date",
		"any.required": "Start date is required",
	}),

	end_date: Joi.date().iso().greater(Joi.ref("start_date")).required().messages({
		"date.base": "End date must be a valid date",
		"date.greater": "End date must be after start date",
		"any.required": "End date is required",
	}),

	targeting: Joi.object().allow(null).messages({
		"object.base": "Targeting must be an object",
	}),
})
	.custom((value, helpers) => {
		// At least one budget type must be specified
		if (!value.total_budget && !value.budget_cpc && !value.budget_cpm) {
			return helpers.error("custom.budget_required");
		}
		return value;
	})
	.messages({
		"custom.budget_required":
			"At least one budget type (total_budget, budget_cpc, or budget_cpm) must be specified",
	});

// Ad validation schema
const adSchema = Joi.object({
	campaign_id: Joi.number().integer().positive().required().messages({
		"number.base": "Campaign ID must be a number",
		"number.positive": "Campaign ID must be positive",
		"any.required": "Campaign ID is required",
	}),

	slot_id: Joi.number().integer().positive().required().messages({
		"number.base": "Slot ID must be a number",
		"number.positive": "Slot ID must be positive",
		"any.required": "Slot ID is required",
	}),

	title: Joi.string().min(3).max(100).required().messages({
		"string.min": "Ad title must be at least 3 characters",
		"string.max": "Ad title cannot exceed 100 characters",
		"any.required": "Ad title is required",
	}),

	image_url: Joi.string().uri().required().messages({
		"string.uri": "Image URL must be a valid URL",
		"any.required": "Image URL is required",
	}),

	target_url: Joi.string().uri().required().messages({
		"string.uri": "Target URL must be a valid URL",
		"any.required": "Target URL is required",
	}),

	max_impressions: Joi.number().integer().positive().allow(null).messages({
		"number.positive": "Max impressions must be positive",
	}),

	max_clicks: Joi.number().integer().positive().allow(null).messages({
		"number.positive": "Max clicks must be positive",
	}),

	weight: Joi.number().integer().min(1).max(1000).default(100).messages({
		"number.min": "Weight must be at least 1",
		"number.max": "Weight cannot exceed 1000",
	}),
});

// Slot validation schema
const slotSchema = Joi.object({
	name: Joi.string().min(3).max(100).required().messages({
		"string.min": "Slot name must be at least 3 characters",
		"string.max": "Slot name cannot exceed 100 characters",
		"any.required": "Slot name is required",
	}),

	width: Joi.number().integer().positive().required().messages({
		"number.positive": "Width must be positive",
		"any.required": "Width is required",
	}),

	height: Joi.number().integer().positive().required().messages({
		"number.positive": "Height must be positive",
		"any.required": "Height is required",
	}),

	description: Joi.string().max(500).allow(null, "").messages({
		"string.max": "Description cannot exceed 500 characters",
	}),

	page: Joi.string().valid("all", "home", "subnets", "companies", "newsletter").required().messages({
		"any.only": "Page must be one of: all, home, subnets, companies, newsletter",
		"any.required": "Page is required",
	}),

	price_cpm: Joi.number().positive().allow(null).messages({
		"number.positive": "CPM price must be positive",
	}),

	price_cpc: Joi.number().positive().allow(null).messages({
		"number.positive": "CPC price must be positive",
	}),

	estimated_views: Joi.number().integer().positive().allow(null).messages({
		"number.positive": "Estimated views must be positive",
	}),

	max_file_size: Joi.string().allow(null, ""),

	allowed_ad_types: Joi.array().items(Joi.string()).allow(null),
});

// Validation middleware functions
const validateCampaign = (req, res, next) => {
	const { error } = campaignSchema.validate(req.body, { abortEarly: false });
	if (error) {
		const validationErrors = error.details.map((detail) => ({
			field: detail.path.join("."),
			message: detail.message,
			value: detail.context?.value,
		}));
		return sendValidationError(res, validationErrors);
	}
	next();
};

const validateAd = (req, res, next) => {
	const { error } = adSchema.validate(req.body, { abortEarly: false });
	if (error) {
		const validationErrors = error.details.map((detail) => ({
			field: detail.path.join("."),
			message: detail.message,
			value: detail.context?.value,
		}));
		return sendValidationError(res, validationErrors);
	}
	next();
};

const validateSlot = (req, res, next) => {
	const { error } = slotSchema.validate(req.body, { abortEarly: false });
	if (error) {
		const validationErrors = error.details.map((detail) => ({
			field: detail.path.join("."),
			message: detail.message,
			value: detail.context?.value,
		}));
		return sendValidationError(res, validationErrors);
	}
	next();
};

module.exports = {
	validateCampaign,
	validateAd,
	validateSlot,
	campaignSchema,
	adSchema,
	slotSchema,
};
