// app/admin/layout.tsx
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardNav } from "@/components/dashboard-nav";
import { auth0 } from "@/lib/auth0";
import { redirect } from "next/navigation";

export default async function AdminLayout({ children }: { children: React.ReactNode }) {
	const session = await auth0.getSession();

	// If user is not logged in, redirect to home (login) page
	if (!session?.user) {
		redirect("/");
	}

	// Check if user is admin by calling the backend API
	try {
		const { token } = await auth0.getAccessToken();

		if (!token) {
			redirect("/");
		}

		// Try to access admin verify endpoint to check admin status
		const response = await fetch(`${process.env.API_BASE_URL}/admin/verify`, {
			headers: {
				Authorization: `Bearer ${token}`,
			},
		});

		// If the request fails with 403, user is not an admin
		if (response.status === 403) {
			redirect("/dashboard?error=admin_required");
		}

		// If the request fails with other errors, redirect to login
		if (!response.ok && response.status !== 403) {
			redirect("/");
		}
	} catch (error) {
		console.error("Admin access check failed:", error);
		redirect("/");
	}

	return (
		<div className="flex min-h-screen flex-col">
			<DashboardHeader isAdmin={true} />
			<div className="flex flex-1">
				<aside className="hidden w-64 border-r bg-muted/40 md:block">
					<div className="fixed h-full w-64 overflow-y-auto py-6 pr-6 lg:py-8">
						<DashboardNav isAdmin={true} />
					</div>
				</aside>
				<main className="flex-1 overflow-auto p-6">{children}</main>
			</div>
		</div>
	);
}
