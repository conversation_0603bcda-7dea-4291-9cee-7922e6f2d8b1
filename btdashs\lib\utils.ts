import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export function getSubnetSymbol(subnet: { subnet_symbol?: string; name: string }) {
	if (typeof subnet.subnet_symbol === "string" && subnet.subnet_symbol.trim()) {
		return subnet.subnet_symbol;
	}

	if (typeof subnet.name === "string" && subnet.name.trim()) {
		return subnet.name.charAt(0).toUpperCase();
	}

	return "?";
}

/**
 * Turn any string into a URL-friendly slug:
 * - lower-cases
 * - trims
 * - replaces spaces & non-word chars with single hyphens
 */
export function slugify(text: string): string {
	return text
		.toLowerCase()
		.trim()
		.replace(/[\s\W-]+/g, "-") // collapse spaces & non-word into hyphen
		.replace(/^-+|-+$/g, ""); // trim leading/trailing hyphens
}

export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout | null = null;

	return function executedFunction(...args: Parameters<T>) {
		const later = () => {
			timeout = null;
			func(...args);
		};

		if (timeout) {
			clearTimeout(timeout);
		}
		timeout = setTimeout(later, wait);
	};
}
